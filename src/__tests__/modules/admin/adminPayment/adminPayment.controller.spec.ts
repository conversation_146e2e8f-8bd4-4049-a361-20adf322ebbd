import { PaymentInstrumentRepository } from "@nest/modules/database/repositories/paymentInstument.repository";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import FakeRepository from "@tests/utils/services/FakeRepository.specs.utils";

import { AdminPaymentController } from "../../../../nestJs/modules/admin/adminPayment/adminPayment.controller";
import { PaymentInformationStatus } from "../../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../../nestJs/modules/payment/dto/paymentInformationType.dto";
import { PaymentService } from "../../../../nestJs/modules/payment/payment.service";
import { TransactionFactoryService } from "../../../../nestJs/modules/transaction/transactionFactory/transactionFactory.service";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import { fakePaymentTx } from "../../../utils/fakeData/fakeDataPaymentTx.specs.utils";

describe("AdminPaymentController", () => {
  const capturePaymentMock = jest.fn();
  const postPaymentProcessMock = jest.fn();

  const fakePaymentService = {
    capturePayment: capturePaymentMock,
  };

  const fakeTransactionFactoryService = {
    postPaymentProcess: postPaymentProcessMock,
  };

  const controller = new AdminPaymentController(
    fakePaymentService as unknown as PaymentService,
    fakeTransactionFactoryService as unknown as TransactionFactoryService,
    new FakeRepository() as unknown as TxRepository,
    new FakeRepository() as unknown as PaymentInstrumentRepository,
  );

  beforeEach(() => {
    jest.clearAllMocks();
    capturePaymentMock.mockReset();
    postPaymentProcessMock.mockReset();
  });

  describe("capturePayment", () => {
    it("should call capturePayment and postPaymentProcess", async () => {
      capturePaymentMock.mockImplementation(() => {
        const capturePayment = fakePaymentTx();
        capturePayment.type = PaymentInformationType.CAPTURE;
        capturePayment.status = PaymentInformationStatus.SUCCESS;
        return capturePayment;
      });
      const req: any = {
        get: jest.fn(),
      };
      const capturedPaymentTx = await controller.capturePayment(
        "5a04256cb293686739a252e052e898aaf321decf",
        { amount: 200 },
        req,
      );
      expect(postPaymentProcessMock).toHaveBeenCalledWith(capturedPaymentTx.tx, true);
    });

    it("should call capturePayment only and not call postPaymentProcess, when capture fail", async () => {
      capturePaymentMock.mockImplementation(() => {
        throw new Error(
          "The Capture Amount: 200 exceeds auth: 123 for paymentTx: 5a04256cb293686739a252e052e898aaf321decf",
        );
      });
      const req: any = {
        get: jest.fn(),
      };
      expect(
        controller.capturePayment("5a04256cb293686739a252e052e898aaf321decf", { amount: 200 }, req),
      ).rejects.toThrow(errorBuilder.payment.amountExceedsAuth(fakePaymentTx(), 200));
      expect(postPaymentProcessMock).not.toHaveBeenCalled();
    });
  });
});

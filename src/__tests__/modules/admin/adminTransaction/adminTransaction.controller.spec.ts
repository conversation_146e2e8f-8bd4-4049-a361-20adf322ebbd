import "../../../initTests";

import { AdminTransactionController } from "../../../../nestJs/modules/admin/adminTransaction/adminTransaction.controller";
import { AdminTransactionService } from "../../../../nestJs/modules/admin/adminTransaction/adminTransaction.service";
import Tx from "../../../../nestJs/modules/database/entities/tx.entity";
import { TransactionService } from "../../../../nestJs/modules/transaction/transaction.service";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";

describe("adminTransactionController", () => {
  const getTransactionByIdMock = jest.fn();
  const updateMetadataMock = jest.fn();
  const checkPaymentTxStatusMock = jest.fn();

  const fakeService = {
    getTransactionById: getTransactionByIdMock,
    updateMetadata: updateMetadataMock,
  };

  const fakeAdminTransactionService = {
    checkPaymentTxStatus: checkPaymentTxStatusMock,
  };

  const fakeAdminTransactionController = new AdminTransactionController(
    fakeService as unknown as TransactionService,
    fakeAdminTransactionService as unknown as AdminTransactionService,
  );

  describe("checkPaymentType", () => {
    it("should return not found if wrong txId", async () => {
      const txId = "123456";
      getTransactionByIdMock.mockImplementationOnce(() => undefined);
      await expect(fakeAdminTransactionController.checkPaymentType(txId)).rejects.toThrow(
        errorBuilder.transaction.notFound("123456"),
      );
    });

    it("should skip update if have openAuth", async () => {
      const txId = "123456";
      getTransactionByIdMock.mockReturnValueOnce(new Tx());
      checkPaymentTxStatusMock.mockReturnValue({ skipUpdate: true, paymentType: "" });

      await fakeAdminTransactionController.checkPaymentType(txId);

      expect(updateMetadataMock).not.toHaveBeenCalled();
    });

    it("should update metadata if no openAuth", async () => {
      const txId = "123456";
      const tx = new Tx();
      getTransactionByIdMock.mockReturnValueOnce(tx);
      checkPaymentTxStatusMock.mockReturnValue({ skipUpdate: false, paymentType: "" });

      await fakeAdminTransactionController.checkPaymentType(txId);

      expect(updateMetadataMock).toHaveBeenCalledWith(tx, { paymentType: "" });
    });
  });
});

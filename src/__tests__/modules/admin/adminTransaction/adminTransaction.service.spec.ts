import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { PaymentType } from "@nest/modules/payment/dto/paymentType.dto";
import FakeRepository from "@tests/utils/services/FakeRepository.specs.utils";
import { newTestDriverService, newTestLoggerServiceAdapter } from "@tests/utils/services/TestServices.specs.utils";

import { AdminTransactionService } from "../../../../nestJs/modules/admin/adminTransaction/adminTransaction.service";
import Tx from "../../../../nestJs/modules/database/entities/tx.entity";
import { TransactionService } from "../../../../nestJs/modules/transaction/transaction.service";
import {
  fakePaymentTx_Auth_SUCCESS_1,
  fakePaymentTx_CAPTURE_SUCCESS_1,
  fakePaymentTx_SALE_SUCCESS_1,
  fakePaymentTx_VOID_SUCCESS_1,
} from "../../../utils/fakeData/fakeDataPaymentTx.specs.utils";
import { fakeTx } from "../../../utils/fakeData/fakeDataTx.specs.utils";

describe("adminTransactionService", () => {
  const convertPaymentTxFromArrayToTreeMock = jest.fn();

  const fakeService = {
    convertPaymentTxFromArrayToTree: convertPaymentTxFromArrayToTreeMock,
  };

  const service = new AdminTransactionService(
    fakeService as unknown as TransactionService,
    newTestDriverService(),
    new FakeRepository() as unknown as MerchantRepository,
    newTestLoggerServiceAdapter(),
  );

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("checkPaymentTxStatus", () => {
    it("should return skipUpdate as true when no payment_tx", async () => {
      const { skipUpdate, paymentType } = service.checkPaymentTxStatus(new Tx());
      expect(skipUpdate).toBe(true);
      expect(paymentType).toBe("");
    });
    it("should return skipUpdate as true when has Open Auth", async () => {
      const tx = fakeTx;
      const convertedPaymentTx = [{ ...fakePaymentTx_Auth_SUCCESS_1, canDoVoidOrCapture: true }];
      convertPaymentTxFromArrayToTreeMock.mockReturnValueOnce(convertedPaymentTx);
      const { skipUpdate, paymentType } = service.checkPaymentTxStatus(tx);
      expect(skipUpdate).toBe(true);
      expect(paymentType).toBe("");
    });

    it("should return skipUpdate as false when doesn't havs Open Auth, and set type to be empty", async () => {
      const tx = fakeTx;
      tx.paymentTx = [fakePaymentTx_Auth_SUCCESS_1, fakePaymentTx_VOID_SUCCESS_1];
      const convertedPaymentTx = [{ ...fakePaymentTx_Auth_SUCCESS_1, canDoVoidOrCapture: false }];
      convertPaymentTxFromArrayToTreeMock.mockReturnValueOnce(convertedPaymentTx);
      const { skipUpdate, paymentType } = service.checkPaymentTxStatus(tx);
      expect(skipUpdate).toBe(false);
      expect(paymentType).toBe("");
    });

    it("should return skipUpdate as false when doesn't havs Open Auth, and set type to be DASH", async () => {
      const tx = fakeTx;
      tx.paymentTx = [fakePaymentTx_Auth_SUCCESS_1, fakePaymentTx_CAPTURE_SUCCESS_1];
      const convertedPaymentTx = [{ ...fakePaymentTx_Auth_SUCCESS_1, canDoVoidOrCapture: false }];
      convertPaymentTxFromArrayToTreeMock.mockReturnValueOnce(convertedPaymentTx);
      const { skipUpdate, paymentType } = service.checkPaymentTxStatus(tx);
      expect(skipUpdate).toBe(false);
      expect(paymentType).toBe(PaymentType.DASH);
    });

    it("should return skipUpdate as false when doesn't havs Open Auth, and set type to be DASH for SALE type", async () => {
      const tx = fakeTx;
      tx.paymentTx = [fakePaymentTx_SALE_SUCCESS_1];
      const convertedPaymentTx = [{ ...fakePaymentTx_SALE_SUCCESS_1, canDoVoidOrCapture: true }];
      convertPaymentTxFromArrayToTreeMock.mockReturnValueOnce(convertedPaymentTx);
      const { skipUpdate, paymentType } = service.checkPaymentTxStatus(tx);
      expect(skipUpdate).toBe(false);
      expect(paymentType).toBe(PaymentType.DASH);
    });
  });
});

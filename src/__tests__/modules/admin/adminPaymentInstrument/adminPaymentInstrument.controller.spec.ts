import { randomUUID } from "crypto";

import { AdminPaymentInstrumentController } from "../../../../nestJs/modules/admin/adminPaymentInstrument/adminPaymentInstrument.controller";
import { PaymentInstrumentResponseDto } from "../../../../nestJs/modules/admin/adminPaymentInstrument/dto/paymentInstrumentResponse.dto";
import PaymentInstrument from "../../../../nestJs/modules/database/entities/paymentInstrument.entity";
import { PaymentGatewayTypes } from "../../../../nestJs/modules/payment/dto/paymentGatewayTypes.dto";
import {
  PaymentInstrumentState,
  PaymentInstrumentType,
} from "../../../../nestJs/modules/payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import "../../../initTests";
import { mockFind } from "../../../utils/services/FakeRepository.specs.utils";
import { newTestAdminPaymentInstrumentController } from "../../../utils/services/TestServices.specs.utils";

describe("adminPaymentInstrumentController", () => {
  const paymentInstrument = new PaymentInstrument();
  paymentInstrument.id = randomUUID();
  paymentInstrument.instrumentIdentifier = "instrumentIdentifier";
  paymentInstrument.cardHolderName = "cardHolderName";
  paymentInstrument.verifiedAt = new Date();
  paymentInstrument.expirationDate = new Date();
  paymentInstrument.expirationYear = "expirationYear";
  paymentInstrument.expirationMonth = "expirationMonth";
  paymentInstrument.state = PaymentInstrumentState.ACTIVE;
  paymentInstrument.cardPrefix = "cardPrefix";
  paymentInstrument.cardSuffix = "cardSuffix";
  paymentInstrument.verificationTransactionId = "verificationTransactionId";
  paymentInstrument.isPayerAuthEnroled = true;
  paymentInstrument.isPreferred = true;
  paymentInstrument.cardType = PaymentInstrumentType.MASTERCARD;
  paymentInstrument.deletedAt = new Date();
  paymentInstrument.paymentGateway = PaymentGatewayTypes.SOEPAY;
  paymentInstrument.token = "token";

  const paymentInstrumentResponse = PaymentInstrumentResponseDto.fromEntity(paymentInstrument);

  let adminPaymentInstrumentController: AdminPaymentInstrumentController;
  beforeEach(() => {
    adminPaymentInstrumentController = newTestAdminPaymentInstrumentController();
  });

  describe("checkGetPaymentInstrumentsForUser", () => {
    it("should return array of payment instrument responses", async () => {
      const userId = randomUUID();
      mockFind.mockResolvedValue([paymentInstrument, paymentInstrument, paymentInstrument]);
      expect(await adminPaymentInstrumentController.getPaymentInstrumentsForUser(userId)).toStrictEqual([
        paymentInstrumentResponse,
        paymentInstrumentResponse,
        paymentInstrumentResponse,
      ]);
      expect(mockFind).toHaveBeenCalledWith({ where: { user: { id: userId } }, withDeleted: true });
      expect(mockFind).toHaveBeenCalledTimes(1);
    });
  });
});

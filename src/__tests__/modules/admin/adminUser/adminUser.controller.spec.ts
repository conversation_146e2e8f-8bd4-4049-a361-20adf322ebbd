import { randomUUID } from "crypto";

import { <PERSON>ike } from "typeorm";

import { AdminUserController } from "../../../../nestJs/modules/admin/adminUser/adminUser.controller";
import {
  UserListingQueryDto,
  UserSortableType,
} from "../../../../nestJs/modules/admin/adminUser/dto/userListingQuery.dto";
import { UserResponseDto } from "../../../../nestJs/modules/admin/adminUser/dto/userResponse.dto";
import User from "../../../../nestJs/modules/database/entities/user.entity";
import { GenderType, PreferredLanguageType } from "../../../../nestJs/modules/identity/dto/user.dto";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import { DirectionType } from "../../../../nestJs/modules/validation/dto/listingSchema.dto";
import "../../../initTests";
import {
  mockAndWhere,
  mockFindOne,
  mockGetManyAndCount,
  mockOrWhere,
  mockOrderBy,
  mockSkip,
  mockTake,
} from "../../../utils/services/FakeRepository.specs.utils";
import { newTestAdminUserController } from "../../../utils/services/TestServices.specs.utils";

describe("adminUserController", () => {
  const user: User = {
    id: randomUUID(),
    firstName: "firstName",
    lastName: "lastName",
    email: "<EMAIL>",
    appDatabaseId: "appDatabaseId",
    dateOfBirth: new Date(),
    deletedAt: new Date(),
    gender: GenderType.MALE,
    publicKey: "publicKey1234567890",
    phoneNumber: "+85212345678",
    marketingPreferenceEmail: false,
    marketingPreferenceSMS: false,
    marketingPreferencePush: false,
    marketingConsent: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    pinErrorCount: 0,
    preferredLanguage: PreferredLanguageType.EN,
    maskedPhoneNumber: "+8521234***",
    paymentInstruments: [],
  };

  const userResponse: UserResponseDto = UserResponseDto.fromEntity(user);

  let adminUserController: AdminUserController;
  beforeEach(() => {
    adminUserController = newTestAdminUserController();
  });

  describe("checkGetUserListing", () => {
    it("should return paginated user response with default query", async () => {
      const defaultQuery: UserListingQueryDto = {};

      mockGetManyAndCount.mockResolvedValue([[user], 1]);
      expect(await adminUserController.getUsers(defaultQuery)).toStrictEqual({
        count: 1,
        data: [userResponse],
      });
      expect(mockOrderBy).toHaveBeenCalledWith(`user.${UserSortableType.CREATED_AT}`, DirectionType.DESC);
      expect(mockSkip).toHaveBeenCalledWith(0);
      expect(mockTake).toHaveBeenCalledWith(50);
      expect(mockGetManyAndCount).toHaveBeenCalled();
    });

    it("should query first and last names with name query", async () => {
      const query: UserListingQueryDto = {
        name: "first",
      };

      mockGetManyAndCount.mockResolvedValue([[user], 1]);
      await adminUserController.getUsers(query);
      expect(mockAndWhere).toHaveBeenCalledWith({ firstName: ILike(`%${query.name}%`) });
      expect(mockOrWhere).toHaveBeenCalledWith({ lastName: ILike(`%${query.name}%`) });
    });
  });

  describe("checkGetUserById", () => {
    it("should find one user and return user response", async () => {
      mockFindOne.mockResolvedValue(user);
      expect(await adminUserController.getUserById(user.id)).toStrictEqual(userResponse);
      expect(mockFindOne).toHaveBeenCalledWith({ where: { id: user.id } });
      expect(mockFindOne).toHaveBeenCalledTimes(1);
    });

    it("should return notFoundInSql error if no user in database", async () => {
      const searchingUserId = randomUUID();
      mockFindOne.mockResolvedValue(null);
      await expect(adminUserController.getUserById(searchingUserId)).rejects.toThrow(
        errorBuilder.user.notFoundInSql(searchingUserId),
      );
    });
  });

  // TODO: Uncomment this test after re-implementing updateUserById
  // describe("checkUpdateUserById", () => {
  //   it("should call update of user", async () => {
  //     const updateUserDto: UpdateUserDto = {
  //       firstName: "updatedFirstName",
  //     };
  //     const updatedUser: User = {
  //       ...user,
  //       ...updateUserDto,
  //       maskedPhoneNumber: "+8521234***",
  //     };
  //     mockFindOne.mockResolvedValue(user);
  //     mockSave.mockResolvedValue(updatedUser);
  //     expect(await adminUserController.updateUserById(user.id, updateUserDto)).toStrictEqual(
  //       UserResponseDto.fromEntity(updatedUser),
  //     );
  //     expect(mockSave).toHaveBeenCalledTimes(1);
  //   });
  // });
});

import { randomUUID } from "crypto";

import { AdminNotificationController } from "../../../../../nestJs/modules/admin/adminNotification/admin-notification.controller";
import { AdminNotificationService } from "../../../../../nestJs/modules/admin/adminNotification/admin-notification.service";
import {
  CreateManyNotificationRequestDto,
  NotificationTaskRequestStatus,
} from "../../../../../nestJs/modules/cloud-task-notification-handler/dto/request.dto";
import NotificationTask, {
  NotificationTaskStatus,
  NotificationTaskType,
  NotificationUserType,
} from "../../../../../nestJs/modules/database/entities/notificationTask.entity";
import {
  NotificationTaskFilterDto,
  NotificationTaskSortKey,
  NotificationTaskSortOrder,
} from "../../../../../nestJs/modules/notification/dto/notification-filters.dto";
import "../../../../initTests";

describe("AdminNotification Module Integration", () => {
  let adminNotificationController: AdminNotificationController;
  let adminNotificationService: AdminNotificationService;

  const mockAdminEmail = "<EMAIL>";
  const mockRequest = {
    user: {
      email: mockAdminEmail,
    },
  };

  const mockTaskId = randomUUID();
  const mockCloudTaskReference =
    "projects/dash-dev2-edcb3/locations/asia-east2/queues/notifications-task-queue/tasks/123456789";

  // Create a partial mock that has the required properties
  const mockNotificationTask = Object.assign(new NotificationTask(), {
    id: mockTaskId,
    name: "Test Notification",
    type: NotificationTaskType.PUSH,
    userType: NotificationUserType.USERS,
    status: NotificationTaskStatus.SCHEDULED,
    createdBy: mockAdminEmail,
    createdAt: new Date(),
    updatedAt: new Date(),
    scheduledAt: new Date(),
    cloudTaskReference: mockCloudTaskReference,
    payload: {
      phoneNumbers: ["+85254000455", "+85251159365"],
      notificationRequest: {
        titleEn: "Test Notification",
        titleHk: "測試通知",
        bodyEn: "This is a test notification",
        bodyHk: "這是一個測試通知",
      },
    },
    failureReason: undefined,
  });

  beforeEach(() => {
    // Create mocks
    adminNotificationService = {
      createNotifications: jest.fn().mockResolvedValue([mockNotificationTask]),
      updateNotifications: jest.fn().mockResolvedValue(mockNotificationTask),
      deleteNotifications: jest.fn().mockResolvedValue(mockNotificationTask),
      getNotifications: jest.fn().mockResolvedValue({
        data: [mockNotificationTask],
        pagination: {
          count: 1,
          page: 1,
          pageSize: 10,
        },
      }),
    } as unknown as AdminNotificationService;

    adminNotificationController = new AdminNotificationController(
      {
        log: jest.fn(),
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
      } as any,
      adminNotificationService,
    );
  });

  describe("Module Integration", () => {
    it("should create notifications through the controller and service", async () => {
      const createDto: CreateManyNotificationRequestDto = {
        name: "Test Campaign",
        type: NotificationTaskType.PUSH,
        status: NotificationTaskRequestStatus.SCHEDULED,
        userType: NotificationUserType.USERS,
        phoneNumbers: ["+85254000455", "+85251159365"],
        notificationRequest: {
          titleEn: "Test Notification",
          titleHk: "測試通知",
          bodyEn: "This is a test notification",
          bodyHk: "這是一個測試通知",
        },
        scheduledAt: new Date(),
      };

      const result = await adminNotificationController.createNotifications(createDto, mockRequest as any);

      expect(adminNotificationService.createNotifications).toHaveBeenCalledWith(createDto, mockAdminEmail);
      expect(result).toEqual([mockNotificationTask]);
    });

    it("should get notifications through the controller and service", async () => {
      const filterDto: NotificationTaskFilterDto = {
        page: 1,
        pageSize: 10,
        sort: NotificationTaskSortOrder.DESC,
        sortBy: NotificationTaskSortKey.CREATED_AT,
      };

      const result = await adminNotificationController.getNotifications(filterDto);

      expect(adminNotificationService.getNotifications).toHaveBeenCalledWith(filterDto);
      expect(result).toEqual({
        data: [mockNotificationTask],
        pagination: {
          count: 1,
          page: 1,
          pageSize: 10,
        },
      });
    });

    it("should update notifications through the controller and service", async () => {
      const updateDto = {
        name: "Updated Test Campaign",
        notificationRequest: {
          titleEn: "Updated Test Notification",
          titleHk: "更新測試通知",
          bodyEn: "This is an updated test notification",
          bodyHk: "這是一個更新的測試通知",
        },
        scheduledAt: new Date(),
      };

      const result = await adminNotificationController.updateNotifications(mockTaskId, updateDto, mockRequest as any);

      expect(adminNotificationService.updateNotifications).toHaveBeenCalledWith(mockAdminEmail, mockTaskId, updateDto);
      expect(result).toEqual(mockNotificationTask);
    });

    it("should delete notifications through the controller and service", async () => {
      const result = await adminNotificationController.deleteNotifications(mockTaskId, mockRequest as any);

      expect(adminNotificationService.deleteNotifications).toHaveBeenCalledWith(mockAdminEmail, mockTaskId);
      expect(result).toEqual(mockNotificationTask);
    });
  });
});

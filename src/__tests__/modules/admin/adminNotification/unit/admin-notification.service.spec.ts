import { randomUUID } from "crypto";

import { Test, TestingModule } from "@nestjs/testing";

import { NotificationManagerService } from "@nest/modules/notification/notification-manager.service";

import { AdminNotificationService } from "../../../../../nestJs/modules/admin/adminNotification/admin-notification.service";
import { CloudTaskClientService } from "../../../../../nestJs/modules/cloud-task-client/cloud-task-client.service";
import {
  CreateManyNotificationRequestDto,
  NotificationTaskRequestStatus,
  UpdateManyNotificationRequestDto,
} from "../../../../../nestJs/modules/cloud-task-notification-handler/dto/request.dto";
import NotificationTask, {
  NotificationTaskStatus,
  NotificationTaskStatusError,
  NotificationTaskType,
  NotificationUserType,
} from "../../../../../nestJs/modules/database/entities/notificationTask.entity";
import {
  NotificationTaskFilterDto,
  NotificationTaskSortKey,
  NotificationTaskSortOrder,
} from "../../../../../nestJs/modules/notification/dto/notification-filters.dto";
import { UserNotificationFactory } from "../../../../../nestJs/modules/notification/user-notification.factory";
import LoggerServiceAdapter from "../../../../../nestJs/modules/utils/logger/logger.service";

import "../../../../initTests";

describe("AdminNotificationService", () => {
  let adminNotificationService: AdminNotificationService;
  let cloudTaskClientService: CloudTaskClientService;
  let userNotificationFactory: UserNotificationFactory;
  let notificationManagerService: NotificationManagerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminNotificationService,
        {
          provide: CloudTaskClientService,
          useValue: {
            enqueueSendNotificationTask: jest.fn(),
            removeTaskFromQueue: jest.fn(),
          },
        },
        {
          provide: UserNotificationFactory,
          useValue: {
            getSender: jest.fn(),
            getValidator: jest.fn(),
            getValidatorByTaskId: jest.fn(),
          },
        },
        {
          provide: LoggerServiceAdapter,
          useValue: {
            log: jest.fn(),
            info: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
          },
        },
        {
          provide: NotificationManagerService,
          useValue: {
            createNotificationTask: jest.fn(),
            updateNotificationAfterCallback: jest.fn(),
            deleteNotificationAfterCallback: jest.fn(),
            getNotificationTasks: jest.fn(),
          },
        },
      ],
      controllers: [],
    }).compile();

    cloudTaskClientService = module.get<CloudTaskClientService>(CloudTaskClientService);
    userNotificationFactory = module.get<UserNotificationFactory>(UserNotificationFactory);
    notificationManagerService = module.get<NotificationManagerService>(NotificationManagerService);
    adminNotificationService = module.get<AdminNotificationService>(AdminNotificationService);
  });

  describe("constructor", () => {
    it("should be defined", () => {
      expect(adminNotificationService).toBeDefined();
      expect(cloudTaskClientService).toBeDefined();
      expect(userNotificationFactory).toBeDefined();
      expect(notificationManagerService).toBeDefined();
    });
  });

  describe("createNotifications", () => {
    it("should create notification task and enqueue cloud task", async () => {
      const createDto: CreateManyNotificationRequestDto = {
        name: "Test Campaign",
        type: NotificationTaskType.PUSH,
        status: NotificationTaskRequestStatus.SCHEDULED,
        userType: NotificationUserType.USERS,
        phoneNumbers: ["+85211111111", "+85299999999"],
        notificationRequest: {
          titleEn: "Test Notification",
          titleHk: "測試通知",
          bodyEn: "This is a test notification",
          bodyHk: "這是一個測試通知",
        },
        scheduledAt: new Date(),
      };

      const mockTaskId = randomUUID();
      const mockCloudTaskReference = randomUUID();
      const mockAdminEmail = "<EMAIL>";

      const mockNotificationTask = new NotificationTask();

      jest.spyOn(userNotificationFactory, "getValidator").mockReturnValue({
        validateUserPhoneNumbers: jest.fn(),
      });
      jest
        .spyOn(notificationManagerService, "createNotificationTask")
        .mockImplementation(async (dto, createdBy, callback) => {
          mockNotificationTask.createdBy = createdBy;
          mockNotificationTask.id = mockTaskId;
          mockNotificationTask.scheduledAt = dto.scheduledAt;
          await callback(mockNotificationTask);
          return mockNotificationTask;
        });
      jest.spyOn(cloudTaskClientService, "enqueueSendNotificationTask").mockResolvedValue(mockCloudTaskReference);

      const result = await adminNotificationService.createNotifications(createDto, mockAdminEmail);

      expect(userNotificationFactory.getValidator).toHaveBeenCalledWith(createDto.userType);
      expect(notificationManagerService.createNotificationTask).toHaveBeenCalledWith(
        createDto,
        mockAdminEmail,
        expect.any(Function),
      );
      expect(cloudTaskClientService.enqueueSendNotificationTask).toHaveBeenCalledWith(
        mockTaskId,
        mockNotificationTask.scheduledAt,
      );
      expect(result).toEqual([mockNotificationTask]);
    });

    it("should throw error for invalid phone numbers", async () => {
      const createDto: CreateManyNotificationRequestDto = {
        name: "Test Campaign",
        type: NotificationTaskType.PUSH,
        status: NotificationTaskRequestStatus.SCHEDULED,
        userType: NotificationUserType.USERS,
        phoneNumbers: ["+85211111111", "+85299999999"],
        notificationRequest: {
          titleEn: "Test Notification",
          titleHk: "測試通知",
          bodyEn: "This is a test notification",
          bodyHk: "這是一個測試通知",
        },
        scheduledAt: new Date(),
      };

      const mockAdminEmail = "<EMAIL>";

      jest.spyOn(userNotificationFactory, "getValidator").mockReturnValue({
        validateUserPhoneNumbers: jest.fn().mockImplementation(() => {
          throw new Error("Invalid phone numbers");
        }),
      });

      await expect(adminNotificationService.createNotifications(createDto, mockAdminEmail)).rejects.toThrow(
        new Error("Invalid phone numbers"),
      );

      expect(userNotificationFactory.getValidator).toHaveBeenCalledWith(createDto.userType);
      expect(notificationManagerService.createNotificationTask).not.toHaveBeenCalled();
      expect(cloudTaskClientService.enqueueSendNotificationTask).not.toHaveBeenCalled();
    });

    it("should catch the error when createNotificationTask fails", async () => {
      const createDto: CreateManyNotificationRequestDto = {
        name: "Test Campaign",
        type: NotificationTaskType.PUSH,
        status: NotificationTaskRequestStatus.SCHEDULED,
        userType: NotificationUserType.USERS,
        phoneNumbers: ["+85211111111", "+85299999999"],
        notificationRequest: {
          titleEn: "Test Notification",
          titleHk: "測試通知",
          bodyEn: "This is a test notification",
          bodyHk: "這是一個測試通知",
        },
        scheduledAt: new Date(),
      };

      const mockAdminEmail = "<EMAIL>";

      jest.spyOn(userNotificationFactory, "getValidator").mockReturnValue({
        validateUserPhoneNumbers: jest.fn(),
      });
      jest
        .spyOn(notificationManagerService, "createNotificationTask")
        .mockImplementation(async (dto, createdBy, callback) => {
          throw new Error("Failed to create notification task");
        });

      await expect(adminNotificationService.createNotifications(createDto, mockAdminEmail)).rejects.toThrow(
        new Error("Failed to create notification task"),
      );

      expect(userNotificationFactory.getValidator).toHaveBeenCalledWith(createDto.userType);
      expect(notificationManagerService.createNotificationTask).toHaveBeenCalledWith(
        createDto,
        mockAdminEmail,
        expect.any(Function),
      );
      expect(cloudTaskClientService.enqueueSendNotificationTask).not.toHaveBeenCalled();
    });
  });
  describe("updateNotifications", () => {
    const mockTaskId = randomUUID();
    const mockAdminEmail = "<EMAIL>";
    const mockCloudTaskReference = randomUUID();

    it("should update notification task and re-enqueue cloud task", async () => {
      const scheduledDate = new Date();
      const updateDto: UpdateManyNotificationRequestDto = {
        name: "Updated Campaign",
        phoneNumbers: ["+85211111111", "+85222222222"],
        notificationRequest: {
          titleEn: "Updated Notification",
          titleHk: "更新通知",
          bodyEn: "This is an updated notification",
          bodyHk: "這是一個更新通知",
        },
        scheduledAt: scheduledDate,
      };

      const mockNotificationTask = new NotificationTask();
      mockNotificationTask.id = mockTaskId;
      mockNotificationTask.cloudTaskReference = mockCloudTaskReference;
      mockNotificationTask.scheduledAt = scheduledDate;

      const mockValidator = {
        validateUserPhoneNumbers: jest.fn(),
      };

      jest.spyOn(userNotificationFactory, "getValidatorByTaskId").mockResolvedValue(mockValidator);
      jest
        .spyOn(notificationManagerService, "updateNotificationAfterCallback")
        .mockImplementation(async (taskId, adminEmail, updateDto, callback) => {
          await callback(mockNotificationTask);
          return mockNotificationTask;
        });
      jest.spyOn(cloudTaskClientService, "removeTaskFromQueue").mockResolvedValue({} as any);
      jest.spyOn(cloudTaskClientService, "enqueueSendNotificationTask").mockResolvedValue(mockCloudTaskReference);

      const result = await adminNotificationService.updateNotifications(mockAdminEmail, mockTaskId, updateDto);

      expect(userNotificationFactory.getValidatorByTaskId).toHaveBeenCalledWith(mockTaskId);
      expect(notificationManagerService.updateNotificationAfterCallback).toHaveBeenCalledWith(
        mockTaskId,
        mockAdminEmail,
        updateDto,
        expect.any(Function),
      );
      expect(cloudTaskClientService.removeTaskFromQueue).toHaveBeenCalledWith(mockCloudTaskReference);
      expect(cloudTaskClientService.enqueueSendNotificationTask).toHaveBeenCalledWith(
        mockTaskId,
        mockNotificationTask.scheduledAt,
      );
      expect(result).toEqual(mockNotificationTask);
    });

    it("should handle task without existing cloudTaskReference", async () => {
      const scheduledDate = new Date();
      const updateDto: UpdateManyNotificationRequestDto = {
        name: "Updated Campaign",
        phoneNumbers: ["+85211111111", "+85222222222"],
        scheduledAt: scheduledDate,
      };

      const mockNotificationTask = new NotificationTask();
      mockNotificationTask.id = mockTaskId;
      mockNotificationTask.cloudTaskReference = null as unknown as string; // No existing cloud task reference
      mockNotificationTask.scheduledAt = scheduledDate;

      const mockValidator = {
        validateUserPhoneNumbers: jest.fn(),
      };

      jest.spyOn(userNotificationFactory, "getValidatorByTaskId").mockResolvedValue(mockValidator);
      jest
        .spyOn(notificationManagerService, "updateNotificationAfterCallback")
        .mockImplementation(async (taskId, adminEmail, updateDto, callback) => {
          await callback(mockNotificationTask);
          return mockNotificationTask;
        });
      jest.spyOn(cloudTaskClientService, "removeTaskFromQueue").mockResolvedValue({} as any);
      jest.spyOn(cloudTaskClientService, "enqueueSendNotificationTask").mockResolvedValue(mockCloudTaskReference);

      const result = await adminNotificationService.updateNotifications(mockAdminEmail, mockTaskId, updateDto);

      expect(userNotificationFactory.getValidatorByTaskId).toHaveBeenCalledWith(mockTaskId);
      expect(notificationManagerService.updateNotificationAfterCallback).toHaveBeenCalledWith(
        mockTaskId,
        mockAdminEmail,
        updateDto,
        expect.any(Function),
      );
      expect(cloudTaskClientService.removeTaskFromQueue).not.toHaveBeenCalled();
      expect(cloudTaskClientService.enqueueSendNotificationTask).toHaveBeenCalledWith(
        mockTaskId,
        mockNotificationTask.scheduledAt,
      );
      expect(result).toEqual(mockNotificationTask);
    });

    it("should throw error for invalid phone numbers", async () => {
      const scheduledDate = new Date();
      const updateDto: UpdateManyNotificationRequestDto = {
        name: "Updated Campaign",
        phoneNumbers: ["+85211111111", "invalid-number"],
        scheduledAt: scheduledDate,
      };

      const mockValidator = {
        validateUserPhoneNumbers: jest.fn().mockImplementation(() => {
          throw new Error("Invalid phone numbers");
        }),
      };

      jest.spyOn(userNotificationFactory, "getValidatorByTaskId").mockResolvedValue(mockValidator);

      await expect(adminNotificationService.updateNotifications(mockAdminEmail, mockTaskId, updateDto)).rejects.toThrow(
        new Error("Invalid phone numbers"),
      );

      expect(userNotificationFactory.getValidatorByTaskId).toHaveBeenCalledWith(mockTaskId);
      expect(notificationManagerService.updateNotificationAfterCallback).not.toHaveBeenCalled();
    });

    it("should handle NotificationTaskStatusError and rethrow as taskInvalidState error", async () => {
      const scheduledDate = new Date();
      const updateDto: UpdateManyNotificationRequestDto = {
        name: "Updated Campaign",
        phoneNumbers: ["+85211111111", "+85222222222"],
        scheduledAt: scheduledDate,
      };

      const statusError = new NotificationTaskStatusError(
        mockTaskId,
        NotificationTaskStatus.PROCESSING,
        NotificationTaskStatus.SCHEDULED,
      );

      const mockValidator = {
        validateUserPhoneNumbers: jest.fn(),
      };

      jest.spyOn(userNotificationFactory, "getValidatorByTaskId").mockResolvedValue(mockValidator);
      jest.spyOn(notificationManagerService, "updateNotificationAfterCallback").mockRejectedValue(statusError);

      await expect(
        adminNotificationService.updateNotifications(mockAdminEmail, mockTaskId, updateDto),
      ).rejects.toThrow();

      expect(userNotificationFactory.getValidatorByTaskId).toHaveBeenCalledWith(mockTaskId);
      expect(notificationManagerService.updateNotificationAfterCallback).toHaveBeenCalledWith(
        mockTaskId,
        mockAdminEmail,
        updateDto,
        expect.any(Function),
      );
    });

    it("should rethrow other errors", async () => {
      const scheduledDate = new Date();
      const updateDto: UpdateManyNotificationRequestDto = {
        name: "Updated Campaign",
        phoneNumbers: ["+85211111111", "+85222222222"],
        scheduledAt: scheduledDate,
      };

      const genericError = new Error("Something went wrong");

      const mockValidator = {
        validateUserPhoneNumbers: jest.fn(),
      };

      jest.spyOn(userNotificationFactory, "getValidatorByTaskId").mockResolvedValue(mockValidator);
      jest.spyOn(notificationManagerService, "updateNotificationAfterCallback").mockRejectedValue(genericError);

      await expect(adminNotificationService.updateNotifications(mockAdminEmail, mockTaskId, updateDto)).rejects.toThrow(
        "Something went wrong",
      );

      expect(userNotificationFactory.getValidatorByTaskId).toHaveBeenCalledWith(mockTaskId);
      expect(notificationManagerService.updateNotificationAfterCallback).toHaveBeenCalledWith(
        mockTaskId,
        mockAdminEmail,
        updateDto,
        expect.any(Function),
      );
    });
  });

  describe("deleteNotifications", () => {
    const mockTaskId = randomUUID();
    const mockAdminEmail = "<EMAIL>";
    const mockCloudTaskReference = randomUUID();

    it("should delete notification task and remove cloud task", async () => {
      const mockNotificationTask = new NotificationTask();
      mockNotificationTask.id = mockTaskId;
      mockNotificationTask.cloudTaskReference = mockCloudTaskReference;

      jest
        .spyOn(notificationManagerService, "deleteNotificationAfterCallback")
        .mockImplementation(async (taskId, deletedBy, callback) => {
          await callback(mockNotificationTask);
          return mockNotificationTask;
        });
      jest.spyOn(cloudTaskClientService, "removeTaskFromQueue").mockResolvedValue({} as any);

      const result = await adminNotificationService.deleteNotifications(mockAdminEmail, mockTaskId);

      expect(notificationManagerService.deleteNotificationAfterCallback).toHaveBeenCalledWith(
        mockTaskId,
        mockAdminEmail,
        expect.any(Function),
      );
      expect(cloudTaskClientService.removeTaskFromQueue).toHaveBeenCalledWith(mockCloudTaskReference);
      expect(result).toEqual(mockNotificationTask);
    });

    it("should handle NotificationTaskStatusError and rethrow as taskInvalidState error", async () => {
      const statusError = new NotificationTaskStatusError(
        mockTaskId,
        NotificationTaskStatus.PROCESSING,
        NotificationTaskStatus.DELETED,
      );

      jest.spyOn(notificationManagerService, "deleteNotificationAfterCallback").mockRejectedValue(statusError);

      await expect(adminNotificationService.deleteNotifications(mockAdminEmail, mockTaskId)).rejects.toThrow();

      expect(notificationManagerService.deleteNotificationAfterCallback).toHaveBeenCalledWith(
        mockTaskId,
        mockAdminEmail,
        expect.any(Function),
      );
      expect(cloudTaskClientService.removeTaskFromQueue).not.toHaveBeenCalled();
    });

    it("should rethrow other errors", async () => {
      const genericError = new Error("Something went wrong");

      jest.spyOn(notificationManagerService, "deleteNotificationAfterCallback").mockRejectedValue(genericError);

      await expect(adminNotificationService.deleteNotifications(mockAdminEmail, mockTaskId)).rejects.toThrow(
        "Something went wrong",
      );

      expect(notificationManagerService.deleteNotificationAfterCallback).toHaveBeenCalledWith(
        mockTaskId,
        mockAdminEmail,
        expect.any(Function),
      );
      expect(cloudTaskClientService.removeTaskFromQueue).not.toHaveBeenCalled();
    });
  });

  describe("getNotifications", () => {
    it("should call notificationManagerService.getNotificationTasks with correct parameters", async () => {
      const filterDto: NotificationTaskFilterDto = {
        page: 1,
        pageSize: 10,
        sort: NotificationTaskSortOrder.DESC,
        sortBy: NotificationTaskSortKey.CREATED_AT,
      };

      const paginatedResponse = {
        data: [new NotificationTask()],
        pagination: {
          count: 1,
          page: 1,
          pageSize: 10,
        },
      };

      jest.spyOn(notificationManagerService, "getNotificationTasks").mockResolvedValue(paginatedResponse);

      const result = await adminNotificationService.getNotifications(filterDto);

      expect(notificationManagerService.getNotificationTasks).toHaveBeenCalledWith(filterDto);
      expect(result).toEqual(paginatedResponse);
    });
  });
});

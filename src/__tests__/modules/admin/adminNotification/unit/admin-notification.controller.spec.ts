import { randomUUID } from "crypto";

import { Test, TestingModule } from "@nestjs/testing";

import { AdminNotificationService } from "@nest/modules/admin/adminNotification/admin-notification.service";
import { CloudTaskClientService } from "@nest/modules/cloud-task-client/cloud-task-client.service";
import { NotificationManagerService } from "@nest/modules/notification/notification-manager.service";
import { UserNotificationFactory } from "@nest/modules/notification/user-notification.factory";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

import { AdminNotificationController } from "../../../../../nestJs/modules/admin/adminNotification/admin-notification.controller";
import {
  CreateManyNotificationRequestDto,
  NotificationTaskRequestStatus,
  UpdateManyNotificationRequestDto,
} from "../../../../../nestJs/modules/cloud-task-notification-handler/dto/request.dto";
import NotificationTask, {
  NotificationTaskStatus,
  NotificationTaskType,
  NotificationUserType,
} from "../../../../../nestJs/modules/database/entities/notificationTask.entity";
import {
  NotificationTaskFilterDto,
  NotificationTaskSortKey,
  NotificationTaskSortOrder,
} from "../../../../../nestJs/modules/notification/dto/notification-filters.dto";
import { PaginatedResponseDto } from "../../../../../nestJs/modules/utils/paginated.dto";
import "../../../../initTests";
import { newTestAdminNotificationController } from "../../../../utils/services/TestServices.specs.utils";

describe("AdminNotificationController", () => {
  let adminNotificationController: AdminNotificationController;
  let adminNotificationService: AdminNotificationService;

  const mockAdminEmail = "<EMAIL>";
  const mockRequest = {
    user: {
      email: mockAdminEmail,
    },
  };

  // Create a partial mock that has the required properties
  const mockNotificationTask = Object.assign(new NotificationTask(), {
    id: randomUUID(),
    name: "Test Notification",
    type: NotificationTaskType.PUSH,
    userType: NotificationUserType.USERS,
    status: NotificationTaskStatus.SCHEDULED,
    createdBy: mockAdminEmail,
    createdAt: new Date(),
    updatedAt: new Date(),
    scheduledAt: new Date(),
    cloudTaskReference: "projects/dash-dev2-edcb3/locations/asia-east2/queues/notifications-task-queue/tasks/123456789",
    payload: {
      phoneNumbers: ["+85254000455", "+85251159365"],
      notificationRequest: {
        titleEn: "Test Notification",
        titleHk: "測試通知",
        bodyEn: "This is a test notification",
        bodyHk: "這是一個測試通知",
      },
    },
    failureReason: undefined,
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminNotificationService,
        {
          provide: CloudTaskClientService,
          useValue: {
            enqueueSendNotificationTask: jest.fn(),
            removeTaskFromQueue: jest.fn(),
          },
        },
        {
          provide: UserNotificationFactory,
          useValue: {
            getSender: jest.fn(),
            getValidator: jest.fn(),
            getValidatorByTaskId: jest.fn(),
          },
        },
        {
          provide: LoggerServiceAdapter,
          useValue: {},
        },
        {
          provide: NotificationManagerService,
          useValue: {
            createNotificationTask: jest.fn(),
            updateNotificationAfterCallback: jest.fn(),
            deleteNotificationAfterCallback: jest.fn(),
            getNotificationTasks: jest.fn(),
          },
        },
      ],
      controllers: [],
    }).compile();

    adminNotificationService = module.get<AdminNotificationService>(AdminNotificationService);
    adminNotificationController = newTestAdminNotificationController();
  });

  describe("constructor", () => {
    it("should be defined", () => {
      expect(adminNotificationController).toBeDefined();
      expect(adminNotificationService).toBeDefined();
      expect(adminNotificationController["adminNotificationService"]).toBeDefined();
    });
  });

  describe("createNotifications", () => {
    it("should call adminNotificationService.createNotifications with correct parameters", async () => {
      const createDto: CreateManyNotificationRequestDto = {
        name: "Test Campaign",
        type: NotificationTaskType.PUSH,
        status: NotificationTaskRequestStatus.SCHEDULED,
        userType: NotificationUserType.USERS,
        phoneNumbers: ["+85254000455", "+85251159365"],
        notificationRequest: {
          titleEn: "Test Notification",
          titleHk: "測試通知",
          bodyEn: "This is a test notification",
          bodyHk: "這是一個測試通知",
        },
        scheduledAt: new Date(),
      };

      jest
        .spyOn(adminNotificationController["adminNotificationService"], "createNotifications")
        .mockResolvedValue([mockNotificationTask as any]);

      const result = await adminNotificationController.createNotifications(createDto, mockRequest as any);

      expect(adminNotificationController["adminNotificationService"].createNotifications).toHaveBeenCalledWith(
        createDto,
        mockAdminEmail,
      );
      expect(result).toEqual([mockNotificationTask]);
    });
  });

  describe("deleteNotifications", () => {
    it("should call adminNotificationService.deleteNotifications with correct parameters", async () => {
      const taskId = randomUUID();

      jest
        .spyOn(adminNotificationController["adminNotificationService"], "deleteNotifications")
        .mockResolvedValue(mockNotificationTask as any);

      const result = await adminNotificationController.deleteNotifications(taskId, mockRequest as any);

      expect(adminNotificationController["adminNotificationService"].deleteNotifications).toHaveBeenCalledWith(
        mockAdminEmail,
        taskId,
      );
      expect(result).toEqual(mockNotificationTask);
    });
  });

  describe("getNotifications", () => {
    it("should call adminNotificationService.getNotifications with correct parameters", async () => {
      const filterDto: NotificationTaskFilterDto = {
        page: 1,
        pageSize: 10,
        sort: NotificationTaskSortOrder.DESC,
        sortBy: NotificationTaskSortKey.CREATED_AT,
      };

      const paginatedResponse: PaginatedResponseDto<any> = {
        data: [mockNotificationTask],
        pagination: {
          count: 1,
          page: 1,
          pageSize: 10,
        },
      };

      jest
        .spyOn(adminNotificationController["adminNotificationService"], "getNotifications")
        .mockResolvedValue(paginatedResponse);

      const result = await adminNotificationController.getNotifications(filterDto);

      expect(adminNotificationController["adminNotificationService"].getNotifications).toHaveBeenCalledWith(filterDto);
      expect(result).toEqual(paginatedResponse);
    });
  });

  describe("updateNotifications", () => {
    it("should call adminNotificationService.updateNotifications with correct parameters", async () => {
      const taskId = randomUUID();
      const updateDto: UpdateManyNotificationRequestDto = {
        name: "Updated Test Campaign",
        notificationRequest: {
          titleEn: "Updated Test Notification",
          titleHk: "更新測試通知",
          bodyEn: "This is an updated test notification",
          bodyHk: "這是一個更新的測試通知",
        },
        scheduledAt: new Date(),
      };

      jest
        .spyOn(adminNotificationController["adminNotificationService"], "updateNotifications")
        .mockResolvedValue(mockNotificationTask as any);

      const result = await adminNotificationController.updateNotifications(taskId, updateDto, mockRequest as any);

      expect(adminNotificationController["adminNotificationService"].updateNotifications).toHaveBeenCalledWith(
        mockAdminEmail,
        taskId,
        updateDto,
      );
      expect(result).toEqual(mockNotificationTask);
    });
  });
});

import { Request, Response, NextFunction } from "express";
import { getAuth } from "firebase-admin/auth";

import { AdminAuthMiddleware } from "../../../../nestJs/infrastructure/middlewares/adminAuth.middleware";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import FakeLoggerService from "../../../utils/fakeLogger.service.specs";

jest.mock("firebase-admin/auth", () => {
  return {
    getAuth: jest.fn(),
  };
});

describe("AuthMiddleware", () => {
  let middleware: AdminAuthMiddleware;
  let req: Request;
  let res: Response;
  let next: NextFunction;
  let mockedGetAuth: jest.Mock;
  let mockVerifyIdToken: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    middleware = new AdminAuthMiddleware(FakeLoggerService);
    req = {
      path: "/admin/orders",
      headers: {},
    } as Request;
    res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    } as unknown as Response;
    next = jest.fn();
    mockedGetAuth = getAuth as jest.Mock;
    mockVerifyIdToken = jest.fn();
    mockedGetAuth.mockReturnValue({
      verifyIdToken: mockVerifyIdToken,
    });
  });

  it("should throw error if authorization header is not set", async () => {
    expect(middleware.use(req, res, next)).rejects.toThrow(errorBuilder.auth.tokenNotSet());
    expect(next).not.toHaveBeenCalled();
  });

  it("should throw error if authorization header is not starting with Bearer", async () => {
    req = {
      path: "/admin/orders",
      headers: {
        authorization: "Basic 123",
      },
    } as Request;
    expect(middleware.use(req, res, next)).rejects.toThrow(errorBuilder.auth.tokenNotSet());
    expect(next).not.toHaveBeenCalled();
  });

  it("should throw if token is not valid", async () => {
    req = {
      path: "/admin/orders",
      headers: {
        authorization: "Bearer eyJhb.eyJBR.LGL-9d9dUA",
      },
    } as Request;
    const decodedToken = {
      admin: false,
      email: "<EMAIL>",
    };
    mockVerifyIdToken.mockResolvedValue(decodedToken);
    expect(middleware.use(req, res, next)).rejects.toThrow(errorBuilder.auth.tokenInvalidRole());
    expect(next).not.toHaveBeenCalled();
  });

  it("should execute next if token is valid", async () => {
    req = {
      path: "/admin/orders",
      headers: {
        authorization: "Bearer eyJhbGciOiJeyJhbGciOiJ.eyJBRE1eyJBRE1JTiI6JTiI6.LGL-9d9dU2zjITpU2zjITp-aqA",
      },
    } as Request;
    const decodedToken = {
      admin: true,
      email: "<EMAIL>",
    };
    mockVerifyIdToken.mockResolvedValue(decodedToken);
    await middleware.use(req, res, next);
    expect(next).toHaveBeenCalled();
  });
});

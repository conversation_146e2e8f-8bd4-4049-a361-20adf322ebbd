import Joi from "joi";

import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import genericSchemas from "../../../nestJs/modules/validation/dto/genericSchemas.dto";
import { ValidationService } from "../../../nestJs/modules/validation/validation.service";

/**
 *
 */
describe("validation.service", () => {
  beforeEach(() => {
    jest.resetModules();
  });

  it("Should return the value when test success", () => {
    expect(ValidationService.validate([{ value: "test", schema: genericSchemas.string }])).toEqual(["test"]);
  });

  it("Should return the values when test success", () => {
    const userSchema = Joi.object({
      name: Joi.string().required(),
      age: Joi.number().required(),
    });

    expect(
      ValidationService.validate([
        { value: "hello", schema: genericSchemas.string },
        { schema: userSchema, value: { name: "toto", age: 2 } },
      ]),
    ).toEqual(["hello", { name: "toto", age: 2 }]);
  });

  it("Should not cast/convert the types", () => {
    const complexSchema = Joi.object({
      details: Joi.object({
        name: Joi.string().required(),
        age: Joi.number().required(),
      }),
    });

    expect(() =>
      ValidationService.validate([
        { value: "hello", schema: genericSchemas.string },
        { schema: complexSchema, value: { details: { name: "toto", age: "2" } } },
      ]),
    ).toThrowError(errorBuilder.validation.failed("'details.age' must be a number"));
  });

  it("Should throw an error when the test fails", () => {
    const notString = { not: "string" } as unknown as string;

    expect(() => ValidationService.validate([{ value: notString, schema: genericSchemas.string }])).toThrowError(
      errorBuilder.validation.failed("'value' must be a string"),
    );
  });

  it("Should throw an error when the test fails with multiple errors", () => {
    const notString = { not: "string" } as unknown as string;
    const userSchema = Joi.object({
      name: Joi.string().required(),
      age: Joi.number().required(),
    });

    expect(() =>
      ValidationService.validate<[{ name: string; age: number }, string]>([
        { value: notString, schema: genericSchemas.string },
        { value: { name: "toto", age: { some: "data" } }, schema: userSchema },
      ]),
    ).toThrowError(errorBuilder.validation.failed("'value' must be a string, 'age' must be a number"));
  });

  it("Should throw an error with the list of errors when both fields fail", () => {
    const userSchema = Joi.object({
      name: Joi.string().required(),
      age: Joi.number().required(),
    });

    expect(() => ValidationService.validate([{ schema: userSchema, value: {} }])).toThrowError(
      errorBuilder.validation.failed("'name' is required. 'age' is required"),
    );
  });
});

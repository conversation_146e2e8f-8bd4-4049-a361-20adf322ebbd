import { Signature } from "jws";

import { AuthController } from "../../../nestJs/modules/auth/auth.controller";
import { DeviceAuthDto } from "../../../nestJs/modules/auth/dto/meter-auth.dto";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import { mockedJws } from "../../utils/jws/jwsMock.specs.utils";
import {
  devicePublicVerifyMock,
  isAlgorithmSupportedMock,
} from "../../utils/services/FakeRsaEncryptionService.spec.utils";
import { newTestAuthController } from "../../utils/services/TestServices.specs.utils";

describe("authController", () => {
  let authController: AuthController;
  beforeEach(() => {
    authController = newTestAuthController();
  });

  describe("checkDeviceAuth", () => {
    const deviceAuth: DeviceAuthDto = {
      deviceToken:
        "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    };

    const validDecodedJwt: Signature = {
      header: {
        alg: "RS256",
        kid: "ba62596f52f52ed44049396be7df34d2c64f453e",
        typ: "JWT",
      },
      payload: { test: "test" },
      signature: "signature",
    };

    it("should return invalid device jwt if RSA verification fails", async () => {
      mockedJws.decode.mockReturnValueOnce(validDecodedJwt);
      isAlgorithmSupportedMock.mockReturnValueOnce(true);
      devicePublicVerifyMock.mockResolvedValue(false);
      await expect(authController.deviceAuthenticate(deviceAuth)).rejects.toThrow(errorBuilder.auth.invalidDeviceJwt());
      expect(mockedJws.decode).toHaveBeenCalledWith(deviceAuth.deviceToken);
      expect(isAlgorithmSupportedMock).toHaveBeenCalledWith(validDecodedJwt.header.alg);
      expect(devicePublicVerifyMock).toHaveBeenCalledWith(deviceAuth.deviceToken);
    });
  });
});

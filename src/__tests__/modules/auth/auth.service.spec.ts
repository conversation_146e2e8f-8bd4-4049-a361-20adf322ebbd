import { AxiosError, AxiosResponse } from "axios";
import { Filter } from "firebase-admin/firestore";
import { AuthUserRecord } from "firebase-functions/v2/identity";

import { UpdateAuthUserRoleDto } from "@nest/modules/admin/adminAuthUser/dto/updateUserRole.dto";

import { AuthDocument } from "../../../nestJs/modules/appDatabase/documents/auth.document";
import { AuthService } from "../../../nestJs/modules/auth/auth.service";
import { AuthStatus } from "../../../nestJs/modules/auth/dto/auth.dto";
import { AuthResultsType } from "../../../nestJs/modules/auth/dto/email-auth-response.dto";
import { FirebaseAuthResultsDto } from "../../../nestJs/modules/auth/dto/firebase-auth-results.dto";
import { CreateUserError, Role } from "../../../nestJs/modules/auth/types";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import "../../initTests";
import { axiosPostMock } from "../../utils/axios/axiosMock.specs.utils";
import { getAuthMock as getFirebaseAuthMock, updateUserMock } from "../../utils/firebase-admin/getAuthMock.specs.utils";
import {
  findMock,
  getAuthByValidResetTokenMock,
  getAuthByEmailMock,
  getDriverMock,
  saveAuthMock,
} from "../../utils/services/FakeAppDatabaseService.specs.utils";
import { sendActionEmailMock } from "../../utils/services/FakeEmailService.specs.utils";
import { newTestAuthService } from "../../utils/services/TestServices.specs.utils";

describe("authService", () => {
  const successfulLoginResponse: FirebaseAuthResultsDto = {
    kind: "identitytoolkit#VerifyPasswordResponse",
    localId: "localId",
    email: "email",
    displayName: "displayName",
    idToken:
      "eyJhbGciOiJSUzI1NiIsImtpZCI6ImUyYjIyZmQ0N2VkZTY4MmY2OGZhY2NmZTdjNGNmNWIxMWIxMmI1NGIiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CJ6uHB8ia1TkfCqQGviuNyVpy-Qte5bbJrKVStauo2oXZxvPP_EkKyjq0x_OpByjdQLaw8Mkb-FdOQ9BiRksrIw5OFjnCd8mV1UHGwhGPTQg5vEQzXfGxLQx5Mh5gMbJm6r0TRQyMNTB81JIrR-NPBMRi8rV2kIhw0YDZSJddvpXw_eG4ZAGNjAFtFxI3HhDqfD7M0n8rVyV_U15lwBPfv0SvgnsbGv2ip8M6Kz1nkzkoZepCNJtLD6IpvHxrz91YbaAHeubPZJquu05c7cXRcHyqfkOqMc8sGlgQ8RMGU_DrhfAR3RCqkhCRBRod4Pa78WmC5fMlPi3WbTPzt3ZrQ",
    registered: true,
  };

  const mfaRequiredLoginResponse: FirebaseAuthResultsDto = {
    kind: "identitytoolkit#VerifyPasswordResponse",
    localId: "localId",
    email: "email",
    displayName: "displayName",
    registered: true,
    mfaPendingCredential: "mfaPendingCredential",
  };

  const authUserRecordMock: AuthUserRecord = {
    disabled: false,
    emailVerified: true,
    metadata: { creationTime: "", lastSignInTime: "" },
    providerData: [],
    uid: "userId",
  };

  const customToken = "customToken";

  const lastAuth = new Date();
  const authDocument = AuthDocument.fromDto({
    status: AuthStatus.ACTIVE,
    role: Role.ADMIN,
    userId: "userId",
    email: "email",
    failedAuthAttempts: 0,
    previousHashes: [],
    lastAuth,
    passwordReset: null,
  });

  const firebaseAuthUser = {
    multiFactor: {
      enrolledFactors: [{ factorId: "phone", phoneNumber: "phone" }],
    },
  };

  let authService: AuthService;
  const firebaseAuthMock = getFirebaseAuthMock();
  beforeEach(() => {
    jest.clearAllMocks();
    authService = newTestAuthService();
  });

  describe("signInWithEmailAndPassword", () => {
    it("should return auth attempts exceeded error if auth attempts are exceeded", async () => {
      const authWithExceededAttempts = AuthDocument.fromDto({
        status: AuthStatus.ACTIVE,
        role: Role.ADMIN,
        userId: "userId",
        email: "email",
        lastAuth: new Date(),
        failedAuthAttempts: 5,
        previousHashes: [],
        passwordReset: null,
      });

      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(authWithExceededAttempts)));
      await expect(authService.signInWithEmailAndPassword("email", "password")).rejects.toThrow(
        errorBuilder.auth.authAttemptsExceeded(),
      );
      expect(getAuthByEmailMock).toHaveBeenCalled();
    });

    it("should save new auth when none exists and return token on successful login", async () => {
      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(undefined)));
      axiosPostMock.mockImplementationOnce(() => new Promise((resolve) => resolve({ data: successfulLoginResponse })));
      firebaseAuthMock.getUserByEmail.mockReturnValue(firebaseAuthUser);
      saveAuthMock.mockImplementationOnce(() => Promise.resolve());

      expect(await authService.signInWithEmailAndPassword("email", "password")).toEqual({
        type: AuthResultsType.RESET_REQUIRED,
      });
      expect(axiosPostMock).toHaveBeenCalled();
      expect(saveAuthMock).toHaveBeenCalled();
      expect(sendActionEmailMock).toHaveBeenCalled();
    });

    it("should return type of MfaRequired when MFA is enabled for account", async () => {
      getAuthByEmailMock.mockResolvedValueOnce(authDocument);
      axiosPostMock.mockImplementationOnce(() => new Promise((resolve) => resolve({ data: mfaRequiredLoginResponse })));
      firebaseAuthMock.getUserByEmail.mockReturnValue(firebaseAuthUser);
      saveAuthMock.mockImplementationOnce(() => Promise.resolve());
      firebaseAuthMock.createCustomToken.mockReturnValue(customToken);

      expect(await authService.signInWithEmailAndPassword("email", "password")).toEqual({
        ...mfaRequiredLoginResponse,
        customToken,
        type: AuthResultsType.MFA_REQUIRED,
        role: authDocument.role,
        phone: firebaseAuthUser.multiFactor.enrolledFactors[0].phoneNumber,
      });
    });

    it("should create a new auth document if none when user has MFA enabled for account", async () => {
      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(undefined)));
      axiosPostMock.mockImplementationOnce(() => new Promise((resolve) => resolve({ data: mfaRequiredLoginResponse })));
      firebaseAuthMock.getUserByEmail.mockReturnValue(firebaseAuthUser);
      saveAuthMock.mockImplementationOnce(() => Promise.resolve());
      firebaseAuthMock.createCustomToken.mockReturnValue(customToken);

      await authService.signInWithEmailAndPassword("email", "password");
      expect(saveAuthMock).toHaveBeenCalled();
    });

    it("should update last auth value when auth is found", async () => {
      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(authDocument)));
      axiosPostMock.mockImplementationOnce(() => new Promise((resolve) => resolve({ data: successfulLoginResponse })));
      saveAuthMock.mockImplementationOnce(() => Promise.resolve());

      await authService.signInWithEmailAndPassword("email", "password");
      expect(saveAuthMock).toHaveBeenCalled();
      expect(authDocument.lastAuth?.getTime()).toBeGreaterThan(lastAuth.getTime());
    });
  });

  describe("getClaimsForEmailUser", () => {
    it("should return a role claim for email user", async () => {
      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(authDocument)));

      expect(await authService.getClaimsForUser({ ...authUserRecordMock, email: "email" })).toEqual({
        role: Role.ADMIN,
        admin: true,
      });
    });

    it("should return a role claim for OTP user", async () => {
      getDriverMock.mockResolvedValueOnce({ phoneNumber: "phone" });

      expect(await authService.getClaimsForUser({ ...authUserRecordMock, phoneNumber: "phone" })).toEqual({
        role: Role.DRIVER,
      });
    });

    it("should return undefined for non-email and non-otp login token", async () => {
      expect(await authService.getClaimsForUser({ ...authUserRecordMock })).toEqual(undefined);
    });
  });

  describe("startMfa", () => {
    it("should throw mfa error", async () => {
      const unknownError = new Error();
      axiosPostMock.mockImplementationOnce(() => new Promise((_, reject) => reject(unknownError)));
      await expect(
        authService.startMfa({
          mfaPendingCredential: "mfa",
          mfaEnrollmentId: "enrollmentId",
          phoneSignInInfo: { phoneNumber: "phone", recaptchaToken: "recaptcha" },
        }),
      ).rejects.toThrow(errorBuilder.auth.mfaError(unknownError));
    });
  });

  describe("startResetPassword", () => {
    it("should create token and send action email", async () => {
      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(authDocument)));
      saveAuthMock.mockImplementationOnce(() => Promise.resolve());
      await authService.startResetPassword("email");
      expect(sendActionEmailMock).toHaveBeenCalled();
    });
  });

  describe("finalizeResetPassword", () => {
    const goodPassword = "12345678912%";

    it("should throw inadequate password error if new password doesn't meet requirements", async () => {
      const badPassword = "1234";
      getAuthByValidResetTokenMock.mockImplementationOnce(() => new Promise((resolve) => resolve(authDocument)));
      await expect(authService.finalizeResetPassword("token", badPassword)).rejects.toThrow(
        errorBuilder.auth.inadequatePassword(),
      );
      expect(updateUserMock).not.toHaveBeenCalled();
    });

    it("should update firebase auth with new password and verification", async () => {
      getAuthByValidResetTokenMock.mockImplementationOnce(() => new Promise((resolve) => resolve(authDocument)));
      await authService.finalizeResetPassword("token", goodPassword);
      expect(updateUserMock).toHaveBeenCalledWith(authDocument.userId, {
        password: goodPassword,
        emailVerified: true,
      });
      expect(saveAuthMock).toHaveBeenCalled();
    });
  });

  describe("signUpWithEmail", () => {
    it("should throw error if email is already in firestore", async () => {
      const authInFirestore = AuthDocument.fromDto({
        status: AuthStatus.ACTIVE,
        role: Role.ADMIN,
        userId: "userId",
        email: "email",
        lastAuth: new Date(),
        failedAuthAttempts: 0,
        previousHashes: [],
        passwordReset: null,
      });
      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(authInFirestore)));
      await expect(authService.signUpWithEmail("email")).rejects.toThrow(errorBuilder.auth.emailAlreadyExists("email"));
      expect(getAuthByEmailMock).toHaveBeenCalled();
      expect(saveAuthMock).not.toHaveBeenCalled();
    });
    it("should throw error if email is already in firebase auth", async () => {
      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(undefined)));
      const error = new AxiosError();
      const response = { data: { error: { message: CreateUserError.EMAIL_EXISTS } } } as AxiosResponse;
      error.response = response;

      axiosPostMock.mockImplementationOnce(() => new Promise((_, reject) => reject(error)));
      await expect(authService.signUpWithEmail("email")).rejects.toThrow(errorBuilder.auth.emailAlreadyExists("email"));
      expect(getAuthByEmailMock).toHaveBeenCalled();
      expect(saveAuthMock).not.toHaveBeenCalled();
    });
    it("should create new user", async () => {
      const successfulSignUpResponse: FirebaseAuthResultsDto = {
        kind: "identitytoolkit#VerifyPasswordResponse",
        localId: "localId",
        email: "email",
        displayName: "displayName",
        idToken:
          "eyJhbGciOiJSUzI1NiIsImtpZCI6ImUyYjIyZmQ0N2VkZTY4MmY2OGZhY2NmZTdjNGNmNWIxMWIxMmI1NGIiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CJ6uHB8ia1TkfCqQGviuNyVpy-Qte5bbJrKVStauo2oXZxvPP_EkKyjq0x_OpByjdQLaw8Mkb-FdOQ9BiRksrIw5OFjnCd8mV1UHGwhGPTQg5vEQzXfGxLQx5Mh5gMbJm6r0TRQyMNTB81JIrR-NPBMRi8rV2kIhw0YDZSJddvpXw_eG4ZAGNjAFtFxI3HhDqfD7M0n8rVyV_U15lwBPfv0SvgnsbGv2ip8M6Kz1nkzkoZepCNJtLD6IpvHxrz91YbaAHeubPZJquu05c7cXRcHyqfkOqMc8sGlgQ8RMGU_DrhfAR3RCqkhCRBRod4Pa78WmC5fMlPi3WbTPzt3ZrQ",
        registered: true,
      };

      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(undefined)));
      axiosPostMock.mockImplementationOnce(() => new Promise((resolve) => resolve({ data: successfulSignUpResponse })));
      saveAuthMock.mockImplementationOnce(() => Promise.resolve());

      await authService.signUpWithEmail("email");
      expect(axiosPostMock).toHaveBeenCalled();
      expect(saveAuthMock).toHaveBeenCalled();
    });
  });

  describe("updateUserRole", () => {
    it("should throw error if email not found in firestore", async () => {
      const updateRoleDto: UpdateAuthUserRoleDto = {
        email: "email",
        roleData: {
          role: Role.ADMIN,
        },
      };
      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(undefined)));

      await expect(authService.updateUserRole(updateRoleDto.email, updateRoleDto.roleData)).rejects.toThrow(
        errorBuilder.auth.userNotFound("email"),
      );
      expect(getAuthByEmailMock).toHaveBeenCalled();
      expect(saveAuthMock).not.toHaveBeenCalled();
    });
    it("should update role in firestore", async () => {
      const updateRoleDto: UpdateAuthUserRoleDto = {
        email: "email",
        roleData: {
          role: Role.ADMIN,
        },
      };
      const auth = AuthDocument.fromDto({
        status: AuthStatus.ACTIVE,
        role: Role.DEFAULT,
        userId: "userId",
        email: "email",
        lastAuth: new Date(),
        failedAuthAttempts: 0,
        previousHashes: [],
        passwordReset: null,
      });

      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(auth)));
      firebaseAuthMock.setCustomUserClaims.mockImplementationOnce(() => Promise.resolve());
      await authService.updateUserRole(updateRoleDto.email, updateRoleDto.roleData);
      expect(saveAuthMock).toHaveBeenCalled();
    });
  });

  describe("getUsers", () => {
    it("should find all users", async () => {
      await authService.getUsers({});
      expect(findMock).toHaveBeenLastCalledWith();
    });
    it("should find users with role", async () => {
      const mockFilters = { field: "role", operator: "==", value: Role.ADMIN };
      (Filter.where as jest.Mock).mockImplementation((field, operator, value) => {
        return { field, operator, value };
      });

      await authService.getUsers({ role: Role.ADMIN });
      expect(Filter.where).toHaveBeenCalledWith("role", "==", Role.ADMIN);
      expect(findMock).toHaveBeenLastCalledWith(mockFilters);
    });
  });

  describe("resetFailedLoginAttempts", () => {
    it("should throw error if email not found in firestore", async () => {
      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(undefined)));
      await expect(authService.resetFailedLoginAttempts("<EMAIL>")).rejects.toThrow(
        errorBuilder.auth.userNotFound("<EMAIL>"),
      );
      expect(getAuthByEmailMock).toHaveBeenCalled();
      expect(saveAuthMock).not.toHaveBeenCalled();
    });
    it("should skip save if fail attemps is less than 5", async () => {
      const auth = AuthDocument.fromDto({
        status: AuthStatus.ACTIVE,
        role: Role.DEFAULT,
        userId: "userId",
        email: "<EMAIL>",
        lastAuth: new Date(),
        failedAuthAttempts: 4,
        previousHashes: [],
        passwordReset: null,
      });

      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(auth)));
      await authService.resetFailedLoginAttempts("<EMAIL>");
      expect(saveAuthMock).not.toHaveBeenCalled();
    });
    it("should update failed attempts to 0", async () => {
      const auth = AuthDocument.fromDto({
        status: AuthStatus.ACTIVE,
        role: Role.DEFAULT,
        userId: "userId",
        email: "<EMAIL>",
        lastAuth: new Date(),
        failedAuthAttempts: 5,
        previousHashes: [],
        passwordReset: null,
      });

      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(auth)));
      await authService.resetFailedLoginAttempts("<EMAIL>");
      expect(saveAuthMock).toHaveBeenCalled();
    });
  });
  describe("forceResetPassword", () => {
    it("should throw error if email not found in firestore", async () => {
      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(undefined)));
      await expect(authService.forceResetPassword("<EMAIL>")).rejects.toThrow(
        errorBuilder.auth.userNotFound("<EMAIL>"),
      );
      expect(getAuthByEmailMock).toHaveBeenCalled();
      expect(saveAuthMock).not.toHaveBeenCalled();
    });
    it("should update status to reset password", async () => {
      const auth = AuthDocument.fromDto({
        status: AuthStatus.ACTIVE,
        role: Role.DEFAULT,
        userId: "userId",
        email: "<EMAIL>",
        lastAuth: new Date(),
        failedAuthAttempts: 4,
        previousHashes: [],
        passwordReset: null,
      });

      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(auth)));
      const result = await authService.forceResetPassword("<EMAIL>");
      expect(saveAuthMock).toHaveBeenCalled();
      expect(result).toEqual({ ...auth, status: AuthStatus.NEW });
    });
  });
  describe("disableUser", () => {
    it("should throw error if email not found in firestore", async () => {
      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(undefined)));
      await expect(authService.disableUser("<EMAIL>")).rejects.toThrow(
        errorBuilder.auth.userNotFound("<EMAIL>"),
      );
      expect(getAuthByEmailMock).toHaveBeenCalled();
      expect(saveAuthMock).not.toHaveBeenCalled();
    });
    it("should update failed attempts to 5", async () => {
      const auth = AuthDocument.fromDto({
        status: AuthStatus.ACTIVE,
        role: Role.DEFAULT,
        userId: "userId",
        email: "<EMAIL>",
        lastAuth: new Date(),
        failedAuthAttempts: 2,
        previousHashes: [],
        passwordReset: null,
      });

      getAuthByEmailMock.mockImplementationOnce(() => new Promise((resolve) => resolve(auth)));
      const result = await authService.disableUser("<EMAIL>");
      expect(saveAuthMock).toHaveBeenCalled();
      expect(result).toEqual({ ...auth, failedAuthAttempts: 5 });
    });
  });
});

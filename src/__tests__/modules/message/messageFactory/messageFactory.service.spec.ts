import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";

import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";
import MockAppDatabaseService from "@tests/utils/services/FakeAppDatabaseService.specs.utils";
import { newTestLoggerServiceAdapter } from "@tests/utils/services/TestServices.specs.utils";

import { MessageRepository } from "../../../../nestJs/modules/database/repositories/message.repository";
import { TemplateTypesText } from "../../../../nestJs/modules/message/dto/templateType.dto";
import { MessageFactoryService } from "../../../../nestJs/modules/message/messageFactory/messageFactory.service";
import { NotificationService } from "../../../../nestJs/modules/message/messageFactory/modules/notification/notification.service";
import { SmsServices } from "../../../../nestJs/modules/message/messageFactory/modules/sms/sms.service";
import { WhatsappServices } from "../../../../nestJs/modules/message/messageFactory/modules/whatsapp/whatsapp.service";
import { PublishMessageForMessageProcessingParams } from "../../../../nestJs/modules/pubsub/dto/publishMessageForMessageProcessing.dto";
import { PubSubService } from "../../../../nestJs/modules/pubsub/pubsub.service";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import {
  expectedPublishSMSMessage,
  expectedPublishWhatsappMessage,
} from "../../../utils/fakeData/fakeMessage.specs.utils";
import FakeMessageRepository from "../../../utils/repositories/FakeMessageRepository.specs.utils";
import FakeConfigService from "../../../utils/services/FakeConfigService.specs.utils";
import FakeHttpService from "../../../utils/services/FakeHttpService.specs.utils";
import FakePubSubService from "../../../utils/services/FakePubSubService.specs.utils";

const mockSendSMSMessage = jest.fn().mockResolvedValue("success");
jest.spyOn(SmsServices.prototype, "sendSMSMessage").mockImplementation(mockSendSMSMessage);

const mockSendWhatsappMessage = jest.fn().mockResolvedValue("success");
jest.spyOn(WhatsappServices.prototype, "sendWhatsappMessage").mockImplementation(mockSendWhatsappMessage);
/**
 * This test is to check if the factory service is returning the right data
 * and if the service is not implemented or the factory type is not valid,
 * it should throw an error
 */
describe("messageFactory.service", () => {
  const factoryService = new MessageFactoryService(
    new WhatsappServices(
      new FakeConfigService() as unknown as ConfigService,
      new FakeHttpService() as unknown as HttpService,
      new FakeMessageRepository() as unknown as MessageRepository,
      new FakePubSubService() as unknown as PubSubService,
      new MockAppDatabaseService() as unknown as AppDatabaseService,
      newTestLoggerServiceAdapter(),
    ),
    new SmsServices(
      new FakeConfigService() as unknown as ConfigService,
      new FakeMessageRepository() as unknown as MessageRepository,
    ),
    new NotificationService(new FakeMessageRepository() as unknown as MessageRepository),
  );

  it("should return the right data using the Whatsapp/SMS service", () => {
    const whatsappService = new WhatsappServices(
      new FakeConfigService() as unknown as ConfigService,
      new FakeHttpService() as unknown as HttpService,
      new FakeMessageRepository() as unknown as MessageRepository,
      new FakePubSubService() as unknown as PubSubService,
      new MockAppDatabaseService() as unknown as AppDatabaseService,
      newTestLoggerServiceAdapter(),
    );
    const smsService = new SmsServices(
      new FakeConfigService() as unknown as ConfigService,
      new FakeMessageRepository() as unknown as MessageRepository,
    );

    const whatsappMessage = factoryService.processMessage(expectedPublishWhatsappMessage);
    const whatsappMessageFromSmsService = whatsappService.processMessage(expectedPublishWhatsappMessage);

    const smsMessage = factoryService.processMessage(expectedPublishSMSMessage);
    const smsMessageFromSmsService = smsService.processMessage(expectedPublishSMSMessage);

    expect(JSON.stringify(smsMessage)).toEqual(JSON.stringify(smsMessageFromSmsService));
    expect(JSON.stringify(whatsappMessage)).toEqual(JSON.stringify(whatsappMessageFromSmsService));
  });

  it("should throw an error if this type of service is not implemented", async () => {
    const document = {
      metadata: {
        schemeVersion: "1.1.2",
        createdAt: new Date("2023-01-01T00:00:01.000Z"),
      },
      recipient: {
        phone: "+85212345678",
      },
      traceId: "5a04256cb293686739a252e052e898aaf321decf",
      channel: "OTHER_CHANNEL",
      language: "zh-hk",
      params: {
        receiptLink: "https://dash-dev-81bb1.web.app/receipt/043f160c-792e-4f91-8031-4b786bbf2ce9",
        licensePlate: "TEST1234",
        tripTotal: 29.2,
        tripEndTime: "2023-01-01T00:01:00.000Z",
      },
      messageId: 1234,
      template: TemplateTypesText.RECEIPT,
    } as unknown as PublishMessageForMessageProcessingParams;

    expect(factoryService.processMessage(document)).rejects.toThrowError(errorBuilder.message.factoryNotImplemented());
  });
});

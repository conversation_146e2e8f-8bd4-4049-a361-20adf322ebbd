import { of } from "rxjs";

import Message from "../../../../../../nestJs/modules/database/entities/message.entity";
import User from "../../../../../../nestJs/modules/database/entities/user.entity";
import { ChannelTypes } from "../../../../../../nestJs/modules/message/dto/channelType.dto";
import { WhatsappServices } from "../../../../../../nestJs/modules/message/messageFactory/modules/whatsapp/whatsapp.service";
import { TopicNamesType } from "../../../../../../nestJs/modules/pubsub/dto/topicName.dto";
import { errorBuilder } from "../../../../../../nestJs/modules/utils/utils/error.utils";
import { mockCreateTopic, mockPublishMessage } from "../../../../../utils/@google-cloud/pubsub.specs.utils";
import {
  expectedPublishWhatsappMessage,
  expectedPublishWhatsappMessageWithoutCampaignName,
  expectedPublishWhatsappMessageWithoutReceiptLink,
} from "../../../../../utils/fakeData/fakeMessage.specs.utils";
import { resetHttpServiceMock, mockPost } from "../../../../../utils/services/FakeHttpService.specs.utils";
import { mockSave } from "../../../../../utils/services/FakeRepository.specs.utils";
import { newTestWhatsappService } from "../../../../../utils/services/TestServices.specs.utils";

jest.mock("axios-retry");
jest.mock("firebase-functions/logger");

describe("messageFactory", () => {
  describe("whatsapp.service", () => {
    let service: WhatsappServices;

    beforeEach(() => {
      service = newTestWhatsappService();

      resetHttpServiceMock({});

      mockPublishMessage.mockImplementation(() => {
        return () => new Promise((resolve) => resolve(""));
      });
    });

    afterEach(async () => {
      mockPost.mockReset();
      mockSave.mockClear();
      mockCreateTopic.mockClear();
      mockPublishMessage.mockClear();
    });

    describe("processMessage", () => {
      it("should return the Message after calling Wati successfully and saving successfully", async () => {
        const faksMessageProviderId = "abcdefg1234567";
        const fakeUser = new User();
        fakeUser.phoneNumber = expectedPublishWhatsappMessage.recipient.phone;

        const expectedToBeSavedToSql = new Message();
        expectedToBeSavedToSql.type = ChannelTypes.WHATSAPP;
        expectedToBeSavedToSql.metadata = expectedPublishWhatsappMessage.metadata;
        expectedToBeSavedToSql.template = expectedPublishWhatsappMessage.template;
        expectedToBeSavedToSql.language = expectedPublishWhatsappMessage.language;
        expectedToBeSavedToSql.params = expectedPublishWhatsappMessage.params;
        expectedToBeSavedToSql.messageProviderId = faksMessageProviderId;
        expectedToBeSavedToSql.user = fakeUser;

        mockPost.mockImplementation(() => {
          return of({
            status: 200,
            data: {
              validWhatsAppNumber: true,
              contact: {
                id: faksMessageProviderId,
              },
            },
          });
        });

        await service.processMessage(expectedPublishWhatsappMessage);
        expect(mockPost).toHaveBeenCalled();
        expect(mockSave).toHaveBeenCalledWith(expectedToBeSavedToSql);
      });

      it("should throw an error when there is no receiptLink or licensePlate, resned SmsMessage to pubsub, and not call save to sql", async () => {
        await expect(service.processMessage(expectedPublishWhatsappMessageWithoutReceiptLink)).rejects.toThrow(
          errorBuilder.global.requiredParam("'receiptLink', 'licensePlate', 'tripTotal' or 'tripEndTime'"),
        );
        expect(mockCreateTopic).toHaveBeenCalledWith(TopicNamesType.MESSAGE_PROCESSING);
        expect(mockPublishMessage).toHaveBeenCalledTimes(1);
        expect(mockSave).not.toHaveBeenCalled();
      });

      it("should throw an error when Wati call fails, resned SmsMessage to pubsub, and not call save to sql", async () => {
        mockPost.mockImplementation(() => {
          throw new Error("Wati Error");
        });
        await expect(service.processMessage(expectedPublishWhatsappMessage)).rejects.toThrow(
          errorBuilder.global.unknown(new Error("Wati Error")),
        );
        expect(mockCreateTopic).toHaveBeenCalledWith(TopicNamesType.MESSAGE_PROCESSING);
        expect(mockPublishMessage).toHaveBeenCalledTimes(1);
        expect(mockSave).not.toHaveBeenCalled();
      });

      it("should throw an error when there is no campaignName or discount when send discountLink", async () => {
        await expect(service.processMessage(expectedPublishWhatsappMessageWithoutCampaignName)).rejects.toThrow(
          errorBuilder.global.requiredParam("'id', 'campaignName' or 'discount'"),
        );
        expect(mockSave).not.toHaveBeenCalled();
      });
    });
  });
});

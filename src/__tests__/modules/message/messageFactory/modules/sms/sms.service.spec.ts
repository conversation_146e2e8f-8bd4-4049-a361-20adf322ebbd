import { ConfigService } from "@nestjs/config";

import { MessageRepository } from "../../../../../../nestJs/modules/database/repositories/message.repository";
import { SmsServices } from "../../../../../../nestJs/modules/message/messageFactory/modules/sms/sms.service";
import { errorBuilder } from "../../../../../../nestJs/modules/utils/utils/error.utils";
import {
  expectedPublishSMSMessage,
  expectedPublishSMSMessageWithoutReceiptLink,
} from "../../../../../utils/fakeData/fakeMessage.specs.utils";
import FakeMessageRepository, {
  mockSaveMessageForPhoneRecipient,
} from "../../../../../utils/repositories/FakeMessageRepository.specs.utils";
import FakeConfigService from "../../../../../utils/services/FakeConfigService.specs.utils";
import { mockMessageCreate } from "../../../../../utils/twilio.specs.utils";

jest.mock("firebase-functions/logger");
jest.mock("twilio");

describe("messageFactory", () => {
  describe("sms.service", () => {
    let service: SmsServices;

    beforeEach(() => {
      service = new SmsServices(
        new FakeConfigService() as unknown as ConfigService,
        new FakeMessageRepository() as unknown as MessageRepository,
      );
    });

    afterEach(async () => {
      mockMessageCreate.mockReset();
      mockSaveMessageForPhoneRecipient.mockClear();
    });

    describe("processMessage", () => {
      it("should return the Message after calling Twillo successfully and saving to mysql successfully", async () => {
        mockMessageCreate.mockResolvedValue({
          sid: "MS12345678",
          status: "sent",
        });

        await service.processMessage(expectedPublishSMSMessage);
        expect(mockSaveMessageForPhoneRecipient).toHaveBeenCalledWith(expectedPublishSMSMessage, "MS12345678");
      });

      it("should throw an error when there is no receiptLink", async () => {
        expect(service.processMessage(expectedPublishSMSMessageWithoutReceiptLink)).rejects.toThrowError(
          errorBuilder.global.requiredParam("receiptLink"),
        );
      });

      it("should throw an error when twillo call fails", async () => {
        mockMessageCreate.mockImplementation(() => {
          throw new Error("Twilio Error");
        });

        expect(service.processMessage(expectedPublishSMSMessage)).rejects.toThrowError(
          errorBuilder.message.sms.twilioCreate(new Error("Twilio Error")),
        );
      });
    });
  });
});

import { UserDocument } from "../../../nestJs/modules/appDatabase/documents/user.document";
import User from "../../../nestJs/modules/database/entities/user.entity";
import { UserService } from "../../../nestJs/modules/user/user.service";
import { setFirestoreMock } from "../../utils/firebase-admin/firestoreMock.specs.utils";
import {
  mockCreateProfileAudit,
  mockFindOne,
  mockGetCount,
  mockGetRawMany,
} from "../../utils/services/FakeRepository.specs.utils";
import { newTestUserService } from "../../utils/services/TestServices.specs.utils";
import "../../initTests";

describe("user.service", () => {
  let userService: UserService;
  const updateDatabaseUserMock = jest.fn();
  const spy = jest.spyOn(UserService.prototype as any, "updateDatabaseUser").mockImplementation(updateDatabaseUserMock);

  beforeEach(() => {
    jest.clearAllMocks();
    userService = newTestUserService();
  });

  afterAll(() => {
    spy.mockRestore();
  });

  describe("userProfileChangedInFirestore", () => {
    const appDatabaseId = "Juag8EQX7bfZF0kLYxHKoVoVqO03";
    const userId = "Juag8EQX7bfZF0kLYxHKoVoVqO03";
    it("should create user to firestore", async () => {
      const user = new User();
      user.id = userId;
      user.publicKey = "publicKey1234567890";
      user.phoneNumber = "+85212345678";

      mockGetRawMany.mockReturnValueOnce([]);
      mockGetCount.mockReturnValueOnce(0);
      await userService.setUserInFirestore(appDatabaseId, user);
      expect(setFirestoreMock).toHaveBeenCalledTimes(1);
    });

    // commented out because it is not used right now, and it will be used in the future and will be changed
    // it("should create user to firestore and if there are campaigns, should copy too", async () => {
    //   const user = new User();
    //   user.id = userId;
    //   user.publicKey = "publicKey1234567890";
    //   user.phoneNumber = "+85212345678";

    //   mockGetRawMany.mockReturnValueOnce([{}, {}]);
    //   mockGetCount.mockReturnValueOnce(2);
    //   await userService.setUserInFirestore(appDatabaseId, user);
    //   expect(setFirestoreMock).toHaveBeenCalledTimes(3);
    // });

    it("should copy user prrfile to sql. If it is market_sms or email update, should write to profile audit too.", async () => {
      const user = new User();
      user.id = userId;
      user.publicKey = "publicKey1234567890";
      user.phoneNumber = "+85212345678";
      user.marketingPreferenceEmail = false;
      user.marketingPreferenceSMS = false;
      user.marketingConsent = false;
      user.createdAt = new Date();
      user.updatedAt = new Date();
      user.pinErrorCount = 0;

      const updatedUser = new User();
      updatedUser.id = userId;
      updatedUser.publicKey = "publicKey1234567890";
      updatedUser.phoneNumber = "+85212345678";
      updatedUser.marketingPreferenceEmail = true;
      updatedUser.marketingPreferenceSMS = false;
      updatedUser.marketingConsent = false;
      updatedUser.createdAt = new Date();
      updatedUser.updatedAt = new Date();
      updatedUser.pinErrorCount = 0;

      mockFindOne.mockReturnValueOnce(user);
      updateDatabaseUserMock.mockReturnValueOnce(updatedUser);
      const userDocumentAfter: UserDocument = {
        id: appDatabaseId,
        isUserInfoSubmitted: true,
        marketingPreferenceEmail: true,
      };
      const userDocumentbefore: UserDocument = {
        id: appDatabaseId,
        isUserInfoSubmitted: true,
        marketingPreferenceEmail: false,
      };
      await userService.processUserProfileChanged(appDatabaseId, userDocumentAfter, userDocumentbefore);
      expect(updateDatabaseUserMock).toHaveBeenCalled();
      expect(mockCreateProfileAudit).toHaveBeenCalled();
    });
  });
});

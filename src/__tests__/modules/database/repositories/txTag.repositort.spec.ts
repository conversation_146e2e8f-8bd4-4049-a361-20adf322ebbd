import { In, <PERSON><PERSON><PERSON> } from "typeorm";

import Tx from "../../../../nestJs/modules/database/entities/tx.entity";
import TxTag from "../../../../nestJs/modules/database/entities/txTag.entity";
import { TxTagRepository } from "../../../../nestJs/modules/database/repositories/txTag.repository";
import { TxTagType } from "../../../../nestJs/modules/transaction/dto/txTagType.dto";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import FakeRepository from "../../../utils/services/FakeRepository.specs.utils";
import { softDeleteTypeormMock } from "../../../utils/typeorm/repository/Repository.specs.utils";

jest.mock("typeorm/repository/Repository");

describe("txTag.repository", () => {
  let txTagRepository: TxTagRepository;

  beforeEach(() => {
    return (txTagRepository = new TxTagRepository(new FakeRepository() as unknown as TxTagRepository));
  });

  describe("removeTagById", () => {
    it("should update removedAt for the given tag IDs", async () => {
      const idsToUpdate = [1, 2, 3];

      softDeleteTypeormMock.mockResolvedValue({ affected: 3 });
      await txTagRepository.removeTagById("123", idsToUpdate);

      expect(softDeleteTypeormMock).toHaveBeenCalledWith({
        id: In(idsToUpdate),
        tx: { id: "123" },
        removedAt: IsNull(),
      });
    });

    it("should throw an error if no tags are affected by the update", async () => {
      const idsToUpdate = [4, 5];

      softDeleteTypeormMock.mockResolvedValue({ affected: 0 });

      expect(txTagRepository.removeTagById("123", idsToUpdate)).rejects.toThrowError(
        errorBuilder.transaction.tag.notFoundOrRemoved("123", idsToUpdate),
      );
      expect(softDeleteTypeormMock).toHaveBeenCalledWith({
        id: In(idsToUpdate),
        tx: { id: "123" },
        removedAt: IsNull(),
      });
    });
  });

  describe("addTagsToTx", () => {
    let tx: Tx;
    beforeEach(() => {
      tx = new Tx();
      tx.id = "123";
    });

    it("should add 2 new tags, 0 => 2", async () => {
      const newTags = [
        TxTag.createTxTag(TxTagType.AMOUNT_MISMATCH, tx.id, "SYSTEM"),
        TxTag.createTxTag(TxTagType.UNABLE_TO_CAPTURE, tx.id, "SYSTEM"),
      ];

      await txTagRepository.addTagsToTx(tx, newTags);
      expect(tx.txTag).toEqual(newTags);
    });

    it("shouldn't add new tag when exist same tag, 1 => 1", async () => {
      const existingTags = [TxTag.createTxTag(TxTagType.AMOUNT_MISMATCH, tx.id, "ADMIN")];
      tx.txTag = existingTags;
      const newTags = [TxTag.createTxTag(TxTagType.AMOUNT_MISMATCH, tx.id, "SYSTEM")];
      await txTagRepository.addTagsToTx(tx, newTags);
      expect(tx.txTag).toEqual(existingTags);
    });

    it("should add new tag when same tag is removed, 0 => 1", async () => {
      const existingTags = [TxTag.createTxTag(TxTagType.AMOUNT_MISMATCH, tx.id, "ADMIN")];
      existingTags[0].removedAt = new Date();
      tx.txTag = existingTags;
      const newTags = [TxTag.createTxTag(TxTagType.AMOUNT_MISMATCH, tx.id, "SYSTEM")];
      await txTagRepository.addTagsToTx(tx, newTags);
      expect(tx.txTag).toEqual(newTags);
    });
  });
});

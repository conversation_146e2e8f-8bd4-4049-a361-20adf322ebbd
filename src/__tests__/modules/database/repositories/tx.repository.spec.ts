import { DecodedIdToken } from "firebase-admin/auth";
import { <PERSON>tityManager } from "typeorm";

import PaymentTx from "../../../../nestJs/modules/database/entities/paymentTx.entity";
import Tx from "../../../../nestJs/modules/database/entities/tx.entity";
import { PaymentTxRepository } from "../../../../nestJs/modules/database/repositories/paymentTx.repository";
import { TxRepository } from "../../../../nestJs/modules/database/repositories/tx.repository";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import { UtilsService } from "../../../../nestJs/modules/utils/utils.service";
import decodedIdTokenMock from "../../../mockData/firebase/decodedIdToken.mock";
import expects from "../../../utils/expects.specs.utils";
import FakeLoggerService from "../../../utils/fakeLogger.service.specs";
import FakeEntityManager, { insertMock, findOneMock } from "../../../utils/services/FakeEntityManager.specs.utils";
import FakeRepository, { mockUpsertPaymentTx } from "../../../utils/services/FakeRepository.specs.utils";
import { mockShouldUpdateTxMetadata } from "../../../utils/services/FakeTransactionFactoryService.specs.utils";
import {
  createQueryBuilderTypeormMock,
  findOneTypeormMock,
  upsertTypeormMock,
} from "../../../utils/typeorm/repository/Repository.specs.utils";

import "../../../initTests";

describe("tx.repository", () => {
  const user: DecodedIdToken = decodedIdTokenMock;

  describe("upsertTxAndUpdatePaymentTx", () => {
    let txRepository: TxRepository;
    let tx: Tx;
    let paymentTx: PaymentTx[];

    beforeEach(() => {
      txRepository = new TxRepository(
        new FakeRepository() as unknown as TxRepository,
        FakeEntityManager as unknown as EntityManager,
        new UtilsService(),
        new FakeRepository() as unknown as PaymentTxRepository,
        FakeLoggerService,
      );

      tx = new Tx();
      tx.id = "123";
      paymentTx = Array(3)
        .fill(() => {})
        .map((data, idx) => {
          const ptx = new PaymentTx();
          ptx.id = idx.toString();
          ptx.tx = tx;
          return ptx;
        });
    });

    it("should call findOne and upsert when the tx was not in the db", async () => {
      findOneTypeormMock.mockImplementation(() => undefined);

      await txRepository.upsertTxAndUpdatePaymentTx(tx, paymentTx, mockShouldUpdateTxMetadata);

      expect(findOneTypeormMock).toHaveBeenCalledWith({
        relations: ["paymentTx", "txTag", "merchant", "txApp", "paymentTx.paymentInstrument"],
        where: { id: tx.id },
      });
      expect(upsertTypeormMock).toHaveBeenCalledWith(
        { ...tx },
        { conflictPaths: ["id"], skipUpdateIfNoValuesChanged: true },
      );
    });

    it("should call findOne and update when the tx was in the db, and upssert paymentTx", async () => {
      const foundTx = new Tx();
      foundTx.id = "456";
      const existingPaymentTx = new PaymentTx();
      existingPaymentTx.id = "9999";
      foundTx.paymentTx = [existingPaymentTx];
      findOneTypeormMock.mockImplementation(() => new Promise((resolve) => resolve(foundTx)));

      await txRepository.upsertTxAndUpdatePaymentTx(tx, paymentTx, mockShouldUpdateTxMetadata);

      expect(findOneTypeormMock).toHaveBeenCalledWith({
        relations: ["paymentTx", "txTag", "merchant", "txApp", "paymentTx.paymentInstrument"],
        where: { id: tx.id },
      });
      expect(createQueryBuilderTypeormMock).toHaveBeenCalled();
      expect(mockUpsertPaymentTx).toHaveBeenCalledWith(paymentTx);
    });

    describe("createTxAdjustment", () => {
      const total = 456;
      const reason = "Because!!";
      const parentTxId = "123";

      beforeEach(() => {
        findOneMock.mockImplementation(() => new Promise((resolve) => resolve(tx)));
      });

      it("should create the transaction and save the adjustment", async () => {
        tx.adjustment = 135;
        const newTx = await txRepository.createTxAdjustment(parentTxId, total, reason, user);

        const newAdjustment1 = await Tx.newAdjustmentFromJson({
          parentTx: tx,
          total,
          reason,
          createdBy: user.email,
        });

        expect(findOneMock).toHaveBeenCalledWith(Tx, {
          relations: ["merchant", "payoutMerchant", "user"],
          where: { id: parentTxId },
        });
        expect(insertMock).not.toHaveBeenCalled();
        expect(newTx).toEqual({
          adjustmentTx: { ...newAdjustment1, id: expect.stringMatching(expects.uuid) },
          parentTx: {
            adjustment: 591,
            id: "123",
          },
        });
      });

      it("should throw an error when transaction is not found", async () => {
        findOneMock.mockImplementation(() => new Promise((resolve) => resolve(undefined)));

        expect(txRepository.createTxAdjustment(parentTxId, total, reason, user)).rejects.toThrowError(
          errorBuilder.transaction.notFound(parentTxId),
        );
      });
    });
  });
});

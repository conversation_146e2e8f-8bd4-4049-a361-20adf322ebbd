import User from "../../../../nestJs/modules/database/entities/user.entity";
import { UserRepository } from "../../../../nestJs/modules/database/repositories/user.repository";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import { mockGenSaltSync } from "../../../utils/bcrypt.specs.utils";
import { mockGenerateKeyPairSync } from "../../../utils/crypto.specs.utils";
import FakeRepository, { mockFind, mockFindOne, mockSave } from "../../../utils/services/FakeRepository.specs.utils";
jest.mock("typeorm/repository/Repository");
jest.mock("bcrypt");
jest.mock("crypto");
describe("user.repository", () => {
  let userRepository: UserRepository;

  beforeEach(() => {
    return (userRepository = new UserRepository(new FakeRepository() as unknown as UserRepository));
  });

  describe("findAnonymousUserByPhoneNumber", () => {
    it("should find user if exist in sql db", async () => {
      const phoneNumber = "+85212345678";
      const id = "12345678-1234-1234-1234-123456781234";
      const user = new User();
      user.id = id;
      user.phoneNumber = phoneNumber;

      mockFindOne.mockImplementation(() => new Promise((resolve) => resolve(user)));

      const result = await userRepository.findAnonymousUserByPhoneNumber(phoneNumber);

      expect(result.id).toEqual(id);
    });

    it("should create new user if not exist in sql db", async () => {
      const phoneNumber = "+85212345678";

      mockFindOne.mockImplementation(() => new Promise((resolve) => resolve(undefined)));

      const result = await userRepository.findAnonymousUserByPhoneNumber(phoneNumber);

      expect(result.id).toEqual(undefined);
      expect(result.phoneNumber).toEqual(phoneNumber);
    });
  });

  describe("find2ndRowUserByPhoneNumberOrCreate", () => {
    it("should throw error if user already registered", async () => {
      const phoneNumber = "+85212345678";
      const user = new User();
      user.appDatabaseId = "mockAppDatabaseId";

      mockFind.mockImplementation(() => new Promise((resolve) => resolve([user])));

      expect(userRepository.find2ndRowUserByPhoneNumberOrCreate(phoneNumber)).rejects.toThrow(
        errorBuilder.user.alreadyRegisteredShouldGoToApp(phoneNumber),
      );
    });

    it("should find user if exist in sql db", async () => {
      const phoneNumber = "+85212345678";
      const id = "12345678-1234-1234-1234-123456781234";
      const user = new User();
      user.id = id;
      user.phoneNumber = phoneNumber;

      mockFind.mockImplementation(() => new Promise((resolve) => resolve([user])));

      const result = await userRepository.find2ndRowUserByPhoneNumberOrCreate(phoneNumber);

      expect(result.id).toEqual(id);
    });

    it("should create new user if not exist in sql db", async () => {
      const phoneNumber = "+85212345678";

      mockFind.mockImplementation(() => new Promise((resolve) => resolve([])));

      await userRepository.find2ndRowUserByPhoneNumberOrCreate(phoneNumber);
      expect(mockSave).toHaveBeenCalledWith({
        phoneNumber: "+85212345678",
      });
    });
  });

  describe("find2ndRowUserByPhoneNumberOrCreateIgnoreRegisteredUser", () => {
    it("should pass even user already registered", async () => {
      const phoneNumber = "+85212345678";
      const user = new User();
      user.appDatabaseId = "mockAppDatabaseId";

      mockFind.mockImplementation(() => new Promise((resolve) => resolve([user])));

      await userRepository.find2ndRowUserByPhoneNumberOrCreateIgnoreRegisteredUser(phoneNumber);
      expect(mockSave).toHaveBeenCalledWith({
        phoneNumber: "+85212345678",
      });
    });

    it("should find user if exist in sql db", async () => {
      const phoneNumber = "+85212345678";
      const id = "12345678-1234-1234-1234-123456781234";
      const user = new User();
      user.id = id;
      user.phoneNumber = phoneNumber;

      mockFind.mockImplementation(() => new Promise((resolve) => resolve([user])));

      const result = await userRepository.find2ndRowUserByPhoneNumberOrCreateIgnoreRegisteredUser(phoneNumber);

      expect(result.id).toEqual(id);
    });

    it("should create new user if not exist in sql db", async () => {
      const phoneNumber = "+85212345678";

      mockFind.mockImplementation(() => new Promise((resolve) => resolve([])));

      await userRepository.find2ndRowUserByPhoneNumberOrCreateIgnoreRegisteredUser(phoneNumber);
      expect(mockSave).toHaveBeenCalledWith({
        phoneNumber: "+85212345678",
      });
    });
  });
  describe("findAppUserById", () => {
    it("should find user if exist in sql db", async () => {
      const appDatabaseId = "yW8375hfgdirewOYB8";
      const id = "12345678-1234-1234-1234-123456781234";
      const user = new User();
      user.id = id;
      user.appDatabaseId = appDatabaseId;
      user.salt = "mockSalt";
      user.privateKey = "mockPrivateKey";
      user.publicKey = "mockPublicKey";

      mockFindOne.mockImplementation(() => new Promise((resolve) => resolve(user)));

      const result = await userRepository.findAppUserById(appDatabaseId);

      expect(result.id).toEqual(id);
    });

    it("should throw error if not exist in sql db", async () => {
      const appDatabaseId = "yW8375hfgdirewOYB8";

      mockFindOne.mockImplementation(() => new Promise((resolve) => resolve(undefined)));

      expect(userRepository.findAppUserById(appDatabaseId)).rejects.toThrow(
        errorBuilder.user.notFoundInSql(appDatabaseId),
      );
    });

    it("should throw error if no salt value", async () => {
      const appDatabaseId = "yW8375hfgdirewOYB8";
      const id = "12345678-1234-1234-1234-123456781234";
      const user = new User();
      user.id = id;
      user.appDatabaseId = appDatabaseId;
      user.privateKey = "mockPrivateKey";
      user.publicKey = "mockPublicKey";

      mockFindOne.mockImplementation(() => new Promise((resolve) => resolve(user)));

      expect(userRepository.findAppUserById(appDatabaseId)).rejects.toThrow(
        errorBuilder.user.invalidUserType(appDatabaseId),
      );
    });
  });

  describe("createUserToDatabaseFromFirebaseAuth", () => {
    const userRecord: Partial<User> = {
      appDatabaseId: "yW8375hfgdirewOYB8",
      phoneNumber: "+85212345678",
    };

    it("should create new user if not exist in sql, so will generate salt value", async () => {
      mockFindOne.mockImplementation(() => new Promise((resolve) => resolve(undefined)));
      mockGenSaltSync.mockReturnValue("mockSalt");
      mockGenerateKeyPairSync.mockReturnValue({
        publicKey: "mockPublicKey",
        privateKey: "mockPrivateKey",
      });
      await userRepository.createUser(userRecord);

      expect(mockSave).toHaveBeenCalledWith({
        appDatabaseId: "yW8375hfgdirewOYB8",
        phoneNumber: "+85212345678",
        publicKey: "mockPublicKey",
        privateKey: "mockPrivateKey",
        salt: "mockSalt",
        pinErrorCount: 0,
      });
    });
  });
});

import fs from "fs";
import { join } from "path";

import { DefaultEntity } from "@nest/modules/database/entities/defaultEntity";

/**
 * This test is to check if all entities are extending the DefaultEntity class
 */
describe("defaultEntity", () => {
  const entities: any[] = [];

  beforeAll(async () => {
    const directory = join(__dirname, "../../../../nestJs/modules/database/entities");
    return await new Promise((resolve) => {
      fs.readdir(directory, (err, files) => {
        files
          .filter((file) => file !== "defaultEntity.ts")
          .forEach(async (file) => {
            const filePath = join(directory, "/", file);
            const imp = await import(filePath);
            entities.push(imp.default);
            if (files.length - 1 === entities.length) resolve(true);
          });
      });
    });
  });

  it("should find the DefaultEntity extended class in the entities", () => {
    entities.forEach((entity) => {
      expect(entity).not.toBeFalsy();
      // check the prototype tree instead of Regex :)
      expect(entity.prototype instanceof DefaultEntity).toBeTruthy();
    });
  });
});

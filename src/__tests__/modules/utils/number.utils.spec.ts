import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import {
  calculateDashTransactionFee,
  roundAwayFromZero,
  roundDownOneDecimal,
  roundUpOneDecimal,
} from "../../../nestJs/modules/utils/utils/number.utils";

describe("number.utils", () => {
  describe("roundUpOneDecimal", () => {
    test.each([
      [1.23456789, 1.3],
      [1.25456789, 1.3],
      [-123.456789, -123.4],
      [-123.446789, -123.4],
      [-123.496789, -123.5],
    ])("should round up %p to %p", (input, expected) => {
      expect(roundUpOneDecimal(input)).toEqual(expected);
    });
    it("should throw an error if the param is not a number", () => {
      expect(() => roundUpOneDecimal("abcd" as unknown as number)).toThrowError(
        errorBuilder.global.invalidParam("value: 'abcd', type: 'string'"),
      );
    });
  });

  describe("roundDownOneDecimal", () => {
    test.each([
      [1.23456789, 1.2],
      [1.25456789, 1.2],
      [-123.456789, -123.5],
      [-123.446789, -123.5],
      [-123.496789, -123.5],
    ])("should round down %p to %p", (input, expected) => {
      expect(roundDownOneDecimal(input)).toEqual(expected);
    });
    it("should throw an error if the param is not a number", () => {
      expect(() => roundDownOneDecimal("abcd" as unknown as number)).toThrowError(
        errorBuilder.global.invalidParam("value: 'abcd', type: 'string'"),
      );
    });
  });

  describe("roundAwayFromZero", () => {
    test.each([
      [1.23456789, 1.3],
      [1.25456789, 1.3],
      [-123.456789, -123.5],
      [-123.446789, -123.5],
      [-123.496789, -123.5],
    ])("should round %p away from zero to %p", (input, expected) => {
      expect(roundAwayFromZero(input)).toEqual(expected);
    });
    it("should throw an error if the param is not a number", () => {
      expect(() => roundAwayFromZero("abcd" as unknown as number)).toThrowError(
        errorBuilder.global.invalidParam("value: 'abcd', type: 'string'"),
      );
    });
  });

  describe("calculateDashFee", () => {
    it("should return the right dashFee", () => {
      expect(
        calculateDashTransactionFee({ tripTotal: 90, dashTips: 10, dashFeeRate: 0.03, dashFeeConstant: 1 }),
      ).toEqual(4);
    });
    it("should return the right dashFee when dashFeeRate is null", () => {
      expect(
        calculateDashTransactionFee({
          tripTotal: 90,
          dashTips: 10,
          dashFeeRate: null as unknown as number,
          dashFeeConstant: 1,
        }),
      ).toEqual(1);
    });
    it("should return the right dashFee when dashFeeConstant is null", () => {
      expect(
        calculateDashTransactionFee({
          tripTotal: 90,
          dashTips: 10,
          dashFeeRate: 0.03,
          dashFeeConstant: null as unknown as number,
        }),
      ).toEqual(3);
    });
    it("should return the right dashFee when dashTips is null", () => {
      expect(
        calculateDashTransactionFee({
          tripTotal: 90,
          dashTips: null as unknown as number,
          dashFeeRate: 0.03,
          dashFeeConstant: 1,
        }),
      ).toEqual(3.7);
    });
    it("should return the right dashFee when dashTips, dashFeeRate and dashFeeConstant are null", () => {
      expect(
        calculateDashTransactionFee({
          tripTotal: 90,
          dashTips: null as unknown as number,
          dashFeeRate: null as unknown as number,
          dashFeeConstant: null as unknown as number,
        }),
      ).toEqual(0);
    });
    it("should return 0 when all params are null", () => {
      expect(
        calculateDashTransactionFee({
          tripTotal: null as unknown as number,
          dashTips: null as unknown as number,
          dashFeeRate: null as unknown as number,
          dashFeeConstant: null as unknown as number,
        }),
      ).toEqual(0);
    });
  });
});

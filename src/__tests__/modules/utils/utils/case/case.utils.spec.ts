import casesUtils from "../../../../../nestJs/modules/utils/utils/case/case.utils";

/**
 * Test case for case.utils.ts
 */
describe("case.utils", () => {
  describe("shouldPreserve", () => {
    it("should return true if key is in the list of keys to preserve", () => {
      const key = "i18n";
      expect(casesUtils.shouldPreserve(key)).toBe(true);
    });
    it("should return false if key is not in the list of keys to preserve", () => {
      const key = "UFO";
      expect(casesUtils.shouldPreserve(key)).toBe(false);
    });
  });
  describe("isLocaleSnakeCase", () => {
    it("should return true if key is in snake_case", () => {
      const key = "zh_hk";
      expect(casesUtils.isLocaleSnakeCase(key)).toBe(true);
    });
    it("should return false if key is not in snake_case", () => {
      const key = "zhHK";
      expect(casesUtils.isLocaleSnakeCase(key)).toBe(false);
    });
    it("should return false if key is not in snake_case", () => {
      const key = "zh_HK";
      expect(casesUtils.isLocaleSnakeCase(key)).toBe(false);
    });
    it("should return false if key is not in snake_case", () => {
      const key = "zh_Hk";
      expect(casesUtils.isLocaleSnakeCase(key)).toBe(false);
    });
    it("should return false if key is not in snake_case", () => {
      const key = "zh_HKG";
      expect(casesUtils.isLocaleSnakeCase(key)).toBe(false);
    });
  });
  describe("isLocaleCamelCase", () => {
    it("should return true if key is in camelCase", () => {
      const key = "zhHK";
      expect(casesUtils.isLocaleCamelCase(key)).toBe(true);
    });
    it("should return false if key is not in camelCase", () => {
      const key = "zhHk";
      expect(casesUtils.isLocaleCamelCase(key)).toBe(false);
    });
    it("should return false if key is not in camelCase", () => {
      const key = "zh_Hk";
      expect(casesUtils.isLocaleCamelCase(key)).toBe(false);
    });
    it("should return false if key is not in camelCase", () => {
      const key = "zh_HKG";
      expect(casesUtils.isLocaleCamelCase(key)).toBe(false);
    });
  });
  describe("customSnakeCase", () => {
    it("should convert camelCase locale keys to snake_case", () => {
      const key = "zhHK";
      expect(casesUtils.customSnakeCase(key)).toBe("zh_hk");
    });
  });
  describe("customCamelCase", () => {
    it("should convert snake_case locale keys to camelCase", () => {
      const key = "zh_hk";
      expect(casesUtils.customCamelCase(key)).toBe("zhHK");
    });
  });
  describe("snakeKeys", () => {
    it("should convert camelCase keys to snake_case", () => {
      const obj = {
        camelCase: "value",
        anotherKey: "anotherValue",
        nestedObject: {
          nestedKey: "nestedValue",
        },
      };

      const result = casesUtils.snakeKeys(obj);

      expect(result).toEqual({
        camel_case: "value",
        another_key: "anotherValue",
        nested_object: {
          nested_key: "nestedValue",
        },
      });
    });
    it("should convert two consecutive upper case in camelCase keys to snake_case correctly", () => {
      const obj = {
        camelCase: "value",
        anotherKey: "anotherValue",
        nestedObject: {
          nestedKey: "nestedValue",
          nestedURL: "nestedValue",
        },
      };

      const result = casesUtils.snakeKeys(obj);

      expect(result).toEqual({
        camel_case: "value",
        another_key: "anotherValue",
        nested_object: {
          nested_key: "nestedValue",
          nested_url: "nestedValue",
        },
      });
    });
    it("should handle arrays of objects", () => {
      const input = [{ firstName: "John" }, { lastName: "Doe" }, { middleName: "A." }];
      const expected = [{ first_name: "John" }, { last_name: "Doe" }, { middle_name: "A." }];
      expect(casesUtils.snakeKeys(input)).toEqual(expected);
    });
    it("should handle complex objects", () => {
      const input = [
        {
          i18n: {
            en: { displayName: "1 Lee Garden Road", formattedAddress: "1 Lee Garden Rd, Causeway Bay" },
            zhHK: { displayName: "1 利園山道", formattedAddress: "香港銅鑼灣利園山道1號" },
          },
          index: 0,
          lat: 22.2779639,
          lng: 114.18523859999999,
          placeId: "ChIJO4I52lYABDQRvs-5GGIgFSY",
        },
        {
          i18n: {
            en: {
              displayName: "Grand Central Plaza",
              formattedAddress: "5/F, Grand Central Plaza, 138 Sha Tin Rural Committee Rd, Sha Tin",
            },
            zhHK: {
              displayName: "Grand Central Plaza",
              formattedAddress: "5/F, Grand Central Plaza, 138 Sha Tin Rural Committee Rd, Sha Tin, 香港",
            },
          },
          index: 1,
          lat: 22.3855358,
          lng: 114.1877806,
          placeId: "ChIJH7whZrIHBDQR47YbRnn5Pcw",
        },
      ];
      const expected = [
        {
          i18n: {
            en: { display_name: "1 Lee Garden Road", formatted_address: "1 Lee Garden Rd, Causeway Bay" },
            zh_hk: { display_name: "1 利園山道", formatted_address: "香港銅鑼灣利園山道1號" },
          },
          index: 0,
          lat: 22.2779639,
          lng: 114.18523859999999,
          place_id: "ChIJO4I52lYABDQRvs-5GGIgFSY",
        },
        {
          i18n: {
            en: {
              display_name: "Grand Central Plaza",
              formatted_address: "5/F, Grand Central Plaza, 138 Sha Tin Rural Committee Rd, Sha Tin",
            },
            zh_hk: {
              display_name: "Grand Central Plaza",
              formatted_address: "5/F, Grand Central Plaza, 138 Sha Tin Rural Committee Rd, Sha Tin, 香港",
            },
          },
          index: 1,
          lat: 22.3855358,
          lng: 114.1877806,
          place_id: "ChIJH7whZrIHBDQR47YbRnn5Pcw",
        },
      ];
      expect(casesUtils.snakeKeys(input)).toEqual(expected);
    });
  });

  describe("camelKeys", () => {
    it("should convert snake_case keys to camelCase", () => {
      const obj = {
        snake_case: "value",
        another_key: "anotherValue",
        nested_object: {
          nested_key: "nestedValue",
        },
      };

      const result = casesUtils.camelizeKeys(obj);

      expect(result).toEqual({
        snakeCase: "value",
        anotherKey: "anotherValue",
        nestedObject: {
          nestedKey: "nestedValue",
        },
      });
    });
    it("should handle complex objects", () => {
      const input = [
        {
          i18n: {
            en: { display_name: "1 Lee Garden Road", formatted_address: "1 Lee Garden Rd, Causeway Bay" },
            zh_hk: { display_name: "1 利園山道", formatted_address: "香港銅鑼灣利園山道1號" },
          },
          index: 0,
          lat: 22.2779639,
          lng: 114.18523859999999,
          place_id: "ChIJO4I52lYABDQRvs-5GGIgFSY",
        },
        {
          i18n: {
            en: {
              display_name: "Grand Central Plaza",
              formatted_address: "5/F, Grand Central Plaza, 138 Sha Tin Rural Committee Rd, Sha Tin",
            },
            zh_hk: {
              display_name: "Grand Central Plaza",
              formatted_address: "5/F, Grand Central Plaza, 138 Sha Tin Rural Committee Rd, Sha Tin, 香港",
            },
          },
          index: 1,
          lat: 22.3855358,
          lng: 114.1877806,
          place_id: "ChIJH7whZrIHBDQR47YbRnn5Pcw",
        },
      ];
      const expected = [
        {
          i18n: {
            en: { displayName: "1 Lee Garden Road", formattedAddress: "1 Lee Garden Rd, Causeway Bay" },
            zhHK: { displayName: "1 利園山道", formattedAddress: "香港銅鑼灣利園山道1號" },
          },
          index: 0,
          lat: 22.2779639,
          lng: 114.18523859999999,
          placeId: "ChIJO4I52lYABDQRvs-5GGIgFSY",
        },
        {
          i18n: {
            en: {
              displayName: "Grand Central Plaza",
              formattedAddress: "5/F, Grand Central Plaza, 138 Sha Tin Rural Committee Rd, Sha Tin",
            },
            zhHK: {
              displayName: "Grand Central Plaza",
              formattedAddress: "5/F, Grand Central Plaza, 138 Sha Tin Rural Committee Rd, Sha Tin, 香港",
            },
          },
          index: 1,
          lat: 22.3855358,
          lng: 114.1877806,
          placeId: "ChIJH7whZrIHBDQR47YbRnn5Pcw",
        },
      ];
      expect(casesUtils.camelizeKeys(input)).toEqual(expected);
    });
  });
});

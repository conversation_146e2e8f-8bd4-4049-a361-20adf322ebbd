import retryUtils from "../../../nestJs/modules/utils/utils/retry.utils";
import "../../initTests";

describe("retry.utils", () => {
  describe("retryFct", () => {
    let mockPromise = jest.fn();
    beforeEach(() => {
      mockPromise = jest.fn();
    });

    it("should call the function without delay", async () => {
      const startTime = Date.now();
      await retryUtils.retryFct(mockPromise);
      const endTime = Date.now();

      expect(mockPromise).toHaveBeenCalledTimes(1);
      expect(endTime - startTime).toBeLessThan(200);
    });

    it("should call the function 2 times with delay", async () => {
      const startTime = Date.now();
      await retryUtils.retryFct(mockPromise, 1, () => true);
      const endTime = Date.now();

      expect(mockPromise).toHaveBeenCalledTimes(2);
      expect(endTime - startTime).toBeLessThan(2000);
    });

    it("should call the function 3 times with delay when throw", async () => {
      const error = new Error("error 1");
      const startTime = Date.now();
      let counter = 0;

      await expect(
        retryUtils.retryFct(
          () => {
            counter++;
            throw error;
          },
          2,
          () => true,
        ),
      ).rejects.toThrow(error);

      const endTime = Date.now();

      expect(counter).toEqual(3);
      expect(endTime - startTime).toBeLessThan(3000);
    });

    it("should should return the result after throw", async () => {
      const error = new Error("error 1");
      const startTime = Date.now();
      let counter = 0;

      const result = await retryUtils.retryFct<{ some: string }>(async () => {
        counter++;
        if (counter === 2) {
          return { some: "result" };
        }
        throw error;
      }, 2);

      const endTime = Date.now();

      await expect(result).toEqual({ elapsed: 1, result: { some: "result" } });
      expect(counter).toEqual(2);
      expect(endTime - startTime).toBeLessThan(2000);
    });

    it("should return the last result when the first is not expected", async () => {
      const startTime = Date.now();
      let counter = 0;

      const result = await retryUtils.retryFct<{ some: string }>(
        async () => {
          counter++;
          if (counter === 2) {
            return { some: "stop" };
          }
          return { some: "continue" };
        },
        2,
        (result) => result.some === "continue",
      );

      const endTime = Date.now();

      await expect(result).toEqual({ elapsed: 1, result: { some: "stop" } });
      expect(counter).toEqual(2);
      expect(endTime - startTime).toBeLessThan(2000);
    });
  });
});

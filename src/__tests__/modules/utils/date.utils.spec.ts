import moment from "moment";

import dateUtils from "../../../nestJs/modules/utils/utils/date.utils";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";

describe("date.utils", () => {
  describe("parseDuration", () => {
    let duration: moment.Duration;
    beforeEach(() => {
      duration = moment.duration();
    });

    it("30 days", () => {
      duration.add(30, "days");
      expect(dateUtils.parseDuration("30D")).toEqual(duration);
    });

    it("30 mins", () => {
      duration.add(30, "minutes");
      expect(dateUtils.parseDuration("30M")).toEqual(duration);
    });

    it("1 hour 30 mins", () => {
      duration.add(1, "hours");
      duration.add(30, "minutes");
      expect(dateUtils.parseDuration("1H 30M")).toEqual(duration);
    });

    it("90 mins", () => {
      duration.add(90, "minutes");
      expect(dateUtils.parseDuration("90M")).toEqual(duration);
    });

    it("throw error when invalid duration unit", () => {
      expect(() => {
        dateUtils.parseDuration("30X");
      }).toThrowError(errorBuilder.global.invalidParam("duration unit: x"));
    });
  });
});

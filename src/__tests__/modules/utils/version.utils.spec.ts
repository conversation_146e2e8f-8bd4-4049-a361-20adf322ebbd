import { isGreaterOrEqualThan } from "../../../nestJs/modules/utils/utils/version.utils";
import "../../initTests";

describe("version.utils", () => {
  describe("isGreaterOrEqualThan", () => {
    it("should return true for 1.2.1 vs 1.2.0", () => {
      expect(isGreaterOrEqualThan("1.2.1", "1.2.0")).toEqual(true);
    });
    it("should return true for 1.2.1 vs 1.2", () => {
      expect(isGreaterOrEqualThan("1.2.1", "1.2")).toEqual(true);
    });
    it("should return true for 1.2.0 to 1.2.0", () => {
      expect(isGreaterOrEqualThan("1.2.0", "1.2.0")).toEqual(true);
    });
    it("should return false for 1.1.9 vs 1.2.0", () => {
      expect(isGreaterOrEqualThan("1.1.9", "1.2.0")).toEqual(false);
    });
    it("should return false for 1.1 vs 1.2.0", () => {
      expect(isGreaterOrEqualThan("1.1", "1.2.0")).toEqual(false);
    });
    it("should return false for 1.3 vs 1.2.0", () => {
      expect(isGreaterOrEqualThan("1.3", "1.2.0")).toEqual(true);
    });
    it("should return false for 1.0.0.1 vs 1.2.0", () => {
      expect(isGreaterOrEqualThan("1.0.0.1", "1.2.0")).toEqual(false);
    });
    it("should return false for 1.2.0.1 vs 1.2.0", () => {
      expect(isGreaterOrEqualThan("1.2.0.1", "1.2.0")).toEqual(true);
    });
  });
});

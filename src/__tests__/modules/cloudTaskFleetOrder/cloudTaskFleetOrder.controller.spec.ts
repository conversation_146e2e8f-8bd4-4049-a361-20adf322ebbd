import { Test, TestingModule } from "@nestjs/testing";

import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";
import { CloudTaskClientService } from "@nest/modules/cloud-task-client/cloud-task-client.service";
import { CloudTaskFleetOrderController } from "@nest/modules/cloudTaskFleetOrder/cloudTaskFleetOrder.controller";
import { SyncabUpdateFleetOrderDelegatee } from "@nest/modules/cloudTaskFleetOrder/delegatees/SyncabUpdateFleetOrderDelegatee";
import { UpdateFleetOrderDelegatee } from "@nest/modules/cloudTaskFleetOrder/delegatees/UpdateFleetOrderDelegatee";
import { FleetOrderStatus } from "@nest/modules/database/entities/fleetOrder.entity";
import { FleetOrderRepository } from "@nest/modules/database/repositories/fleetOrder.repository";
import { FleetOrderTimelineRepository } from "@nest/modules/database/repositories/fleetOrderTimeline.repository";
import { FleetPartnerRepository } from "@nest/modules/database/repositories/fleetPartner.repository";
import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { DriverService } from "@nest/modules/merchant/merchantDriver/merchantDriver.service";
import { SyncabUpdateFleetOrderDelegateeFactory } from "@tests/v2/mockFactories/mcokSyncabUpdateDelegateeFactory";
import { AppDatabaseFactory } from "@tests/v2/mockFactories/mockAppDatabaseFactory";
import { DriverServiceFactory } from "@tests/v2/mockFactories/mockDriverServiceFactory";
import { FleetOrderFactory } from "@tests/v2/mockFactories/mockFleetFactory";
import { HailingApiServiceFactory } from "@tests/v2/mockFactories/mockHailingApiServiceFactory";
import { MerchantFactory } from "@tests/v2/mockFactories/mockMerchantFactory";
import { MockSyncabApiServiceProvider } from "@tests/v2/mockModules/mcokSyncabApiModule";
import { MockAppDatabaseServiceProvider } from "@tests/v2/mockModules/mockAppDatabaseModule";
import { MockCloudTaskClientServiceProvider } from "@tests/v2/mockModules/mockCloudTaskClientModule";
import { MockDriverService } from "@tests/v2/mockModules/mockDriverModule";
import {
  MockFleetOrderRepository,
  MockFleetOrderTimelineRepository,
  MockFleetPartnerRepository,
  MockFleetQuoteRepository,
} from "@tests/v2/mockModules/mockFleetModule";
import { MockHailingApiServiceProvider } from "@tests/v2/mockModules/mockHailingApiModule";
import { MockLoggerServiceAdapterProvider } from "@tests/v2/mockModules/mockLoggerServiceAdapter";
import { MockMerchantRepository } from "@tests/v2/mockModules/mockMerchantModule";
import { MockPubsubServiceProvider } from "@tests/v2/mockModules/mockPubsubModule";
import { MockTransactionEventServiceProvider } from "@tests/v2/mockModules/mockTranscationEventModule";
import { MockUserRepository } from "@tests/v2/mockModules/mockUserModule";

describe("cloudTaskFleetOrder", () => {
  let controller: CloudTaskFleetOrderController;
  let fleetOrderRepository: FleetOrderRepository;
  let fleetPartnerRepository: FleetPartnerRepository;
  let syncabUpdateFleetOrderDelegatee: SyncabUpdateFleetOrderDelegatee;
  let fleetOrderTimelineRepository: FleetOrderTimelineRepository;
  let appDatabaseService: AppDatabaseService;
  let merchantRepository: MerchantRepository;
  let hailingApiService: HailingApiService;
  let cloudTaskClientService: CloudTaskClientService;
  let driverService: DriverService;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [
        MockAppDatabaseServiceProvider,
        MockLoggerServiceAdapterProvider,
        MockTransactionEventServiceProvider,
        MockDriverService,
        MockCloudTaskClientServiceProvider,
        MockPubsubServiceProvider,
        MockHailingApiServiceProvider,
        MockSyncabApiServiceProvider,
        UpdateFleetOrderDelegatee,
        SyncabUpdateFleetOrderDelegatee,
        MockFleetOrderRepository,
        MockFleetOrderTimelineRepository,
        MockFleetPartnerRepository,
        MockFleetQuoteRepository,
        MockMerchantRepository,
        MockUserRepository,
      ],
      controllers: [CloudTaskFleetOrderController],
      exports: [],
    }).compile();

    controller = moduleRef.get(CloudTaskFleetOrderController);
    fleetOrderRepository = moduleRef.get(FleetOrderRepository);
    fleetPartnerRepository = moduleRef.get(FleetPartnerRepository);
    fleetOrderTimelineRepository = moduleRef.get(FleetOrderTimelineRepository);
    syncabUpdateFleetOrderDelegatee = moduleRef.get(SyncabUpdateFleetOrderDelegatee);
    appDatabaseService = moduleRef.get(AppDatabaseService);
    merchantRepository = moduleRef.get(MerchantRepository);
    hailingApiService = moduleRef.get(HailingApiService);
    cloudTaskClientService = moduleRef.get(CloudTaskClientService);
    driverService = moduleRef.get(DriverService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe(".updateFleetOrder", () => {
    it("when syncab have new status update should update fleet order", async () => {
      FleetOrderFactory.mockFleetOrderFindOne(fleetOrderRepository);
      FleetOrderFactory.mockFleetPartnerFindOne(fleetPartnerRepository);
      FleetOrderFactory.mockFleetOrderTimelineFindLastTimelineByFleetOrderId(fleetOrderTimelineRepository);
      AppDatabaseFactory.mockMeterFindOne(appDatabaseService.meterRepository());
      AppDatabaseFactory.mockDriverFindOne(appDatabaseService.driverRepository());
      MerchantFactory.mockMerchantFindOne(merchantRepository);
      HailingApiServiceFactory.mockUpdateFleetHailingRequest(hailingApiService);
      HailingApiServiceFactory.mockUpdateDriverHeartBeat(hailingApiService);
      SyncabUpdateFleetOrderDelegateeFactory.mockExecute(syncabUpdateFleetOrderDelegatee);

      await controller.updateFleetOrder({
        fleetOrderId: "123",
      });

      expect(hailingApiService.updateFleetHailingRequest).toHaveBeenCalled();
      expect(hailingApiService.updateDriverHeartBeat).toHaveBeenCalled();
      expect(fleetOrderRepository.update).toHaveBeenCalled();
      expect(cloudTaskClientService.enqueueUpdateFleetTask).toHaveBeenCalled();
      expect(fleetOrderTimelineRepository.save).toHaveBeenCalled();
    });

    it("when no status update should return do nothing", async () => {
      FleetOrderFactory.mockFleetOrderFindOne(fleetOrderRepository);
      FleetOrderFactory.mockFleetPartnerFindOne(fleetPartnerRepository);
      FleetOrderFactory.mockFleetOrderTimelineFindLastTimelineByFleetOrderId(fleetOrderTimelineRepository);
      AppDatabaseFactory.mockMeterFindOne(appDatabaseService.meterRepository());
      AppDatabaseFactory.mockDriverFindOne(appDatabaseService.driverRepository());
      MerchantFactory.mockMerchantFindOne(merchantRepository);
      HailingApiServiceFactory.mockUpdateFleetHailingRequest(hailingApiService);
      HailingApiServiceFactory.mockUpdateDriverHeartBeat(hailingApiService);
      SyncabUpdateFleetOrderDelegateeFactory.mockExecute(syncabUpdateFleetOrderDelegatee, FleetOrderStatus.MATCHING, {
        thirdPartyStatus: "searching-for-driver",
      });

      await controller.updateFleetOrder({
        fleetOrderId: "123",
      });

      expect(hailingApiService.updateFleetHailingRequest).not.toHaveBeenCalled();
      expect(hailingApiService.updateDriverHeartBeat).toHaveBeenCalled();
      expect(fleetOrderRepository.update).not.toHaveBeenCalled();
      expect(fleetOrderTimelineRepository.save).not.toHaveBeenCalled();
      expect(cloudTaskClientService.enqueueUpdateFleetTask).toHaveBeenCalled();
    });

    it("when no status match should create new timeline but no other action", async () => {
      FleetOrderFactory.mockFleetOrderFindOne(fleetOrderRepository);
      FleetOrderFactory.mockFleetPartnerFindOne(fleetPartnerRepository);
      FleetOrderFactory.mockFleetOrderTimelineFindLastTimelineByFleetOrderId(fleetOrderTimelineRepository);
      AppDatabaseFactory.mockMeterFindOne(appDatabaseService.meterRepository());
      AppDatabaseFactory.mockDriverFindOne(appDatabaseService.driverRepository());
      MerchantFactory.mockMerchantFindOne(merchantRepository);
      HailingApiServiceFactory.mockUpdateFleetHailingRequest(hailingApiService);
      HailingApiServiceFactory.mockUpdateDriverHeartBeat(hailingApiService);
      SyncabUpdateFleetOrderDelegateeFactory.mockExecute(syncabUpdateFleetOrderDelegatee, null, {
        thirdPartyStatus: "any-status",
      });

      await controller.updateFleetOrder({
        fleetOrderId: "123",
      });

      expect(hailingApiService.updateFleetHailingRequest).not.toHaveBeenCalled();
      expect(hailingApiService.updateDriverHeartBeat).toHaveBeenCalled();
      expect(fleetOrderRepository.update).not.toHaveBeenCalled();
      expect(cloudTaskClientService.enqueueUpdateFleetTask).toHaveBeenCalled();
      expect(fleetOrderTimelineRepository.save).toHaveBeenCalled();
    });

    it("when order is completed should do nothing", async () => {
      FleetOrderFactory.mockFleetOrderFindOne(fleetOrderRepository, { status: FleetOrderStatus.COMPLETED });
      FleetOrderFactory.mockFleetPartnerFindOne(fleetPartnerRepository);
      FleetOrderFactory.mockFleetOrderTimelineFindLastTimelineByFleetOrderId(fleetOrderTimelineRepository);

      await controller.updateFleetOrder({
        fleetOrderId: "123",
      });

      expect(hailingApiService.updateFleetHailingRequest).not.toHaveBeenCalled();
      expect(hailingApiService.updateDriverHeartBeat).not.toHaveBeenCalled();
      expect(fleetOrderRepository.update).not.toHaveBeenCalled();
      expect(fleetOrderTimelineRepository.save).not.toHaveBeenCalled();
      expect(cloudTaskClientService.enqueueUpdateFleetTask).not.toHaveBeenCalled();
    });

    it("when order is cancelled should do nothing", async () => {
      FleetOrderFactory.mockFleetOrderFindOne(fleetOrderRepository, { status: FleetOrderStatus.CANCELLED });
      FleetOrderFactory.mockFleetPartnerFindOne(fleetPartnerRepository);
      FleetOrderFactory.mockFleetOrderTimelineFindLastTimelineByFleetOrderId(fleetOrderTimelineRepository);

      await controller.updateFleetOrder({
        fleetOrderId: "123",
      });

      expect(hailingApiService.updateFleetHailingRequest).not.toHaveBeenCalled();
      expect(hailingApiService.updateDriverHeartBeat).not.toHaveBeenCalled();
      expect(fleetOrderRepository.update).not.toHaveBeenCalled();
      expect(fleetOrderTimelineRepository.save).not.toHaveBeenCalled();
      expect(cloudTaskClientService.enqueueUpdateFleetTask).not.toHaveBeenCalled();
    });

    it("when order is completed should do nothing", async () => {
      FleetOrderFactory.mockFleetOrderFindOne(fleetOrderRepository, { status: FleetOrderStatus.COMPLETED });
      FleetOrderFactory.mockFleetPartnerFindOne(fleetPartnerRepository);
      FleetOrderFactory.mockFleetOrderTimelineFindLastTimelineByFleetOrderId(fleetOrderTimelineRepository);

      await controller.updateFleetOrder({
        fleetOrderId: "123",
      });

      expect(hailingApiService.updateFleetHailingRequest).not.toHaveBeenCalled();
      expect(hailingApiService.updateDriverHeartBeat).not.toHaveBeenCalled();
      expect(fleetOrderRepository.update).not.toHaveBeenCalled();
      expect(fleetOrderTimelineRepository.save).not.toHaveBeenCalled();
      expect(cloudTaskClientService.enqueueUpdateFleetTask).not.toHaveBeenCalled();
    });

    it("when order is updated to completed should call meterEndTrip", async () => {
      FleetOrderFactory.mockFleetOrderFindOne(fleetOrderRepository);
      FleetOrderFactory.mockFleetPartnerFindOne(fleetPartnerRepository);
      FleetOrderFactory.mockFleetOrderTimelineFindLastTimelineByFleetOrderId(fleetOrderTimelineRepository);
      AppDatabaseFactory.mockMeterFindOne(appDatabaseService.meterRepository());
      AppDatabaseFactory.mockDriverFindOne(appDatabaseService.driverRepository());
      AppDatabaseFactory.mockMeterFindOne(appDatabaseService.meterTripRepository("123"));
      MerchantFactory.mockMerchantFindOne(merchantRepository);
      HailingApiServiceFactory.mockUpdateFleetHailingRequest(hailingApiService);
      HailingApiServiceFactory.mockUpdateDriverHeartBeat(hailingApiService);
      SyncabUpdateFleetOrderDelegateeFactory.mockExecute(syncabUpdateFleetOrderDelegatee, FleetOrderStatus.COMPLETED, {
        thirdPartyStatus: "completed",
      });
      SyncabUpdateFleetOrderDelegateeFactory.mockGetBookingReceiptSnapshot(syncabUpdateFleetOrderDelegatee);
      DriverServiceFactory.mockUpdateMeterTripEndByFleet(driverService);

      await controller.updateFleetOrder({
        fleetOrderId: "123",
      });

      expect(hailingApiService.updateFleetHailingRequest).toHaveBeenCalled();
      expect(hailingApiService.updateDriverHeartBeat).toHaveBeenCalled();
      expect(fleetOrderRepository.update).toHaveBeenCalled();
      expect(fleetOrderTimelineRepository.save).toHaveBeenCalled();
      expect(cloudTaskClientService.enqueueUpdateFleetTask).not.toHaveBeenCalled();
      expect(syncabUpdateFleetOrderDelegatee.getBookingReceiptSnapshot).toHaveBeenCalled();
      expect(syncabUpdateFleetOrderDelegatee.execute).toHaveBeenCalled();
      expect(driverService.updateMeterTripEndByFleet).toHaveBeenCalled();
    });

    it("when no fleet order timeline found should throw error", async () => {
      FleetOrderFactory.mockFleetOrderFindOne(fleetOrderRepository);
      FleetOrderFactory.mockFleetPartnerFindOne(fleetPartnerRepository);
      FleetOrderFactory.mockFleetOrderTimelineFindLastTimelineByFleetOrderId(fleetOrderTimelineRepository, {}, true);

      await expect(
        controller.updateFleetOrder({
          fleetOrderId: "123",
        }),
      ).rejects.toThrow();
    });

    it("when no fleet order not found should throw error", async () => {
      FleetOrderFactory.mockFleetOrderFindOne(fleetOrderRepository, {}, true);

      await expect(
        controller.updateFleetOrder({
          fleetOrderId: "123",
        }),
      ).rejects.toThrow();
    });
  });
});

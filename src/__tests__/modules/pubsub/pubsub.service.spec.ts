import { TopicNamesType } from "../../../nestJs/modules/pubsub/dto/topicName.dto";
import { PubSubService } from "../../../nestJs/modules/pubsub/pubsub.service";
import { resetPubsubMocks, mockCreateTopic, mockPublishMessage } from "../../utils/@google-cloud/pubsub.specs.utils";
import {
  expectedCaptureQueueEntry,
  expectedCopyTripToDriveQueueEntry,
  expectedMessageQueueEntry,
  expectedTxQueueEntry,
  expectedVoidQueueEntry,
} from "../../utils/fakeData/fakeDataPubsub.specs.utils";
import { fakeCacheMockGet } from "../../utils/services/FakeCache.specs.utils";
import { newTestPubSubService } from "../../utils/services/TestServices.specs.utils";

const messageId = "9dsa-9dsa-9dsa-9dsa-9dsa";
const createdTopicName = "I have been created";

jest.mock("@google-cloud/pubsub");

/**
 * This test is to check if the pubsub service is returning the right data
 */
describe("pubsub.service", () => {
  let pubsubService: PubSubService;

  beforeEach(() => {
    jest.clearAllMocks();
    pubsubService = newTestPubSubService();

    resetPubsubMocks({
      messageId,
      createdTopicName,
      topicNamesType: TopicNamesType.TRIP_PROCESSING,
    });
    fakeCacheMockGet.mockImplementationOnce(() => {
      return new Promise((resolve) => resolve([{ name: TopicNamesType.TRIP_PROCESSING }]));
    });
  });

  describe("createTopic", () => {
    it("should create a topic and return it", async () => {
      const createdTopic = await pubsubService.createTopic(TopicNamesType.TRIP_PROCESSING);

      expect(mockCreateTopic).toHaveBeenCalledWith(TopicNamesType.TRIP_PROCESSING);
      expect(createdTopic.name).toEqual(createdTopicName);
    });
  });

  describe("getTopic", () => {
    it("should create a topic if it is not found", async () => {
      fakeCacheMockGet.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve([{ name: TopicNamesType.MESSAGE_PROCESSING }]));
      });
      const createdTopic = await pubsubService.getTopic(TopicNamesType.TRIP_PROCESSING);

      expect(mockCreateTopic).toHaveBeenCalledWith(TopicNamesType.TRIP_PROCESSING);
      expect(createdTopic.name).toEqual(createdTopicName);
    });
  });

  describe("publishMessage", () => {
    it("should publish the message", async () => {
      const message = "my message";
      const response = await pubsubService.publishMessage(TopicNamesType.TRIP_PROCESSING, message);

      expect(mockCreateTopic).toHaveBeenCalledWith(TopicNamesType.TRIP_PROCESSING);
      expect(mockPublishMessage).toHaveBeenCalledTimes(1);
      expect(response).toEqual(messageId);
    });
  });

  describe("publishMessageForTripProcessing", () => {
    it("should build the right object and publish the message", async () => {
      const response = await pubsubService.publishMessageForTripProcessing(expectedTxQueueEntry);

      expect(mockCreateTopic).toHaveBeenCalledWith(TopicNamesType.TRIP_PROCESSING);
      expect(mockPublishMessage).toHaveBeenCalledTimes(1);
      expect(response).toEqual(messageId);
    });
  });

  describe("publishMessageForMessageProcessingParams", () => {
    it("should build the right object and publish the message", async () => {
      const response = await pubsubService.publishMessageForMessageProcessingParams(expectedMessageQueueEntry);

      expect(mockCreateTopic).toHaveBeenCalledWith(TopicNamesType.MESSAGE_PROCESSING);
      expect(mockPublishMessage).toHaveBeenCalledTimes(1);
      expect(response).toEqual(messageId);
    });
  });

  describe("publishMessageForCaptureProcessing", () => {
    it("should build the right object and publish the message", async () => {
      const response = await pubsubService.publishMessageForCaptureProcessing(expectedCaptureQueueEntry);

      expect(mockCreateTopic).toHaveBeenCalledWith(TopicNamesType.CAPTURE_PROCESSING);
      expect(mockPublishMessage).toHaveBeenCalledTimes(1);
      expect(response).toEqual(messageId);
    });
  });

  describe("publishMessageForVoidProcessing", () => {
    it("should build the right object and publish the message", async () => {
      const response = await pubsubService.publishMessageForVoidProcessing(expectedVoidQueueEntry);

      expect(mockCreateTopic).toHaveBeenCalledWith(TopicNamesType.VOID_PROCESSING);
      expect(mockPublishMessage).toHaveBeenCalledTimes(1);
      expect(response).toEqual(messageId);
    });
  });

  describe("publishMessageForCopyTrioToDriverProcessing", () => {
    it("should build the right object and publish the message", async () => {
      const response = await pubsubService.publishMessageForCopyTripToDriverProcessing(
        expectedCopyTripToDriveQueueEntry,
      );

      expect(mockCreateTopic).toHaveBeenCalledWith(TopicNamesType.COPY_TRIP_TO_DRIVER_PROCESSING);
      expect(mockPublishMessage).toHaveBeenCalledTimes(1);
      expect(response).toEqual(messageId);
    });
  });
});

import {
  DASH_FEE_RECALCULATION,
  txAdjustmentAmountSchema,
  txAdjustmentCustomAmountSchema,
  txAdjustmentCreateSchema,
  txAdjustmentDiscountSchema,
  txAdjustmentFullRefundSchema,
} from "../../../../nestJs/modules/transaction/dto/txAdjustment.dto";

describe("txAdjustmentCreateSchema (alternatives)", () => {
  it("should validate a valid ADJUSTED_AMOUNT adjustment", () => {
    const validInput = {
      type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
      reason: "Test adjustment",
      amount: 100,
    };

    const { error } = txAdjustmentCreateSchema.validate(validInput);
    expect(error).toBeUndefined();
  });

  it("should invalidate an ADJUSTED_AMOUNT adjustment with missing fields", () => {
    const invalidInput = {
      type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
      reason: "Test adjustment",
    };

    const { error } = txAdjustmentCreateSchema.validate(invalidInput);
    expect(error).toBeDefined();
    expect(error?.details[0].message).toBe('"value" does not match any of the allowed types');
  });

  it("should validate a valid CUSTOM_AMOUNT adjustment", () => {
    const validInput = {
      type: DASH_FEE_RECALCULATION.CUSTOM_AMOUNT,
      reason: "Test adjustment",
      payoutAmount: 100,
      dashFee: 10,
    };

    const { error } = txAdjustmentCreateSchema.validate(validInput);
    expect(error).toBeUndefined();
  });

  it("should not allow extra fields", () => {
    const invalidInput = {
      type: DASH_FEE_RECALCULATION.FULL_REFUND,
      reason: "Test adjustment",
      extraField: "some value",
    };
    const { error } = txAdjustmentCreateSchema.validate(invalidInput);
    expect(error).toBeDefined();
    expect(error?.details[0].message).toBe('"value" does not match any of the allowed types');
  });

  it("should validate a valid DISCOUNTED_AMOUNT adjustment", () => {
    const validInput = {
      type: DASH_FEE_RECALCULATION.DISCOUNTED_AMOUNT,
      reason: "Test adjustment",
      discount: -10,
    };
    const { error } = txAdjustmentCreateSchema.validate(validInput);
    expect(error).toBeUndefined();
  });

  it("should validate a valid FULL_REFUND adjustment", () => {
    const validInput = {
      type: DASH_FEE_RECALCULATION.FULL_REFUND,
      reason: "Test adjustment",
    };
    const { error } = txAdjustmentCreateSchema.validate(validInput);
    expect(error).toBeUndefined();
  });

  it("should invalidate a CUSTOM_AMOUNT adjustment with missing fields", () => {
    const invalidInput = {
      type: DASH_FEE_RECALCULATION.CUSTOM_AMOUNT,
      reason: "Test adjustment",
      dashFee: 10,
    };
    const { error } = txAdjustmentCreateSchema.validate(invalidInput);
    expect(error).toBeDefined();
    expect(error?.details[0].message).toContain('"value" does not match any of the allowed types');
  });

  it("should invalidate a DISCOUNTED_AMOUNT adjustment with missing fields", () => {
    const invalidInput = {
      type: DASH_FEE_RECALCULATION.DISCOUNTED_AMOUNT,
      reason: "Test adjustment",
    };
    const { error } = txAdjustmentCreateSchema.validate(invalidInput);
    expect(error).toBeDefined();
    expect(error?.details[0].message).toContain('"value" does not match any of the allowed types');
  });

  it("should invalidate an object with an invalid type", () => {
    const invalidInput = {
      type: "INVALID_TYPE",
      reason: "Test adjustment",
      amount: 100,
    };
    const { error } = txAdjustmentCreateSchema.validate(invalidInput);
    expect(error).toBeDefined();
  });
});

describe("txAdjustmentAmountSchema", () => {
  it("should validate a valid schema", () => {
    const validInput = {
      type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
      reason: "Test",
      amount: 10.1,
    };
    const { error } = txAdjustmentAmountSchema.validate(validInput);
    expect(error).toBeUndefined();
  });

  it("should invalidate with a missing amount", () => {
    const invalidInput = {
      type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
      reason: "Test",
    };
    const { error } = txAdjustmentAmountSchema.validate(invalidInput);
    expect(error).toBeDefined();
    expect(error?.details[0].message).toBe('"amount" is required');
  });

  it("should invalidate with wrong type for amount", () => {
    const invalidInput = {
      type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
      reason: "Test",
      amount: "10",
    };
    const { error } = txAdjustmentAmountSchema.validate(invalidInput);
    expect(error).toBeDefined();
    expect(error?.details[0].message).toBe('"amount" must be a number');
  });

  it("should invalidate with wrong precision for amount", () => {
    const invalidInput = {
      type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
      reason: "Test",
      amount: 10.12,
    };
    const { error } = txAdjustmentAmountSchema.validate(invalidInput);
    expect(error).toBeDefined();
    expect(error?.details[0].message).toBe('"amount" must have no more than 1 decimal places');
  });
});

describe("txAdjustmentCustomAmountSchema", () => {
  it("should validate a valid schema", () => {
    const validInput = {
      type: DASH_FEE_RECALCULATION.CUSTOM_AMOUNT,
      reason: "Test",
      dashFee: 5,
      payoutAmount: 10,
    };
    const { error } = txAdjustmentCustomAmountSchema.validate(validInput);
    expect(error).toBeUndefined();
  });

  it("should invalidate with a missing dashFee", () => {
    const invalidInput = {
      type: DASH_FEE_RECALCULATION.CUSTOM_AMOUNT,
      reason: "Test",
      payoutAmount: 10,
    };
    const { error } = txAdjustmentCustomAmountSchema.validate(invalidInput);
    expect(error).toBeDefined();
    expect(error?.details[0].message).toBe('"dashFee" is required');
  });

  it("should invalidate with a missing payoutAmount", () => {
    const invalidInput = {
      type: DASH_FEE_RECALCULATION.CUSTOM_AMOUNT,
      reason: "Test",
      dashFee: 5,
    };
    const { error } = txAdjustmentCustomAmountSchema.validate(invalidInput);
    expect(error).toBeDefined();
    expect(error?.details[0].message).toBe('"payoutAmount" is required');
  });
});

describe("txAdjustmentDiscountSchema", () => {
  it("should validate a valid schema", () => {
    const validInput = {
      type: DASH_FEE_RECALCULATION.DISCOUNTED_AMOUNT,
      reason: "Test",
      discount: -10.5,
    };
    const { error } = txAdjustmentDiscountSchema.validate(validInput);
    expect(error).toBeUndefined();
  });

  it("should invalidate with a missing discount", () => {
    const invalidInput = {
      type: DASH_FEE_RECALCULATION.DISCOUNTED_AMOUNT,
      reason: "Test",
    };
    const { error } = txAdjustmentDiscountSchema.validate(invalidInput);
    expect(error).toBeDefined();
    expect(error?.details[0].message).toBe('"discount" is required');
  });
});

describe("txAdjustmentFullRefundSchema", () => {
  it("should validate a valid schema", () => {
    const validInput = {
      type: DASH_FEE_RECALCULATION.FULL_REFUND,
      reason: "Test",
    };
    const { error } = txAdjustmentFullRefundSchema.validate(validInput);
    expect(error).toBeUndefined();
  });

  it("should invalidate with an extra field", () => {
    const invalidInput = {
      type: DASH_FEE_RECALCULATION.FULL_REFUND,
      reason: "Test",
      amount: 100,
    };
    const { error } = txAdjustmentFullRefundSchema.validate(invalidInput);
    expect(error).toBeDefined();
    expect(error?.details[0].message).toBe('"amount" is not allowed');
  });
});

import { In } from "typeorm";

import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { TxEventRepository } from "@nest/modules/database/repositories/txEvent.repository";

import { AppDatabaseService } from "../../../nestJs/modules/appDatabase/appDatabase.service";
import { BankService } from "../../../nestJs/modules/bank/bank.service";
import { BankNames } from "../../../nestJs/modules/bank/dto/bankName.dto";
import {
  BankResponseLineItemStatus,
  PayoutBankFileRow,
  bankFileSchemas,
} from "../../../nestJs/modules/bank/dto/payoutBankFile.dto";
import Merchant from "../../../nestJs/modules/database/entities/merchant.entity";
import PaymentTx from "../../../nestJs/modules/database/entities/paymentTx.entity";
import Payout from "../../../nestJs/modules/database/entities/payout.entity";
import Tx from "../../../nestJs/modules/database/entities/tx.entity";
import TxTag from "../../../nestJs/modules/database/entities/txTag.entity";
import { DiscountRepository } from "../../../nestJs/modules/database/repositories/discount.repository";
import { PaymentInstrumentRepository } from "../../../nestJs/modules/database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../../../nestJs/modules/database/repositories/paymentTx.repository";
import { PayoutRepository } from "../../../nestJs/modules/database/repositories/payout.repository";
import { TxRepository } from "../../../nestJs/modules/database/repositories/tx.repository";
import { TxTagRepository } from "../../../nestJs/modules/database/repositories/txTag.repository";
import { UserRepository } from "../../../nestJs/modules/database/repositories/user.repository";
import { ChannelTypes } from "../../../nestJs/modules/message/dto/channelType.dto";
import { NotificationType } from "../../../nestJs/modules/message/messageFactory/modules/notification/notification.dto";
import { PaymentInformationStatus } from "../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../nestJs/modules/payment/dto/paymentInformationType.dto";
import { PaymentService } from "../../../nestJs/modules/payment/payment.service";
import { PubSubService } from "../../../nestJs/modules/pubsub/pubsub.service";
import { Secret } from "../../../nestJs/modules/secrets/types";
import { StorageService } from "../../../nestJs/modules/storage/storage.service";
import {
  CompareType,
  MetadataFilterKey,
  TxSortableType,
} from "../../../nestJs/modules/transaction/dto/transactionListing.dto";
import {
  DASH_FEE_RECALCULATION,
  TxAdjustmentCreateDto,
} from "../../../nestJs/modules/transaction/dto/txAdjustment.dto";
import { TxPayoutStatus } from "../../../nestJs/modules/transaction/dto/txPayoutStatus.dto";
import { TxTagType } from "../../../nestJs/modules/transaction/dto/txTagType.dto";
import { TransactionService } from "../../../nestJs/modules/transaction/transaction.service";
import { TransactionFactoryService } from "../../../nestJs/modules/transaction/transactionFactory/transactionFactory.service";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import { UtilsService } from "../../../nestJs/modules/utils/utils.service";
import { LanguageOption } from "../../../nestJs/modules/validation/dto/language.dto";
import decodedIdTokenMock from "../../mockData/firebase/decodedIdToken.mock";
import expects from "../../utils/expects.specs.utils";
import {
  fakePaymentTx,
  fakePaymentTx_Auth_Fail,
  fakePaymentTx_Auth_Fail_1,
  fakePaymentTx_Auth_SUCCESS_1,
  fakePaymentTx_Auth_SUCCESS_2,
  fakePaymentTx_SALE_SUCCESS_1,
  fakePaymentTx_SALE_SUCCESS_2,
  fakePaymentTx_VOID_FAIL_1,
  fakePaymentTx_VOID_SALE_SUCCESS_1,
  fakePaymentTx_VOID_SUCCESS_1,
} from "../../utils/fakeData/fakeDataPaymentTx.specs.utils";
import { expectedTxQueueEntry } from "../../utils/fakeData/fakeDataPubsub.specs.utils";
import { expectedResultSavedTx } from "../../utils/fakeData/fakeDataTx.specs.utils";
import FakeLoggerService from "../../utils/fakeLogger.service.specs";
import { unlinkSyncMock } from "../../utils/fs.specs.utils";
import MockAppDatabaseService from "../../utils/services/FakeAppDatabaseService.specs.utils";
import FakeBankService, {
  bankFileTransformerMock,
  generatePayoutFileContentMock,
} from "../../utils/services/FakeBankService.specs.utils";
import { sendNotificationEmailMock } from "../../utils/services/FakeEmailService.specs.utils";
import MockPaymentService, {
  mockFindAllSalesInProcessingStatus,
  mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid,
} from "../../utils/services/FakePaymentService.specs.utils";
import FakePubSubService, {
  mockPublishMessageForCaptureProcessing,
  mockPublishMessageForCopyTripToDriverProcessing,
  mockPublishMessageForMessageProcessingParams,
  mockPublishMessageForVoidProcessing,
} from "../../utils/services/FakePubSubService.specs.utils";
import FakeRepository, {
  mockAddTagsToTx,
  mockCreateTxAdjustment,
  mockFind,
  mockFindOne,
  mockFindOneBy,
  mockSave,
  mockUpdate,
  mockUpsertTxAndUpdatePaymentTx,
} from "../../utils/services/FakeRepository.specs.utils";
import { getSecretMock } from "../../utils/services/FakeSecretsService.specs.utils";
import FakeStorageService, {
  getXlsxFileContentFromBucketFileMock,
  savePayoutFileMock,
} from "../../utils/services/FakeStorageService.specs.utils";
import FakeTransactionFactoryService, {
  mockGetTxsToPayout,
  mockIsPayWithDash,
  mockPostAdjustmentProcess,
  mockPostCashPaymentProcess,
  mockPostPayoutProcess,
  mockPostTxEndProcess,
} from "../../utils/services/FakeTransactionFactoryService.specs.utils";
import {
  newTestEmailService,
  newTestMessageTeamsService,
  newTestSecretService,
  newTestTransactionEventService,
} from "../../utils/services/TestServices.specs.utils";

const foundMerchant = new Merchant();
foundMerchant.id = "1234";
foundMerchant.phoneNumber = "+85298765432";
foundMerchant.name = "Tony";
foundMerchant.showCashTrip = true;

describe("transaction.service", () => {
  let service: TransactionService;
  let expectedSavedTx: any;
  const txRepository = new FakeRepository() as unknown as TxRepository;
  const utilsService = new UtilsService();

  beforeEach(async () => {
    jest.clearAllMocks();

    mockCreateTxAdjustment.mockImplementation(() => ({ adjustmentTx: new Tx(), parentTx: new Tx() }));

    service = new TransactionService(
      new MockAppDatabaseService() as unknown as AppDatabaseService,
      new FakeRepository() as unknown as MerchantRepository,
      new MockPaymentService() as unknown as PaymentService,
      txRepository,
      new FakeRepository() as unknown as TxTagRepository,
      new FakeRepository() as unknown as PayoutRepository,
      new FakeBankService() as unknown as BankService,
      new FakeStorageService() as unknown as StorageService,
      new FakePubSubService() as unknown as PubSubService,
      new FakeTransactionFactoryService() as unknown as TransactionFactoryService,
      utilsService,
      FakeLoggerService,
      new FakeRepository() as unknown as PaymentInstrumentRepository,
      new FakeRepository() as unknown as PaymentTxRepository,
      newTestMessageTeamsService(),
      newTestTransactionEventService(),
      new FakeRepository() as unknown as UserRepository,
      newTestSecretService(),
      newTestEmailService(),
      new FakeRepository() as unknown as DiscountRepository,
      new FakeRepository() as unknown as TxEventRepository,
    );

    mockFindOne.mockImplementation(() => {
      return new Promise((resolve) => resolve(foundMerchant));
    });

    mockSave.mockImplementation(() => {
      return new Promise((resolve) => resolve("saved"));
    });
    expectedSavedTx = expectedResultSavedTx; //[{ ...expectedResultSavedTx }, [{ ...expectedResultSavedTx.paymentTx[0] }]];
    // expectedSavedTx[0].paymentTx = undefined;
    // expectedSavedTx[1][0].tx = { id: expectedResultSavedTx.id };

    mockPublishMessageForCaptureProcessing.mockReset();
    mockPublishMessageForVoidProcessing.mockReset();
    mockPublishMessageForCopyTripToDriverProcessing.mockReset();
    mockPostCashPaymentProcess.mockReset();
    mockPostTxEndProcess.mockReset();
    mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid.mockReset();
    mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid.mockReturnValue([]);
    mockFindAllSalesInProcessingStatus.mockReset();
    mockFindAllSalesInProcessingStatus.mockReturnValue([]);
  });

  describe("createTxAdjustment", () => {
    it("should create a new tx with the adjustment amount", async () => {
      const tx = new Tx();
      tx.id = "1234";
      tx.merchant = foundMerchant;

      const txAdjustmentRequest: TxAdjustmentCreateDto = {
        type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
        reason: "because",
        amount: 2,
      };

      const adjustmentTx = new Tx();
      const parentTx = new Tx();
      mockCreateTxAdjustment.mockResolvedValue({ adjustmentTx, parentTx });
      mockPostAdjustmentProcess.mockResolvedValue({ ok: "OK!" } as any);

      const newTx = await service.createTxAdjustment(tx.id, txAdjustmentRequest, decodedIdTokenMock);

      expect(newTx).toEqual({ ok: "OK!" });

      expect(mockCreateTxAdjustment).toHaveBeenCalledWith(tx.id, 0, "because", decodedIdTokenMock);

      expect(mockPostAdjustmentProcess).toHaveBeenCalledWith(adjustmentTx);
    });

    it("should throw an error for unsupported adjustment types", async () => {
      const tx = new Tx();
      tx.id = "1234";
      const txAdjustmentRequest: any = {
        type: "UNSUPPORTED_TYPE",
        reason: "because",
        amount: 2,
      };

      const adjustmentTx = new Tx();
      const parentTx = new Tx();
      mockCreateTxAdjustment.mockResolvedValue({ adjustmentTx, parentTx });

      await expect(service.createTxAdjustment(tx.id, txAdjustmentRequest, decodedIdTokenMock)).rejects.toThrow(
        errorBuilder.transaction.adjustmentNotSupported(tx.id, txAdjustmentRequest),
      );
    });
  });

  describe("adjustTxWithAmount", () => {
    it("should adjust with fee based on original amount", () => {
      const parentTx = new Tx();
      parentTx.total = 100;
      parentTx.metadata = {
        dashFeeRate: 0.1,
        dashFeeConstant: 1,
      } as any;
      const adjustmentTx = new Tx();
      const result = service.adjustTxWithAmount(
        {
          amount: 10,
          type: DASH_FEE_RECALCULATION.ORIGINAL_AMOUNT,
          reason: "test",
        },
        adjustmentTx,
        parentTx,
      );
      expect(result.dashFee).toBe(0);
      expect(result.total).toBe(10);
      expect(result.payoutAmount).toBe(10);
      expect(result.dashFee! + result.payoutAmount!).toBe(result.total);
    });

    it("should adjust with fee based on adjusted amount", () => {
      const parentTx = new Tx();
      parentTx.payoutAmount = 90;
      parentTx.dashFee = 10;
      parentTx.metadata = {
        billing: {
          dashFeeSettings: {
            dashFeeRate: 0.1,
            dashFeeConstant: 1,
          },
        },
      } as any;
      const adjustmentTx = new Tx();
      const roundAwayFromZeroSpy = jest
        .spyOn(utilsService.number, "roundAwayFromZero")
        .mockImplementation((val) => val);
      const calculateDashFeeSpy = jest.spyOn(utilsService.number, "calculateDashFee").mockReturnValue(11);

      const result = service.adjustTxWithAmount(
        {
          amount: 10,
          type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT,
          reason: "test",
        },
        adjustmentTx,
        parentTx,
      );

      expect(calculateDashFeeSpy).toHaveBeenCalledWith(
        {
          tripTotal: 100,
          dashFeeRate: 0.1,
          dashFeeConstant: 1,
        },
        expect.any(Function),
      );
      expect(result.dashFee).toBe(1);
      expect(result.payoutAmount).toBe(10);
      expect(result.total).toBe(11);
      expect(result.dashFee! + result.payoutAmount!).toBe(result.total);

      roundAwayFromZeroSpy.mockRestore();
      calculateDashFeeSpy.mockRestore();
    });
  });

  describe("adjustTxFromCustomAmount", () => {
    it("should adjust the transaction based on custom values", () => {
      const adjustmentTx = new Tx();
      const roundAwayFromZeroSpy = jest
        .spyOn(utilsService.number, "roundAwayFromZero")
        .mockImplementation((val) => val);

      const result = service.adjustTxFromCustomAmount(
        { dashFee: 5, payoutAmount: 10, type: DASH_FEE_RECALCULATION.CUSTOM_AMOUNT, reason: "test" },
        adjustmentTx,
      );

      expect(result.dashFee).toBe(5);
      expect(result.payoutAmount).toBe(10);
      expect(result.total).toBe(15);
      expect(result.dashFee! + result.payoutAmount!).toBe(result.total);
      expect(roundAwayFromZeroSpy).toHaveBeenCalledWith(15);
      roundAwayFromZeroSpy.mockRestore();
    });
  });

  describe("adjustTxFromFullRefund", () => {
    it("should create a full refund adjustment", () => {
      const parentTx = new Tx();
      parentTx.payoutAmount = 90;
      parentTx.dashFee = 10;
      const adjustmentTx = new Tx();
      const roundAwayFromZeroSpy = jest
        .spyOn(utilsService.number, "roundAwayFromZero")
        .mockImplementation((val) => val);

      const result = service.adjustTxFromFullRefund(adjustmentTx, parentTx);

      expect(result.dashFee).toBe(-10);
      expect(result.payoutAmount).toBe(-90);
      expect(result.total).toBe(-100);
      expect(result.dashFee! + result.payoutAmount!).toBe(result.total);
      expect(roundAwayFromZeroSpy).toHaveBeenCalledWith(-100);
      roundAwayFromZeroSpy.mockRestore();
    });
  });

  describe("adjustTxWithDiscount", () => {
    it("should adjust the transaction with a discount", () => {
      const parentTx = new Tx();
      parentTx.total = 100;
      parentTx.payoutAmount = 90;
      parentTx.dashFee = 10;
      parentTx.metadata = {
        billing: {
          dashFeeSettings: {
            dashFeeRate: 0.1,
            dashFeeConstant: 0,
          },
        },
      } as any;
      const adjustmentTx = new Tx();
      const roundAwayFromZeroSpy = jest
        .spyOn(utilsService.number, "roundAwayFromZero")
        .mockImplementation((val) => val);
      const calculateDashFeeSpy = jest
        .spyOn(utilsService.number, "calculateDashFee")
        .mockImplementation((params) => params.tripTotal * 0.1);

      const result = service.adjustTxWithDiscount(
        { discount: -10, type: DASH_FEE_RECALCULATION.DISCOUNTED_AMOUNT, reason: "test" },
        adjustmentTx,
      );

      expect(result.dashFee).toBe(0);
      expect(result.payoutAmount).toBe(0);
      expect(result.total).toBe(-10);
      expect(result.metadata).toBeUndefined();

      roundAwayFromZeroSpy.mockRestore();
      calculateDashFeeSpy.mockRestore();
    });
  });

  describe("processTransaction", () => {
    it("should update tx to sql, and will trigger capture, not trigger copyToDrive and void", async () => {
      mockUpsertTxAndUpdatePaymentTx.mockResolvedValueOnce(expectedSavedTx);
      mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid.mockReturnValueOnce([]);
      await service.processTransaction(expectedTxQueueEntry, "1");
      expect(mockFindOne).toHaveBeenCalledWith({ where: { phoneNumber: "+1231244", platformMerchantType: "DASH" } });
      expect(mockPublishMessageForCaptureProcessing).toHaveBeenCalled();
      expect(mockPostCashPaymentProcess).not.toHaveBeenCalled();
      expect(mockPublishMessageForVoidProcessing).not.toHaveBeenCalled();
    });

    it("should update tx to sql, and will trigger copy trip to driver cause mockIsPayWithDash return false, not trigger capture and void", async () => {
      mockUpsertTxAndUpdatePaymentTx.mockResolvedValueOnce(expectedSavedTx);
      mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid.mockReturnValueOnce([]);
      mockIsPayWithDash.mockImplementationOnce(() => {
        return false;
      });
      await service.processTransaction(expectedTxQueueEntry, "1");

      expect(mockFindOne).toHaveBeenCalledWith({ where: { phoneNumber: "+1231244", platformMerchantType: "DASH" } });
      expect(mockPublishMessageForCaptureProcessing).not.toHaveBeenCalled();
      expect(mockPostCashPaymentProcess).toHaveBeenCalled();
      expect(mockPublishMessageForVoidProcessing).not.toHaveBeenCalled();
    });

    it("should update tx to sql, and will not triger capture when txProcessed is false, and trigger void", async () => {
      mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid.mockReturnValue([fakePaymentTx()]);

      mockUpsertTxAndUpdatePaymentTx.mockResolvedValueOnce(expectedSavedTx);

      await service.processTransaction({ ...expectedTxQueueEntry, txProcessed: false, doVoid: true }, "1");

      expect(mockFindOne).toHaveBeenCalledWith({ where: { phoneNumber: "+1231244", platformMerchantType: "DASH" } });
      expect(mockPublishMessageForCaptureProcessing).not.toHaveBeenCalled();
      expect(mockPostCashPaymentProcess).not.toHaveBeenCalled();
      expect(mockPublishMessageForVoidProcessing).toHaveBeenCalled();
    });

    it("Should throw an error when the merchant is not found", async () => {
      const newMerchant = {
        name: "Prasenjit",
        phoneNumber: "+1231244",
      };
      mockFindOne.mockImplementationOnce(() => new Promise((resolve) => resolve(null)));

      await expect(
        service.processTransaction(
          {
            ...expectedTxQueueEntry,
            merchant: newMerchant,
          },
          "1",
        ),
      ).rejects.toThrow(errorBuilder.transaction.merchantNotFound("+1231244"));
      expect(mockSave).not.toHaveBeenCalled();
    });

    it("should tag the tx when there is a open auth and the trip is not a dash trip anymore", async () => {
      const paymentTx = fakePaymentTx();
      paymentTx.type = PaymentInformationType.AUTH;
      paymentTx.status = PaymentInformationStatus.SUCCESS;
      mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid.mockReturnValue([paymentTx]);

      mockUpsertTxAndUpdatePaymentTx.mockResolvedValueOnce(expectedSavedTx);

      mockIsPayWithDash.mockImplementationOnce(() => {
        return false;
      });
      await service.processTransaction({ ...expectedTxQueueEntry, txProcessed: true, doVoid: false }, "1");

      expect(mockFindOne).toHaveBeenCalledWith({ where: { phoneNumber: "+1231244", platformMerchantType: "DASH" } });
      expect(mockPublishMessageForCaptureProcessing).not.toHaveBeenCalled();
      expect(mockPublishMessageForVoidProcessing).not.toHaveBeenCalled();

      expect(mockAddTagsToTx).toHaveBeenCalledWith(expectedSavedTx, [
        {
          createdBy: "SYSTEM",
          note: "At least one auth is not handled, please capture or void it",
          tag: TxTagType.OPEN_AUTH,
          tx: { id: expect.stringMatching(expects.uuid) },
        },
      ]);
    });

    it("should tag the tx when there is a open sale and the trip is not a dash trip anymore", async () => {
      const paymentTx = fakePaymentTx();
      paymentTx.type = PaymentInformationType.SALE;
      paymentTx.status = PaymentInformationStatus.SUCCESS;
      mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid.mockReturnValue([paymentTx]);

      mockUpsertTxAndUpdatePaymentTx.mockResolvedValueOnce(expectedSavedTx);

      mockIsPayWithDash.mockImplementationOnce(() => {
        return false;
      });
      await service.processTransaction({ ...expectedTxQueueEntry, txProcessed: true, doVoid: false }, "1");

      expect(mockFindOne).toHaveBeenCalledWith({ where: { phoneNumber: "+1231244", platformMerchantType: "DASH" } });
      expect(mockPublishMessageForCaptureProcessing).not.toHaveBeenCalled();
      expect(mockPublishMessageForVoidProcessing).not.toHaveBeenCalled();

      expect(mockAddTagsToTx).toHaveBeenCalledWith(expectedSavedTx, [
        {
          createdBy: "SYSTEM",
          note: "At least one sale is not handled, please check it",
          tag: TxTagType.OPEN_SALE,
          tx: { id: expect.stringMatching(expects.uuid) },
        },
      ]);
    });

    it("should tag the tx when there is a processing sale", async () => {
      mockUpsertTxAndUpdatePaymentTx.mockResolvedValueOnce(expectedSavedTx);
      mockFindAllSalesInProcessingStatus.mockReturnValue([fakePaymentTx()]);

      await service.processTransaction({ ...expectedTxQueueEntry, txProcessed: true, doVoid: false }, "1");

      expect(mockFindOne).toHaveBeenCalledWith({ where: { phoneNumber: "+1231244", platformMerchantType: "DASH" } });
      expect(mockPublishMessageForCaptureProcessing).toHaveBeenCalled();
      expect(mockPostCashPaymentProcess).not.toHaveBeenCalled();
      expect(mockPublishMessageForVoidProcessing).not.toHaveBeenCalled();

      expect(mockAddTagsToTx).toHaveBeenCalledWith(expectedSavedTx, [
        {
          createdBy: "SYSTEM",
          note: "At least one sale is in processing, please check its status",
          tag: TxTagType.OPEN_PROCESSING,
          tx: { id: expect.stringMatching(expects.uuid) },
        },
      ]);
    });
  });

  describe("addNewTagsToTx", () => {
    it("should throw an error when tx not found", async () => {
      mockFindOne.mockImplementationOnce(() => new Promise((resolve) => resolve(null)));

      expect(service.addNewTagsToTx("123", [], "SYSTEM")).rejects.toThrow(errorBuilder.transaction.notFound("123"));
    });

    it("should call addTagsToTx", async () => {
      mockFindOne.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve(new Tx()));
      });

      await service.addNewTagsToTx("123", [], "SYSTEM");

      expect(mockAddTagsToTx).toHaveBeenCalled();
    });
  });

  describe("createPayout", () => {
    it("should generate the file content, save the file and return the fileName", async () => {
      generatePayoutFileContentMock.mockImplementation(() => {
        return new Promise((resolve) =>
          resolve({ content: "file content", processed: ["tx1", "tx23"], unprocessed: ["tx2", "tx89"] }),
        );
      });

      const res = await service.createPayout(["tx1", "tx2", "tx23", "tx89"], BankNames.DBS, "Krillin");

      expect(res).toEqual({
        fileName: expect.stringMatching(/^payout-[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}.txt$/),
        processed: ["tx1", "tx23"],
        unprocessed: ["tx2", "tx89"],
        content: "file content",
      });
      expect(mockSave).toHaveBeenCalledWith({
        ...new Payout({
          originalRequest: { txIds: ["tx1", "tx2", "tx23", "tx89"], bankName: BankNames.DBS },
          requestedBy: "Krillin",
        }),
        id: expect.stringMatching(expects.uuid),
        batchFile: expect.stringMatching(/^payout-[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}.txt$/),
        requestedAt: expect.any(Date),
      });
      expect(savePayoutFileMock).toHaveBeenCalledWith(
        expect.stringMatching(/^payout-[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}.txt$/),
        "file content",
      );
    });

    it("should throw an error when there is no processed id", async () => {
      generatePayoutFileContentMock.mockImplementation(() => {
        return new Promise((resolve) =>
          resolve({ content: "file content", processed: [], unprocessed: ["tx1", "tx23", "tx2", "tx89"] }),
        );
      });

      expect(service.createPayout(["tx1", "tx2", "tx23", "tx89"], BankNames.DBS, "Krillin")).rejects.toThrowError(
        errorBuilder.global.unprocessableEntity("No transaction to process"),
      );
    });
  });

  describe("getTransactions", () => {
    const mockPaginate = jest.fn().mockResolvedValue([[], 0]);
    let originalPaginate: any;
    beforeAll(() => {
      originalPaginate = utilsService.api.paginateRaw;
      utilsService.api.paginateRaw = mockPaginate;
    });

    afterEach(() => {
      mockPaginate.mockClear();
    });

    afterAll(() => {
      utilsService.api.paginateRaw = originalPaginate;
    });

    it("should call paginate, with parameters case one", async () => {
      const listingQueryDto = {
        limit: 10,
        offset: 1,
        sort: TxSortableType.TOTAL,
        payoutStatus: TxPayoutStatus.PRERELEASED,
      };
      await service.getTransactions(listingQueryDto);

      expect(mockPaginate).toHaveBeenCalledWith(
        txRepository,
        "tx",
        "1=1 AND tx.payoutStatus = 'PRERELEASED'",
        { sort: "tx.total", orderBy: undefined, limit: 10, offset: 1 },
        [{ property: "tx.merchant", alias: "merchant" }],
        [
          {
            alias: "TX_TAGS",
            condition: '"TX_TAGS"."txId" =  "tx"."id"',
            subQueryFactory: expect.any(Function),
          },
        ],
        [],
        ["tx.*", 'to_json("merchant") AS "merchant"', 'COALESCE("TX_TAGS"."tags", \'[]\') AS "tags"'],
        true,
      );
    });

    it("should call paginate, with parameters case two", async () => {
      const listingQueryDto = {
        limit: 25,
        offset: 2,
        sort: TxSortableType.TRIP_START,
        payoutStatus: TxPayoutStatus.PRERELEASED,
        tag: TxTagType.DISPUTE,
      };
      await service.getTransactions(listingQueryDto);

      expect(mockPaginate).toHaveBeenCalledWith(
        txRepository,
        "tx",
        `1=1 AND tx.payoutStatus = 'PRERELEASED' AND EXISTS (SELECT * FROM tx_tag WHERE tx_tag.tag = '${TxTagType.DISPUTE}' AND tx_tag."txId" = tx."id" AND tx_tag."removedAt" IS NULL)`,
        { sort: "tx.metadata->>'tripStart'", orderBy: undefined, limit: 25, offset: 2 },
        [{ property: "tx.merchant", alias: "merchant" }],
        [
          {
            alias: "TX_TAGS",
            condition: '"TX_TAGS"."txId" =  "tx"."id"',
            subQueryFactory: expect.any(Function),
          },
        ],
        [],
        ["tx.*", 'to_json("merchant") AS "merchant"', 'COALESCE("TX_TAGS"."tags", \'[]\') AS "tags"'],
        true,
      );
    });

    it("should call paginate, with parameters case three", async () => {
      const listingQueryDto = {
        limit: 15,
        offset: 3,
        compareTypes: [CompareType.LESS_THAN_OR_EQUAL_TO, CompareType.GREATER_THAN_OR_EQUAL_TO],
        metadataFilterKeys: [MetadataFilterKey.TRIP_START, MetadataFilterKey.FARE],
        metadataFilterValues: ["2023-01-01T01:01:01.001Z", 100],
      };
      await service.getTransactions(listingQueryDto);

      expect(mockPaginate).toHaveBeenCalledWith(
        txRepository,
        "tx",
        "1=1 AND to_timestamp(tx.metadata->>'tripStart', 'YYYY-MM-DD\"T\"HH24:MI:SS.MS\"Z\"') <= to_timestamp('2023-01-01T01:01:01.001Z', 'YYYY-MM-DD\"T\"HH24:MI:SS.MS\"Z\"') AND COALESCE(tx.metadata->>'fare', '0')::numeric >= 100",
        { sort: undefined, orderBy: undefined, limit: 15, offset: 3 },
        [{ property: "tx.merchant", alias: "merchant" }],
        [
          {
            alias: "TX_TAGS",
            condition: '"TX_TAGS"."txId" =  "tx"."id"',
            subQueryFactory: expect.any(Function),
          },
        ],
        [],
        ["tx.*", 'to_json("merchant") AS "merchant"', 'COALESCE("TX_TAGS"."tags", \'[]\') AS "tags"'],
        true,
      );
    });
  });

  describe("processPayoutFileFromBank", () => {
    let contentFile: PayoutBankFileRow[];
    let txs: Partial<Tx>[];
    let txIds: string[];
    const defaultMessage = {
      recipient: { id: "" },
      channel: ChannelTypes.NOTIFICATION,
      template: NotificationType.PAYOUT_PAID,
      metadata: {
        schemeVersion: "1.3",
        createdAt: expect.any(Date),
      },
      tranId: expect.any(String),
      language: LanguageOption.EN,
      params: {},
      messageId: expect.stringMatching(expects.uuid),
    };

    beforeEach(() => {
      jest.resetAllMocks();
      jest.clearAllMocks();

      contentFile = [
        {
          fileUploadDate: new Date(),
          paymentDate: new Date(),
          merchantId: "+***********",
          originalBatchFileName: "batch1.txt",
          amount: 123,
          status: BankResponseLineItemStatus.COMPLETED,
        },
        {
          fileUploadDate: new Date(),
          paymentDate: new Date(),
          merchantId: "+***********",
          originalBatchFileName: "batch1.txt",
          amount: 456,
          status: BankResponseLineItemStatus.CONFIRMED,
        },
        {
          fileUploadDate: new Date(),
          paymentDate: new Date(),
          merchantId: "+***********",
          originalBatchFileName: "batch1.txt",
          amount: 789,
          status: BankResponseLineItemStatus.REJECTED,
        },
      ];

      txs = [
        { id: "tx1", merchant: { phoneNumber: "+***********" } as Merchant, payoutStatus: TxPayoutStatus.PRERELEASED },
        { id: "tx2", merchant: { phoneNumber: "+***********" } as Merchant, payoutStatus: TxPayoutStatus.PRERELEASED },
        { id: "tx3", merchant: { phoneNumber: "+***********" } as Merchant, payoutStatus: TxPayoutStatus.PRERELEASED },
        { id: "tx4", merchant: { phoneNumber: "+***********" } as Merchant, payoutStatus: TxPayoutStatus.PRERELEASED },
      ];

      txIds = txs.map((tx) => tx.id ?? "");

      bankFileTransformerMock.mockImplementation(() => {
        return () => {};
      });

      getXlsxFileContentFromBucketFileMock.mockImplementation(() => {
        return new Promise((resolve) => resolve(contentFile));
      });

      mockGetTxsToPayout.mockImplementation(() => {
        return {
          completed: txs,
          failed: [],
          bankProcessing: [],
        };
      });

      mockFind.mockImplementationOnce(() => {
        const payout = new Payout();
        payout.batchFile = "batch1.txt";
        payout.id = "payoutId1";
        payout.originalRequest = { bankName: BankNames.DBS, txIds: txIds };
        return new Promise((resolve) => resolve([payout]));
      });

      mockFind.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve(txs));
      });
    });

    it("should update the txs and tag, and send message to pubsub to update driver/session when multiple files in payout response", async () => {
      contentFile = [
        {
          fileUploadDate: new Date(),
          paymentDate: new Date(),
          merchantId: "+***********",
          originalBatchFileName: "batch1.txt",
          amount: 123,
          status: BankResponseLineItemStatus.COMPLETED,
        },
        {
          fileUploadDate: new Date(),
          paymentDate: new Date(),
          merchantId: "+***********",
          originalBatchFileName: "batch1.txt",
          amount: 456,
          status: BankResponseLineItemStatus.CONFIRMED,
        },
        {
          fileUploadDate: new Date(),
          paymentDate: new Date(),
          merchantId: "+***********",
          originalBatchFileName: "batch1.txt",
          amount: 789,
          status: BankResponseLineItemStatus.REJECTED,
        },
        {
          fileUploadDate: new Date(),
          paymentDate: new Date(),
          merchantId: "+***********",
          originalBatchFileName: "batch2.txt",
          amount: 789,
          status: BankResponseLineItemStatus.COMPLETED,
        },
      ];
      const payout2Txs = [
        { id: "tx5", merchant: { phoneNumber: "+***********" } as Merchant, payoutStatus: TxPayoutStatus.PRERELEASED },
        { id: "tx6", merchant: { phoneNumber: "+***********" } as Merchant, payoutStatus: TxPayoutStatus.PRERELEASED },
        { id: "tx7", merchant: { phoneNumber: "+***********" } as Merchant, payoutStatus: TxPayoutStatus.PRERELEASED },
        { id: "tx8", merchant: { phoneNumber: "+***********" } as Merchant, payoutStatus: TxPayoutStatus.PRERELEASED },
      ];
      mockFind.mockReset();
      const payout1 = new Payout();
      payout1.batchFile = "batch1.txt";
      payout1.id = "payoutId1";
      payout1.originalRequest = { bankName: BankNames.DBS, txIds: txIds };
      const payout2 = new Payout();
      payout2.batchFile = "batch2.txt";
      payout2.id = "payoutId2";
      payout2.originalRequest = { bankName: BankNames.DBS, txIds: payout2Txs.map((tx) => tx.id) };
      const fakePayouts = [payout1, payout2];
      mockFind.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve(fakePayouts));
      });
      mockGetTxsToPayout.mockImplementation(() => {
        return {
          completed: [...txs, ...payout2Txs],
          failed: [],
          bankProcessing: [],
        };
      });

      const allIds = [...txIds, ...payout2Txs.map((tx) => tx.id ?? "")];

      mockFind.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve([...txs, ...payout2Txs]));
      });

      const result = await service.processPayoutFileFromBank(BankNames.DBS, "file.xlsx");

      expect(result).toEqual({
        completed: allIds,
        failed: [],
        bankProcessing: [],
      });
      expect(getXlsxFileContentFromBucketFileMock).toHaveBeenCalledWith(
        "fake-bucket-name",
        "file.xlsx",
        bankFileSchemas[BankNames.DBS],
        expect.any(Function),
      );
      expect(mockFind).toHaveBeenNthCalledWith(1, { where: { batchFile: In(["batch1.txt", "batch2.txt"]) } });
      expect(mockFind).toHaveBeenNthCalledWith(2, {
        where: { id: In(allIds) },
        relations: ["merchant", "payoutMerchant", "parentTx"],
      });
      expect(mockGetTxsToPayout).toHaveBeenCalledWith([...txs, ...payout2Txs], contentFile, fakePayouts);
      expect(mockUpdate).toHaveBeenCalledWith({ id: In(allIds) }, { payoutStatus: TxPayoutStatus.RELEASED });
      expect(mockSave).toHaveBeenCalledWith([]);
      expect(mockUpdate).toHaveBeenCalledWith({ tx: In(allIds) }, { removedAt: expect.any(Date) });
      expect(mockPublishMessageForCopyTripToDriverProcessing).toHaveBeenCalledWith({ txIds: allIds });
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenCalledTimes(8);
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenNthCalledWith(1, {
        ...defaultMessage,
        recipient: { id: "***********" },
      });
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenNthCalledWith(2, {
        ...defaultMessage,
        recipient: { id: "***********" },
      });
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenNthCalledWith(3, {
        ...defaultMessage,
        recipient: { id: "***********" },
      });
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenNthCalledWith(4, {
        ...defaultMessage,
        recipient: { id: "***********" },
      });
      expect(mockPostPayoutProcess).toHaveBeenCalledWith([
        txs[0],
        txs[1],
        txs[2],
        txs[3],
        payout2Txs[0],
        payout2Txs[1],
        payout2Txs[2],
        payout2Txs[3],
      ]);
    });

    it("should update the txs and tag, and send message to pubsub to update driver/session", async () => {
      const result = await service.processPayoutFileFromBank(BankNames.DBS, "file.xlsx");

      expect(result).toEqual({
        completed: txIds,
        failed: [],
        bankProcessing: [],
      });
      expect(getXlsxFileContentFromBucketFileMock).toHaveBeenCalledWith(
        "fake-bucket-name",
        "file.xlsx",
        bankFileSchemas[BankNames.DBS],
        expect.any(Function),
      );
      expect(mockFind).toHaveBeenNthCalledWith(1, { where: { batchFile: In(["batch1.txt"]) } });
      expect(mockFind).toHaveBeenNthCalledWith(2, {
        where: { id: In(txIds) },
        relations: ["merchant", "payoutMerchant", "parentTx"],
      });
      expect(mockGetTxsToPayout).toHaveBeenCalledWith(txs, contentFile, [
        {
          batchFile: "batch1.txt",
          id: "payoutId1",
          originalRequest: { bankName: "DBS", txIds: txIds },
          requestedAt: expect.any(Date),
          status: TxPayoutStatus.PRERELEASED,
        },
      ]);
      expect(mockUpdate).toHaveBeenCalledWith({ id: In(txIds) }, { payoutStatus: TxPayoutStatus.RELEASED });
      expect(mockSave).toHaveBeenCalledWith([]);
      expect(mockUpdate).toHaveBeenCalledWith({ tx: In(txIds) }, { removedAt: expect.any(Date) });
      expect(mockPublishMessageForCopyTripToDriverProcessing).toHaveBeenCalledWith({ txIds: txIds });
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenCalledTimes(4);
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenNthCalledWith(1, {
        ...defaultMessage,
        recipient: { id: "***********" },
      });
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenNthCalledWith(2, {
        ...defaultMessage,
        recipient: { id: "***********" },
      });
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenNthCalledWith(3, {
        ...defaultMessage,
        recipient: { id: "***********" },
      });
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenNthCalledWith(4, {
        ...defaultMessage,
        recipient: { id: "***********" },
      });
      expect(mockPostPayoutProcess).toHaveBeenCalledWith([txs[0], txs[1], txs[2], txs[3]]);
    });

    it("should throw an error when the file has no content", async () => {
      getXlsxFileContentFromBucketFileMock.mockImplementation(() => {
        return new Promise((resolve) => resolve(""));
      });

      await expect(service.processPayoutFileFromBank(BankNames.DBS, "file.xlsx")).rejects.toThrowError(
        errorBuilder.payout.noContentInBankFile("file.xlsx"),
      );

      expect(getXlsxFileContentFromBucketFileMock).toHaveBeenCalledWith(
        "fake-bucket-name",
        "file.xlsx",
        bankFileSchemas[BankNames.DBS],
        expect.any(Function),
      );

      expect(mockFind).not.toHaveBeenCalled();
      expect(mockGetTxsToPayout).not.toHaveBeenCalled();
      expect(mockUpdate).not.toHaveBeenCalled();
      expect(mockSave).not.toHaveBeenCalled();
      expect(mockPublishMessageForCopyTripToDriverProcessing).not.toHaveBeenCalled();
      expect(mockPublishMessageForMessageProcessingParams).not.toHaveBeenCalled();
      expect(mockPostPayoutProcess).not.toHaveBeenCalled();
    });

    it("should throw an error when no payout is found", async () => {
      mockFind.mockReset();
      mockFind.mockImplementation(() => {
        return new Promise((resolve) => resolve(null));
      });

      await expect(service.processPayoutFileFromBank(BankNames.DBS, "file.xlsx")).rejects.toThrowError(
        errorBuilder.payout.notFound("batch1.txt", { filePath: "file.xlsx" }),
      );

      expect(getXlsxFileContentFromBucketFileMock).toHaveBeenCalledWith(
        "fake-bucket-name",
        "file.xlsx",
        bankFileSchemas[BankNames.DBS],
        expect.any(Function),
      );
      expect(mockFind).toHaveBeenNthCalledWith(1, { where: { batchFile: In(["batch1.txt"]) } });
      expect(mockFind).toHaveBeenCalledTimes(1);

      expect(mockGetTxsToPayout).not.toHaveBeenCalled();
      expect(mockUpdate).not.toHaveBeenCalled();
      expect(mockSave).not.toHaveBeenCalled();
      expect(mockPublishMessageForCopyTripToDriverProcessing).not.toHaveBeenCalled();
      expect(mockPublishMessageForMessageProcessingParams).not.toHaveBeenCalled();
      expect(mockPostPayoutProcess).not.toHaveBeenCalled();
    });

    it("should throw an error when no transactions is found", async () => {
      mockFind.mockReset();
      mockFind.mockImplementationOnce(() => {
        const payout = new Payout();
        payout.batchFile = "batch1.txt";
        payout.id = "payoutId1";
        payout.originalRequest = { bankName: BankNames.DBS, txIds: txIds };
        return new Promise((resolve) => resolve([payout]));
      });
      mockFind.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve([]));
      });

      await expect(service.processPayoutFileFromBank(BankNames.DBS, "file.xlsx")).rejects.toThrowError(
        errorBuilder.transaction.notFound(txIds.join(", "), { filePath: "file.xlsx", payout: "payoutId1" }),
      );

      expect(getXlsxFileContentFromBucketFileMock).toHaveBeenCalledWith(
        "fake-bucket-name",
        "file.xlsx",
        bankFileSchemas[BankNames.DBS],
        expect.any(Function),
      );

      expect(mockFind).toHaveBeenNthCalledWith(1, { where: { batchFile: In(["batch1.txt"]) } });
      expect(mockFind).toHaveBeenNthCalledWith(2, {
        where: { id: In(txIds) },
        relations: ["merchant", "payoutMerchant", "parentTx"],
      });

      expect(mockGetTxsToPayout).not.toHaveBeenCalled();
      expect(mockUpdate).not.toHaveBeenCalled();
      expect(mockSave).not.toHaveBeenCalled();
      expect(mockPublishMessageForCopyTripToDriverProcessing).not.toHaveBeenCalled();
      expect(mockPublishMessageForMessageProcessingParams).not.toHaveBeenCalled();
      expect(mockPostPayoutProcess).not.toHaveBeenCalled();
    });

    it("should tag transactions when the merchant payment is rejected", async () => {
      mockGetTxsToPayout.mockImplementation(() => {
        return {
          completed: [txs[1], txs[2]],
          failed: [txs[0], txs[3]],
          bankProcessing: [],
        };
      });

      const result = await service.processPayoutFileFromBank(BankNames.DBS, "file.xlsx");

      expect(result).toEqual({
        completed: [txs[1].id, txs[2].id],
        failed: [txs[0].id, txs[3].id],
        bankProcessing: [],
      });
      expect(getXlsxFileContentFromBucketFileMock).toHaveBeenCalledWith(
        "fake-bucket-name",
        "file.xlsx",
        bankFileSchemas[BankNames.DBS],
        expect.any(Function),
      );
      expect(mockFind).toHaveBeenNthCalledWith(1, { where: { batchFile: In(["batch1.txt"]) } });
      expect(mockFind).toHaveBeenNthCalledWith(2, {
        where: { id: In(txIds) },
        relations: ["merchant", "payoutMerchant", "parentTx"],
      });
      expect(mockGetTxsToPayout).toHaveBeenCalledWith(txs, contentFile, [
        {
          batchFile: "batch1.txt",
          id: "payoutId1",
          originalRequest: { bankName: "DBS", txIds: txIds },
          requestedAt: expect.any(Date),
          status: TxPayoutStatus.PRERELEASED,
        },
      ]);
      expect(mockUpdate).toHaveBeenCalledWith({ id: In(["tx2", "tx3"]) }, { payoutStatus: TxPayoutStatus.RELEASED });
      expect(mockSave).toHaveBeenCalledWith(
        ["tx1", "tx4"].map((txId) => {
          return TxTag.createTxTag(TxTagType.PAYOUT_REJECTED, txId, "SYSTEM", "Payout rejected by bank");
        }),
      );
      expect(mockUpdate).toHaveBeenCalledWith({ tx: In(["tx2", "tx3"]) }, { removedAt: expect.any(Date) });
      expect(mockPublishMessageForCopyTripToDriverProcessing).toHaveBeenCalledWith({ txIds: ["tx2", "tx3"] });
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenCalledTimes(2);
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenNthCalledWith(1, {
        ...defaultMessage,
        recipient: { id: "***********" },
      });
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenNthCalledWith(2, {
        ...defaultMessage,
        recipient: { id: "***********" },
      });
      expect(mockPostPayoutProcess).toHaveBeenCalledWith([txs[1], txs[2]]);
    });

    it("should send fleet payout notification", async () => {
      const fleetPayoutMessage = {
        recipient: { id: "" },
        channel: ChannelTypes.NOTIFICATION,
        template: NotificationType.PAYOUT_PAID_TO_FLEET,
        metadata: {
          schemeVersion: "1.3",
          createdAt: expect.any(Date),
        },
        tranId: expect.any(String),
        language: LanguageOption.EN,
        params: {},
        messageId: expect.stringMatching(expects.uuid),
      };

      contentFile = [
        {
          fileUploadDate: new Date(),
          paymentDate: new Date(),
          merchantId: "+***********",
          originalBatchFileName: "batch1.txt",
          amount: 123,
          status: BankResponseLineItemStatus.COMPLETED,
        },
      ];
      const payoutMerchant = { phoneNumber: "+***********", email: "<EMAIL>" } as Merchant;
      const payoutTxs = [
        {
          id: "tx1",
          merchant: { phoneNumber: "+***********" } as Merchant,
          payoutMerchant: payoutMerchant,
          metadata: {
            licensePlate: "ABC123",
            tripStart: new Date(),
            tripEnd: new Date(),
            locationStartAddress: "start",
            locationEndAddress: "end",
            driver: {
              name: "Goku",
              id: "123",
            },
            billing: {
              fare: 123,
              extra: 0,
              dashTip: 0,
              tripTotal: 123,
              total: 123,
            },
          },
          payoutStatus: TxPayoutStatus.PRERELEASED,
        },
      ];
      mockFind.mockReset();
      mockFind.mockImplementationOnce(() => {
        const payout = new Payout();
        payout.batchFile = "batch1.txt";
        payout.id = "payoutId";
        payout.originalRequest = { bankName: BankNames.DBS, txIds: payoutTxs.map((tx) => tx.id) };
        return new Promise((resolve) => resolve([payout]));
      });
      mockGetTxsToPayout.mockImplementation(() => {
        return {
          completed: payoutTxs,
          failed: [],
          bankProcessing: [],
        };
      });
      mockFindOneBy.mockResolvedValueOnce(payoutMerchant);

      const allIds = payoutTxs.map((tx) => tx.id ?? "");

      mockFind.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve(payoutTxs));
      });

      getSecretMock.mockResolvedValueOnce("password");
      unlinkSyncMock.mockImplementation(() => {});

      const fakeFile = "file.xlsx";
      const result = await service.processPayoutFileFromBank(BankNames.DBS, fakeFile);

      expect(result).toEqual({
        completed: allIds,
        failed: [],
        bankProcessing: [],
      });
      expect(getXlsxFileContentFromBucketFileMock).toHaveBeenCalledWith(
        "fake-bucket-name",
        fakeFile,
        bankFileSchemas[BankNames.DBS],
        expect.any(Function),
      );
      expect(mockFind).toHaveBeenNthCalledWith(1, { where: { batchFile: In(["batch1.txt"]) } });
      expect(mockFind).toHaveBeenNthCalledWith(2, {
        where: { id: In(allIds) },
        relations: ["merchant", "payoutMerchant", "parentTx"],
      });
      expect(mockGetTxsToPayout).toHaveBeenCalledWith(payoutTxs, contentFile, [
        {
          batchFile: "batch1.txt",
          id: "payoutId",
          originalRequest: { bankName: "DBS", txIds: ["tx1"] },
          requestedAt: expect.any(Date),
          status: TxPayoutStatus.PRERELEASED,
        },
      ]);
      expect(mockUpdate).toHaveBeenCalledWith(
        { id: In(allIds) },
        { payoutStatus: TxPayoutStatus.RELEASED_TO_PAYOUT_MERCHANT },
      );
      expect(mockSave).toHaveBeenCalledWith([]);
      expect(mockUpdate).toHaveBeenCalledWith({ tx: In(allIds) }, { removedAt: expect.any(Date) });
      expect(mockPublishMessageForCopyTripToDriverProcessing).toHaveBeenCalledWith({ txIds: allIds });
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenCalled();
      expect(mockPublishMessageForMessageProcessingParams).toHaveBeenNthCalledWith(1, {
        ...fleetPayoutMessage,
        recipient: { id: "***********" },
      });
      expect(mockPostPayoutProcess).toHaveBeenCalledWith([payoutTxs[0]]);
      expect(savePayoutFileMock).toHaveBeenCalled();
      expect(getSecretMock).toHaveBeenCalledWith(Secret.PAYOUT_MERCHANT_RECON_PASSWORD);
      expect(sendNotificationEmailMock).toHaveBeenCalled();
    });
  });

  describe("convertPaymentTxFromArrayToTree", () => {
    it("shouldn't convert, when there is no void/capture type in paymentTx array", async () => {
      const paymentTx: PaymentTx[] = [fakePaymentTx_Auth_Fail, fakePaymentTx_Auth_SUCCESS_1, fakePaymentTx_Auth_Fail_1];
      const paymentTxExtendeds = service.convertPaymentTxFromArrayToTree(paymentTx);
      expect(paymentTxExtendeds.length).toEqual(3);
      expect(paymentTxExtendeds[0].canDoVoidOrCapture).toEqual(false);
      expect(paymentTxExtendeds[1].canDoVoidOrCapture).toEqual(true);
      expect(paymentTxExtendeds[2].canDoVoidOrCapture).toEqual(false);
    });

    it("should convert, when there are void/capture type in paymentTx array", async () => {
      const paymentTx: PaymentTx[] = [
        fakePaymentTx_Auth_SUCCESS_1,
        fakePaymentTx_VOID_SUCCESS_1,
        fakePaymentTx_Auth_SUCCESS_2,
      ];
      const paymentTxExtendeds = service.convertPaymentTxFromArrayToTree(paymentTx);
      expect(paymentTxExtendeds.length).toEqual(2);
      expect(paymentTxExtendeds[0].canDoVoidOrCapture).toEqual(false);
      expect(paymentTxExtendeds[1].canDoVoidOrCapture).toEqual(true);
    });

    it("should convert, when there are sale type in paymentTx array", async () => {
      const paymentTx: PaymentTx[] = [
        fakePaymentTx_SALE_SUCCESS_1,
        fakePaymentTx_VOID_SALE_SUCCESS_1,
        fakePaymentTx_SALE_SUCCESS_2,
      ];
      const paymentTxExtendeds = service.convertPaymentTxFromArrayToTree(paymentTx);
      expect(paymentTxExtendeds.length).toEqual(2);
      expect(paymentTxExtendeds[0].canDoVoidOrCapture).toEqual(false);
      expect(paymentTxExtendeds[1].canDoVoidOrCapture).toEqual(true);
    });

    it("should convert, when there are void/capture type in paymentTx array, but can void again if voided failed before", async () => {
      const paymentTx: PaymentTx[] = [
        fakePaymentTx_Auth_SUCCESS_1,
        fakePaymentTx_VOID_FAIL_1,
        fakePaymentTx_Auth_SUCCESS_2,
      ];
      const paymentTxExtendeds = service.convertPaymentTxFromArrayToTree(paymentTx);
      expect(paymentTxExtendeds.length).toEqual(2);
      expect(paymentTxExtendeds[0].canDoVoidOrCapture).toEqual(true);
      expect(paymentTxExtendeds[1].canDoVoidOrCapture).toEqual(true);
    });
  });
});

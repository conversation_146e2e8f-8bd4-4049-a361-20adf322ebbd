import { randomUUID } from "crypto";

import { Request } from "express";
import { CloudEvent } from "firebase-functions/v2";
import { MessagePublishedData } from "firebase-functions/v2/pubsub";

import { AppDatabaseService } from "../../../nestJs/modules/appDatabase/appDatabase.service";
import { PublishMessageForTripProcessingParams } from "../../../nestJs/modules/pubsub/dto/publishMessageForTripProcessing.dto";
import { ReceiptLanguageType } from "../../../nestJs/modules/transaction/dto/txReceipt.dto";
import { TransactionController } from "../../../nestJs/modules/transaction/transaction.controller";
import { TransactionService } from "../../../nestJs/modules/transaction/transaction.service";
import { UserService } from "../../../nestJs/modules/user/user.service";
import FakeLoggerService from "../../utils/fakeLogger.service.specs";
import MockAppDatabaseService, {
  getReceiptFromTripMock,
} from "../../utils/services/FakeAppDatabaseService.specs.utils";

describe("transaction.controller", () => {
  const createTxAdjustmentMock = jest.fn();
  const processTransactionMock = jest.fn();
  const removeTagsFromTxMock = jest.fn();
  const addNewTagsToTxMock = jest.fn();
  const processVoidMock = jest.fn();
  const getTransactionsMock = jest.fn();
  const getTransactionByIdMock = jest.fn();
  const getReceiptByTxMock = jest.fn();

  const fakeService = {
    createTxAdjustment: createTxAdjustmentMock,
    processTransaction: processTransactionMock,
    removeTagsFromTx: removeTagsFromTxMock,
    addNewTagsToTx: addNewTagsToTxMock,
    processVoid: processVoidMock,
    getTransactions: getTransactionsMock,
    getTransactionById: getTransactionByIdMock,
    getReceiptByTx: getReceiptByTxMock,
  };

  const fakeUserService = {
    getUserById: jest.fn(),
  };

  const controller = new TransactionController(
    fakeService as unknown as TransactionService,
    new MockAppDatabaseService() as unknown as AppDatabaseService,
    fakeUserService as unknown as UserService,
    FakeLoggerService,
  );

  describe("processTransaction", () => {
    it("should call the service to process the transaction", async () => {
      await controller.processTransaction({
        data: {
          message: {
            json: {
              tx: {
                id: "1",
              },
              txId: "1",
              correlationId: randomUUID(),
            },
            messageId: "100",
          },
        },
      } as unknown as CloudEvent<MessagePublishedData<PublishMessageForTripProcessingParams>>);

      expect(processTransactionMock).toBeCalledWith(
        {
          txId: "1",
          tx: {
            id: "1",
          },
        },
        "100",
      );
    });
  });

  /**
   * Test getReceipt function
   */
  describe("getReceipt", () => {
    beforeEach(() => {
      getReceiptByTxMock.mockReset();
      getReceiptFromTripMock.mockReset();
    });
    it("should get receipt data from tx table", async () => {
      const txId = "1";
      const tx = { id: txId };
      const req = {
        headers: {
          "accept-language": "en-HK",
        },
      } as Request;
      getTransactionByIdMock.mockReturnValueOnce(tx);
      await controller.getReceipt(txId, req);
      expect(getReceiptByTxMock).toBeCalledWith(tx, ReceiptLanguageType.ENHK);
      expect(getReceiptFromTripMock).not.toBeCalled();
    });
    it("should get receipt data from firebase trip", async () => {
      // mock service return null
      const txId = "1";
      const tx = null;
      const req = {
        headers: {
          "accept-language": "en-HK",
        },
      } as Request;
      getTransactionByIdMock.mockReturnValueOnce(tx);
      await controller.getReceipt(txId, req);
      expect(getReceiptByTxMock).not.toBeCalled();
      expect(getReceiptFromTripMock).toBeCalledWith(txId);
    });
  });
});

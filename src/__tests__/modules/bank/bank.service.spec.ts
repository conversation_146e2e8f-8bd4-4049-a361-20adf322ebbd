import moment from "moment";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "typeorm";

import {
  CollectionReferenceMock,
  docFirestoreMock,
  docsDataFirestoreMock,
  getFirestoreMock,
} from "@tests/utils/firebase-admin/firestoreMock.specs.utils";
import { newTestAppDatabaseService } from "@tests/utils/services/TestServices.specs.utils";

import { BankService } from "../../../nestJs/modules/bank/bank.service";
import { BankFactoryService } from "../../../nestJs/modules/bank/bankFactory/bankFactory.service";
import { DbsService } from "../../../nestJs/modules/bank/bankFactory/modules/dbs/dbs.service";
import { BankNames } from "../../../nestJs/modules/bank/dto/bankName.dto";
import Merchant from "../../../nestJs/modules/database/entities/merchant.entity";
import Tx from "../../../nestJs/modules/database/entities/tx.entity";
import { PaymentTxRepository } from "../../../nestJs/modules/database/repositories/paymentTx.repository";
import { TxRepository } from "../../../nestJs/modules/database/repositories/tx.repository";
import { TxPayoutStatus } from "../../../nestJs/modules/transaction/dto/txPayoutStatus.dto";
import { UtilsService } from "../../../nestJs/modules/utils/utils.service";
import FakeLoggerService from "../../utils/fakeLogger.service.specs";
import fakeEntityManager, { findMock, updateMock } from "../../utils/services/FakeEntityManager.specs.utils";
import FakeRepository from "../../utils/services/FakeRepository.specs.utils";

describe("bank.service", () => {
  let service: BankService;
  let txs: Tx[];
  let txWithPayoutMerchant: Tx;
  let fileDate: string;

  beforeEach(() => {
    jest.clearAllMocks();
    fileDate = moment(new Date()).format("DDMMYYYY");
    service = new BankService(
      new BankFactoryService(
        new DbsService(
          new TxRepository(
            new FakeRepository() as unknown as TxRepository,
            fakeEntityManager as unknown as EntityManager,
            new UtilsService(),
            new FakeRepository() as unknown as PaymentTxRepository,
            FakeLoggerService,
          ),
          newTestAppDatabaseService(),
        ),
      ),
    );

    docFirestoreMock.mockImplementationOnce(() => ({
      get: getFirestoreMock,
    }));
    CollectionReferenceMock.mockImplementation(() => ({
      doc: docFirestoreMock,
    }));
    getFirestoreMock.mockImplementationOnce(() => ({
      exists: true,
      empty: false,
      data: () => ({ isBulkPayoutEnabled: false }),
      docs: [{ data: docsDataFirestoreMock }],
    }));
    const merchant1 = new Merchant();
    merchant1.id = "merch1";
    merchant1.name = "Goku";
    merchant1.phoneNumber = "********";
    merchant1.bankAccountOwnerName = "Goky";
    merchant1.bankAccount = "********9";
    merchant1.bankId = "123";

    const merchant2 = new Merchant();
    merchant2.id = "merch2";
    merchant2.name = "Vegeta";
    merchant2.phoneNumber = "9486773";
    merchant2.bankAccountOwnerName = "Vegy";
    merchant2.bankAccount = "625314";
    merchant2.bankId = "609";

    txs = [
      {
        id: "tx1",
        total: 200,
        dashFee: 1.5,
        payoutAmount: 198.5,
        merchant: merchant1,
      },
      {
        id: "tx2",
        total: 200,
        dashFee: 1.5,
        payoutAmount: 198.5,
        merchant: merchant2,
      },
      {
        id: "tx3",
        total: 137,
        dashFee: 3.2,
        payoutAmount: 133.8,
        merchant: merchant1,
      },
    ] as unknown as Tx[];

    txWithPayoutMerchant = {
      id: "tx4",
      total: 200,
      dashFee: 1.5,
      payoutAmount: 198.5,
      merchant: merchant1,
      payoutMerchant: merchant2,
    } as unknown as Tx;
  });

  it("should build the right file for 1 tx", async () => {
    findMock.mockImplementation(() => {
      return [txs[0]];
    });
    const result = await service.generatePayoutFileContent(BankNames.DBS, ["tx1"]);

    expect(findMock).toHaveBeenCalledWith(Tx, {
      relations: ["merchant", "payoutMerchant", "txTag"],
      where: { id: In(["tx1"]), payoutStatus: IsNull() },
    });
    expect(updateMock).toHaveBeenCalledWith(
      Tx,
      { id: In(["tx1"]), payoutStatus: IsNull() },
      { payoutStatus: TxPayoutStatus.PRERELEASED },
    );
    expect(result).toStrictEqual({
      content: `HEADER,${fileDate},HKVISM01,VIS MOBILITY LIMITED\nPAYMENT,GPP,*********,HKD,********,HKD,,${fileDate},,,Goky,,,,,********9,,123,,,,,,,,,,198.5,,,,,20,,********,,,,B,,,,CXBSNS,,,,,,,,,,,,,,,,,,,,,,,,,\nTRAILER,1,198.5`,
      processed: ["tx1"],
      unprocessed: [],
      reasons: [],
    });
  });

  it("should build the right file for multiple txs", async () => {
    findMock.mockImplementation(() => {
      return txs;
    });
    const result = await service.generatePayoutFileContent(BankNames.DBS, ["tx1", "tx2", "tx3", "tx4"]);

    expect(findMock).toHaveBeenCalledWith(Tx, {
      relations: ["merchant", "payoutMerchant", "txTag"],
      where: { id: In(["tx1", "tx2", "tx3", "tx4"]), payoutStatus: IsNull() },
    });
    expect(updateMock).toHaveBeenCalledWith(
      Tx,
      { id: In(["tx1", "tx2", "tx3"]), payoutStatus: IsNull() },
      { payoutStatus: TxPayoutStatus.PRERELEASED },
    );

    expect(result).toStrictEqual({
      content: `HEADER,${fileDate},HKVISM01,VIS MOBILITY LIMITED\nPAYMENT,GPP,*********,HKD,********,HKD,,${fileDate},,,Goky,,,,,********9,,123,,,,,,,,,,332.3,,,,,20,,********,,,,B,,,,CXBSNS,,,,,,,,,,,,,,,,,,,,,,,,,\nPAYMENT,GPP,*********,HKD,9486773,HKD,,${fileDate},,,Vegy,,,,,625314,,609,,,,,,,,,,198.5,,,,,20,,9486773,,,,B,,,,CXBSNS,,,,,,,,,,,,,,,,,,,,,,,,,\nTRAILER,2,530.8`,
      processed: ["tx1", "tx2", "tx3"],
      unprocessed: ["tx4"],
      reasons: [
        "Transaction tx4 not found, check if it exists and that payoutStatus is Null",
        "Transaction tx4 does not have a merchant",
        "Transaction tx4 does not have a merchant name",
        "Transaction tx4 does not have a merchant bank account",
        "Transaction tx4 does not have a merchant bank account owner name",
        "Transaction tx4 does not have a merchant bank id",
      ],
    });
  });

  it("should build the right file for multiple txs when no tx found in db", async () => {
    findMock.mockImplementation(() => {
      return [];
    });
    const result = await service.generatePayoutFileContent(BankNames.DBS, ["tx1", "tx2", "tx3", "tx4"]);

    expect(findMock).toHaveBeenCalledWith(Tx, {
      relations: ["merchant", "payoutMerchant", "txTag"],
      where: { id: In(["tx1", "tx2", "tx3", "tx4"]), payoutStatus: IsNull() },
    });
    expect(updateMock).toHaveBeenCalledWith(
      Tx,
      { id: In([]), payoutStatus: IsNull() },
      { payoutStatus: TxPayoutStatus.PRERELEASED },
    );
    expect(result).toStrictEqual({
      content: `HEADER,${fileDate},HKVISM01,VIS MOBILITY LIMITED\n\nTRAILER,0,0`,
      processed: [],
      unprocessed: ["tx1", "tx2", "tx3", "tx4"],
      reasons: [
        "Transaction tx1 not found, check if it exists and that payoutStatus is Null",
        "Transaction tx1 does not have a merchant",
        "Transaction tx1 does not have a merchant name",
        "Transaction tx1 does not have a merchant bank account",
        "Transaction tx1 does not have a merchant bank account owner name",
        "Transaction tx1 does not have a merchant bank id",
        "Transaction tx2 not found, check if it exists and that payoutStatus is Null",
        "Transaction tx2 does not have a merchant",
        "Transaction tx2 does not have a merchant name",
        "Transaction tx2 does not have a merchant bank account",
        "Transaction tx2 does not have a merchant bank account owner name",
        "Transaction tx2 does not have a merchant bank id",
        "Transaction tx3 not found, check if it exists and that payoutStatus is Null",
        "Transaction tx3 does not have a merchant",
        "Transaction tx3 does not have a merchant name",
        "Transaction tx3 does not have a merchant bank account",
        "Transaction tx3 does not have a merchant bank account owner name",
        "Transaction tx3 does not have a merchant bank id",
        "Transaction tx4 not found, check if it exists and that payoutStatus is Null",
        "Transaction tx4 does not have a merchant",
        "Transaction tx4 does not have a merchant name",
        "Transaction tx4 does not have a merchant bank account",
        "Transaction tx4 does not have a merchant bank account owner name",
        "Transaction tx4 does not have a merchant bank id",
      ],
    });
  });

  it("should build the right file for multiple txs when merchant total is < 0", async () => {
    txs[0].total = 1;
    txs[0].dashFee = 3.5;
    txs[0].payoutAmount = -2.5;
    txs[2].total = 4;
    txs[2].dashFee = 3.2;
    txs[2].payoutAmount = 0.8;
    findMock.mockImplementation(() => {
      return txs;
    });
    const result = await service.generatePayoutFileContent(BankNames.DBS, ["tx1", "tx2", "tx3", "tx4"]);

    expect(findMock).toHaveBeenCalledWith(Tx, {
      relations: ["merchant", "payoutMerchant", "txTag"],
      where: { id: In(["tx1", "tx2", "tx3", "tx4"]), payoutStatus: IsNull() },
    });
    expect(updateMock).toHaveBeenCalledWith(
      Tx,
      { id: In(["tx2"]), payoutStatus: IsNull() },
      { payoutStatus: TxPayoutStatus.PRERELEASED },
    );
    expect(result).toEqual({
      content: `HEADER,${fileDate},HKVISM01,VIS MOBILITY LIMITED\nPAYMENT,GPP,*********,HKD,9486773,HKD,,${fileDate},,,Vegy,,,,,625314,,609,,,,,,,,,,198.5,,,,,20,,9486773,,,,B,,,,CXBSNS,,,,,,,,,,,,,,,,,,,,,,,,,\nTRAILER,1,198.5`,
      processed: ["tx2"],
      unprocessed: ["tx4", "tx1", "tx3"],
      reasons: [
        "Transaction tx4 not found, check if it exists and that payoutStatus is Null",
        "Transaction tx4 does not have a merchant",
        "Transaction tx4 does not have a merchant name",
        "Transaction tx4 does not have a merchant bank account",
        "Transaction tx4 does not have a merchant bank account owner name",
        "Transaction tx4 does not have a merchant bank id",
        "Merchant merch1 has a balance of -1.7, the following tx won't be processed: tx1, tx3",
      ],
    });
  });

  it("should payout to payoutMerchant if present", async () => {
    const testTxPayoutMerchant = txWithPayoutMerchant.payoutMerchant;
    findMock.mockImplementation(() => {
      return [txWithPayoutMerchant];
    });
    const result = await service.generatePayoutFileContent(BankNames.DBS, [txWithPayoutMerchant.id]);

    expect(findMock).toHaveBeenCalledWith(Tx, {
      relations: ["merchant", "payoutMerchant", "txTag"],
      where: { id: In([txWithPayoutMerchant.id]), payoutStatus: IsNull() },
    });
    expect(updateMock).toHaveBeenCalledWith(
      Tx,
      { id: In([txWithPayoutMerchant.id]), payoutStatus: IsNull() },
      { payoutStatus: TxPayoutStatus.PRERELEASED },
    );
    expect(result).toStrictEqual({
      content: `HEADER,${fileDate},HKVISM01,VIS MOBILITY LIMITED\nPAYMENT,GPP,*********,HKD,${testTxPayoutMerchant?.phoneNumber},HKD,,${fileDate},,,${testTxPayoutMerchant?.bankAccountOwnerName},,,,,${testTxPayoutMerchant?.bankAccount},,${testTxPayoutMerchant?.bankId},,,,,,,,,,198.5,,,,,20,,${testTxPayoutMerchant?.phoneNumber},,,,B,,,,CXBSNS,,,,,,,,,,,,,,,,,,,,,,,,,\nTRAILER,1,198.5`,
      processed: [txWithPayoutMerchant.id],
      unprocessed: [],
      reasons: [],
    });
  });

  it("should build the right file for 1 tx, but not fps payout", async () => {
    getFirestoreMock.mockReset();
    getFirestoreMock.mockImplementationOnce(() => ({
      exists: true,
      empty: false,
      data: () => ({ isBulkPayoutEnabled: true }),
      docs: [{ data: docsDataFirestoreMock }],
    }));
    findMock.mockImplementation(() => {
      return [txs[0]];
    });
    const result = await service.generatePayoutFileContent(BankNames.DBS, ["tx1"]);

    expect(findMock).toHaveBeenCalledWith(Tx, {
      relations: ["merchant", "payoutMerchant", "txTag"],
      where: { id: In(["tx1"]), payoutStatus: IsNull() },
    });
    expect(updateMock).toHaveBeenCalledWith(
      Tx,
      { id: In(["tx1"]), payoutStatus: IsNull() },
      { payoutStatus: TxPayoutStatus.PRERELEASED },
    );
    expect(result).toStrictEqual({
      content: `HEADER,${fileDate},HKVISM01,VIS MOBILITY LIMITED\nPAYMENT,BPY,*********,HKD,********,HKD,,${fileDate},,,Goky,,,,,456789,,123,123,,,,,,,,,198.5,,,,,20,,********,,,,B,,,,CXBSNS,,,,,,,,,,,,,,,,,,,,,,,,,\nTRAILER,1,198.5`,
      processed: ["tx1"],
      unprocessed: [],
      reasons: [],
    });
  });
});

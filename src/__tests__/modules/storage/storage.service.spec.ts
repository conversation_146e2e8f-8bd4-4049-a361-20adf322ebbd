import { ConfigService } from "@nestjs/config";

import { StorageService } from "../../../nestJs/modules/storage/storage.service";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import {
  mockBucket,
  mockDownload,
  mockFile,
  resetStorageMocks,
} from "../../utils/firebase-admin/storageMock.specs.utils";
import FakeConfigService from "../../utils/services/FakeConfigService.specs.utils";

jest.mock("firebase-admin");

describe("storage.service", () => {
  describe("getJsonFileContentFromBucketFile", () => {
    let service: StorageService;
    let fileContent: any;
    const bucketName = "bucket";
    const filePath = "bucket/dir1/fileName.json";

    beforeEach(() => {
      jest.clearAllMocks();
      resetStorageMocks();
      fileContent = [{ some: "content" }];
      service = new StorageService(new FakeConfigService() as unknown as ConfigService);
    });

    it("should call the storage service and return the file content", async () => {
      const result = await service.getJsonLFileContentFromBucketFile(bucketName, filePath);

      expect(result).toEqual(fileContent);
      expect(mockBucket).toHaveBeenCalledWith(bucketName);
      expect(mockFile).toHaveBeenCalledWith(filePath);
      expect(mockDownload).toHaveBeenCalled();
    });

    it("should throw an error when the file path is not provided", async () => {
      expect(service.getJsonLFileContentFromBucketFile(bucketName, "")).rejects.toThrowError(
        errorBuilder.storage.noFilePath(),
      );
    });

    it("should throw an error when the content of the file is not a json", async () => {
      fileContent = "not a json";
      mockDownload.mockImplementation(() => {
        return new Promise((resolve) => resolve([Buffer.from(fileContent)]));
      });

      await expect(service.getJsonLFileContentFromBucketFile(bucketName, filePath)).rejects.toThrowError(
        errorBuilder.storage.invalidContentType(),
      );
      expect(mockBucket).toHaveBeenCalledWith(bucketName);
      expect(mockFile).toHaveBeenCalledWith(filePath);
      expect(mockDownload).toHaveBeenCalled();
    });
  });
});

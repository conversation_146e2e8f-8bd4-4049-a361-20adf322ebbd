import { CampaignService } from "../../../nestJs/modules/campaign/campaign.service";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import { mockFindOne, mockSave } from "../../utils/services/FakeRepository.specs.utils";
import { newTestCampaignService } from "../../utils/services/TestServices.specs.utils";

describe("campaign.service", () => {
  let campaignService: CampaignService;
  const fakeCampaignId = "fake_id";
  beforeEach(async () => {
    campaignService = newTestCampaignService();
    mockSave.mockClear();
    mockFindOne.mockClear();
  });

  it("throw error if not find campaign in sql db", async () => {
    mockFindOne.mockResolvedValueOnce(null);

    const updateCampaignBodyDto = {
      nameEn: "fake_new_name",
    };
    await expect(campaignService.updateCampaign(fakeCampaignId, updateCampaignBodyDto)).rejects.toThrow(
      errorBuilder.incentive.campaign.notFound(fakeCampaignId),
    );
  });
  it("throw error if new endAt is earlier than current startAt", async () => {
    mockFindOne.mockResolvedValueOnce({ nameEn: "fake_name", startAt: new Date("2024-01-01T00:00:01.000Z") });

    const updateCampaignBodyDto = {
      nameEn: "fake_new_name",
      endAt: new Date("2023-01-01T00:00:01.000Z"),
    };
    await expect(campaignService.updateCampaign(fakeCampaignId, updateCampaignBodyDto)).rejects.toThrow(
      errorBuilder.incentive.campaign.invalidDate(fakeCampaignId),
    );
  });
  it("should save campaign to sql", async () => {
    mockFindOne.mockResolvedValueOnce({ nameEn: "fake_name", startAt: new Date("2024-01-01T00:00:01.000Z") });
    const updateCampaignBodyDto = {
      nameEn: "fake_new_name",
      endAt: new Date("2025-01-01T00:00:01.000Z"),
    };
    await campaignService.updateCampaign(fakeCampaignId, updateCampaignBodyDto);
    expect(mockSave).toHaveBeenCalledWith({
      nameEn: "fake_new_name",
      startAt: new Date("2024-01-01T00:00:01.000Z"),
      endAt: new Date("2025-01-01T00:00:01.000Z"),
    });
  });
});

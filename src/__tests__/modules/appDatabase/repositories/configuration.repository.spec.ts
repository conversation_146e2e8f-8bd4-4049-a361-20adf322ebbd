import ConfigurationRepository from "../../../../nestJs/modules/appDatabase/repositories/configuration.repository";
import FakeLoggerService from "../../../utils/fakeLogger.service.specs";
import {
  CollectionReferenceMock,
  dataFirestoreMock,
  docFirestoreMock,
  getFirestoreMock,
  setFirestoreMock,
} from "../../../utils/firebase-admin/firestoreMock.specs.utils";
import "../../../initTests";

describe("configuration.repository", () => {
  let repository: ConfigurationRepository;
  const cashTripTtl = "30D";
  const dashTripTtl = "90D";

  beforeEach(() => {
    jest.clearAllMocks();
    dataFirestoreMock.mockImplementation(() => ({
      cashTripTtl: "3D",
      dashTripTtl: "5D",
    }));
    repository = new ConfigurationRepository(new CollectionReferenceMock(), FakeLoggerService);
  });

  describe("getCashTripTTL", () => {
    it("should return cash_trip_ttl when cash_trip_ttl is set", async () => {
      const version = await repository.getCashTripTTL();
      expect(version).toEqual("3D");
      expect(docFirestoreMock).toHaveBeenCalledWith("server");
      expect(getFirestoreMock).toHaveBeenCalledTimes(1);
      expect(setFirestoreMock).not.toHaveBeenCalled();
    });

    it("should return defaultCashTripTTL when cash_trip_ttl is not set and server document exist", async () => {
      dataFirestoreMock.mockImplementation(() => ({}));
      const version = await repository.getCashTripTTL();
      expect(version).toEqual(cashTripTtl);
      expect(docFirestoreMock).toHaveBeenCalledWith("server");
      expect(getFirestoreMock).toHaveBeenCalledTimes(1);
      expect(setFirestoreMock).toHaveBeenCalledWith({ cashTripTtl }, { merge: true });
    });

    it("should return defaultCashTripTTL when cash_trip_ttl is not set and server document does not exist", async () => {
      getFirestoreMock.mockImplementationOnce(() => ({
        exists: false,
        data: dataFirestoreMock,
      }));
      const version = await repository.getCashTripTTL();
      expect(version).toEqual(cashTripTtl);
      expect(docFirestoreMock).toHaveBeenCalledWith("server");
      expect(getFirestoreMock).toHaveBeenCalledTimes(1);
      expect(setFirestoreMock).toHaveBeenCalledWith({ cashTripTtl }, { merge: true });
    });
  });

  describe("getDashTripTTL", () => {
    it("should return dash_trip_ttl when dash_trip_ttl is set", async () => {
      const version = await repository.getDashTripTTL();
      expect(version).toEqual("5D");
      expect(docFirestoreMock).toHaveBeenCalledWith("server");
      expect(getFirestoreMock).toHaveBeenCalledTimes(1);
      expect(setFirestoreMock).not.toHaveBeenCalled();
    });

    it("should return defaultDashTripTTL when dash_trip_ttl is not set and server document exist", async () => {
      dataFirestoreMock.mockImplementation(() => ({}));
      const version = await repository.getDashTripTTL();
      expect(version).toEqual(dashTripTtl);
      expect(docFirestoreMock).toHaveBeenCalledWith("server");
      expect(getFirestoreMock).toHaveBeenCalledTimes(1);
      expect(setFirestoreMock).toHaveBeenCalledWith({ dashTripTtl }, { merge: true });
    });

    it("should return defaultDashTripTTL when dash_trip_ttl is not set and server document does not exist", async () => {
      getFirestoreMock.mockImplementationOnce(() => ({
        exists: false,
        data: dataFirestoreMock,
      }));
      const version = await repository.getDashTripTTL();
      expect(version).toEqual(dashTripTtl);
      expect(docFirestoreMock).toHaveBeenCalledWith("server");
      expect(getFirestoreMock).toHaveBeenCalledTimes(1);
      expect(setFirestoreMock).toHaveBeenCalledWith({ dashTripTtl }, { merge: true });
    });
  });
});

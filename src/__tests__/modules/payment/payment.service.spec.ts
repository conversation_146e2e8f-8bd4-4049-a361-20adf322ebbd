import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { newTestAppDatabaseService, newTestLoggerServiceAdapter } from "@tests/utils/services/TestServices.specs.utils";

import PaymentTx from "../../../nestJs/modules/database/entities/paymentTx.entity";
import Tx from "../../../nestJs/modules/database/entities/tx.entity";
import TxTag from "../../../nestJs/modules/database/entities/txTag.entity";
import { PaymentInstrumentRepository } from "../../../nestJs/modules/database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../../../nestJs/modules/database/repositories/paymentTx.repository";
import { TxTagRepository } from "../../../nestJs/modules/database/repositories/txTag.repository";
import { PaymentInformationStatus } from "../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../nestJs/modules/payment/dto/paymentInformationType.dto";
import { PaymentService } from "../../../nestJs/modules/payment/payment.service";
import { PaymentFactoryService } from "../../../nestJs/modules/payment/paymentFactory/paymentFactory.service";
import { TxTagType } from "../../../nestJs/modules/transaction/dto/txTagType.dto";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import {
  expectedFakePaymentTxs_1,
  expectedFakePaymentTxs_2,
  expectedFakePaymentTxs_3,
  expectedFakePaymentTxs_4,
  expectedFakePaymentTxs_5,
  expectedFakePaymentTxs_6,
  fakePaymentTx_Auth_Fail,
  fakePaymentTx_Auth_SUCCESS_1,
  fakePaymentTx_Auth_SUCCESS_2,
  fakePaymentTxs_1,
  fakePaymentTxs_2,
  fakePaymentTxs_3,
  fakePaymentTxs_4,
  fakePaymentTxs_5,
  fakePaymentTxs_6,
  fakePaymentTxs_7,
  paymentTxItem61,
} from "../../utils/fakeData/fakeDataPaymentTx.specs.utils";
import { expectedResultSavedTx } from "../../utils/fakeData/fakeDataTx.specs.utils";
import MockPaymentFactoryService, { mockProcessVoid } from "../../utils/services/FakePaymentFactoryService.specs.utils";
import FakeRepository, { mockAddTagsToTx, mockFindOne } from "../../utils/services/FakeRepository.specs.utils";

jest.mock("firebase-functions", () => {
  return {
    logger: {
      error() {},
      warn() {},
    },
  };
});

describe("payment.service", () => {
  describe("findAllPreviousAuthAndSaleWithoutCaptureAndVoid", () => {
    let service: PaymentService;

    beforeEach(() => {
      jest.clearAllMocks();

      service = new PaymentService(
        new MockPaymentFactoryService() as unknown as PaymentFactoryService,
        new FakeRepository() as unknown as PaymentTxRepository,
        new FakeRepository() as unknown as TxTagRepository,
        new FakeRepository() as unknown as PaymentInstrumentRepository,
        new FakeRepository() as unknown as TxRepository,
        newTestLoggerServiceAdapter(),
        newTestAppDatabaseService(),
      );
    });

    it("should get prevoius authorised paymenttx correctly", async () => {
      const auths = service.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(fakePaymentTxs_1);
      expect(auths).toEqual(expectedFakePaymentTxs_1);
    });

    it("should get prevoius authorised paymenttx correctly even received payment_tx in wrong order", async () => {
      const auths = service.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(fakePaymentTxs_2);
      expect(auths).toEqual(expectedFakePaymentTxs_2);
    });

    it("should return empty paymenttx array when previous auth has been voided successfully", async () => {
      const auths = service.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(fakePaymentTxs_3);
      expect(auths).toEqual(expectedFakePaymentTxs_3);
    });

    it("should return empty paymenttx array when previous auth has been captured successfully", async () => {
      const auths = service.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(fakePaymentTxs_4);
      expect(auths).toEqual(expectedFakePaymentTxs_4);
    });

    it("should return paymenttx array when previous auth has been captured or voided unsuccessfully", async () => {
      const auths = service.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(fakePaymentTxs_5);
      expect(auths).toEqual(expectedFakePaymentTxs_5);
    });

    it("should return empty paymenttx array when previous sale has been voided successfully", async () => {
      const auths = service.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(fakePaymentTxs_6);
      expect(auths).toEqual(expectedFakePaymentTxs_6);
    });
  });

  describe("processAuthsToVoid", () => {
    let service: PaymentService;
    let txData: any;
    let paymentTxsData: any;

    beforeEach(() => {
      jest.clearAllMocks();
      txData = { ...expectedResultSavedTx, total: 23.5 };
      paymentTxsData = expectedResultSavedTx.paymentTx;
      service = new PaymentService(
        new MockPaymentFactoryService() as unknown as PaymentFactoryService,
        new FakeRepository() as unknown as PaymentTxRepository,
        new FakeRepository() as unknown as TxTagRepository,
        new FakeRepository() as unknown as PaymentInstrumentRepository,
        new FakeRepository() as unknown as TxRepository,
        newTestLoggerServiceAdapter(),
        newTestAppDatabaseService(),
      );
    });

    it("should call the processVoid successfully", async () => {
      const tx = Tx.fromJson(txData);
      const authsNeedToVoid = [PaymentTx.fromJson(paymentTxsData[0], tx.id)];

      const results = await service.processAuthsToVoid(tx, authsNeedToVoid);
      expect(mockAddTagsToTx).not.toBeCalled();
      expect(results).toBeInstanceOf(Array);
    });

    it("should throw an error when soepay calls fail and add tags to tx ", async () => {
      const tx = Tx.fromJson(txData);
      const authsNeedToVoid = [PaymentTx.fromJson(paymentTxsData[0], tx.id)];

      mockProcessVoid.mockImplementation(() => {
        throw errorBuilder.payment.soePay.apiFailure(new Error("Soepay call failed"));
      });

      await expect(service.processAuthsToVoid(tx, authsNeedToVoid)).rejects.toThrowError(
        errorBuilder.payment.soePay.apiFailure(new Error("Soepay call failed")),
      );
      expect(mockAddTagsToTx).toHaveBeenCalledWith(tx, [
        TxTag.createTxTag(TxTagType.UNABLE_TO_VOID, tx.id, "SYSTEM", 'Error when void paymentTx: "Soepay call failed"'),
      ]);
    });

    it("should add tags to tx when api is success but status is failure", async () => {
      const tx = Tx.fromJson(txData);
      const authsNeedToVoid = [PaymentTx.fromJson(paymentTxsData[0], tx.id)];

      mockProcessVoid.mockImplementationOnce(() => {
        const newPaymentTx = new PaymentTx();
        newPaymentTx.id = "281746124";
        newPaymentTx.status = PaymentInformationStatus.FAILURE;
        newPaymentTx.type = PaymentInformationType.VOID;
        return new Promise((resolve) => resolve([newPaymentTx]));
      });

      await service.processAuthsToVoid(tx, authsNeedToVoid);

      expect(mockAddTagsToTx).toHaveBeenCalledWith(tx, [
        TxTag.createTxTag(TxTagType.UNABLE_TO_VOID, tx.id, "SYSTEM", "Failed to void paymentTx: 281746124"),
      ]);
    });
  });

  /**
   * Test getPaymentTxById function
   */
  describe("getPaymentTxById", () => {
    let service: PaymentService;

    beforeEach(() => {
      jest.clearAllMocks();

      service = new PaymentService(
        new MockPaymentFactoryService() as unknown as PaymentFactoryService,
        new FakeRepository() as unknown as PaymentTxRepository,
        new FakeRepository() as unknown as TxTagRepository,
        new FakeRepository() as unknown as PaymentInstrumentRepository,
        new FakeRepository() as unknown as TxRepository,
        newTestLoggerServiceAdapter(),
        newTestAppDatabaseService(),
      );
    });

    it("should return authorised paymentTxId", async () => {
      mockFindOne.mockImplementation(() => new Promise((resolve) => resolve(fakePaymentTx_Auth_SUCCESS_1)));
      const foundPaymentTx = await service.getPaymentTxById("123");
      expect(foundPaymentTx).toEqual(fakePaymentTx_Auth_SUCCESS_1);
    });

    it("should throw error when the authorised paymentTxId not found", async () => {
      mockFindOne.mockImplementation(() => new Promise((resolve) => resolve(undefined)));

      expect(service.getPaymentTxById("123")).rejects.toThrowError(errorBuilder.payment.paymentTxNotFound("123"));
    });
  });

  /**
   * Test capture/voidPayment function
   */
  describe("executePayment", () => {
    const requestedBy = "<EMAIL>";
    const paymentTxId = "123";
    let service: PaymentService;

    beforeEach(() => {
      jest.clearAllMocks();
      mockFindOne.mockClear();
      service = new PaymentService(
        new MockPaymentFactoryService() as unknown as PaymentFactoryService,
        new FakeRepository() as unknown as PaymentTxRepository,
        new FakeRepository() as unknown as TxTagRepository,
        new FakeRepository() as unknown as PaymentInstrumentRepository,
        new FakeRepository() as unknown as TxRepository,
        newTestLoggerServiceAdapter(),
        newTestAppDatabaseService(),
      );
    });

    it("should throw error when the authorised paymentTxId type is not AUTH or status is not SUCCESS", async () => {
      mockFindOne.mockImplementation(() => new Promise((resolve) => resolve(fakePaymentTx_Auth_Fail)));

      expect(service.voidPayment(paymentTxId, requestedBy)).rejects.toThrow(
        errorBuilder.payment.paymentTxWrongTypeOrStatus(
          fakePaymentTx_Auth_Fail,
          [PaymentInformationType.AUTH, PaymentInformationType.SALE],
          PaymentInformationStatus.SUCCESS,
        ),
      );
    });
    it("should throw error when the capture amount is greater than paymentTx's amount", async () => {
      mockFindOne.mockImplementation(() => new Promise((resolve) => resolve(fakePaymentTx_Auth_SUCCESS_2)));

      expect(service.capturePayment(paymentTxId, 40, requestedBy)).rejects.toThrowError(
        errorBuilder.payment.amountExceedsAuth(fakePaymentTx_Auth_SUCCESS_2, 40),
      );
    });
  });

  describe("findLastSuccessSaleWithoutVoid", () => {
    let service: PaymentService;

    beforeEach(() => {
      jest.clearAllMocks();

      service = new PaymentService(
        new MockPaymentFactoryService() as unknown as PaymentFactoryService,
        new FakeRepository() as unknown as PaymentTxRepository,
        new FakeRepository() as unknown as TxTagRepository,
        new FakeRepository() as unknown as PaymentInstrumentRepository,
        new FakeRepository() as unknown as TxRepository,
        newTestLoggerServiceAdapter(),
        newTestAppDatabaseService(),
      );
    });

    it("should get last sale paymentTx correctly when only have one sale", async () => {
      const sale = service.findLastSuccessSaleWithoutVoid([paymentTxItem61]);
      expect(sale).toEqual(paymentTxItem61);
    });

    it("should get last sale paymentTx correctly when have more than one sale", async () => {
      const sale = service.findLastSuccessSaleWithoutVoid(fakePaymentTxs_6);
      expect(sale).toEqual(paymentTxItem61);
    });

    it("should get undefined when last sale is voided", async () => {
      const sale = service.findLastSuccessSaleWithoutVoid(fakePaymentTxs_7);
      expect(sale).toEqual(undefined);
    });
  });
});

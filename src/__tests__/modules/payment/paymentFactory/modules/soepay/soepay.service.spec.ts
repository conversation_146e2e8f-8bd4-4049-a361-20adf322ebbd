import { HttpService } from "@nestjs/axios";
import { HttpStatus } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import { PaymentTxRepository } from "../../../../../../nestJs/modules/database/repositories/paymentTx.repository";
import { PaymentGatewayTypes } from "../../../../../../nestJs/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../../../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../../../../nestJs/modules/payment/dto/paymentInformationType.dto";
import { SoePayTranType } from "../../../../../../nestJs/modules/payment/paymentFactory/modules/soepay/dto/soepay.model";
import { SoepayService } from "../../../../../../nestJs/modules/payment/paymentFactory/modules/soepay/soepay.service";
import { DashError, DashErrorCodes, errorBuilder } from "../../../../../../nestJs/modules/utils/utils/error.utils";
import { fakePaymentTx, generateFakePaymentTx } from "../../../../../utils/fakeData/fakeDataPaymentTx.specs.utils";
import {
  soepayVoidFailure,
  soepayCaptureSuccess,
  soepayAuthSuccess,
  soepayCaptureFailure,
  soepayAuthFailure,
  soepayVoidSuccess,
} from "../../../../../utils/fakeData/fakeDataSoepay.specs.utils";
import FakeLoggerService from "../../../../../utils/fakeLogger.service.specs";
import FakeConfigService from "../../../../../utils/services/FakeConfigService.specs.utils";
import FakeHttpService, {
  mockGet,
  mockPost,
  resetHttpServiceMock,
} from "../../../../../utils/services/FakeHttpService.specs.utils";
import FakeRepository, { mockCreate } from "../../../../../utils/services/FakeRepository.specs.utils";

jest.mock("axios-retry");
jest.mock("firebase-functions/logger");

describe("payment", () => {
  describe("soepay.service", () => {
    let service: SoepayService;

    beforeEach(() => {
      service = new SoepayService(
        new FakeConfigService() as unknown as ConfigService,
        new FakeHttpService() as unknown as HttpService,
        new FakeRepository() as unknown as PaymentTxRepository,
        FakeLoggerService,
      );

      resetHttpServiceMock({
        result: 0,
        data: {
          tranType: SoePayTranType.AUTH_COMPLETE,
          payment: {},
        },
      });
    });

    afterEach(async () => {
      mockPost.mockReset();
      mockGet.mockReset();
    });

    describe("extractPaymentTxInfoFromDocument", () => {
      it("should return the right data using the SOEPAY service for Capture Success", () => {
        const paymentTxFromDocument = service.extractPaymentTxInfoFromDocument(soepayCaptureSuccess);

        expect(paymentTxFromDocument.gateway).toEqual(PaymentGatewayTypes.SOEPAY);
        expect(paymentTxFromDocument.type).toEqual(PaymentInformationType.CAPTURE);
        expect(paymentTxFromDocument.status).toEqual(PaymentInformationStatus.SUCCESS);
      });
      it("should return the right data using the SOEPAY service for Capture Failure", () => {
        const paymentTxFromDocument = service.extractPaymentTxInfoFromDocument(soepayCaptureFailure);

        expect(paymentTxFromDocument.gateway).toEqual(PaymentGatewayTypes.SOEPAY);
        expect(paymentTxFromDocument.type).toEqual(PaymentInformationType.CAPTURE);
        expect(paymentTxFromDocument.status).toEqual(PaymentInformationStatus.FAILURE);
      });
      it("should return the right data using the SOEPAY service for Auth Success", () => {
        const paymentTxFromDocument = service.extractPaymentTxInfoFromDocument(soepayAuthSuccess);

        expect(paymentTxFromDocument.gateway).toEqual(PaymentGatewayTypes.SOEPAY);
        expect(paymentTxFromDocument.type).toEqual(PaymentInformationType.AUTH);
        expect(paymentTxFromDocument.status).toEqual(PaymentInformationStatus.SUCCESS);
      });
      it("should return the right data using the SOEPAY service for Auth Failure", () => {
        const paymentTxFromDocument = service.extractPaymentTxInfoFromDocument(soepayAuthFailure);

        expect(paymentTxFromDocument.gateway).toEqual(PaymentGatewayTypes.SOEPAY);
        expect(paymentTxFromDocument.type).toEqual(PaymentInformationType.AUTH);
        expect(paymentTxFromDocument.status).toEqual(PaymentInformationStatus.FAILURE);
      });
      it("should return the right data using the SOEPAY service for Void Success", () => {
        const paymentTxFromDocument = service.extractPaymentTxInfoFromDocument(soepayVoidSuccess);

        expect(paymentTxFromDocument.gateway).toEqual(PaymentGatewayTypes.SOEPAY);
        expect(paymentTxFromDocument.type).toEqual(PaymentInformationType.VOID);
        expect(paymentTxFromDocument.status).toEqual(PaymentInformationStatus.SUCCESS);
      });
      it("should return the right data using the SOEPAY service for Void Failure", () => {
        const paymentTxFromDocument = service.extractPaymentTxInfoFromDocument(soepayVoidFailure);

        expect(paymentTxFromDocument.gateway).toEqual(PaymentGatewayTypes.SOEPAY);
        // expect(paymentTxFromDocument.type).toEqual(PaymentInformationType.VOID); void failure and capture failure are the same
        expect(paymentTxFromDocument.status).toEqual(PaymentInformationStatus.FAILURE);
      });
    });
    describe("processCapture", () => {
      it("should return the SUCCESS paymentTx", async () => {
        const genPaymentTx = fakePaymentTx();
        genPaymentTx.status = PaymentInformationStatus.FAILURE;
        genPaymentTx.type = PaymentInformationType.AUTH;
        const paymentTx = await service.processCapture(genPaymentTx, 100);

        expect(paymentTx.status).toEqual(PaymentInformationStatus.SUCCESS);
        expect(paymentTx.type).toEqual(PaymentInformationType.CAPTURE);
        expect(paymentTx.gatewayResponse).toBeDefined();
        expect(paymentTx.parent).toEqual(genPaymentTx);
        expect(mockPost).toHaveBeenCalled();
      });
      it("should return the FAILED paymentTx when result is not 0", async () => {
        resetHttpServiceMock({ result: 1 });
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        const genPaymentTx = fakePaymentTx();
        genPaymentTx.status = PaymentInformationStatus.SUCCESS;
        genPaymentTx.type = PaymentInformationType.AUTH;

        const paymentTx = await service.processCapture(genPaymentTx, 100);

        expect(paymentTx.status).toEqual(PaymentInformationStatus.FAILURE);
        expect(paymentTx.type).toEqual(PaymentInformationType.CAPTURE);
        expect(paymentTx.gatewayResponse).toBeDefined();
        expect(mockPost).toHaveBeenCalled();
      });
      it("should throw an error when there is no gatewayTransactionId", async () => {
        resetHttpServiceMock({ result: 1 });
        const genPaymentTx = fakePaymentTx();
        genPaymentTx.gatewayTransactionId = undefined;

        try {
          await service.processCapture(genPaymentTx, 100);
        } catch (error: any) {
          expect(error).toBeInstanceOf(DashError);
          expect(error.message).toEqual("gatewayTransactionId for paymentTx is required");
          expect(error.code).toEqual(DashErrorCodes.GLOBAL__REQUIRED_PARAM);
          expect(error.getStatus()).toEqual(HttpStatus.BAD_REQUEST);
        }
      });
      it("should throw an error when there is no amount", async () => {
        resetHttpServiceMock({ result: 1 });
        const genPaymentTx = fakePaymentTx();

        try {
          await service.processCapture(genPaymentTx, 0);
        } catch (error: any) {
          expect(error).toBeInstanceOf(DashError);
          expect(error.message).toEqual("total amount for paymentTx is invalid");
          expect(error.code).toEqual(DashErrorCodes.GLOBAL__INVALID_PARAM);
          expect(error.getStatus()).toEqual(HttpStatus.BAD_REQUEST);
        }
      });
      it("should throw an error when amount is negative", async () => {
        resetHttpServiceMock({ result: 1 });
        const genPaymentTx = fakePaymentTx();

        try {
          await service.processCapture(genPaymentTx, -100);
        } catch (error: any) {
          expect(error).toBeInstanceOf(DashError);
          expect(error.message).toEqual("total amount for paymentTx is invalid");
          expect(error.code).toEqual(DashErrorCodes.GLOBAL__INVALID_PARAM);
          expect(error.getStatus()).toEqual(HttpStatus.BAD_REQUEST);
        }
      });
      it("should throw an error when soepay call fails", async () => {
        resetHttpServiceMock({ result: 1 });
        mockPost.mockImplementation(() => {
          throw new Error("Soepay call failed");
        });
        const genPaymentTx = fakePaymentTx();

        try {
          await service.processCapture(genPaymentTx, 100);
        } catch (error: any) {
          expect(error).toBeInstanceOf(DashError);
          expect(error.message).toEqual("Soepay call failed");
          expect(error.code).toEqual(DashErrorCodes.GLOBAL__UNKNOWN);
          expect(error.getStatus()).toEqual(HttpStatus.INTERNAL_SERVER_ERROR);
        }
      });
      it("should call enquiry api after capture api return FAILURE", async () => {
        resetHttpServiceMock({
          result: 104,
          message: "Transaction is Paid",
        });
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        const paymentTx = generateFakePaymentTx();
        const captureResult = await service.processCapture(paymentTx, 100);
        expect(mockPost).toHaveBeenCalledTimes(1);

        expect(captureResult.status).toEqual(PaymentInformationStatus.FAILURE);
        expect(captureResult.type).toEqual(PaymentInformationType.CAPTURE);

        expect(mockGet).toHaveBeenCalledTimes(1); //get is the enquiry api
      });
    });
    describe("processVoid", () => {
      it("should return results array after all void api call successfully", async () => {
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        const paymentTx1 = generateFakePaymentTx();
        const paymentTx2 = generateFakePaymentTx();
        await service.processVoid([paymentTx1, paymentTx2]);
        expect(mockPost).toHaveBeenCalledTimes(2);
      });

      it("should return results array after all void api call successfully", async () => {
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        const paymentTx1 = generateFakePaymentTx();
        const paymentTx2 = generateFakePaymentTx();
        const voidResult = await service.processVoid([paymentTx1, paymentTx2]);
        expect(voidResult.length).toEqual(2);
        expect(mockPost).toHaveBeenCalledTimes(2);
      });

      it("should throw error after one paymentTx doesn't have gatewayTransactionId", async () => {
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        const paymentTx = generateFakePaymentTx();
        const paymentTxWithoutTranId = generateFakePaymentTx();
        delete paymentTxWithoutTranId.gatewayTransactionId;

        await expect(service.processVoid([paymentTx, paymentTxWithoutTranId])).rejects.toThrow(
          errorBuilder.payment.soePay.voidFailed(
            1,
            errorBuilder.global.requiredParam("gatewayTransactionId for paymentTx").toString(),
          ),
        );

        expect(mockPost).toHaveBeenCalledTimes(1);
      });

      it("should throw error after multiple paymentTx don't have gatewayTransactionId", async () => {
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        const paymentTx1 = generateFakePaymentTx();
        const paymentTxWithoutTranId1 = generateFakePaymentTx();
        delete paymentTxWithoutTranId1.gatewayTransactionId;
        const paymentTx2 = generateFakePaymentTx();
        const paymentTxWithoutTranId2 = generateFakePaymentTx();
        delete paymentTxWithoutTranId2.gatewayTransactionId;
        try {
          await service.processVoid([paymentTx1, paymentTxWithoutTranId1, paymentTx2, paymentTxWithoutTranId2]);
        } catch (error: any) {
          expect(error.message).toEqual(
            "Failed to void 2 payment transaction(s). Reasons: DashError: gatewayTransactionId for paymentTx is required, DashError: gatewayTransactionId for paymentTx is required",
          );
        }
        expect(mockPost).toHaveBeenCalledTimes(2);
      });

      it("should throw error after one void api calling failed", async () => {
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        const paymentTx1 = generateFakePaymentTx();
        const paymentTx2 = generateFakePaymentTx();
        const paymentTx3 = generateFakePaymentTx();
        mockPost.mockImplementationOnce(() => {
          throw new Error(`Error calling soepay void API for tranId: ${paymentTx1.id}`);
        });
        try {
          await service.processVoid([paymentTx1, paymentTx2, paymentTx3]);
        } catch (error: any) {
          expect(error.message).toEqual(
            `Failed to void 1 payment transaction(s). Reasons: DashError: Error calling soepay void API for tranId: ${paymentTx1.id}`,
          );
        }
        expect(mockPost).toHaveBeenCalledTimes(3);
      });

      it("should return the SUCCESS paymentTx", async () => {
        resetHttpServiceMock({
          result: 0,
          data: {
            tranType: SoePayTranType.VOID,
            payment: {},
          },
        });
        const paymentTx = generateFakePaymentTx();
        const voidResult = await service.processVoid([paymentTx]);
        expect(voidResult.length).toEqual(1);
        expect(mockPost).toHaveBeenCalledTimes(1);

        expect(voidResult[0].status).toEqual(PaymentInformationStatus.SUCCESS);
        expect(voidResult[0].type).toEqual(PaymentInformationType.VOID);
        expect(voidResult[0].gatewayResponse).toBeDefined();
      });

      it("should call enquiry api after void api return FAILURE", async () => {
        resetHttpServiceMock({
          result: 104,
          message: "Transaction is non cancelable",
        });
        mockCreate.mockImplementationOnce(() => ({
          //mock enquiryPaymentTx
          id: "123",
          metadata: {},
        }));
        const paymentTx = generateFakePaymentTx();
        const voidResult = await service.processVoid([paymentTx]);
        expect(voidResult.length).toEqual(1);
        expect(mockPost).toHaveBeenCalledTimes(1);

        expect(voidResult[0].status).toEqual(PaymentInformationStatus.FAILURE);
        expect(voidResult[0].type).toEqual(PaymentInformationType.VOID);

        expect(mockGet).toBeCalledTimes(1);
      });
    });
  });
});

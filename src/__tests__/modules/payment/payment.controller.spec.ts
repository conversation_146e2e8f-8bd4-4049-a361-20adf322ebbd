import { randomUUID } from "crypto";

import { CloudEvent } from "firebase-functions/v2";
import { MessagePublishedData } from "firebase-functions/v2/pubsub";

import { CampaignService } from "@nest/modules/campaign/campaign.service";
import { PaymentTxRepository } from "@nest/modules/database/repositories/paymentTx.repository";
import FakeCampaignService from "@tests/utils/services/FakeCampaignService.spec.utils";

import { TripDocument } from "../../../nestJs/modules/appDatabase/documents/trip.document";
import PaymentTx from "../../../nestJs/modules/database/entities/paymentTx.entity";
import Tx from "../../../nestJs/modules/database/entities/tx.entity";
import TxTag from "../../../nestJs/modules/database/entities/txTag.entity";
import { TxRepository } from "../../../nestJs/modules/database/repositories/tx.repository";
import { TxTagRepository } from "../../../nestJs/modules/database/repositories/txTag.repository";
import { PaymentInformationStatus } from "../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../nestJs/modules/payment/dto/paymentInformationType.dto";
import { PaymentController } from "../../../nestJs/modules/payment/payment.controller";
import { PaymentService } from "../../../nestJs/modules/payment/payment.service";
import { PublishMessageForCaptureProcessingParams } from "../../../nestJs/modules/pubsub/dto/publishMessageForCaptureProcessingParams.dto";
import { PublishMessageForVoidProcessingParams } from "../../../nestJs/modules/pubsub/dto/publishMessageForVoidProcessingParams.dto";
import { PublishMessageForDirectSaleProcessingParams } from "../../../nestJs/modules/pubsub/dto/publishMessageToProcessSale.dto";
import { PubSubService } from "../../../nestJs/modules/pubsub/pubsub.service";
import { TxTagType } from "../../../nestJs/modules/transaction/dto/txTagType.dto";
import { TxTypes } from "../../../nestJs/modules/transaction/dto/txType.dto";
import { TransactionFactoryService } from "../../../nestJs/modules/transaction/transactionFactory/transactionFactory.service";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import { UtilsService } from "../../../nestJs/modules/utils/utils.service";
import { fakeCaptureFileContent } from "../../utils/fakeData/fakeDataPaymentTx.specs.utils";
import { expectedResultSavedTx } from "../../utils/fakeData/fakeDataTx.specs.utils";
import FakeLoggerService from "../../utils/fakeLogger.service.specs";
import MockPaymentService, {
  mockProcessCapture,
  mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid,
  mockProcessAuthsToVoid,
  mockProcessSale,
} from "../../utils/services/FakePaymentService.specs.utils";
import FakePubSubService from "../../utils/services/FakePubSubService.specs.utils";
import FakeRepository, {
  mockAddTagsToTx,
  mockFind,
  mockFindOne,
} from "../../utils/services/FakeRepository.specs.utils";
import FakeTransactionFactoryService, {
  mockIsTxCalculationCorrect,
  mockPostPaymentProcess,
  mockSendReceipt,
} from "../../utils/services/FakeTransactionFactoryService.specs.utils";

jest.mock("firebase-admin");

describe("payment.controller", () => {
  describe("processCapture", () => {
    let controller: PaymentController;
    let txData: any;

    const mockProcessVoidAfterCaptureSuccess = jest.fn();
    const spy = jest
      .spyOn(PaymentController.prototype as any, "processVoidAfterCaptureSuccess")
      .mockImplementation(mockProcessVoidAfterCaptureSuccess);
    const mockProcessVoidAndSaleAfterSaleSuccess = jest.fn();
    const spy2 = jest
      .spyOn(PaymentController.prototype as any, "processVoidAndSaleAfterSaleSuccess")
      .mockImplementation(mockProcessVoidAndSaleAfterSaleSuccess);
    beforeEach(() => {
      jest.clearAllMocks();
      controller = new PaymentController(
        new MockPaymentService() as unknown as PaymentService,
        new FakeTransactionFactoryService() as unknown as TransactionFactoryService,
        new FakeCampaignService() as unknown as CampaignService,
        new FakeRepository() as unknown as TxTagRepository,
        new FakeRepository() as unknown as TxRepository,
        FakeLoggerService,
        new UtilsService(),
        new FakePubSubService() as unknown as PubSubService,
        new FakeRepository() as unknown as PaymentTxRepository,
      );
      txData = { ...expectedResultSavedTx, total: 23.5 };
      mockFind.mockImplementation(() => txData.paymentTx);
    });

    afterAll(() => {
      spy.mockRestore();
      spy2.mockRestore;
    });

    it("should call the processCapture to get the capture paymentTx content successfully", async () => {
      const tx = Tx.fromJson(txData);
      const mockTxFromJson = jest.fn().mockReturnValue(tx);
      jest.spyOn(Tx, "fromJson").mockImplementationOnce(mockTxFromJson);

      const newPaymentTx = await controller.processCapture({
        data: {
          message: {
            json: {
              tx: txData,
              correlationId: randomUUID(),
            },
          },
        },
      } as unknown as CloudEvent<MessagePublishedData<PublishMessageForCaptureProcessingParams>>);
      expect(newPaymentTx).toEqual({
        ...expectedResultSavedTx.paymentTx[0],
        status: PaymentInformationStatus.SUCCESS,
        type: PaymentInformationType.CAPTURE,
      });
      expect(mockPostPaymentProcess).toBeCalledWith(tx, true);
      expect(mockSendReceipt).toBeCalledWith(tx, newPaymentTx);
      expect(mockProcessVoidAfterCaptureSuccess).toBeCalledWith(tx, newPaymentTx);
      expect(mockAddTagsToTx).not.toBeCalled();
      expect(newPaymentTx).toBeInstanceOf(PaymentTx);
    });

    it("should throw an error when calculation is wrong and add tags to tx", async () => {
      delete txData.total;
      const tx = Tx.fromJson(txData);
      const mockTxFromJson = jest.fn().mockReturnValue(tx);
      jest.spyOn(Tx, "fromJson").mockImplementationOnce(mockTxFromJson);

      await expect(
        controller.processCapture({
          data: {
            message: {
              json: {
                tx: txData,
                correlationId: randomUUID(),
              },
            },
          },
        } as unknown as CloudEvent<MessagePublishedData<PublishMessageForCaptureProcessingParams>>),
      ).rejects.toThrowError(errorBuilder.transaction.incorrectCalculation(tx.id));
      expect(mockAddTagsToTx).toHaveBeenCalledWith(tx, [
        TxTag.createTxTag(TxTagType.AMOUNT_MISMATCH, tx.id, "SYSTEM", "OK!"),
      ]);
      expect(mockPostPaymentProcess).toBeCalledWith(tx, false);
      expect(mockSendReceipt).not.toBeCalled();
      expect(mockProcessVoidAfterCaptureSuccess).not.toBeCalled();
    });

    it("should return directly and add tag when there is no lastSuccessAuthWithoutCapture", async () => {
      delete txData.paymentTx;
      const tx = Tx.fromJson(txData);
      const mockTxFromJson = jest.fn().mockReturnValue(tx);
      jest.spyOn(Tx, "fromJson").mockImplementationOnce(mockTxFromJson);
      mockFind.mockImplementation(() => []);

      await controller.processCapture({
        data: {
          message: {
            json: {
              tx: txData,
              correlationId: randomUUID(),
            },
          },
        },
      } as unknown as CloudEvent<MessagePublishedData<PublishMessageForCaptureProcessingParams>>);
      expect(mockAddTagsToTx).toHaveBeenCalledWith(tx, [
        TxTag.createTxTag(TxTagType.UNABLE_TO_CAPTURE, tx.id, "SYSTEM", "No last success auth without capture found"),
      ]);
      expect(mockPostPaymentProcess).toBeCalledWith(tx, false);
      expect(mockSendReceipt).not.toBeCalled();
      expect(mockProcessVoidAfterCaptureSuccess).not.toBeCalled();
    });

    it("should throw an error when soepay calls fail and add tags to tx", async () => {
      const tx = Tx.fromJson(txData);
      const mockTxFromJson = jest.fn().mockReturnValue(tx);
      jest.spyOn(Tx, "fromJson").mockImplementationOnce(mockTxFromJson);

      mockProcessCapture.mockImplementation(() => {
        throw errorBuilder.payment.soePay.apiFailure(new Error("Soepay call failed"));
      });

      await expect(
        controller.processCapture({
          data: {
            message: {
              json: {
                tx: txData,
                correlationId: randomUUID(),
              },
            },
          },
        } as unknown as CloudEvent<MessagePublishedData<PublishMessageForCaptureProcessingParams>>),
      ).rejects.toThrowError(errorBuilder.payment.soePay.apiFailure(new Error("Soepay call failed")));
      expect(mockAddTagsToTx).toHaveBeenCalledWith(tx, [
        TxTag.createTxTag(TxTagType.UNABLE_TO_CAPTURE, tx.id, "SYSTEM", "Soepay call failed"),
      ]);
      expect(mockPostPaymentProcess).toBeCalledWith(tx, false);
      expect(mockSendReceipt).not.toBeCalled();
      expect(mockProcessVoidAfterCaptureSuccess).not.toBeCalled();
    });

    it("should add tags to tx when api is success but status is failure", async () => {
      const tx = Tx.fromJson(txData);
      const mockTxFromJson = jest.fn().mockReturnValue(tx);
      jest.spyOn(Tx, "fromJson").mockImplementationOnce(mockTxFromJson);

      mockProcessCapture.mockImplementationOnce((paymentTx: PaymentTx) => {
        const newPaymentTx = Object.create(PaymentTx.prototype);
        Object.assign(newPaymentTx, paymentTx);
        newPaymentTx.status = PaymentInformationStatus.FAILURE;
        newPaymentTx.type = PaymentInformationType.CAPTURE;
        return new Promise((resolve) => resolve(newPaymentTx));
      });

      await controller.processCapture({
        data: {
          message: {
            json: {
              tx: txData,
              correlationId: randomUUID(),
            },
          },
        },
      } as unknown as CloudEvent<MessagePublishedData<PublishMessageForCaptureProcessingParams>>),
        expect(mockAddTagsToTx).toHaveBeenCalledWith(tx, [
          TxTag.createTxTag(
            TxTagType.UNABLE_TO_CAPTURE,
            tx.id,
            "SYSTEM",
            "Can't proceed to postPayment or sendReceipt as last paymentTx is not CAPTURE SUCCESS",
          ),
        ]);
      expect(mockPostPaymentProcess).toBeCalledWith(tx, false);
      expect(mockSendReceipt).not.toBeCalled();
      expect(mockProcessVoidAfterCaptureSuccess).not.toBeCalled();
    });
  });

  describe("data handling before processAuthsToVoid", () => {
    let controller: PaymentController;
    let txData: any;
    let paymentTxsData: any;

    beforeEach(() => {
      jest.clearAllMocks();
      txData = { ...expectedResultSavedTx, total: 23.5 };
      paymentTxsData = expectedResultSavedTx.paymentTx;
      controller = new PaymentController(
        new MockPaymentService() as unknown as PaymentService,
        new FakeTransactionFactoryService() as unknown as TransactionFactoryService,
        new FakeCampaignService() as unknown as CampaignService,
        new FakeRepository() as unknown as TxTagRepository,
        new FakeRepository() as unknown as TxRepository,
        FakeLoggerService,
        new UtilsService(),
        new FakePubSubService() as unknown as PubSubService,
        new FakeRepository() as unknown as PaymentTxRepository,
      );
    });

    it("should call the processAuthsToVoid in processVoid", async () => {
      const tx = Tx.fromJson(txData);
      const mockTxFromJson = jest.fn().mockReturnValue(tx);
      jest.spyOn(Tx, "fromJson").mockImplementationOnce(mockTxFromJson);

      const paymentTx = PaymentTx.fromJson(paymentTxsData[0], tx.id);
      const mockPaymentTxFromJson = jest.fn().mockReturnValue(paymentTx);
      jest.spyOn(PaymentTx, "fromJson").mockImplementationOnce(mockPaymentTxFromJson);

      await controller.processVoid({
        data: {
          message: {
            json: {
              tx: txData,
              paymentTxs: paymentTxsData,
              correlationId: randomUUID(),
            },
          },
        },
      } as unknown as CloudEvent<MessagePublishedData<PublishMessageForVoidProcessingParams>>);

      expect(mockProcessAuthsToVoid).toBeCalledWith(tx, [paymentTx]);
    });

    it("should call the processAuthsToVoid in processVoidAfterCaptureSuccess", async () => {
      const tx = Tx.fromJson(txData);
      const paymentTx = PaymentTx.fromJson(fakeCaptureFileContent.payload, tx.id);
      mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid.mockReturnValueOnce([
        PaymentTx.fromJson(txData.paymentTx[0], tx.id),
      ]);
      await controller.processVoidAfterCaptureSuccess(tx, paymentTx);
      expect(mockProcessAuthsToVoid).toBeCalledWith(tx, [PaymentTx.fromJson(txData.paymentTx[0], tx.id)]);
    });
  });

  describe("processDirectSale", () => {
    let controller: PaymentController;
    let data: any;

    beforeEach(() => {
      jest.clearAllMocks();

      controller = new PaymentController(
        new MockPaymentService() as unknown as PaymentService,
        new FakeTransactionFactoryService() as unknown as TransactionFactoryService,
        new FakeCampaignService() as unknown as CampaignService,
        new FakeRepository() as unknown as TxTagRepository,
        new FakeRepository() as unknown as TxRepository,
        FakeLoggerService,
        new UtilsService(),
        new FakePubSubService() as unknown as PubSubService,
        new FakeRepository() as unknown as PaymentTxRepository,
      );
      data = { txId: "07aa2173-a3d9-45aa-be74-61e359761766", paymentInstrumentId: "1234567890" };
    });

    it("should call the processSale to get the sale paymentTx content successfully", async () => {
      const fakeFoundTx = new Tx();
      fakeFoundTx.id = data.txId;
      fakeFoundTx.total = 100;
      fakeFoundTx.dashFee = 10;
      fakeFoundTx.type = TxTypes.TRIP;
      const fakeMetadata = new TripDocument();
      fakeMetadata.total = 100;
      fakeMetadata.dashFee = 10;
      fakeMetadata.tripEnd = new Date();
      fakeFoundTx.metadata = fakeMetadata;
      mockFindOne.mockReturnValueOnce(fakeFoundTx);
      mockIsTxCalculationCorrect.mockImplementationOnce(() => {
        return { result: true, reason: "OK!" };
      });
      mockProcessSale.mockImplementation((foundTx: Tx, paymentInstrumentId: string, overwriteAmount?: number) => {
        const newPaymentTx = new PaymentTx();
        newPaymentTx.tx = foundTx;
        newPaymentTx.status = PaymentInformationStatus.SUCCESS;
        newPaymentTx.type = PaymentInformationType.SALE;
        return new Promise((resolve) => resolve(newPaymentTx));
      });
      const newPaymentTx = await controller.processDirectSale({
        data: {
          message: {
            json: {
              txId: data.txId,
              paymentInstrumentId: data.paymentInstrumentId,
              correlationId: randomUUID(),
            },
          },
        },
      } as unknown as CloudEvent<MessagePublishedData<PublishMessageForDirectSaleProcessingParams>>);
      expect(newPaymentTx).toEqual({
        tx: fakeFoundTx,
        status: PaymentInformationStatus.SUCCESS,
        type: PaymentInformationType.SALE,
      });
      expect(mockPostPaymentProcess).toHaveBeenCalledWith(fakeFoundTx, true);
      expect(mockAddTagsToTx).not.toHaveBeenCalled();
    });

    it("should throw an error when calculation is wrong and add tags to tx", async () => {
      const fakeFoundTx = new Tx();
      fakeFoundTx.id = data.txId;
      fakeFoundTx.total = 100;
      fakeFoundTx.dashFee = 10;
      fakeFoundTx.type = TxTypes.TRIP;
      const fakeMetadata = new TripDocument();
      fakeMetadata.total = 111;
      fakeMetadata.dashFee = 11;
      fakeMetadata.tripEnd = new Date();
      fakeFoundTx.metadata = fakeMetadata;
      mockFindOne.mockReturnValueOnce(fakeFoundTx);
      mockIsTxCalculationCorrect.mockImplementationOnce(() => {
        return { result: false, reason: "OK!" };
      });
      await expect(
        controller.processDirectSale({
          data: {
            message: {
              json: {
                txId: data.txId,
                paymentInstrumentId: data.paymentInstrumentId,
                correlationId: randomUUID(),
              },
            },
          },
        } as unknown as CloudEvent<MessagePublishedData<PublishMessageForDirectSaleProcessingParams>>),
      ).rejects.toThrow(errorBuilder.transaction.incorrectCalculation(fakeFoundTx.id));
      expect(mockAddTagsToTx).toHaveBeenCalledWith(fakeFoundTx, [
        TxTag.createTxTag(TxTagType.AMOUNT_MISMATCH, fakeFoundTx.id, "SYSTEM", "OK!"),
      ]);
      expect(mockPostPaymentProcess).toHaveBeenCalledWith(fakeFoundTx, false);
    });

    it("should add tags to tx when api fail", async () => {
      const fakeFoundTx = new Tx();
      fakeFoundTx.id = data.txId;
      fakeFoundTx.total = 100;
      fakeFoundTx.dashFee = 10;
      fakeFoundTx.type = TxTypes.TRIP;
      const fakeMetadata = new TripDocument();
      fakeMetadata.total = 100;
      fakeMetadata.dashFee = 10;
      fakeMetadata.tripEnd = new Date();
      fakeFoundTx.metadata = fakeMetadata;
      mockFindOne.mockReturnValueOnce(fakeFoundTx);
      mockIsTxCalculationCorrect.mockImplementationOnce(() => {
        return { result: true, reason: "OK!" };
      });
      mockProcessSale.mockRejectedValueOnce(errorBuilder.payment.unableToSell(data.txId, data.paymentInstrumentId));

      await expect(
        controller.processDirectSale({
          data: {
            message: {
              json: {
                txId: data.txId,
                paymentInstrumentId: data.paymentInstrumentId,
                correlationId: randomUUID(),
              },
            },
          },
        } as unknown as CloudEvent<MessagePublishedData<PublishMessageForDirectSaleProcessingParams>>),
      ).rejects.toThrow(errorBuilder.payment.unableToSell(data.txId, data.paymentInstrumentId));
      expect(mockAddTagsToTx).toHaveBeenCalledWith(fakeFoundTx, [
        TxTag.createTxTag(
          TxTagType.UNABLE_TO_SELL,
          fakeFoundTx.id,
          "SYSTEM",
          "The payment has failed for tx: 07aa2173-a3d9-45aa-be74-61e359761766 and payment instrument: 1234567890",
        ),
      ]);
      expect(mockPostPaymentProcess).toHaveBeenCalledWith(fakeFoundTx, false);
    });
  });
});

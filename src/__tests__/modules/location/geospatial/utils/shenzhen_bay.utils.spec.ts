import { ShenzhenBayUtils } from "@nest/modules/location/geospatial/utils/shenzhen_bay.utils";

describe("ShenzhenBayUtils", () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("autocompleteResultProxy", () => {
    let mockResults: any[];
    beforeEach(() => {
      mockResults = [
        {
          placeId: "1",
          mainText: "Test Place 1",
          secondaryText: "Test Address 1",
        },
        {
          placeId: "2",
          mainText: "Test Place 2",
          secondaryText: "Test Address 2",
        },
      ];
    });

    it("should add Shenzhen Bay Port suggestion for valid English search", () => {
      jest.spyOn(ShenzhenBayUtils, "searchIsShenzhenEn").mockReturnValue(true);
      const input = "";
      const originalLength = mockResults.length;
      const result = ShenzhenBayUtils.autocompleteResultProxy(mockResults, input);
      expect(result[0].placeId).toBe(ShenzhenBayUtils.SHENZHEN_BAY_PORT_PLACE_ID);
      expect(result[0].mainText).toBe(ShenzhenBayUtils.SHENZHEN_BAY_PLACE.displayNameEn);
      expect(result[0].secondaryText).toBe(ShenzhenBayUtils.SHENZHEN_BAY_PLACE.formattedAddressEn);
      expect(result.length).toBe(originalLength + 1);
      expect(result[1].placeId).toBe("1");
      expect(result[2].placeId).toBe("2");
    });

    it("should add Shenzhen Bay Port suggestion for valid Chinese search", () => {
      jest.spyOn(ShenzhenBayUtils, "searchIsShenzhenZh").mockReturnValue(true);
      const input = "";
      const originalLength = mockResults.length;
      const result = ShenzhenBayUtils.autocompleteResultProxy(mockResults, input);
      expect(result[0].placeId).toBe(ShenzhenBayUtils.SHENZHEN_BAY_PORT_PLACE_ID);
      expect(result[0].mainText).toBe(ShenzhenBayUtils.SHENZHEN_BAY_PLACE.displayNameZh);
      expect(result[0].secondaryText).toBe(ShenzhenBayUtils.SHENZHEN_BAY_PLACE.formattedAddressZh);
      expect(result.length).toBe(originalLength + 1);
      expect(result[1].placeId).toBe("1");
      expect(result[2].placeId).toBe("2");
    });

    it("should not add Shenzhen Bay Port suggestion for invalid search", () => {
      jest.spyOn(ShenzhenBayUtils, "searchIsShenzhenEn").mockReturnValue(false);
      jest.spyOn(ShenzhenBayUtils, "searchIsShenzhenZh").mockReturnValue(false);
      const input = "invalid search";
      const result = ShenzhenBayUtils.autocompleteResultProxy(mockResults, input);
      expect(result.length).toBe(mockResults.length);
      expect(result[0].placeId).toBe("1");
      expect(result[1].placeId).toBe("2");
    });
  });

  describe("searchIsShenzhenEn", () => {
    it("should return true for 'shen'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenEn("shen");
      expect(result).toBe(true);
    });

    it("should return true for 'shenzh'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenEn("shenzhen");
      expect(result).toBe(true);
    });

    it("should return true for 'shenzhen bay'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenEn("shenzhen bay");
      expect(result).toBe(true);
    });

    it("should return true for 'shenzhen bay port'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenEn("shenzhen bay port");
      expect(result).toBe(true);
    });

    it("should return false for 'she', which is not a complete prefix", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenEn("she");
      expect(result).toBe(false);
    });

    it("should return false for 'shenabc'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenEn("shenabc");
      expect(result).toBe(false);
    });

    it("should return false for 'shenzhenabc'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenEn("shenzhenabc");
      expect(result).toBe(false);
    });
  });

  describe("searchIsShenzhenZh", () => {
    it("should return true for '深圳'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenZh("深圳");
      expect(result).toBe(true);
    });

    it("should return true for '深圳灣'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenZh("深圳灣");
      expect(result).toBe(true);
    });

    it("should return true for '深圳灣口岸'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenZh("深圳灣口岸");
      expect(result).toBe(true);
    });

    it("should return false for '深'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenZh("深");
      expect(result).toBe(false);
    });

    it("should return false for '深水埗'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenZh("深");
      expect(result).toBe(false);
    });

    it("should return false for '深圳abc'", () => {
      const result = ShenzhenBayUtils.searchIsShenzhenZh("深圳abc");
      expect(result).toBe(false);
    });
  });
});

import { GeocodeResult } from "@googlemaps/google-maps-services-js";

import { LocationLanguage } from "../../../nestJs/modules/location/dto/location.dto";
import { LocationService } from "../../../nestJs/modules/location/location.service";
import reverseGeocodeCoordResponseMock from "../../mockData/google/reverseGeocodeCoordResponse.mock";
import {
  exampleChinese,
  exampleEnglish,
  examplePremisePlusCode,
  test1,
  test2,
  test3,
  test4,
  test5,
  test6,
} from "../../utils/fakeData/fakeReverseGeocodeCoordResonse.specs.utils";
import { newTestLocationService } from "../../utils/services/TestServices.specs.utils";

describe("location.service", () => {
  let locationService: LocationService;
  beforeEach(async () => {
    locationService = newTestLocationService();
    jest.clearAllMocks();
    // mockSave.mockClear();
    // mockFindOne.mockClear();
  });

  describe("getReverseGeocodeAddress", () => {
    it("should get correct address : test default", async () => {
      const result = locationService.getReverseGeocodeAddress(
        reverseGeocodeCoordResponseMock.results as unknown as GeocodeResult[],
        LocationLanguage.EN,
      );
      expect(result).toEqual({
        displayName: "11 Tong Chun Street",
        formattedAddress: "11 Tong Chun St, Tseung Kwan O",
        placeId: "ChIJo2ZWr_IDBDQRNkB-6gQz2Lw",
      });
    });
    it("should get correct address : test 1", async () => {
      const result = locationService.getReverseGeocodeAddress(
        test1.results as unknown as GeocodeResult[],
        LocationLanguage.EN,
      );
      expect(result).toEqual({
        displayName: "30号 Sample Boulevard",
        formattedAddress: "30 Sample Blvd, Sample Neighborhood, Sample City",
        placeId: "ChIJ1234567890QRST",
      });
    });
    it("should get correct address : test 2", async () => {
      const result = locationService.getReverseGeocodeAddress(
        test2.results as unknown as GeocodeResult[],
        LocationLanguage.EN,
      );
      expect(result).toEqual({
        displayName: "40号 Sample Drive",
        formattedAddress: "40 Sample Dr, Example Neighborhood, Example Town",
        placeId: "ChIJ9876543210TUVWX",
      });
    });
    it("should get correct address : test 3", async () => {
      const result = locationService.getReverseGeocodeAddress(
        test3.results as unknown as GeocodeResult[],
        LocationLanguage.EN,
      );
      expect(result).toEqual({
        displayName: "Testville",
        formattedAddress: "1 Sample Rd, Test Neighborhood, Testville",
        placeId: "ChIJ1234567890ABCDE",
      });
    });
    it("should get correct address : test 4", async () => {
      const result = locationService.getReverseGeocodeAddress(
        test4.results as unknown as GeocodeResult[],
        LocationLanguage.EN,
      );
      expect(result).toEqual({
        displayName: "12号",
        formattedAddress: "12 Another Example Rd, Sample Neighborhood, Sampletown",
        placeId: "ChIJ2345678901JKLMN",
      });
    });
    it("should get correct address : test 5", async () => {
      const result = locationService.getReverseGeocodeAddress(
        test5.results as unknown as GeocodeResult[],
        LocationLanguage.EN,
      );
      expect(result).toEqual({
        displayName: "40号 Sample Drive",
        formattedAddress: "40 Sample Dr, Example Neighborhood, Example Town",
        placeId: "ChIJ9876543210TUVWX",
      });
    });
    it("should get correct address : test 6", async () => {
      const result = locationService.getReverseGeocodeAddress(
        test6.results as unknown as GeocodeResult[],
        LocationLanguage.EN,
      );
      expect(result).toEqual({
        displayName: "Science Park",
        formattedAddress: "Fo Yin Rd, Science Park",
        placeId: "ChIJgVr06ZAIBDQRoP5TSbOkhUQ",
      });
    });
    it("should get correct address : test English", async () => {
      const result = locationService.getReverseGeocodeAddress(
        exampleEnglish.results as unknown as GeocodeResult[],
        LocationLanguage.EN,
      );
      expect(result).toEqual({
        displayName: "10 Example Road",
        formattedAddress: "10 Example Rd, Sample Neighborhood",
        placeId: "ChIJ1234567890EFGH",
      });
    });
    it("should get correct address : test Chinese", async () => {
      const result = locationService.getReverseGeocodeAddress(
        exampleChinese.results as unknown as GeocodeResult[],
        LocationLanguage.ZHHK,
      );
      expect(result).toEqual({
        displayName: "示例路10號",
        formattedAddress: "示例省示例市示例社区示例路10号, 12345",
        placeId: "ChIJ1234567890EFGH",
      });
    });
    it("should get correct address: test premise type with plus code as first component", async () => {
      const result = locationService.getReverseGeocodeAddress(
        examplePremisePlusCode.results as unknown as GeocodeResult[],
        LocationLanguage.EN,
      );
      expect(result).toEqual({
        displayName: "Lai Chi Yuen Cemetery",
        formattedAddress: "Lai Chi Yuen Cemetery, Mui Wo",
        placeId: "ChIJMYBx6dFXATQRzGkJ-VS_9sY",
      });
    });
  });
});

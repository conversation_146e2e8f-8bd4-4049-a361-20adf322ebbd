import { Test, TestingModule } from "@nestjs/testing";

import { NotificationUserType } from "../../../../../src/nestJs/modules/database/entities/notificationTask.entity";
import { NotificationRequestDto } from "../../../../../src/nestJs/modules/notification/dto/notification.dto";
import { EmailNotificationService } from "../../../../../src/nestJs/modules/notification/notification-method/email-notification.service";
import { INotificationUserTokenParam } from "../../../../../src/nestJs/modules/notification/notification-method/notification-method.interface";

describe("EmailNotificationService", () => {
  let service: EmailNotificationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [EmailNotificationService],
    }).compile();

    service = module.get<EmailNotificationService>(EmailNotificationService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("notify", () => {
    it("should throw not implemented error", () => {
      // Arrange
      const token: INotificationUserTokenParam = {
        userId: "123",
        tokenId: "token-id-123",
        token: "test-token",
        preferredLanguage: "en",
        userType: NotificationUserType.USERS,
      };

      const message: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
        clickAction: "OPEN_APP",
      };

      // Act & Assert
      expect(() => service.notify(token, message)).toThrow();
    });
  });

  describe("notifyMany", () => {
    it("should throw not implemented error", () => {
      // Arrange
      const tokens: INotificationUserTokenParam[] = [
        {
          userId: "123",
          tokenId: "token-id-123",
          token: "test-token-1",
          preferredLanguage: "en",
          userType: NotificationUserType.USERS,
        },
        {
          userId: "456",
          tokenId: "token-id-456",
          token: "test-token-2",
          preferredLanguage: "hk",
          userType: NotificationUserType.USERS,
        },
      ];

      const message: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
        clickAction: "OPEN_APP",
      };

      // Act & Assert
      expect(() => service.notifyMany(tokens, message)).toThrow();
    });
  });
});

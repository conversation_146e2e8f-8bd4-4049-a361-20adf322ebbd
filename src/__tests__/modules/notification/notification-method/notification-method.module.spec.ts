import { Test, TestingModule } from "@nestjs/testing";

import { NotificationHistoryRepository } from "../../../../../src/nestJs/modules/database/repositories/notificationHistory.repository";
import { EmailNotificationService } from "../../../../../src/nestJs/modules/notification/notification-method/email-notification.service";
import { PushNotificationService } from "../../../../../src/nestJs/modules/notification/notification-method/push-notification.service";
import { SmsNotificationService } from "../../../../../src/nestJs/modules/notification/notification-method/sms-notification.service";
import LoggerServiceAdapter from "../../../../../src/nestJs/modules/utils/logger/logger.service";
import { NotificationFactory } from "../../../../nestJs/modules/notification/notification-method/notification.factory";

const mockNotificationHistoryRepository = {
  save: jest.fn(),
  findOne: jest.fn(),
  find: jest.fn(),
  create: jest.fn(),
};

const mockLoggerService = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
};

describe("NotificationMethodsModule", () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      providers: [
        EmailNotificationService,
        SmsNotificationService,
        PushNotificationService,
        NotificationFactory,
        {
          provide: NotificationHistoryRepository,
          useValue: mockNotificationHistoryRepository,
        },
        {
          provide: LoggerServiceAdapter,
          useValue: mockLoggerService,
        },
      ],
    }).compile();
  });

  it("should be defined", () => {
    expect(module).toBeDefined();
  });

  it("should provide EmailNotificationService", () => {
    const service = module.get<EmailNotificationService>(EmailNotificationService);
    expect(service).toBeDefined();
    expect(service).toBeInstanceOf(EmailNotificationService);
  });

  it("should provide SmsNotificationService", () => {
    const service = module.get<SmsNotificationService>(SmsNotificationService);
    expect(service).toBeDefined();
    expect(service).toBeInstanceOf(SmsNotificationService);
  });

  it("should provide PushNotificationService", () => {
    const service = module.get<PushNotificationService>(PushNotificationService);
    expect(service).toBeDefined();
    expect(service).toBeInstanceOf(PushNotificationService);
  });

  it("should provide NotificationFactory", () => {
    const service = module.get<NotificationFactory>(NotificationFactory);
    expect(service).toBeDefined();
    expect(service).toBeInstanceOf(NotificationFactory);
  });

  it("should provide NotificationHistoryRepository", () => {
    const repository = module.get<NotificationHistoryRepository>(NotificationHistoryRepository);
    expect(repository).toBeDefined();
    expect(repository).toBe(mockNotificationHistoryRepository);
  });
});

import { getMessaging } from "firebase-admin/messaging";

import { NotificationHistoryStatusEnum } from "../../../../nestJs/modules/database/entities/notificationHistory.entity";
import { NotificationUserType } from "../../../../nestJs/modules/database/entities/notificationTask.entity";
import { NotificationHistoryRepository } from "../../../../nestJs/modules/database/repositories/notificationHistory.repository";
import { PreferredLanguageType } from "../../../../nestJs/modules/identity/dto/user.dto";
import { NotificationRequestDto } from "../../../../nestJs/modules/notification/dto/notification.dto";
import { INotificationUserTokenParam } from "../../../../nestJs/modules/notification/notification-method/notification-method.interface";
import { PushNotificationService } from "../../../../nestJs/modules/notification/notification-method/push-notification.service";
import LoggerServiceAdapter from "../../../../nestJs/modules/utils/logger/logger.service";
import "../../../initTests";

// Mock Firebase Admin Messaging
jest.mock("firebase-admin/messaging", () => ({
  getMessaging: jest.fn().mockReturnValue({
    send: jest.fn(),
    sendMulticast: jest.fn(),
  }),
}));

describe("PushNotificationService", () => {
  let pushNotificationService: PushNotificationService;
  let notificationHistoryRepository: NotificationHistoryRepository;
  let logger: LoggerServiceAdapter;
  let mockMessaging: any;

  beforeEach(() => {
    notificationHistoryRepository = {
      create: jest.fn(),
      save: jest.fn(),
    } as unknown as NotificationHistoryRepository;

    logger = {
      log: jest.fn(),
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    } as unknown as LoggerServiceAdapter;

    mockMessaging = {
      send: jest.fn(),
      sendMulticast: jest.fn(),
    };
    (getMessaging as jest.Mock).mockReturnValue(mockMessaging);

    pushNotificationService = new PushNotificationService(logger, notificationHistoryRepository);
  });

  describe("notify", () => {
    it("should send push notification and save history with English language", async () => {
      const token: INotificationUserTokenParam = {
        token: "push-token-123",
        userId: "user-123",
        tokenId: "token-id-123",
        preferredLanguage: PreferredLanguageType.EN,
        userType: NotificationUserType.USERS,
      };

      const notification: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
        clickAction: "OPEN_APP",
      };

      const mockMessageId = "message-id-123";
      mockMessaging.send.mockResolvedValue(mockMessageId);

      const mockHistoryEntity = { id: "history-123" };
      (notificationHistoryRepository.create as jest.Mock).mockReturnValue(mockHistoryEntity);
      (notificationHistoryRepository.save as jest.Mock).mockResolvedValue(mockHistoryEntity);

      const result = await pushNotificationService.notify(token, notification);

      // Verify Firebase message was sent with correct parameters
      expect(mockMessaging.send).toHaveBeenCalled();
      const sentMessage = mockMessaging.send.mock.calls[0][0];
      expect(sentMessage.token).toBe(token.token);
      expect(sentMessage.notification.title).toBe(notification.titleEn);
      expect(sentMessage.notification.body).toBe(notification.bodyEn);

      // Verify notification history was created and saved
      expect(notificationHistoryRepository.create).toHaveBeenCalledWith({
        userId: token.userId,
        userType: token.userType,
        title: notification.titleEn,
        body: notification.bodyEn,
        status: NotificationHistoryStatusEnum.SUCCESS,
        metadata: {
          userTokenId: token.tokenId,
          messageId: mockMessageId,
        },
      });

      expect(notificationHistoryRepository.save).toHaveBeenCalledWith(mockHistoryEntity);
      expect(result).toBe(mockHistoryEntity.id);
    });

    it("should send push notification and save history with Chinese language", async () => {
      const token: INotificationUserTokenParam = {
        token: "push-token-123",
        userId: "user-123",
        tokenId: "token-id-123",
        preferredLanguage: PreferredLanguageType.ZHHK,
        userType: NotificationUserType.USERS,
      };

      const notification: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
        clickAction: "OPEN_APP",
      };

      const mockMessageId = "message-id-123";
      mockMessaging.send.mockResolvedValue(mockMessageId);

      const mockHistoryEntity = { id: "history-123" };
      (notificationHistoryRepository.create as jest.Mock).mockReturnValue(mockHistoryEntity);
      (notificationHistoryRepository.save as jest.Mock).mockResolvedValue(mockHistoryEntity);

      const result = await pushNotificationService.notify(token, notification);

      // Verify Firebase message was sent with correct parameters
      expect(mockMessaging.send).toHaveBeenCalled();
      const sentMessage = mockMessaging.send.mock.calls[0][0];
      expect(sentMessage.token).toBe(token.token);
      expect(sentMessage.notification.title).toBe(notification.titleHk);
      expect(sentMessage.notification.body).toBe(notification.bodyHk);

      // Verify notification history was created and saved
      expect(notificationHistoryRepository.create).toHaveBeenCalledWith({
        userId: token.userId,
        userType: token.userType,
        title: notification.titleHk,
        body: notification.bodyHk,
        status: NotificationHistoryStatusEnum.SUCCESS,
        metadata: {
          userTokenId: token.tokenId,
          messageId: mockMessageId,
        },
      });

      expect(notificationHistoryRepository.save).toHaveBeenCalledWith(mockHistoryEntity);
      expect(result).toBe(mockHistoryEntity.id);
    });

    it("should handle error when sending push notification fails", async () => {
      const token: INotificationUserTokenParam = {
        token: "invalid-token",
        userId: "user-123",
        tokenId: "token-id-123",
        preferredLanguage: PreferredLanguageType.EN,
        userType: NotificationUserType.USERS,
      };

      const notification: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
      };

      const error = new Error("Invalid token");
      mockMessaging.send.mockRejectedValue(error);

      const mockHistoryEntity = { id: "history-123" };
      (notificationHistoryRepository.create as jest.Mock).mockReturnValue(mockHistoryEntity);
      (notificationHistoryRepository.save as jest.Mock).mockResolvedValue(mockHistoryEntity);

      const result = await pushNotificationService.notify(token, notification);

      // Verify notification history was created with failure status
      expect(notificationHistoryRepository.create).toHaveBeenCalledWith({
        userId: token.userId,
        userType: token.userType,
        title: notification.titleEn,
        body: notification.bodyEn,
        status: NotificationHistoryStatusEnum.FAILURE,
        failureReason: "Invalid token",
        metadata: {
          userTokenId: token.tokenId,
          messageId: undefined,
        },
      });

      expect(notificationHistoryRepository.save).toHaveBeenCalledWith(mockHistoryEntity);
      expect(result).toBe(mockHistoryEntity.id);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe("notifyMany", () => {
    it("should send multiple push notifications", async () => {
      const tokens: INotificationUserTokenParam[] = [
        {
          token: "push-token-1",
          userId: "user-1",
          tokenId: "token-id-1",
          preferredLanguage: PreferredLanguageType.EN,
          userType: NotificationUserType.USERS,
        },
        {
          token: "push-token-2",
          userId: "user-2",
          tokenId: "token-id-2",
          preferredLanguage: PreferredLanguageType.ZHHK,
          userType: NotificationUserType.USERS,
        },
      ];

      const notification: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
      };

      // Mock the notifyEach method directly
      const mockHistoryIds = ["history-1", "history-2"];

      // Create a spy on the private method using any
      const notifyEachSpy = jest
        .spyOn(pushNotificationService as any, "notifyEach")
        .mockResolvedValueOnce(mockHistoryIds[0])
        .mockResolvedValueOnce(mockHistoryIds[1]);

      const result = await pushNotificationService.notifyMany(tokens, notification);

      expect(notifyEachSpy).toHaveBeenCalledTimes(2);
      expect(notifyEachSpy).toHaveBeenCalledWith(tokens[0], notification);
      expect(notifyEachSpy).toHaveBeenCalledWith(tokens[1], notification);
      expect(result).toEqual(mockHistoryIds);
    });
  });
});

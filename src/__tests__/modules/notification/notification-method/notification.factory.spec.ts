import {
  NotificationTaskType,
  NotificationUserType,
} from "../../../../nestJs/modules/database/entities/notificationTask.entity";
import { NotificationRequestDto } from "../../../../nestJs/modules/notification/dto/notification.dto";
import { EmailNotificationService } from "../../../../nestJs/modules/notification/notification-method/email-notification.service";
import { INotificationUserTokenParam } from "../../../../nestJs/modules/notification/notification-method/notification-method.interface";
import { NotificationFactory } from "../../../../nestJs/modules/notification/notification-method/notification.factory";
import { PushNotificationService } from "../../../../nestJs/modules/notification/notification-method/push-notification.service";
import { SmsNotificationService } from "../../../../nestJs/modules/notification/notification-method/sms-notification.service";
import "../../../initTests";

describe("NotificationFactory", () => {
  let notificationDelegator: NotificationFactory;
  let pushNotificationService: PushNotificationService;
  let emailNotificationService: EmailNotificationService;
  let smsNotificationService: SmsNotificationService;

  beforeEach(() => {
    pushNotificationService = {
      notify: jest.fn(),
      notifyMany: jest.fn(),
    } as unknown as PushNotificationService;

    emailNotificationService = {
      notify: jest.fn(),
      notifyMany: jest.fn(),
    } as unknown as EmailNotificationService;

    smsNotificationService = {
      notify: jest.fn(),
      notifyMany: jest.fn(),
    } as unknown as SmsNotificationService;

    notificationDelegator = new NotificationFactory(
      pushNotificationService,
      emailNotificationService,
      smsNotificationService,
    );
  });

  describe("notify", () => {
    it("should call pushNotificationService.notify for PUSH type", async () => {
      const token: INotificationUserTokenParam = {
        token: "push-token",
        userId: "user-123",
        tokenId: "token-id-123",
        preferredLanguage: "en",
        userType: NotificationUserType.USERS,
      };
      const message: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
      };
      const expectedResult = "notification-id";

      (pushNotificationService.notify as jest.Mock).mockResolvedValue(expectedResult);

      const result = await notificationDelegator.get(NotificationTaskType.PUSH).notify(token, message);

      expect(pushNotificationService.notify).toHaveBeenCalledWith(token, message);
      expect(result).toBe(expectedResult);
    });

    it("should call emailNotificationService.notify for EMAIL type", async () => {
      // Using the correct interface structure but with an email token
      const token: INotificationUserTokenParam = {
        token: "<EMAIL>", // Using token field for email
        userId: "user-123",
        tokenId: "token-id-123",
        preferredLanguage: "en",
        userType: NotificationUserType.USERS,
      };
      const message: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
      };
      const expectedResult = "notification-id";

      (emailNotificationService.notify as jest.Mock).mockResolvedValue(expectedResult);

      const result = await notificationDelegator.get(NotificationTaskType.EMAIL).notify(token, message);

      expect(emailNotificationService.notify).toHaveBeenCalledWith(token, message);
      expect(result).toBe(expectedResult);
    });

    it("should call smsNotificationService.notify for SMS type", async () => {
      // Using the correct interface structure but with a phone number token
      const token: INotificationUserTokenParam = {
        token: "+85212345678", // Using token field for phone number
        userId: "user-123",
        tokenId: "token-id-123",
        preferredLanguage: "en",
        userType: NotificationUserType.USERS,
      };
      const message: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
      };
      const expectedResult = "notification-id";

      (smsNotificationService.notify as jest.Mock).mockResolvedValue(expectedResult);

      const result = await notificationDelegator.get(NotificationTaskType.SMS).notify(token, message);

      expect(smsNotificationService.notify).toHaveBeenCalledWith(token, message);
      expect(result).toBe(expectedResult);
    });
  });

  describe("notifyMany", () => {
    it("should call pushNotificationService.notifyMany for PUSH type", async () => {
      const tokens: INotificationUserTokenParam[] = [
        {
          token: "push-token-1",
          userId: "user-1",
          tokenId: "token-id-1",
          preferredLanguage: "en",
          userType: NotificationUserType.USERS,
        },
        {
          token: "push-token-2",
          userId: "user-2",
          tokenId: "token-id-2",
          preferredLanguage: "en",
          userType: NotificationUserType.USERS,
        },
      ];
      const message: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
      };
      const expectedResult = ["notification-id-1", "notification-id-2"];

      (pushNotificationService.notifyMany as jest.Mock).mockResolvedValue(expectedResult);

      const result = await notificationDelegator.get(NotificationTaskType.PUSH).notifyMany(tokens, message);

      expect(pushNotificationService.notifyMany).toHaveBeenCalledWith(tokens, message);
      expect(result).toBe(expectedResult);
    });

    it("should call emailNotificationService.notifyMany for EMAIL type", async () => {
      const tokens: INotificationUserTokenParam[] = [
        {
          token: "<EMAIL>",
          userId: "user-1",
          tokenId: "token-id-1",
          preferredLanguage: "en",
          userType: NotificationUserType.USERS,
        },
        {
          token: "<EMAIL>",
          userId: "user-2",
          tokenId: "token-id-2",
          preferredLanguage: "en",
          userType: NotificationUserType.USERS,
        },
      ];
      const message: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
      };
      const expectedResult = ["notification-id-1", "notification-id-2"];

      (emailNotificationService.notifyMany as jest.Mock).mockResolvedValue(expectedResult);

      const result = await notificationDelegator.get(NotificationTaskType.EMAIL).notifyMany(tokens, message);

      expect(emailNotificationService.notifyMany).toHaveBeenCalledWith(tokens, message);
      expect(result).toBe(expectedResult);
    });

    it("should call smsNotificationService.notifyMany for SMS type", async () => {
      const tokens: INotificationUserTokenParam[] = [
        {
          token: "+85212345678",
          userId: "user-1",
          tokenId: "token-id-1",
          preferredLanguage: "en",
          userType: NotificationUserType.USERS,
        },
        {
          token: "+85287654321",
          userId: "user-2",
          tokenId: "token-id-2",
          preferredLanguage: "en",
          userType: NotificationUserType.USERS,
        },
      ];
      const message: NotificationRequestDto = {
        titleEn: "Test Title",
        titleHk: "測試標題",
        bodyEn: "Test Body",
        bodyHk: "測試內容",
      };
      const expectedResult = ["notification-id-1", "notification-id-2"];

      (smsNotificationService.notifyMany as jest.Mock).mockResolvedValue(expectedResult);

      const result = await notificationDelegator.get(NotificationTaskType.SMS).notifyMany(tokens, message);

      expect(smsNotificationService.notifyMany).toHaveBeenCalledWith(tokens, message);
      expect(result).toBe(expectedResult);
    });
  });
});

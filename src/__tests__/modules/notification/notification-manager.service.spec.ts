import { Test, TestingModule } from "@nestjs/testing";
import { DataSource, QueryRunner } from "typeorm";

import {
  CreateManyNotificationRequestDto,
  NotificationTaskRequestStatus,
  UpdateManyNotificationRequestDto,
} from "@nest/modules/cloud-task-notification-handler/dto/request.dto";
import NotificationTask, {
  NotificationTaskStatus,
  NotificationTaskType,
  NotificationUserType,
} from "@nest/modules/database/entities/notificationTask.entity";
import { NotificationTaskRepository } from "@nest/modules/database/repositories/notificationTask.repository";
import {
  NotificationTaskSortKey,
  NotificationTaskSortOrder,
} from "@nest/modules/notification/dto/notification-filters.dto";
import { NotificationRequestDto } from "@nest/modules/notification/dto/notification.dto";
import { NotificationManagerService } from "@nest/modules/notification/notification-manager.service";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

describe("NotificationManagerService", () => {
  let service: NotificationManagerService;
  let mockNotificationTaskRepository: NotificationTaskRepository;
  let mockQueryRunner: QueryRunner;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationManagerService,
        {
          provide: LoggerServiceAdapter,
          useValue: {
            info: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
        {
          provide: NotificationTaskRepository,
          useValue: {
            create: jest.fn(),
            findOneBy: jest.fn(),
            findAndCount: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: {
            createQueryRunner: jest.fn().mockReturnValue({
              connect: jest.fn(),
              startTransaction: jest.fn(),
              commitTransaction: jest.fn(),
              rollbackTransaction: jest.fn(),
              release: jest.fn(),
              manager: {
                save: jest.fn(),
              },
            } as unknown as QueryRunner),
          },
        },
      ],
    }).compile();

    service = module.get(NotificationManagerService);
    mockNotificationTaskRepository = module.get(NotificationTaskRepository);
    mockQueryRunner = module.get(DataSource).createQueryRunner();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
    expect(mockNotificationTaskRepository).toBeDefined();
  });

  describe("createNotificationTask", () => {
    const phoneNumbers = ["+85212345678", "+85287654321"];
    const notificationRequest: NotificationRequestDto = {
      titleEn: "Test",
      titleHk: "測試",
      bodyEn: "Test notification",
      bodyHk: "測試通知",
    };

    const createManyDto: CreateManyNotificationRequestDto = {
      phoneNumbers,
      notificationRequest,
      name: "Test Notification",
      type: NotificationTaskType.PUSH,
      userType: NotificationUserType.USERS,
      status: NotificationTaskRequestStatus.SCHEDULED,
      scheduledAt: new Date(),
    };

    const mockTask: NotificationTask = Object.assign(new NotificationTask(), {
      id: "task-id",
      name: "Test Notification",
      type: NotificationTaskType.PUSH,
      status: NotificationTaskStatus.SCHEDULED,
      userType: NotificationUserType.USERS,
      scheduledAt: new Date(),
      createdBy: "test-user",
      payload: {
        phoneNumbers,
        notificationRequest,
      },
      cloudTaskReference: null,
    });

    it("should create a notification task with SCHEDULED status", async () => {
      jest.spyOn(mockNotificationTaskRepository, "create").mockReturnValue(mockTask);
      jest.spyOn(mockQueryRunner.manager, "save").mockResolvedValue({ ...mockTask, id: "task-id" });

      const cloudTaskCallback = jest.fn().mockResolvedValue("cloud-task-name");

      await service.createNotificationTask(createManyDto, "test-user", cloudTaskCallback);

      expect(mockNotificationTaskRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: "Test Notification",
          type: NotificationTaskType.PUSH,
          status: NotificationTaskStatus.SCHEDULED,
          userType: NotificationUserType.USERS,
        }),
      );

      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.manager.save).toHaveBeenCalledTimes(2);
      expect(cloudTaskCallback).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
    });

    it("should create a notification task with DRAFT status", async () => {
      const draftDto = Object.assign({}, createManyDto, { status: NotificationTaskRequestStatus.DRAFT });
      const draftTask = Object.assign({}, mockTask, { status: NotificationTaskStatus.DRAFT });

      jest.spyOn(mockNotificationTaskRepository, "create").mockReturnValue(draftTask);
      jest.spyOn(mockQueryRunner.manager, "save").mockResolvedValue({ ...draftTask, id: "task-id" });

      const cloudTaskCallback = jest.fn().mockResolvedValue("cloud-task-name");

      await service.createNotificationTask(draftDto, "test-user", cloudTaskCallback);

      expect(mockNotificationTaskRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          status: NotificationTaskStatus.DRAFT,
        }),
      );

      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.manager.save).toHaveBeenCalledTimes(1);
      expect(cloudTaskCallback).not.toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
    });

    it("should rollback transaction when an error occurs", async () => {
      jest.spyOn(mockNotificationTaskRepository, "create").mockReturnValue(mockTask);
      jest.spyOn(mockQueryRunner.manager, "save").mockRejectedValue(new Error("Database error"));

      const cloudTaskCallback = jest.fn().mockResolvedValue("cloud-task-name");

      await expect(service.createNotificationTask(createManyDto, "test-user", cloudTaskCallback)).rejects.toThrow();

      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).not.toHaveBeenCalled();
    });
  });

  describe("updateNotificationAfterCallback", () => {
    const taskId = "task-id";
    const updatedBy = "test-user";
    const updateDto: UpdateManyNotificationRequestDto = {
      name: "Updated Notification",
      type: NotificationTaskType.PUSH,
      scheduledAt: new Date(),
      phoneNumbers: ["+85212345678"],
      notificationRequest: {
        titleEn: "Updated",
        titleHk: "更新",
        bodyEn: "Updated notification",
        bodyHk: "更新通知",
      },
      status: NotificationTaskRequestStatus.SCHEDULED,
      userType: NotificationUserType.USERS,
    };

    const mockTask: NotificationTask = Object.assign(new NotificationTask(), {
      id: taskId,
      name: "Test Notification",
      type: NotificationTaskType.PUSH,
      status: NotificationTaskStatus.DRAFT,
      userType: NotificationUserType.USERS,
      scheduledAt: new Date(),
      payload: {
        phoneNumbers: ["+85212345678", "+85287654321"],
        notificationRequest: {
          titleEn: "Test",
          titleHk: "測試",
          bodyEn: "Test notification",
          bodyHk: "測試通知",
        },
      },
      cloudTaskReference: null,
    });

    it("should update a notification task", async () => {
      const newMockTask = Object.assign(new NotificationTask(), mockTask, { status: NotificationTaskStatus.DRAFT });
      jest.spyOn(mockNotificationTaskRepository, "findOneBy").mockResolvedValue(newMockTask);
      jest.spyOn(newMockTask, "transition");

      const cloudTaskCallback = jest.fn().mockResolvedValue("cloud-task-name");

      await service.updateNotificationAfterCallback(taskId, updatedBy, updateDto, cloudTaskCallback);

      expect(mockNotificationTaskRepository.findOneBy).toHaveBeenCalledWith({ id: taskId });
      expect(newMockTask.transition).toHaveBeenCalled();
      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.manager.save).toHaveBeenCalled();
      expect(cloudTaskCallback).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
    });

    it("should throw an error when task is not found", async () => {
      jest.spyOn(mockNotificationTaskRepository, "findOneBy").mockResolvedValue(null);

      await expect(service.updateNotificationAfterCallback(taskId, updatedBy, updateDto, jest.fn())).rejects.toThrow();

      expect(mockQueryRunner.startTransaction).not.toHaveBeenCalled();
    });

    it("should throw an error when status transition is not allowed", async () => {
      const mockTaskWithInvalidTransition: NotificationTask = Object.assign(new NotificationTask(), mockTask, {
        status: NotificationTaskStatus.PENDING_DELETION,
      });

      jest.spyOn(mockNotificationTaskRepository, "findOneBy").mockResolvedValue(mockTaskWithInvalidTransition);
      jest.spyOn(mockTaskWithInvalidTransition, "canTransition").mockReturnValue(false);

      await expect(service.updateNotificationAfterCallback(taskId, updatedBy, updateDto, jest.fn())).rejects.toThrow();

      expect(mockTaskWithInvalidTransition.canTransition).toHaveBeenCalled();
      expect(mockQueryRunner.startTransaction).not.toHaveBeenCalled();
    });
  });

  describe("deleteNotificationAfterCallback", () => {
    const taskId = "task-id";
    const deletedBy = "test-user";

    const mockTask: NotificationTask = Object.assign(new NotificationTask(), {
      id: taskId,
      status: NotificationTaskStatus.SCHEDULED,
      payload: {},
      cloudTaskReference: "cloud-task-reference",
    });

    it("should delete a notification task", async () => {
      jest.spyOn(mockTask, "forDeletion");
      jest.spyOn(mockTask, "delete");
      jest.spyOn(mockNotificationTaskRepository, "findOneBy").mockResolvedValue(mockTask);

      const cloudTaskCallback = jest.fn().mockResolvedValue("deleted");

      await service.deleteNotificationAfterCallback(taskId, deletedBy, cloudTaskCallback);

      expect(mockNotificationTaskRepository.findOneBy).toHaveBeenCalledWith({ id: taskId });
      expect(mockTask.forDeletion).toHaveBeenCalled();
      expect(mockTask.delete).toHaveBeenCalled();
      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.manager.save).toHaveBeenCalledTimes(2);
      expect(cloudTaskCallback).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
    });

    it("should be idempotent when task is already deleted", async () => {
      const alreadyDeletedTask = Object.assign(new NotificationTask(), mockTask, {
        status: NotificationTaskStatus.DELETED,
      });

      jest.spyOn(mockNotificationTaskRepository, "findOneBy").mockResolvedValue(alreadyDeletedTask);

      await service.deleteNotificationAfterCallback(taskId, deletedBy, jest.fn());

      expect(mockQueryRunner.startTransaction).not.toHaveBeenCalled();
      expect(mockQueryRunner.manager.save).not.toHaveBeenCalled();
    });

    it("should throw an error when task is not found", async () => {
      jest.spyOn(mockNotificationTaskRepository, "findOneBy").mockResolvedValue(null);

      await expect(service.deleteNotificationAfterCallback(taskId, deletedBy, jest.fn())).rejects.toThrow();

      expect(mockQueryRunner.startTransaction).not.toHaveBeenCalled();
    });

    it("should not perform any operation when cloud task was already deleted", async () => {
      const taskWithoutCloudRef = Object.assign(new NotificationTask(), mockTask, {
        cloudTaskReference: null,
        status: NotificationTaskStatus.DELETED,
      });

      jest.spyOn(mockTask, "forDeletion");
      jest.spyOn(mockTask, "delete");
      jest.spyOn(mockNotificationTaskRepository, "findOneBy").mockResolvedValue(taskWithoutCloudRef);

      const cloudTaskCallback = jest.fn();

      await service.deleteNotificationAfterCallback(taskId, deletedBy, cloudTaskCallback);

      expect(mockQueryRunner.startTransaction).not.toHaveBeenCalled();
      expect(mockQueryRunner.rollbackTransaction).not.toHaveBeenCalled();
      expect(mockTask.forDeletion).not.toHaveBeenCalled();
      expect(cloudTaskCallback).not.toHaveBeenCalled();
      expect(mockTask.delete).not.toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).not.toHaveBeenCalled();
    });
  });

  describe("getNotificationTasks", () => {
    it("should return paginated notification tasks", async () => {
      const mockTasks: NotificationTask[] = [
        Object.assign(new NotificationTask(), { id: "task-1", name: "Task 1" }),
        Object.assign(new NotificationTask(), { id: "task-2", name: "Task 2" }),
      ];

      jest.spyOn(mockNotificationTaskRepository, "findAndCount").mockResolvedValue([mockTasks, mockTasks.length]);

      const result = await service.getNotificationTasks({
        page: 1,
        pageSize: 10,
        status: NotificationTaskStatus.SCHEDULED,
        sortBy: NotificationTaskSortKey.CREATED_AT,
        sort: NotificationTaskSortOrder.ASC,
      });

      expect(result).toEqual({
        data: mockTasks,
        pagination: {
          count: mockTasks.length,
          page: 1,
          pageSize: 10,
        },
      });
      expect(mockNotificationTaskRepository.findAndCount).toHaveBeenCalledWith(
        expect.objectContaining({ where: { status: NotificationTaskStatus.SCHEDULED } }),
      );
    });

    it("should handle errors when fetching tasks", async () => {
      jest.spyOn(mockNotificationTaskRepository, "findAndCount").mockRejectedValue(new Error("Database error"));

      await expect(service.getNotificationTasks()).rejects.toThrow("Failed to fetch notification tasks");
      expect(mockNotificationTaskRepository.findAndCount).toHaveBeenCalled();
    });
  });
});

import { Test, TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";

import Merchant from "@nest/modules/database/entities/merchant.entity";
import { NotificationUserType } from "@nest/modules/database/entities/notificationTask.entity";
import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "@nest/modules/database/repositories/merchantNotificationToken.repository";
import { PreferredLanguageType } from "@nest/modules/identity/dto/user.dto";
import { MerchantNotificationService } from "@nest/modules/notification/merchant-notification/merchant-notification.service";
import { INotificationUserTokenParam } from "@nest/modules/notification/notification-method/notification-method.interface";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

describe("MerchantNotificationService", () => {
  let service: MerchantNotificationService;
  let mockMerchantRepository: MerchantRepository;
  let mockMerchantNotificationTokenRepository: MerchantNotificationTokenRepository;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MerchantNotificationService,
        {
          provide: LoggerServiceAdapter,
          useValue: {
            info: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(MerchantRepository),
          useValue: {
            findDashMerchantsByPhoneNumbers: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(MerchantNotificationTokenRepository),
          useValue: {
            findDashMerchantByPhoneNumbers: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<MerchantNotificationService>(MerchantNotificationService);
    mockMerchantRepository = module.get<MerchantRepository>(getRepositoryToken(MerchantRepository));
    mockMerchantNotificationTokenRepository = module.get<MerchantNotificationTokenRepository>(
      getRepositoryToken(MerchantNotificationTokenRepository),
    );
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
    expect(mockMerchantRepository).toBeDefined();
    expect(mockMerchantNotificationTokenRepository).toBeDefined();
  });

  describe("getValidPushNotificationTokens", () => {
    it("should return valid push notification tokens for given phone numbers", async () => {
      const phoneNumbers = ["+85212345678", "+85287654321"];
      const mockUserTokens: INotificationUserTokenParam[] = [
        {
          userId: "1",
          token: "token-1",
          preferredLanguage: PreferredLanguageType.EN,
          tokenId: "token1",
          userType: NotificationUserType.USERS,
        },
        {
          userId: "2",
          token: "token-2",
          preferredLanguage: PreferredLanguageType.ZHHK,
          tokenId: "token2",
          userType: NotificationUserType.USERS,
        },
      ];

      jest.spyOn(service, "getValidPushNotificationTokens").mockResolvedValue(mockUserTokens);

      const result = await service.getValidPushNotificationTokens(phoneNumbers);

      expect(result).toEqual(mockUserTokens);
      expect(service.getValidPushNotificationTokens).toHaveBeenCalledWith(phoneNumbers);
    });

    it("should log and throw an error if no users found for phone numbers", async () => {
      const phoneNumbers = ["+85212345678", "+85287654321"];
      jest.spyOn(mockMerchantNotificationTokenRepository, "findDashMerchantByPhoneNumbers").mockImplementation(() => {
        throw new Error("No users found");
      });

      await expect(service.getValidPushNotificationTokens(phoneNumbers)).rejects.toThrow();
      expect(mockMerchantNotificationTokenRepository.findDashMerchantByPhoneNumbers).toHaveBeenCalledWith(phoneNumbers);
    });
  });

  describe("validateUserPhoneNumbers", () => {
    it("should not throw for valid phone numbers", async () => {
      const phoneNumbers = ["+85212345678", "+85287654321"];
      jest.spyOn(mockMerchantRepository, "findDashMerchantsByPhoneNumbers").mockResolvedValue([
        Object.assign(new Merchant(), {
          id: "1",
          phoneNumber: "+85212345678",
          preferredLanguage: PreferredLanguageType.EN,
        }),
        Object.assign(new Merchant(), {
          id: "2",
          phoneNumber: "+85287654321",
          preferredLanguage: PreferredLanguageType.ZHHK,
        }),
      ]);

      await expect(service.validateUserPhoneNumbers(phoneNumbers)).resolves.not.toThrow();
      expect(mockMerchantRepository.findDashMerchantsByPhoneNumbers).toHaveBeenCalledWith(phoneNumbers);
    });

    it("should throw for invalid phone numbers", async () => {
      const phoneNumbers = ["invalid-phone", "123"];
      jest.spyOn(mockMerchantRepository, "findDashMerchantsByPhoneNumbers").mockResolvedValue([]);

      await expect(service.validateUserPhoneNumbers(phoneNumbers)).rejects.toThrow();
      expect(mockMerchantRepository.findDashMerchantsByPhoneNumbers).toHaveBeenCalledWith(phoneNumbers);
    });
  });
});

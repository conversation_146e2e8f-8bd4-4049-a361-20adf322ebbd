import { Test, TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";

import { NotificationUserType } from "@nest/modules/database/entities/notificationTask.entity";
import User from "@nest/modules/database/entities/user.entity";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { UserNotificationTokenRepository } from "@nest/modules/database/repositories/userNotificationToken.repository";
import { PreferredLanguageType } from "@nest/modules/identity/dto/user.dto";
import { AppUserNotificationService } from "@nest/modules/notification/app-user-notification/app-user-notification.service";
import { INotificationUserTokenParam } from "@nest/modules/notification/notification-method/notification-method.interface";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

describe("AppUserNotificationService", () => {
  let service: AppUserNotificationService;
  let userNotificationTokenRepository: UserNotificationTokenRepository;
  let userRepository: UserRepository;

  beforeEach(async () => {
    jest.clearAllMocks();
    jest.resetAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AppUserNotificationService,
        {
          provide: LoggerServiceAdapter,
          useValue: {
            info: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserRepository),
          useValue: {
            findAppUsersByPhoneNumbers: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserNotificationTokenRepository),
          useValue: {
            findByPhoneNumbers: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AppUserNotificationService>(AppUserNotificationService);
    userNotificationTokenRepository = module.get<UserNotificationTokenRepository>(
      getRepositoryToken(UserNotificationTokenRepository),
    );
    userRepository = module.get<UserRepository>(getRepositoryToken(UserRepository));
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
    expect(userNotificationTokenRepository).toBeDefined();
  });

  describe("getValidPushNotificationTokens", () => {
    it("should return valid push notification tokens for given phone numbers", async () => {
      const phoneNumbers = ["+85212345678", "+85287654321"];
      const mockUserTokens: INotificationUserTokenParam[] = [
        {
          userId: "1",
          token: "token-1",
          preferredLanguage: PreferredLanguageType.EN,
          tokenId: "token1",
          userType: NotificationUserType.USERS,
        },
        {
          userId: "2",
          token: "token-2",
          preferredLanguage: PreferredLanguageType.ZHHK,
          tokenId: "token2",
          userType: NotificationUserType.USERS,
        },
      ];

      jest.spyOn(service, "getValidPushNotificationTokens").mockResolvedValue(mockUserTokens);

      const result = await service.getValidPushNotificationTokens(phoneNumbers);

      expect(result).toEqual(mockUserTokens);
      expect(service.getValidPushNotificationTokens).toHaveBeenCalledWith(phoneNumbers);
    });

    it("should log and throw an error if no users found for phone numbers", async () => {
      const phoneNumbers = ["+85212345678", "+85287654321"];
      jest.spyOn(userNotificationTokenRepository, "findByPhoneNumbers").mockImplementation(() => {
        throw new Error("No users found");
      });

      await expect(service.getValidPushNotificationTokens(phoneNumbers)).rejects.toThrow();
      expect(userNotificationTokenRepository.findByPhoneNumbers).toHaveBeenCalledWith(phoneNumbers);
    });
  });

  describe("validateUserPhoneNumbers", () => {
    it("should not throw for valid phone numbers", async () => {
      const phoneNumbers = ["+85212345678", "+85287654321"];
      jest.spyOn(userRepository, "findAppUsersByPhoneNumbers").mockResolvedValue([
        Object.assign(new User(), {
          id: "1",
          phoneNumber: "+85212345678",
          preferredLanguage: PreferredLanguageType.EN,
        }),
        Object.assign(new User(), {
          id: "2",
          phoneNumber: "+85287654321",
          preferredLanguage: PreferredLanguageType.ZHHK,
        }),
      ]);

      await expect(service.validateUserPhoneNumbers(phoneNumbers)).resolves.not.toThrow();
      expect(userRepository.findAppUsersByPhoneNumbers).toHaveBeenCalledWith(phoneNumbers);
    });

    it("should throw for invalid phone numbers", async () => {
      const phoneNumbers = ["invalid-phone", "123"];
      jest.spyOn(userRepository, "findAppUsersByPhoneNumbers").mockResolvedValue([]);

      await expect(service.validateUserPhoneNumbers(phoneNumbers)).rejects.toThrow();
      expect(userRepository.findAppUsersByPhoneNumbers).toHaveBeenCalledWith(phoneNumbers);
    });
  });
});

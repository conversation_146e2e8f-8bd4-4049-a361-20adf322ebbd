import NotificationTask, {
  NotificationTaskStatus,
  NotificationTaskType,
  NotificationUserType,
  NotificationTaskStatusError,
  NotificationTaskStateOrder,
} from "@nest/modules/database/entities/notificationTask.entity";

describe("NotificationTask Entity", () => {
  let task: NotificationTask;

  beforeEach(() => {
    task = new NotificationTask();
    task.id = "test-task-id";
    task.name = "Test Task";
    task.type = NotificationTaskType.PUSH;
    task.userType = NotificationUserType.USERS;
    task.status = NotificationTaskStatus.DRAFT;
    task.createdBy = "test-user";
    task.scheduledAt = new Date();
    task.payload = {};
  });

  describe("State Transitions", () => {
    it("should transition from DRAFT to SCHEDULED", () => {
      task.schedule();
      expect(task.status).toBe(NotificationTaskStatus.SCHEDULED);
    });

    it("should transition from SCHEDULED to PROCESSING", () => {
      task.status = NotificationTaskStatus.SCHEDULED;
      task.process();
      expect(task.status).toBe(NotificationTaskStatus.PROCESSING);
    });

    it("should transition from PROCESSING to COMPLETED", () => {
      task.status = NotificationTaskStatus.PROCESSING;
      task.complete();
      expect(task.status).toBe(NotificationTaskStatus.COMPLETED);
    });

    it("should transition from PROCESSING to FAILED", () => {
      task.status = NotificationTaskStatus.PROCESSING;
      task.fail("Test failure reason");
      expect(task.status).toBe(NotificationTaskStatus.FAILED);
      expect(task.failureReason).toBe("Test failure reason");
    });

    it("should transition to PENDING_DELETION", () => {
      task.forDeletion();
      expect(task.status).toBe(NotificationTaskStatus.PENDING_DELETION);
    });

    it("should transition from PENDING_DELETION to DELETED", () => {
      task.status = NotificationTaskStatus.PENDING_DELETION;
      task.delete();
      expect(task.status).toBe(NotificationTaskStatus.DELETED);
    });

    it("should throw error for invalid transition", () => {
      task.status = NotificationTaskStatus.COMPLETED;
      expect(() => task.process()).toThrow(NotificationTaskStatusError);
    });
  });

  describe("canTransition method", () => {
    it("should return true for valid transitions", () => {
      task.status = NotificationTaskStatus.DRAFT;
      expect(task.canTransition(NotificationTaskStatus.SCHEDULED)).toBe(true);
      expect(task.canTransition(NotificationTaskStatus.PENDING_DELETION)).toBe(true);
    });

    it("should return false for invalid transitions", () => {
      task.status = NotificationTaskStatus.DRAFT;
      expect(task.canTransition(NotificationTaskStatus.PROCESSING)).toBe(false);
      expect(task.canTransition(NotificationTaskStatus.COMPLETED)).toBe(false);
      expect(task.canTransition(NotificationTaskStatus.FAILED)).toBe(false);
      expect(task.canTransition(NotificationTaskStatus.DELETED)).toBe(false);
    });

    it("should not allow transitions from final states", () => {
      // Test COMPLETED state
      task.status = NotificationTaskStatus.COMPLETED;
      Object.values(NotificationTaskStatus).forEach((status) => {
        expect(task.canTransition(status)).toBe(false);
      });

      // Test FAILED state
      task.status = NotificationTaskStatus.FAILED;
      Object.values(NotificationTaskStatus).forEach((status) => {
        expect(task.canTransition(status)).toBe(false);
      });

      // Test DELETED state
      task.status = NotificationTaskStatus.DELETED;
      Object.values(NotificationTaskStatus).forEach((status) => {
        expect(task.canTransition(status)).toBe(false);
      });
    });
  });

  describe("transition method", () => {
    it("should update status for valid transitions", () => {
      task.status = NotificationTaskStatus.DRAFT;
      task.transition(NotificationTaskStatus.SCHEDULED);
      expect(task.status).toBe(NotificationTaskStatus.SCHEDULED);
    });

    it("should throw NotificationTaskStatusError for invalid transitions", () => {
      task.status = NotificationTaskStatus.DRAFT;
      expect(() => task.transition(NotificationTaskStatus.COMPLETED)).toThrow(NotificationTaskStatusError);

      const error = new NotificationTaskStatusError(
        task.id,
        NotificationTaskStatus.DRAFT,
        NotificationTaskStatus.COMPLETED,
      );
      expect(error.taskId).toBe(task.id);
      expect(error.currentState).toBe(NotificationTaskStatus.DRAFT);
      expect(error.newState).toBe(NotificationTaskStatus.COMPLETED);
      expect(error.message).toContain("Invalid state transition");
    });
  });

  describe("NotificationTaskStateOrder", () => {
    it("should define valid transitions for each state", () => {
      expect(NotificationTaskStateOrder[NotificationTaskStatus.DRAFT]).toContain(NotificationTaskStatus.SCHEDULED);
      expect(NotificationTaskStateOrder[NotificationTaskStatus.SCHEDULED]).toContain(NotificationTaskStatus.PROCESSING);
      expect(NotificationTaskStateOrder[NotificationTaskStatus.PROCESSING]).toContain(NotificationTaskStatus.COMPLETED);
      expect(NotificationTaskStateOrder[NotificationTaskStatus.PROCESSING]).toContain(NotificationTaskStatus.FAILED);
      expect(NotificationTaskStateOrder[NotificationTaskStatus.PENDING_DELETION]).toContain(
        NotificationTaskStatus.DELETED,
      );
    });

    it("should have empty arrays for final states", () => {
      expect(NotificationTaskStateOrder[NotificationTaskStatus.COMPLETED].length).toBe(0);
      expect(NotificationTaskStateOrder[NotificationTaskStatus.FAILED].length).toBe(0);
      expect(NotificationTaskStateOrder[NotificationTaskStatus.DELETED].length).toBe(0);
    });
  });
});

import { Test, TestingModule } from "@nestjs/testing";
import { DataSource, QueryRunner } from "typeorm";

import {
  CreateManyNotificationRequestDto,
  NotificationTaskRequestStatus,
  UpdateManyNotificationRequestDto,
} from "@nest/modules/cloud-task-notification-handler/dto/request.dto";
import NotificationTask, {
  NotificationTaskStatus,
  NotificationTaskType,
  NotificationUserType,
} from "@nest/modules/database/entities/notificationTask.entity";
import { NotificationTaskRepository } from "@nest/modules/database/repositories/notificationTask.repository";
import {
  NotificationTaskSortKey,
  NotificationTaskSortOrder,
} from "@nest/modules/notification/dto/notification-filters.dto";
import { NotificationRequestDto } from "@nest/modules/notification/dto/notification.dto";
import { NotificationManagerService } from "@nest/modules/notification/notification-manager.service";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

describe("NotificationManagerService - Additional Tests", () => {
  let service: NotificationManagerService;
  let mockNotificationTaskRepository: NotificationTaskRepository;
  let mockQueryRunner: QueryRunner;
  let mockLogger: LoggerServiceAdapter;

  beforeEach(async () => {
    jest.clearAllMocks();

    mockQueryRunner = {
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      rollbackTransaction: jest.fn(),
      release: jest.fn(),
      manager: {
        save: jest.fn(),
      },
    } as unknown as QueryRunner;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationManagerService,
        {
          provide: LoggerServiceAdapter,
          useValue: {
            info: jest.fn(),
            warn: jest.fn(),
            error: jest.fn(),
          },
        },
        {
          provide: NotificationTaskRepository,
          useValue: {
            create: jest.fn(),
            findOneBy: jest.fn(),
            findAndCount: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: {
            createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
          },
        },
      ],
    }).compile();

    service = module.get(NotificationManagerService);
    mockNotificationTaskRepository = module.get(NotificationTaskRepository);
    mockLogger = module.get(LoggerServiceAdapter);
  });

  describe("getNotificationTasks - Edge Cases", () => {
    it("should use default pagination values when not provided", async () => {
      const mockTasks: NotificationTask[] = [Object.assign(new NotificationTask(), { id: "task-1", name: "Task 1" })];

      jest.spyOn(mockNotificationTaskRepository, "findAndCount").mockResolvedValue([mockTasks, mockTasks.length]);

      await service.getNotificationTasks();

      expect(mockNotificationTaskRepository.findAndCount).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 0,
          take: 10,
          order: { createdAt: "DESC" },
        }),
      );
    });

    it("should handle empty results", async () => {
      jest.spyOn(mockNotificationTaskRepository, "findAndCount").mockResolvedValue([[], 0]);

      const result = await service.getNotificationTasks();

      expect(result).toEqual({
        data: [],
        pagination: {
          count: 0,
          page: 1,
          pageSize: 10,
        },
      });
    });

    it("should apply multiple filters correctly", async () => {
      const mockTasks: NotificationTask[] = [Object.assign(new NotificationTask(), { id: "task-1", name: "Task 1" })];

      jest.spyOn(mockNotificationTaskRepository, "findAndCount").mockResolvedValue([mockTasks, mockTasks.length]);

      await service.getNotificationTasks({
        page: 2,
        pageSize: 5,
        status: NotificationTaskStatus.SCHEDULED,
        type: NotificationTaskType.PUSH,
        sort: NotificationTaskSortOrder.ASC,
        sortBy: NotificationTaskSortKey.CREATED_AT,
      });

      expect(mockNotificationTaskRepository.findAndCount).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            status: NotificationTaskStatus.SCHEDULED,
            type: NotificationTaskType.PUSH,
          },
          skip: 5,
          take: 5,
          order: { createdAt: "ASC" },
        }),
      );
    });
  });

  describe("updateNotificationAfterCallback - Edge Cases", () => {
    const taskId = "task-id";
    const updatedBy = "test-user";

    const updateDto: UpdateManyNotificationRequestDto = {
      name: "Updated Notification",
      type: NotificationTaskType.PUSH,
      scheduledAt: new Date(),
      phoneNumbers: ["+85212345678"],
      notificationRequest: {
        titleEn: "Updated",
        titleHk: "更新",
        bodyEn: "Updated notification",
        bodyHk: "更新通知",
      },
      status: NotificationTaskRequestStatus.SCHEDULED,
      userType: NotificationUserType.USERS,
    };

    it("should update only specified fields", async () => {
      const mockTask: NotificationTask = Object.assign(new NotificationTask(), {
        id: taskId,
        name: "Original Name",
        type: NotificationTaskType.PUSH,
        status: NotificationTaskStatus.DRAFT,
        userType: NotificationUserType.USERS,
        scheduledAt: new Date("2023-01-01"),
        payload: {
          phoneNumbers: ["+85212345678"],
          notificationRequest: {
            titleEn: "Original",
            titleHk: "原始",
            bodyEn: "Original notification",
            bodyHk: "原始通知",
          },
        },
        canTransition: jest.fn().mockReturnValue(true),
        transition: jest.fn(),
      });

      jest.spyOn(mockNotificationTaskRepository, "findOneBy").mockResolvedValue(mockTask);
      jest.spyOn(mockQueryRunner.manager, "save").mockResolvedValue(mockTask);

      // Update only name and type
      const partialUpdateDto: UpdateManyNotificationRequestDto = {
        name: "Partial Update",
        type: NotificationTaskType.EMAIL,
      };

      await service.updateNotificationAfterCallback(taskId, updatedBy, partialUpdateDto, jest.fn());

      expect(mockTask.name).toBe("Partial Update");
      expect(mockTask.type).toBe(NotificationTaskType.EMAIL);
      expect(mockTask.transition).not.toHaveBeenCalled(); // No status change
      expect(mockQueryRunner.manager.save).toHaveBeenCalledWith(
        expect.objectContaining({
          name: "Partial Update",
          type: NotificationTaskType.EMAIL,
        }),
      );
    });

    it("should handle transaction errors during update", async () => {
      const mockTask: NotificationTask = Object.assign(new NotificationTask(), {
        id: taskId,
        status: NotificationTaskStatus.DRAFT,
        canTransition: jest.fn().mockReturnValue(true),
        transition: jest.fn(),
      });

      jest.spyOn(mockNotificationTaskRepository, "findOneBy").mockResolvedValue(mockTask);
      jest.spyOn(mockQueryRunner.manager, "save").mockRejectedValue(new Error("Database error"));

      await expect(service.updateNotificationAfterCallback(taskId, updatedBy, updateDto, jest.fn())).rejects.toThrow(
        "Database error",
      );

      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).not.toHaveBeenCalled();
    });
  });

  describe("deleteNotificationAfterCallback - Edge Cases", () => {
    const taskId = "task-id";
    const deletedBy = "test-user";

    it("should handle transaction errors during deletion", async () => {
      const mockTask: NotificationTask = Object.assign(new NotificationTask(), {
        id: taskId,
        status: NotificationTaskStatus.SCHEDULED,
        payload: {},
        cloudTaskReference: "cloud-task-reference",
        forDeletion: jest.fn(),
        delete: jest.fn(),
      });

      jest.spyOn(mockNotificationTaskRepository, "findOneBy").mockResolvedValue(mockTask);
      jest.spyOn(mockQueryRunner.manager, "save").mockRejectedValue(new Error("Database error"));

      await expect(service.deleteNotificationAfterCallback(taskId, deletedBy, jest.fn())).rejects.toThrow(
        "Database error",
      );

      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).not.toHaveBeenCalled();
    });

    it("should handle task without cloud task reference", async () => {
      const mockTask: NotificationTask = Object.assign(new NotificationTask(), {
        id: taskId,
        status: NotificationTaskStatus.SCHEDULED,
        payload: {},
        cloudTaskReference: null, // No cloud task reference
        forDeletion: jest.fn(),
        delete: jest.fn(),
      });

      jest.spyOn(mockNotificationTaskRepository, "findOneBy").mockResolvedValue(mockTask);
      jest.spyOn(mockQueryRunner.manager, "save").mockResolvedValue(mockTask);

      const cloudTaskCallback = jest.fn();

      await service.deleteNotificationAfterCallback(taskId, deletedBy, cloudTaskCallback);

      expect(mockTask.forDeletion).toHaveBeenCalled();
      expect(mockTask.delete).toHaveBeenCalled();
      expect(cloudTaskCallback).not.toHaveBeenCalled(); // Should not call callback when no reference
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
    });
  });

  describe("createNotificationTask - Edge Cases", () => {
    const phoneNumbers = ["+85212345678"];
    const notificationRequest: NotificationRequestDto = {
      titleEn: "Test",
      titleHk: "測試",
      bodyEn: "Test notification",
      bodyHk: "測試通知",
    };

    const createManyDto: CreateManyNotificationRequestDto = {
      phoneNumbers,
      notificationRequest,
      name: "Test Notification",
      type: NotificationTaskType.PUSH,
      userType: NotificationUserType.USERS,
      status: NotificationTaskRequestStatus.SCHEDULED,
      scheduledAt: new Date(),
    };

    it("should handle cloud task creation failure", async () => {
      const mockTask: NotificationTask = Object.assign(new NotificationTask(), {
        id: "task-id",
        name: "Test Notification",
        type: NotificationTaskType.PUSH,
        status: NotificationTaskStatus.SCHEDULED,
        userType: NotificationUserType.USERS,
        scheduledAt: new Date(),
        payload: {
          phoneNumbers,
          notificationRequest,
        },
      });

      jest.spyOn(mockNotificationTaskRepository, "create").mockReturnValue(mockTask);
      jest.spyOn(mockQueryRunner.manager, "save").mockResolvedValue(mockTask);

      // Cloud task callback that fails
      const cloudTaskCallback = jest.fn().mockRejectedValue(new Error("Cloud task creation failed"));

      await expect(service.createNotificationTask(createManyDto, "test-user", cloudTaskCallback)).rejects.toThrow();

      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it("should create task with all fields properly set", async () => {
      const scheduledDate = new Date("2023-05-15T10:00:00Z");
      const createDto: CreateManyNotificationRequestDto = {
        ...createManyDto,
        scheduledAt: scheduledDate,
      };

      const mockTask: NotificationTask = Object.assign(new NotificationTask(), {
        id: "task-id",
        name: "Test Notification",
        type: NotificationTaskType.PUSH,
        status: NotificationTaskStatus.SCHEDULED,
        userType: NotificationUserType.USERS,
        scheduledAt: scheduledDate,
        payload: {
          phoneNumbers,
          notificationRequest,
        },
      });

      jest.spyOn(mockNotificationTaskRepository, "create").mockReturnValue(mockTask);
      jest.spyOn(mockQueryRunner.manager, "save").mockResolvedValue(mockTask);

      const cloudTaskCallback = jest.fn().mockResolvedValue("cloud-task-name");

      await service.createNotificationTask(createDto, "test-user", cloudTaskCallback);

      expect(mockNotificationTaskRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          scheduledAt: scheduledDate,
        }),
      );
    });
  });
});

import { randomUUID } from "crypto";

import { Test, TestingModule } from "@nestjs/testing";

import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "@nest/modules/database/repositories/merchantNotificationToken.repository";
import { NotificationHistoryRepository } from "@nest/modules/database/repositories/notificationHistory.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { UserNotificationTokenRepository } from "@nest/modules/database/repositories/userNotificationToken.repository";
import { EmailNotificationService } from "@nest/modules/notification/notification-method/email-notification.service";
import { NotificationFactory } from "@nest/modules/notification/notification-method/notification.factory";
import { PushNotificationService } from "@nest/modules/notification/notification-method/push-notification.service";
import { SmsNotificationService } from "@nest/modules/notification/notification-method/sms-notification.service";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

import { NotificationUserType } from "../../../nestJs/modules/database/entities/notificationTask.entity";
import { NotificationTaskRepository } from "../../../nestJs/modules/database/repositories/notificationTask.repository";
import { AppUserNotificationService } from "../../../nestJs/modules/notification/app-user-notification/app-user-notification.service";
import { MerchantNotificationService } from "../../../nestJs/modules/notification/merchant-notification/merchant-notification.service";
import { UserNotificationFactory } from "../../../nestJs/modules/notification/user-notification.factory";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";

import "../../initTests";

describe("UserNotificationFactory", () => {
  let userNotificationFactory: UserNotificationFactory;
  let appUserNotificationService: AppUserNotificationService;
  let merchantNotificationService: MerchantNotificationService;
  let notificationTaskRepository: NotificationTaskRepository;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AppUserNotificationService,
        MerchantNotificationService,
        UserNotificationFactory,
        {
          provide: NotificationFactory,
          useValue: {
            notify: jest.fn(),
            notifyMany: jest.fn(),
          },
        },
        {
          provide: PushNotificationService,
          useValue: {
            notify: jest.fn(),
            notifyMany: jest.fn(),
          },
        },
        {
          provide: EmailNotificationService,
          useValue: {
            notify: jest.fn(),
            notifyMany: jest.fn(),
          },
        },
        {
          provide: SmsNotificationService,
          useValue: {
            notify: jest.fn(),
            notifyMany: jest.fn(),
          },
        },
        {
          provide: NotificationTaskRepository,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: UserRepository,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: UserNotificationTokenRepository,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: MerchantRepository,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: MerchantNotificationTokenRepository,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: NotificationHistoryRepository,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: LoggerServiceAdapter,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            info: jest.fn(),
          },
        },
      ],
    }).compile();

    appUserNotificationService = module.get(AppUserNotificationService);
    merchantNotificationService = module.get(MerchantNotificationService);
    notificationTaskRepository = module.get(NotificationTaskRepository);

    userNotificationFactory = module.get(UserNotificationFactory);
  });

  describe("getSender", () => {
    it("should return appUserNotificationService for USERS type", () => {
      const result = userNotificationFactory.getSender(NotificationUserType.USERS);
      expect(result).toBe(appUserNotificationService);
    });

    it("should return merchantNotificationService for MERCHANTS type", () => {
      const result = userNotificationFactory.getSender(NotificationUserType.MERCHANTS);
      expect(result).toBe(merchantNotificationService);
    });

    it("should throw an error for invalid user type", () => {
      const invalidType = "INVALID" as NotificationUserType;
      expect(() => userNotificationFactory.getSender(invalidType)).toThrow(
        errorBuilder.notification.invalidUserType(invalidType),
      );
    });
  });

  describe("getValidatorByTaskId", () => {
    it("should return appUserNotificationService when task is for USERS", async () => {
      const taskId = randomUUID();
      const mockTask = {
        id: taskId,
        userType: NotificationUserType.USERS,
      };

      (notificationTaskRepository.findOne as jest.Mock).mockResolvedValue(mockTask);

      const result = await userNotificationFactory.getValidatorByTaskId(taskId);

      expect(notificationTaskRepository.findOne).toHaveBeenCalledWith({
        where: { id: taskId },
      });
      expect(result).toBe(appUserNotificationService);
    });

    it("should return merchantNotificationService when task is for MERCHANTS", async () => {
      const taskId = randomUUID();
      const mockTask = {
        id: taskId,
        userType: NotificationUserType.MERCHANTS,
      };

      (notificationTaskRepository.findOne as jest.Mock).mockResolvedValue(mockTask);

      const result = await userNotificationFactory.getValidatorByTaskId(taskId);

      expect(notificationTaskRepository.findOne).toHaveBeenCalledWith({
        where: { id: taskId },
      });
      expect(result).toBe(merchantNotificationService);
    });

    it("should throw an error when task is not found", async () => {
      const taskId = randomUUID();

      (notificationTaskRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(userNotificationFactory.getValidatorByTaskId(taskId)).rejects.toThrow(
        errorBuilder.notification.taskNotFound(taskId),
      );

      expect(notificationTaskRepository.findOne).toHaveBeenCalledWith({
        where: { id: taskId },
      });
    });
  });

  describe("getValidator", () => {
    it("should return appUserNotificationService for USERS type", () => {
      const result = userNotificationFactory.getValidator(NotificationUserType.USERS);
      expect(result).toBe(appUserNotificationService);
    });

    it("should return merchantNotificationService for MERCHANTS type", () => {
      const result = userNotificationFactory.getValidator(NotificationUserType.MERCHANTS);
      expect(result).toBe(merchantNotificationService);
    });

    it("should throw an error for invalid user type", () => {
      const invalidType = "INVALID" as NotificationUserType;
      expect(() => userNotificationFactory.getValidator(invalidType)).toThrow(
        errorBuilder.notification.invalidUserType(invalidType),
      );
    });
  });
});

import { Request } from "express";

import { OperatingArea } from "@legacy/model/meter";

import { MerchantDriverController } from "../../../../nestJs/modules/merchant/merchantDriver/merchantDriver.controller";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import {
  dataFirestoreMock,
  docFirestoreMock,
  getFirestoreMock,
} from "../../../utils/firebase-admin/firestoreMock.specs.utils";
import { newTestDriverController } from "../../../utils/services/TestServices.specs.utils";

import "../../../initTests";

describe("driver.getMeterVehicleData.controller", () => {
  let controller: MerchantDriverController;

  beforeEach(() => {
    controller = newTestDriverController();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("getMeterVehicleData", () => {
    it("should return the correct response", async () => {
      dataFirestoreMock.mockImplementationOnce(() => ({
        settings: { vehicle: { some: "data" }, operatingArea: OperatingArea.URBAN, vehicleId: "abc" },
      }));
      const result = await controller.getMeterSettingsData("123", { user: {} } as Request);

      expect(result).toEqual({
        isDashMeter: false,
        vehicle: { some: "data" },
        operatingArea: OperatingArea.URBAN,
        vehicleId: "abc",
      });

      expect(docFirestoreMock).toHaveBeenCalledWith("123");
      expect(getFirestoreMock).toHaveBeenCalledWith();
    });

    it("should return null for operatingArea if not available", async () => {
      dataFirestoreMock.mockImplementationOnce(() => ({
        settings: { vehicle: { some: "data" }, vehicleId: "abc" },
      }));
      const result = await controller.getMeterSettingsData("123", { user: {} } as Request);

      expect(result).toEqual({
        isDashMeter: false,
        vehicle: { some: "data" },
        operatingArea: null,
        vehicleId: "abc",
      });

      expect(docFirestoreMock).toHaveBeenCalledWith("123");
      expect(getFirestoreMock).toHaveBeenCalledWith();
    });

    it("should throw an error when there is no meter", async () => {
      dataFirestoreMock.mockImplementationOnce(() => undefined);
      await expect(controller.getMeterSettingsData("123", { user: {} } as Request)).rejects.toThrow(
        errorBuilder.meter.notFound("123"),
      );

      expect(docFirestoreMock).toHaveBeenCalledWith("123");
      expect(getFirestoreMock).toHaveBeenCalledWith();
    });

    it("should throw an error when there is no user", async () => {
      dataFirestoreMock.mockImplementationOnce(() => undefined);
      await expect(controller.getMeterSettingsData("123", { user: undefined } as Request)).rejects.toThrow(
        errorBuilder.user.missing(),
      );

      expect(docFirestoreMock).not.toHaveBeenCalled();
      expect(getFirestoreMock).not.toHaveBeenCalled();
    });
  });
});

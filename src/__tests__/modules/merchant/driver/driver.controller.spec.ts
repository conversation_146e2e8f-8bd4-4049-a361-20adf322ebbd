import { DocumentSnapshot } from "firebase-admin/firestore";
import { Change, FirestoreEvent } from "firebase-functions/v2/firestore";
import { InsertResult } from "typeorm";

import Merchant from "../../../../nestJs/modules/database/entities/merchant.entity";
import { MerchantMetadataTrip } from "../../../../nestJs/modules/merchant/merchantDriver/dto/merchantMetadataTrip.dto";
import { MerchantDriverController } from "../../../../nestJs/modules/merchant/merchantDriver/merchantDriver.controller";
import { DriverService } from "../../../../nestJs/modules/merchant/merchantDriver/merchantDriver.service";
import { PubSubService } from "../../../../nestJs/modules/pubsub/pubsub.service";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import FakeLoggerService from "../../../utils/fakeLogger.service.specs";
import FakeDriverService, {
  driverTripChangeMock,
  ingestFromBucketFileMock,
  upsertMerchantsMock,
} from "../../../utils/services/FakeDriverService.specs.utils";
import FakePubSubService from "../../../utils/services/FakePubSubService.specs.utils";
import { newTestMessageTeamsService } from "../../../utils/services/TestServices.specs.utils";

describe("driver.controller", () => {
  let service: DriverService = new FakeDriverService() as unknown as DriverService;
  const pubsubService = new FakePubSubService() as unknown as PubSubService;
  let controller: MerchantDriverController = new MerchantDriverController(
    service,
    FakeLoggerService,
    newTestMessageTeamsService(),
    pubsubService,
  );

  beforeEach(() => {
    jest.clearAllMocks();

    service = new FakeDriverService() as unknown as DriverService;
    controller = new MerchantDriverController(service, FakeLoggerService, newTestMessageTeamsService(), pubsubService);

    ingestFromBucketFileMock.mockImplementation(() => {
      return Promise.resolve({} as unknown as InsertResult);
    });
  });

  describe("updateMerchant", () => {
    it("should call the saveMerchant on the service when the data is correct", async () => {
      await controller.updateMerchant({
        params: { driverId: "+85211111111" },
        data: {
          after: {
            data: () => ({
              phone_number: "fake!",
              name: "Tony Stark",
              driver_license: "J.A.R.V.I.S.",
            }),
          },
          before: {
            data: () => ({}),
          },
        },
      } as unknown as FirestoreEvent<Change<DocumentSnapshot>, { driverId: string }>);

      expect(upsertMerchantsMock).toHaveBeenCalledTimes(1);
      expect(upsertMerchantsMock).toHaveBeenCalledWith([
        expect.objectContaining<Partial<Merchant>>({
          phoneNumber: "+85211111111",
          name: "Tony Stark",
          metadata: expect.objectContaining<Partial<MerchantMetadataTrip>>({ driverLicense: "J.A.R.V.I.S." }),
        }),
      ]);
    });

    it("should throw an error when there is no data", async () => {
      expect(
        controller.updateMerchant({
          params: { driverId: "+85211111111" },
          data: {
            after: {
              data: () => undefined,
            },
          },
        } as unknown as FirestoreEvent<Change<DocumentSnapshot>, { driverId: string }>),
      ).rejects.toThrowError(errorBuilder.merchant.driver.updatedNoData());
    });
  });

  describe("driverTripChange", () => {
    it("should call the driverTripChange on the service when the data is correct", async () => {
      await controller.driverTripChanged({
        params: { driverId: "+85211111111", sessionId: "session-1", tripId: "trip-1" },
        data: {
          after: {
            data: () => ({
              adjustment: 20,
              meter_software_version: "4.48",
            }),
          },
          before: {
            data: () => ({
              adjustment: 15,
            }),
          },
        },
      } as unknown as FirestoreEvent<Change<DocumentSnapshot>, { driverId: string; sessionId: string; tripId: string }>);

      expect(driverTripChangeMock).toHaveBeenCalledTimes(1);
      expect(driverTripChangeMock).toHaveBeenCalledWith(
        "+85211111111",
        "session-1",
        {
          adjustment: 20,
          meter_software_version: "4.48",
        },
        {
          adjustment: 15,
        },
      );
    });

    it("should throw an error when there is no data", async () => {
      expect(
        controller.driverTripChanged({
          params: { driverId: "+85211111111", tripId: "trip-1", sessionId: "session-1" },
          data: {
            after: {
              data: () => undefined,
            },
            before: {
              data: () => undefined,
            },
          },
        } as unknown as FirestoreEvent<Change<DocumentSnapshot>, { driverId: string; sessionId: string; tripId: string }>),
      ).rejects.toThrowError(
        errorBuilder.merchant.driver.trip.tripChangedNoData("+85211111111", "session-1", "trip-1"),
      );
    });
  });
});

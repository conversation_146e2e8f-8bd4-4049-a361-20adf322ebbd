import { randomUUID } from "crypto";

import { Cache } from "cache-manager";
import { DecodedIdToken } from "firebase-admin/auth";
import { Timestamp } from "firebase-admin/firestore";
import { InsertResult } from "typeorm";

import { DriverDocument, NotificationDocument } from "@nest/modules/appDatabase/documents/driver.document";
import MerchantNotificationToken from "@nest/modules/database/entities/merchantNotificationToken.entity";
import { WebhookRepository } from "@nest/modules/database/repositories/webhook.repository";
import { WebhookService } from "@nest/modules/webhook/webhook.service";
import FakeCache from "@tests/utils/services/FakeCache.specs.utils";
import FakeWebhookService from "@tests/utils/services/FakeWebhookService.specs.utils";
import {
  newTestFcmService,
  newTestHailingService,
  newTestTripService,
} from "@tests/utils/services/TestServices.specs.utils";

import { AppDatabaseService } from "../../../../nestJs/modules/appDatabase/appDatabase.service";
import Merchant from "../../../../nestJs/modules/database/entities/merchant.entity";
import { MerchantRepository } from "../../../../nestJs/modules/database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "../../../../nestJs/modules/database/repositories/merchantNotificationToken.repository";
import { TxRepository } from "../../../../nestJs/modules/database/repositories/tx.repository";
import { DriverService } from "../../../../nestJs/modules/merchant/merchantDriver/merchantDriver.service";
import { StorageService } from "../../../../nestJs/modules/storage/storage.service";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import { UtilsService } from "../../../../nestJs/modules/utils/utils.service";
import decodedIdTokenMock from "../../../mockData/firebase/decodedIdToken.mock";
import { fakeTx } from "../../../utils/fakeData/fakeDataTx.specs.utils";
import FakeLoggerService from "../../../utils/fakeLogger.service.specs";
import MockAppDatabaseService, {
  addMock,
  findOneByIdMock,
  runTransactionMock,
  saveSessionTripMock,
  saveTripMock,
  updateSessionTripMock,
  updateTripMock,
} from "../../../utils/services/FakeAppDatabaseService.specs.utils";
import FakeRepository, {
  mockFind,
  mockFindOne,
  mockSave,
  mockUpsert,
} from "../../../utils/services/FakeRepository.specs.utils";
import FakeStorageService, { readFileFromBucketMock } from "../../../utils/services/FakeStorageService.specs.utils";

const getNewDriverService = () => {
  return new DriverService(
    new FakeStorageService() as unknown as StorageService,
    new FakeRepository() as unknown as MerchantRepository,
    new FakeRepository() as unknown as TxRepository,
    new FakeRepository() as unknown as MerchantNotificationTokenRepository,
    new MockAppDatabaseService() as unknown as AppDatabaseService,
    FakeLoggerService,
    new UtilsService(),
    newTestHailingService(),
    newTestTripService(),
    newTestFcmService(),
    new FakeCache() as unknown as Cache,
    new FakeWebhookService() as unknown as WebhookService,
    new FakeRepository() as unknown as WebhookRepository,
  );
};

describe("driver.service", () => {
  let service = getNewDriverService();

  beforeEach(() => {
    jest.resetAllMocks();
    service = getNewDriverService();

    mockSave.mockImplementation(() => {
      return Promise.resolve({} as unknown as InsertResult);
    });

    mockUpsert.mockImplementation(() => {
      return Promise.resolve({} as unknown as InsertResult);
    });

    readFileFromBucketMock.mockImplementation(() => {
      return Promise.resolve(
        Buffer.from(JSON.stringify({ meta: {}, data: { "+85211111111": { phone_number: "+85211111111" } } })),
      );
    });

    runTransactionMock.mockImplementation(async (transactionFunction) => {
      await transactionFunction({
        get: jest.fn().mockResolvedValue({ data: () => null }),
        set: jest.fn(),
      } as unknown as FirebaseFirestore.Transaction);
    });
  });

  describe("ingestFromBucketFile", () => {
    it("should return the insert data and call the mocks when data is correct", async () => {
      expect(await service.ingestFromBucketFile({ bucketName: "bucket-1", fileName: "file-a" })).toEqual({});

      expect(readFileFromBucketMock).toHaveBeenCalledTimes(1);
      expect(readFileFromBucketMock).toHaveBeenCalledWith("bucket-1", "file-a");
      expect(mockUpsert).toHaveBeenCalledWith(
        [expect.objectContaining({ phoneNumber: "+85211111111" })],
        ["phoneNumber", "platformMerchantType"],
      );
    });

    it("should throw an error when the file content is not valid", async () => {
      readFileFromBucketMock.mockImplementation(() => {
        return Promise.resolve(Buffer.from(JSON.stringify({})));
      });

      expect(service.ingestFromBucketFile({ bucketName: "bucket-1", fileName: "file-a" })).rejects.toThrowError(
        errorBuilder.validation.failed("'data' is required"),
      );
    });
  });

  describe("upsertMerchants", () => {
    it("should return the insert data and call the mocks when data is correct", async () => {
      expect(
        await service.upsertMerchants([Merchant.fromJson({ phone_number: "+85211111111", name: "name" })]),
      ).toEqual({});

      expect(mockUpsert).toHaveBeenCalledWith(
        [expect.objectContaining({ phoneNumber: "+85211111111", name: "name" })],
        ["phoneNumber", "platformMerchantType"],
      );
    });
  });

  describe("copyToDriverCollectionInFirStore", () => {
    it("should save one trip to driver when data is correct", async () => {
      const expiresAt = new Date();
      const metadata = { ...fakeTx.metadata, tripEnd: Timestamp.fromDate(new Date("2023-05-22T22:49:23.000Z")) };
      const tx = { ...fakeTx, metadata: metadata };
      mockFindOne.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve(tx));
      });
      await service.copyToDriverCollectionInFireStore({
        txId: "07aa2173-a3d9-45aa-be74-61e359761766",
        expiresAt: expiresAt,
      });
      expect(saveTripMock).toBeCalledWith(expect.any(Object), "07aa2173-a3d9-45aa-be74-61e359761766", tx, expiresAt);
      expect(saveSessionTripMock).toBeCalledWith(
        expect.any(Object),
        "07aa2173-a3d9-45aa-be74-61e359761766",
        tx,
        expiresAt,
      );
      expect(updateTripMock).not.toBeCalled();
      expect(updateSessionTripMock).not.toBeCalled();
    });

    it("should update one trip to driver when data is correct", async () => {
      mockFindOne.mockImplementationOnce(() => {
        const metadata = { ...fakeTx.metadata, tripEnd: Timestamp.fromDate(new Date("2023-05-22T22:49:23.000Z")) };
        const tx = { ...fakeTx, metadata: metadata };
        return new Promise((resolve) => resolve(tx));
      });
      await service.copyToDriverCollectionInFireStore({
        txId: "07aa2173-a3d9-45aa-be74-61e359761766",
      });
      expect(saveTripMock).not.toBeCalled();
      expect(updateTripMock).toBeCalledWith(
        expect.any(Object),
        "07aa2173-a3d9-45aa-be74-61e359761766",
        fakeTx.adjustment,
        fakeTx.payoutStatus,
      );
      expect(saveSessionTripMock).not.toBeCalled();
      expect(updateSessionTripMock).toBeCalledWith(
        expect.any(Object),
        "07aa2173-a3d9-45aa-be74-61e359761766",
        fakeTx.adjustment,
        fakeTx.payoutStatus,
      );
    });

    it("should throw error when tx is not found", async () => {
      mockFindOne.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve(undefined));
      });

      await expect(
        service.copyToDriverCollectionInFireStore({
          txId: "07aa2173-a3d9-45aa-be74-61e359761766",
        }),
      ).rejects.toThrow(errorBuilder.transaction.notFound("07aa2173-a3d9-45aa-be74-61e359761766"));
    });

    it("should updated multiple trips to driver when data is correct", async () => {
      mockFind.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve([fakeTx, fakeTx, fakeTx]));
      });
      await service.copyToDriverCollectionInFireStore({
        txIds: [
          "07aa2173-a3d9-45aa-be74-61e359761766",
          "07aa2173-a3d9-45aa-be74-61e359761767",
          "07aa2173-a3d9-45aa-be74-61e359761768",
        ],
      });
      expect(saveTripMock).not.toBeCalled();
      expect(updateTripMock).toBeCalledTimes(3);
      expect(saveSessionTripMock).not.toBeCalled();
      expect(updateSessionTripMock).toBeCalledTimes(3);
    });
  });

  describe("upsertNotificationToken", () => {
    it("should upsert the notification token", async () => {
      const merchant = new Merchant();
      merchant.id = "merchant123-db";
      const user: DecodedIdToken = {
        ...decodedIdTokenMock,
        phone_number: "+852-12345678",
      };
      mockFindOne.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve(merchant));
      });
      const result = await service.upsertNotificationToken(
        {
          token: "456",
        },
        user,
      );

      expect(result).toEqual({});

      expect(mockUpsert).toHaveBeenCalledWith(
        { token: "456", lastUpdateDate: expect.any(Date), merchant: { id: "merchant123-db" } },
        ["token"],
      );
    });
  });

  describe("pushNotification", () => {
    it("should push notification and save to firestore successfully", async () => {
      const merchant = new Merchant();
      merchant.id = randomUUID();
      merchant.phoneNumber = "+85212345678";
      mockFindOne.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve(merchant));
      });
      const merchantNotificationToken = new MerchantNotificationToken();
      merchantNotificationToken.id = randomUUID();
      merchantNotificationToken.merchant = merchant;
      mockFind.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve([merchantNotificationToken]));
      });
      const mockPushNotification = jest.fn();
      service["fcmService"].pushNotificationToSpecificDevicesWithToken = mockPushNotification;
      mockPushNotification.mockImplementation(() => {
        return Promise.resolve();
      });

      findOneByIdMock.mockImplementationOnce(() => new Promise((resolve) => resolve(new DriverDocument())));
      addMock.mockImplementationOnce(() => new Promise((resolve) => resolve(new NotificationDocument())));
      const result = await service.pushNotification({
        driverId: "+85212345678",
        title: "title",
        body: "body",
        persistInInbox: true,
      });

      expect(result).toEqual({
        driverId: "+85212345678",
        isPushNotificationSuccess: true,
        willPersistInInbox: true,
        isPersistInInboxSuccess: true,
      });
    });

    it("should push notification and not save to firestore", async () => {
      const merchant = new Merchant();
      merchant.id = randomUUID();
      merchant.phoneNumber = "+85212345679";
      mockFindOne.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve(merchant));
      });
      const merchantNotificationToken = new MerchantNotificationToken();
      merchantNotificationToken.id = randomUUID();
      merchantNotificationToken.merchant = merchant;
      mockFind.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve([merchantNotificationToken]));
      });
      const mockPushNotification = jest.fn();
      service["fcmService"].pushNotificationToSpecificDevicesWithToken = mockPushNotification;
      mockPushNotification.mockImplementation(() => {
        return Promise.resolve();
      });

      const result = await service.pushNotification({
        driverId: "+85212345679",
        title: "title",
        body: "body",
        persistInInbox: false,
      });

      expect(result).toEqual({
        driverId: "+85212345679",
        isPushNotificationSuccess: true,
        willPersistInInbox: false,
      });
      expect(findOneByIdMock).not.toBeCalled();
      expect(addMock).not.toBeCalled();
    });

    it("cannot push notification when driver is not found", async () => {
      mockFindOne.mockImplementationOnce(() => {
        return new Promise((resolve) => resolve(undefined));
      });

      const result = await service.pushNotification({
        driverId: "+85212345679",
        title: "title",
        body: "body",
        persistInInbox: false,
      });

      expect(result).toEqual({
        driverId: "+85212345679",
        isPushNotificationSuccess: false,
        willPersistInInbox: false,
      });
    });
  });
});

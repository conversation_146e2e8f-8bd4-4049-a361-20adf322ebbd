import { Request } from "express";

import { OperatingArea } from "@legacy/model/meter";

import { MerchantDriverController } from "../../../../nestJs/modules/merchant/merchantDriver/merchantDriver.controller";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import {
  dataFirestoreMock,
  docFirestoreMock,
  getFirestoreMock,
  setFirestoreMock,
} from "../../../utils/firebase-admin/firestoreMock.specs.utils";
import { newTestDriverController } from "../../../utils/services/TestServices.specs.utils";

import "../../../initTests";

describe("driver.createMeter.controller", () => {
  let controller: MerchantDriverController;

  beforeEach(() => {
    controller = newTestDriverController();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createMeter", () => {
    it("should return the correct response", async () => {
      dataFirestoreMock.mockImplementationOnce(() => undefined);
      setFirestoreMock.mockImplementationOnce(() => ({ some: "data" }));
      const result = await controller.createMeter(
        "123",
        {
          make: "Audi",
          model: "R8",
          wheelchairRamp: true,
          operatingArea: OperatingArea.URBAN,
          vehicleId: "abc",
          vehicleLicenseImage: "image_url",
        },
        { user: { uid: "usedId" } } as Request,
      );

      expect(result).toEqual({
        createdAt: expect.any(Date),
        createdBy: "usedId",
        id: "123",
        licensePlate: "123",
        settings: {
          isDashMeter: false,
          vehicle: {
            make: "Audi",
            model: "R8",
            wheelchairRamp: true,
          },
          operatingArea: OperatingArea.URBAN,
          vehicleId: "abc",
          vehicleLicenseImage: "image_url",
        },
      });

      expect(docFirestoreMock).toHaveBeenCalledTimes(2);
      expect(docFirestoreMock).toHaveBeenNthCalledWith(1, "123");
      expect(docFirestoreMock).toHaveBeenNthCalledWith(2, "123");
      expect(getFirestoreMock).toHaveBeenCalledWith();
      expect(setFirestoreMock).toHaveBeenCalledWith(
        {
          licensePlate: "123",
          settings: {
            isDashMeter: false,
            vehicle: {
              make: "Audi",
              model: "R8",
              wheelchairRamp: true,
            },
            operatingArea: OperatingArea.URBAN,
            vehicleId: "abc",
            vehicleLicenseImage: "image_url",
          },
          id: "123",
          createdBy: "usedId",
          createdAt: expect.any(Date),
        },
        { merge: true },
      );
    });

    it("should throw an error when there is a meter", async () => {
      dataFirestoreMock.mockImplementationOnce(() => ({ settings: { vehicle: { some: "data" } } }));
      await expect(
        controller.createMeter(
          "123",
          {
            make: "string",
            model: "string",
            wheelchairRamp: true,
            operatingArea: OperatingArea.URBAN,
            vehicleId: "abc",
            vehicleLicenseImage: "image_url",
          },
          { user: { uid: "usedId" } } as Request,
        ),
      ).rejects.toThrow(errorBuilder.meter.alreadyExist("123"));

      expect(docFirestoreMock).toHaveBeenCalledTimes(1);
      expect(docFirestoreMock).toHaveBeenCalledWith("123");
      expect(getFirestoreMock).toHaveBeenCalledWith();
      expect(setFirestoreMock).not.toHaveBeenCalled();
    });

    it("should throw an error when there is no user", async () => {
      await expect(
        controller.createMeter(
          "123",
          {
            make: "string",
            model: "string",
            wheelchairRamp: true,
            operatingArea: OperatingArea.URBAN,
            vehicleId: "abc",
            vehicleLicenseImage: "image_url",
          },
          { user: undefined } as Request,
        ),
      ).rejects.toThrow(errorBuilder.user.missing());

      expect(docFirestoreMock).not.toHaveBeenCalled();
      expect(getFirestoreMock).not.toHaveBeenCalled();
      expect(setFirestoreMock).not.toHaveBeenCalled();
    });
  });
});

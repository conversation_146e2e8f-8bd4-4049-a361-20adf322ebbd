import moment from "moment-timezone";

import { futureDateSchemaWithDefaultNow } from "@nest/modules/cloud-task-notification-handler/dto/request.dto";

describe("Validate Schema", () => {
  const data = {
    validScheduledAt: "2047-07-01T14:30:32+08:00", // valid date in UTC+8 timezone
    validScheduledAt2: "2025-06-21T07:39:30.182Z", // valid date in UTC timezone
    notScheduledAt: 123, // invalid type
    pastScheduledAt: "2022-05-12T14:00:00+08:00", // past date
    emptyObject: {}, // object without scheduledAt property
  };

  it("should validate a valid HKT date", () => {
    const result = futureDateSchemaWithDefaultNow.validate(data.validScheduledAt);
    expect(typeof result.value).toBe("object");
    expect(result.value instanceof Date).toBeTruthy();
    const value = moment(result.value).tz("Asia/Hong_Kong");
    expect(value.isValid()).toBeTruthy();
    expect(value.isSame(data.validScheduledAt)).toBeTruthy();
    expect(value.year()).toBe(2047);
    expect(value.month()).toBe(6); // month is zero-indexed
    expect(value.day()).toBe(1);
    expect(value.hour()).toBe(14);
    expect(value.minute()).toBe(30);
    expect(value.second()).toBe(32);
    expect(result.error).toBeFalsy();
  });

  it("should fallback to default", () => {
    const result = futureDateSchemaWithDefaultNow.validate(undefined);
    expect(result.value).toBeTruthy();
    expect(typeof result.value).toBe("object");
    expect(result.value instanceof Date).toBeTruthy();
    const value = moment(result.value);
    expect(value.isValid()).toBeTruthy();
    expect(result.error).toBeFalsy();
  });

  it("should fallback to current datetime", () => {
    const result = futureDateSchemaWithDefaultNow.validate(undefined);
    const value = moment(result.value);
    expect(typeof result.value).toBe("object");
    expect(result.value instanceof Date).toBeTruthy();
    expect(value.isValid()).toBeTruthy();
    expect(value.year()).toBe(moment().year());
    expect(value.month()).toBe(moment().month());
    expect(value.day()).toBe(moment().day());
    expect(value.hour()).toBe(moment().hour());
    expect(value.minute()).toBe(moment().minute());
    expect(value.second()).toBe(moment().second());
    expect(result.error).toBeFalsy();
  });

  it("should not validate an invalid type", () => {
    const result = futureDateSchemaWithDefaultNow.validate(data.notScheduledAt);
    expect(result.error).toBeTruthy();
  });

  it("should not validate a past date", () => {
    const result = futureDateSchemaWithDefaultNow.validate(data.pastScheduledAt);
    expect(result.error).toBeTruthy();
  });

  it("should not validate an empty object", () => {
    const result = futureDateSchemaWithDefaultNow.validate(data.emptyObject);
    expect(result.error).toBeTruthy();
  });
});

import { getAuth } from "firebase-admin/auth";

import { AppDatabaseService } from "../../../nestJs/modules/appDatabase/appDatabase.service";
import User from "../../../nestJs/modules/database/entities/user.entity";
import { ProfileAuditRepository } from "../../../nestJs/modules/database/repositories/profileAudit.repository";
import { IdentityEventMethodNameType } from "../../../nestJs/modules/identity/dto/methodNameType.dto";
import { IdentityService } from "../../../nestJs/modules/identity/identity.service";
import { UserService } from "../../../nestJs/modules/user/user.service";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import MockAppDatabaseService, { setMock } from "../../utils/services/FakeAppDatabaseService.specs.utils";
import FakeRepository, { mockCreateProfileAudit } from "../../utils/services/FakeRepository.specs.utils";
import FakeUserService, {
  createUserMock,
  setUserInFirestoreMock,
  updateUserEmailMock,
} from "../../utils/services/FakeUserService.specs.utils";
const getNewIdentityService = () => {
  return new IdentityService(
    new FakeUserService() as unknown as UserService,
    new FakeRepository() as unknown as ProfileAuditRepository,
    new MockAppDatabaseService() as unknown as AppDatabaseService,
  );
};
jest.mock("firebase-admin/auth", () => {
  return {
    getAuth: jest.fn(),
  };
});
describe("identity.service", () => {
  let service = getNewIdentityService();
  let mockedGetAuth: jest.Mock;
  let mockGetUser: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    service = getNewIdentityService();
    mockedGetAuth = getAuth as jest.Mock;
    mockGetUser = jest.fn();
    mockedGetAuth.mockReturnValue({
      getUser: mockGetUser,
    });
  });

  describe("processIdentityEvent", () => {
    it("SIGN_IN_WITH_PHONE_NUMBER: when find user in firebase auth while not found in sql, should create it to sql database", async () => {
      const userInFirebaseAuth = {
        uid: "Juag8EQX7bfZF0kLYxHKoVoVqO03",
        phoneNumber: "+85212345678",
      };
      mockGetUser.mockResolvedValue(userInFirebaseAuth);
      createUserMock.mockResolvedValue(new User());

      await service.processIdentityEvent({
        insertId: "k2g88d4mjg",
        jsonPayload: {
          methodName: IdentityEventMethodNameType.SIGN_IN_WITH_PHONE_NUMBER,
          response: {
            localId: "Juag8EQX7bfZF0kLYxHKoVoVqO03",
            phoneNumber: "+85212345678",
          },
          requestMetadata: {
            callerIp: "************",
            callerSuppliedUserAgent:
              "Dalvik/2.1.0 (Linux; U; Android 14; SM-S901U1 Build/UP1A.231005.007),gzip(gfe),gzip(gfe)",
          },
        },
      });
      const userToBeCreated = new User();
      userToBeCreated.appDatabaseId = "Juag8EQX7bfZF0kLYxHKoVoVqO03";
      userToBeCreated.phoneNumber = "+85212345678";
      expect(createUserMock).toHaveBeenLastCalledWith(userToBeCreated);
      expect(setUserInFirestoreMock).toHaveBeenCalled();
      expect(mockCreateProfileAudit).toHaveBeenCalled();
    });

    it("SIGN_IN_WITH_PHONE_NUMBER: when not find user in firebase auth, throw error", async () => {
      mockGetUser.mockRejectedValue(new Error());
      expect(
        service.processIdentityEvent({
          insertId: "k2g88d4mjk",
          jsonPayload: {
            methodName: IdentityEventMethodNameType.SIGN_IN_WITH_PHONE_NUMBER,
            response: {
              localId: "Juag8EQX7bfZF0kLYxHKoVoVqO03",
              phoneNumber: "+85212345678",
            },
            requestMetadata: {
              callerIp: "************",
              callerSuppliedUserAgent:
                "Dalvik/2.1.0 (Linux; U; Android 14; SM-S901U1 Build/UP1A.231005.007),gzip(gfe),gzip(gfe)",
            },
          },
        }),
      ).rejects.toThrow(errorBuilder.user.notFoundInFirebaseAuth("Juag8EQX7bfZF0kLYxHKoVoVqO03"));
      expect(createUserMock).not.toHaveBeenCalled();
      expect(setUserInFirestoreMock).not.toHaveBeenCalled();
    });

    it("SIGN_IN_WITH_PHONE_NUMBER: when find user in firebase auth, but there is no phoneNumber, throw error", async () => {
      const userInFirebaseAuth = {
        uid: "Juag8EQX7bfZF0kLYxHKoVoVqO03",
      };
      mockGetUser.mockResolvedValue(userInFirebaseAuth);
      expect(
        service.processIdentityEvent({
          insertId: "k2g88d4mjo",
          jsonPayload: {
            methodName: IdentityEventMethodNameType.SIGN_IN_WITH_PHONE_NUMBER,
            response: {
              localId: "Juag8EQX7bfZF0kLYxHKoVoVqO03",
              phoneNumber: "+85212345678",
            },
            requestMetadata: {
              callerIp: "************",
              callerSuppliedUserAgent:
                "Dalvik/2.1.0 (Linux; U; Android 14; SM-S901U1 Build/UP1A.231005.007),gzip(gfe),gzip(gfe)",
            },
          },
        }),
      ).rejects.toThrow(errorBuilder.user.notPhoneNumberInFirebaseAuth("Juag8EQX7bfZF0kLYxHKoVoVqO03"));
      expect(createUserMock).not.toHaveBeenCalled();
      expect(setUserInFirestoreMock).not.toHaveBeenCalled();
    });

    it("SET_ACCOUNT_INFO: when email verified, update sql and then update firestore", async () => {
      updateUserEmailMock.mockResolvedValue(new User());

      await service.processIdentityEvent({
        insertId: "k2g88d4mj7",
        jsonPayload: {
          methodName: IdentityEventMethodNameType.SET_ACCOUNT_INFO,
          response: {
            email: "<EMAIL>",
            emailVerified: true,
            localId: "Juag8EQX7bfZF0kLYxHKoVoVqO03",
            newEmail: "<EMAIL>",
            providerUserInfo: [],
          },
          requestMetadata: {
            callerIp: "************",
            callerSuppliedUserAgent:
              "Dalvik/2.1.0 (Linux; U; Android 14; SM-S901U1 Build/UP1A.231005.007),gzip(gfe),gzip(gfe)",
          },
        },
      });

      expect(updateUserEmailMock).toHaveBeenCalled();
      expect(setMock).toHaveBeenCalled();
      expect(mockCreateProfileAudit).toHaveBeenCalled();
    });
  });
});

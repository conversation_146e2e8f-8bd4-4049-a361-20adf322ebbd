import { SchedulerRegistry } from "@nestjs/schedule";
import { Cache } from "cache-manager";
import { Request } from "express";

import { WebhookRepository } from "@nest/modules/database/repositories/webhook.repository";
import { PubSubService } from "@nest/modules/pubsub/pubsub.service";
import { WebhookService } from "@nest/modules/webhook/webhook.service";
import FakeCache from "@tests/utils/services/FakeCache.specs.utils";
import FakePubSubService from "@tests/utils/services/FakePubSubService.specs.utils";
import FakeWebhookService from "@tests/utils/services/FakeWebhookService.specs.utils";

import { AppDatabaseService } from "../../../nestJs/modules/appDatabase/appDatabase.service";
import { LockDocument } from "../../../nestJs/modules/appDatabase/documents/lock.document";
import { SecurityType } from "../../../nestJs/modules/appDatabase/documents/meterSecurity.document";
import { TripDocument } from "../../../nestJs/modules/appDatabase/documents/trip.document";
import Tx from "../../../nestJs/modules/database/entities/tx.entity";
import { TxRepository } from "../../../nestJs/modules/database/repositories/tx.repository";
import { MeterController } from "../../../nestJs/modules/meter/meter.controller";
import { MeterService } from "../../../nestJs/modules/meter/meter.service";
import { PaymentMethodSelected } from "../../../nestJs/modules/payment/dto/paymentMethodSelected.dto";
import { TxTypes } from "../../../nestJs/modules/transaction/dto/txType.dto";
import { TransactionFactoryService } from "../../../nestJs/modules/transaction/transactionFactory/transactionFactory.service";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import { UtilsService } from "../../../nestJs/modules/utils/utils.service";
import decodedIdTokenMock from "../../mockData/firebase/decodedIdToken.mock";
import { SchedulerRegistryMock } from "../../utils/@nestjs/schedule";
import { fakeTripData } from "../../utils/fakeData/fakeTripData.specs.utils";
import FakeLoggerService from "../../utils/fakeLogger.service.specs";
import MockAppDatabaseService, {
  findLatestSecurityByTypeMock,
  findOneByIdMock,
  updateMeterTripTipMock,
} from "../../utils/services/FakeAppDatabaseService.specs.utils";
import FakeRepository, { mockFindOneBy } from "../../utils/services/FakeRepository.specs.utils";
import FakeTransactionFactoryService, {
  mockLock,
} from "../../utils/services/FakeTransactionFactoryService.specs.utils";

describe("meter.controller", () => {
  let controller: MeterController;
  const utilsService = new UtilsService();
  const req: Request = {
    user: decodedIdTokenMock,
  } as Request;
  beforeEach(() => {
    jest.resetModules();
    controller = new MeterController(
      new MeterService(
        new MockAppDatabaseService() as unknown as AppDatabaseService,
        new FakeRepository() as unknown as TxRepository,
        new FakeTransactionFactoryService() as unknown as TransactionFactoryService,
        FakeLoggerService,
        new FakePubSubService() as unknown as PubSubService,
        new FakeWebhookService() as unknown as WebhookService,
        new FakeRepository() as unknown as WebhookRepository,
        new FakeCache() as unknown as Cache,
      ),
      new SchedulerRegistryMock() as unknown as SchedulerRegistry,
      FakeLoggerService,
    );
  });

  it("should call meterTripRepository.findOneById", async () => {
    findOneByIdMock.mockImplementation(
      () =>
        new Promise((resolve) => resolve({ ...utilsService.case.camelizeKeys(fakeTripData), paymentInformation: {} })),
    );
    const meterId = "meterId";
    const tripId = "tripId";
    const trip = await controller.findMeterTripById(meterId, tripId);
    expect(findOneByIdMock).toHaveBeenCalledWith(tripId);
    expect(trip.paymentInformation).toBeUndefined();
    expect(trip.paymentStatus).toBeUndefined();
    expect(trip.paymentType).toBeUndefined();
    expect(trip.session).toBeUndefined();
  });

  it("should throw error when trip is undefined", async () => {
    findOneByIdMock.mockImplementation(() => new Promise((resolve) => resolve(undefined)));
    const meterId = "meterId";
    const tripId = "tripId";

    expect(controller.findMeterTripById(meterId, tripId)).rejects.toThrowError(
      errorBuilder.meter.tripNotFound(meterId, tripId),
    );
    expect(findOneByIdMock).toHaveBeenCalledWith(tripId);
  });

  describe("updateMeterTripById", () => {
    beforeEach(() => {
      findOneByIdMock.mockReset();
      updateMeterTripTipMock.mockReset();
      mockFindOneBy.mockReset();
      mockLock.mockReset();
    });
    const meterId = "DASH123";
    const tripId = "1234-5678-9";
    const tip = 10;
    const paymentMethodSelected = PaymentMethodSelected.VISA;
    const meterTripBodyDto = { tip, paymentMethodSelected };

    const mockstartCheckIsTipsCalculatedInterval = jest.fn();
    const spy = jest
      .spyOn(MeterController.prototype as any, "startCheckIsTipsCalculatedInterval")
      .mockImplementation(mockstartCheckIsTipsCalculatedInterval);
    afterAll(() => {
      spy.mockRestore();
    });

    it("should return not found Tx", async () => {
      mockFindOneBy.mockImplementationOnce(() => new Promise((resolve) => resolve(undefined)));
      expect(controller.updateMeterTripTipById(meterId, tripId, meterTripBodyDto, req)).rejects.toThrowError(
        errorBuilder.transaction.notFound(tripId),
      );
    });

    it("should return not found Meter/Trip", async () => {
      const fakeTx = new Tx();
      const fakeTrip = new TripDocument();
      fakeTx.type = TxTypes.TRIP;
      fakeTx.metadata = fakeTrip;
      mockFindOneBy.mockResolvedValueOnce(fakeTx);
      findOneByIdMock.mockImplementationOnce(() => new Promise((resolve) => resolve(undefined)));
      expect(controller.updateMeterTripTipById(meterId, tripId, meterTripBodyDto, req)).rejects.toThrowError(
        errorBuilder.meter.tripNotFound(meterId, tripId),
      );
    });

    it("should return update fail", async () => {
      const fakeTx = new Tx();
      const fakeTrip = new TripDocument();
      fakeTx.type = TxTypes.TRIP;
      fakeTx.metadata = fakeTrip;
      mockFindOneBy.mockResolvedValueOnce(fakeTx);
      mockLock.mockResolvedValueOnce(new LockDocument());
      findOneByIdMock.mockImplementationOnce(() => new Promise((resolve) => resolve(new Tx())));
      findOneByIdMock.mockImplementation(
        () =>
          new Promise((resolve) =>
            resolve({ ...utilsService.case.camelizeKeys(fakeTripData), paymentInformation: {} }),
          ),
      );
      updateMeterTripTipMock.mockRejectedValueOnce(new Error("Update Failed for meter: DASH123, tripId: 1234-5678-9"));

      expect(controller.updateMeterTripTipById(meterId, tripId, meterTripBodyDto, req)).rejects.toThrow(
        errorBuilder.meter.tripUpdateFailed(meterId, tripId),
      );
    });

    it("should return latest MeterTrip after metersdk update firestore", async () => {
      const fakeTx = new Tx();
      const fakeTrip = new TripDocument();
      fakeTrip.tripInfoAppVersion = "v2.0.0(3390956)";
      fakeTx.type = TxTypes.TRIP;
      fakeTx.metadata = fakeTrip;
      mockFindOneBy.mockResolvedValueOnce(fakeTx);
      mockLock.mockResolvedValueOnce(new LockDocument());
      findOneByIdMock.mockImplementationOnce(() => new Promise((resolve) => resolve(new Tx())));
      findOneByIdMock.mockImplementation(
        () =>
          new Promise((resolve) =>
            resolve({ ...utilsService.case.camelizeKeys(fakeTripData), paymentInformation: {} }),
          ),
      );
      updateMeterTripTipMock.mockImplementation(
        () =>
          new Promise((resolve) =>
            resolve({
              ...utilsService.case.camelizeKeys({
                ...fakeTripData,
                dash_tips: tip,
                payment_method_selected: paymentMethodSelected,
                is_tips_calculated: false,
              }),
              paymentInformation: {},
            }),
          ),
      );
      mockstartCheckIsTipsCalculatedInterval.mockImplementation(
        () =>
          new Promise((resolve) =>
            resolve({
              ...utilsService.case.camelizeKeys({
                ...fakeTripData,
                dash_tips: tip,
                payment_method_selected: paymentMethodSelected,
                is_tips_calculated: true,
              }),
              paymentInformation: {},
            }),
          ),
      );
      const latestMeterTrip = await controller.updateMeterTripTipById(meterId, tripId, meterTripBodyDto, req);
      expect(mockLock).toHaveBeenCalled();
      expect(latestMeterTrip.dashTips).toEqual(tip);
      expect(latestMeterTrip.isTipsCalculated).toEqual(true);
    });

    it("should return latest MeterTrip after metersdk update firestore, but skip lock if tripInfoAppVersion is <= 2.0.0", async () => {
      const fakeTx = new Tx();
      const fakeTrip = new TripDocument();
      fakeTrip.tripInfoAppVersion = "v1.5.2(2390956)";
      fakeTx.type = TxTypes.TRIP;
      fakeTx.metadata = fakeTrip;
      mockFindOneBy.mockResolvedValueOnce(fakeTx);
      mockLock.mockResolvedValueOnce(new LockDocument());
      findOneByIdMock.mockImplementationOnce(() => new Promise((resolve) => resolve(new Tx())));
      findOneByIdMock.mockImplementation(
        () =>
          new Promise((resolve) =>
            resolve({ ...utilsService.case.camelizeKeys(fakeTripData), paymentInformation: {} }),
          ),
      );
      updateMeterTripTipMock.mockImplementation(
        () =>
          new Promise((resolve) =>
            resolve({
              ...utilsService.case.camelizeKeys({
                ...fakeTripData,
                dash_tips: tip,
                payment_method_selected: paymentMethodSelected,
                is_tips_calculated: false,
              }),
              paymentInformation: {},
            }),
          ),
      );
      mockstartCheckIsTipsCalculatedInterval.mockImplementation(
        () =>
          new Promise((resolve) =>
            resolve({
              ...utilsService.case.camelizeKeys({
                ...fakeTripData,
                dash_tips: tip,
                payment_method_selected: paymentMethodSelected,
                is_tips_calculated: true,
              }),
              paymentInformation: {},
            }),
          ),
      );
      const latestMeterTrip = await controller.updateMeterTripTipById(meterId, tripId, meterTripBodyDto, req);
      expect(mockLock).not.toHaveBeenCalled();
      expect(latestMeterTrip.dashTips).toEqual(tip);
      expect(latestMeterTrip.isTipsCalculated).toEqual(true);
    });
    /** Test getting security document */
    it("should return latest security", async () => {
      /** set up mock implementation for find findLatestSecurityByTypeMock */
      findLatestSecurityByTypeMock.mockImplementation(
        () =>
          new Promise((resolve) =>
            resolve({
              type: SecurityType.TOTP,
              secret: "secret",
              createdAt: new Date(),
            }),
          ),
      );
      const meterId = "meterId";
      const securityType = SecurityType.TOTP;
      const security = await controller.getLatestSecurityByMeterId(meterId, securityType);
      expect(findLatestSecurityByTypeMock).toHaveBeenCalledWith(securityType);
      expect(security.type).toEqual(SecurityType.TOTP);
    });
  });
});

import { Request, Response, NextFunction } from "express";

import User from "../../../../nestJs/modules/database/entities/user.entity";
import { VerifyPinMiddleware } from "../../../../nestJs/modules/me/middlewares/verifyPin.middleware";
import { errorBuilder } from "../../../../nestJs/modules/utils/utils/error.utils";
import { mockHash } from "../../../utils/bcrypt.specs.utils";
import { mockPrivateDecrypt } from "../../../utils/crypto.specs.utils";
import { mockFindAppUserById } from "../../../utils/services/FakeRepository.specs.utils";
import { newTestVerifyPinMiddleware } from "../../../utils/services/TestMiddleware.specs.utils";
jest.mock("bcrypt");
jest.mock("crypto");
describe("VerifyPinMiddleware", () => {
  let middleware: VerifyPinMiddleware;
  let req: Request;
  let res: Response;
  let next: NextFunction;
  const fakePin =
    "lqP3FsnwWnEZkYiKRbNMp6JIuafGv4p0jc0h5cAN9LSz5ISxwxgpxq1hCZfCtvD1OPARhDjwHepXaK6IvPkvkHw0hy/ru+8zUF9gTogd13nKleD4Rzskp/GJLhVrPMFO4Mxc7kkhNtMhnXzev4ojP7e7nF380X1YSH2xmFB8BI7apkivTjggcfClgT43//00Q4P1IuwdN1z2i5dEBVFkGxxjdPdQZ/ktNAnjCo3waT9wLmzP8M78wsSN0t9ICNxY/rFjp8ucKE0kfCMxiYlDksmgO+sr7H8i6IwJkAggMIm86WTsC+CkdwdQ73MUNPz/uWUpEzFdu6Eys03aQGI8Pg==";
  const user_id = "qwertyuiop123456789";
  beforeEach(() => {
    jest.clearAllMocks();
    middleware = newTestVerifyPinMiddleware();
    req = {
      headers: {},
    } as Request;
    res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    } as unknown as Response;
    next = jest.fn();
  });

  it("throw error if not find user in sql db", async () => {
    const error = new Error("App User: qwertyuiop123456789 not found in SQL");
    mockFindAppUserById.mockRejectedValue(error);
    req = {
      query: {
        pin: fakePin,
      },
      body: {},
      user: {
        user_id,
      },
    } as unknown as Request;
    await expect(middleware.use(req, res, next)).rejects.toThrow(errorBuilder.user.notFoundInSql(user_id));
  });

  it("throw error if there is no pin in user from sql db", async () => {
    const user = new User();
    user.privateKey = "fake_privateKey";
    user.salt = "fake_salt";
    mockFindAppUserById.mockReturnValue(user);
    req = {
      query: {
        pin: fakePin,
      },
      body: {},
      user: {
        user_id,
      },
    } as unknown as Request;
    await expect(middleware.use(req, res, next)).rejects.toThrow(
      errorBuilder.user.pin.verifyPinFailedWithoutExistingPin(user_id),
    );
  });

  it("should return true if same as the hashedPin to sql", async () => {
    const user = new User();
    user.privateKey = "fake_privateKey";
    user.salt = "fake_salt";
    user.hashedPin = "fake_hashed_pin";
    mockFindAppUserById.mockReturnValue(user);

    mockPrivateDecrypt.mockResolvedValue(Buffer.from(""));
    mockHash.mockResolvedValue("fake_hashed_pin");
    req = {
      query: {
        pin: fakePin,
      },
      body: {},
      user: {
        user_id,
      },
    } as unknown as Request;
    await middleware.use(req, res, next);
    expect(req.verifyPinResult?.verified).toBe(true);
    expect(next).toHaveBeenCalled();
  });

  it("should return false if different from the hashedPin to sql", async () => {
    const user = new User();
    user.privateKey = "fake_privateKey";
    user.salt = "fake_salt";
    user.hashedPin = "fake_hashed_pin_1";
    mockFindAppUserById.mockReturnValue(user);

    mockPrivateDecrypt.mockResolvedValue(Buffer.from(""));
    mockHash.mockResolvedValue("fake_hashed_pin_2");

    req = {
      query: {
        pin: fakePin,
      },
      body: {},
      user: {
        user_id,
      },
    } as unknown as Request;
    await middleware.use(req, res, next);
    expect(req.verifyPinResult?.verified).toBe(false);
    expect(next).toHaveBeenCalled();
  });
});

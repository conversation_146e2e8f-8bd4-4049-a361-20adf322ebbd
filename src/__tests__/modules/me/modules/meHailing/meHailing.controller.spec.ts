import { Request } from "express";

import PaymentTx from "@nest/modules/database/entities/paymentTx.entity";
import { PaymentInformationStatus } from "@nest/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "@nest/modules/payment/dto/paymentInformationType.dto";

import { VehicleClass } from "../../../../../nestJs/modules/fleet/dto/fleet.dto";
import { LocalizedLanguage } from "../../../../../nestJs/modules/location/dto/location.dto";
import { MeHailingController } from "../../../../../nestJs/modules/me/modules/meHailing/meHailing.controller";
import { PaymentGatewayTypes } from "../../../../../nestJs/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentInstrumentState } from "../../../../../nestJs/modules/payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { errorBuilder } from "../../../../../nestJs/modules/utils/utils/error.utils";
import meHailingControllerMock from "../../../../mockData/requests/meHailingController.mock";
import { axiosPostMock } from "../../../../utils/axios/axiosMock.specs.utils";
import { dataFirestoreMock } from "../../../../utils/firebase-admin/firestoreMock.specs.utils";
import { mockCreate, mockFindAppUserById, mockSave } from "../../../../utils/services/FakeRepository.specs.utils";
import { newTestMeHailingController } from "../../../../utils/services/TestServices.specs.utils";

import "../../../../initTests";

describe("meHailing.controller.ts", () => {
  let controller: MeHailingController;

  beforeEach(() => {
    axiosPostMock.mockImplementation(() => Promise.resolve({ data: { hail: { api: { response: {} } } } }));
    dataFirestoreMock.mockImplementation(() => ({
      vehicles: [{ vehicleClass: "STANDARD" }, { vehicleClass: "COMFORT" }, { vehicleClass: "LUXURY" }],
    }));
    mockFindAppUserById.mockImplementation(() => ({
      id: "1",
      paymentInstruments: [
        {
          expirationDate: new Date("2124-08-05T03:55:39.009Z"),
          state: PaymentInstrumentState.VERIFIED,
          isPreferred: true,
          paymentGateway: PaymentGatewayTypes.GLOBAL_PAYMENTS,
        },
      ],
    }));
    const ptx = new PaymentTx();
    ptx.type = PaymentInformationType.AUTH;
    ptx.status = PaymentInformationStatus.SUCCESS;
    mockCreate.mockImplementation(() => ptx);
    mockSave.mockImplementation(() => ptx);
    controller = newTestMeHailingController();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createOrder", () => {
    it("should return the correct response", async () => {
      const result = await controller.createOrder(meHailingControllerMock, {
        user: {},
        headers: { authorization: "123" },
      } as Request);

      expect(result).toEqual({
        charges: {
          cancellationFee: 0,
        },
        hail: {
          api: {
            response: {},
          },
        },
        discounts: {
          discountIdDash: undefined,
          discountIdThirdParty: undefined,
          discountRulesDash: undefined,
          discountRulesThirdParty: undefined,
        },
        request: meHailingControllerMock,
        authedAmount: 20,
      });
    });

    it("should return the correct response when language is chinese", async () => {
      const result = await controller.createOrder(
        {
          ...meHailingControllerMock,
          language: LocalizedLanguage.ZHHK,
        },
        { user: {}, headers: { authorization: "123" } } as Request,
      );

      expect(result).toEqual({
        charges: {
          cancellationFee: 0,
        },
        hail: {
          api: {
            response: {},
          },
        },
        discounts: {
          discountIdDash: undefined,
          discountIdThirdParty: undefined,
          discountRulesDash: undefined,
          discountRulesThirdParty: undefined,
        },
        request: {
          ...meHailingControllerMock,
          language: LocalizedLanguage.ZHHK,
          time: expect.any(Date),
        },
        authedAmount: 20,
      });
    });

    it("should throw an error when the fleet is not found", async () => {
      dataFirestoreMock.mockImplementation(() => undefined);

      await expect(
        controller.createOrder(
          {
            ...meHailingControllerMock,
            fleetVehicleClass: {
              [VehicleClass.STANDARD]: ["1"],
              [VehicleClass.COMFORT]: ["1", "2"],
              [VehicleClass.LUXURY]: ["2"],
            },
          },
          { user: {}, headers: { authorization: "123" } } as Request,
        ),
      ).rejects.toThrow(
        errorBuilder.validation.failed("Fleet 1 not found, Fleet 2 not found", {
          errors: ["Fleet 1 not found", "Fleet 2 not found"],
        }),
      );
    });
  });
});

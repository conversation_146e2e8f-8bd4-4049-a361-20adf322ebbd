import User from "../../../../../nestJs/modules/database/entities/user.entity";
import { MeInitializeService } from "../../../../../nestJs/modules/me/modules/meInitialize/meInitialize.service";
import { errorBuilder } from "../../../../../nestJs/modules/utils/utils/error.utils";
import {
  dataFirestoreMock,
  docsDataFirestoreMock,
  getFirestoreMock,
  setFirestoreMock,
} from "../../../../utils/firebase-admin/firestoreMock.specs.utils";
import { mockFindAppUserById } from "../../../../utils/services/FakeRepository.specs.utils";
import { newTestMeInitializeService } from "../../../../utils/services/TestServices.specs.utils";
import "../../../../initTests";

describe("meInitialize.service", () => {
  describe("initialize", () => {
    let service: MeInitializeService;

    beforeEach(() => {
      service = newTestMeInitializeService();
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it("should throw error if user not found in sql", async () => {
      const error = new Error("App User: appDatabaseId not found in SQL");
      mockFindAppUserById.mockRejectedValueOnce(error);
      expect(
        service.initialize(
          {
            token: "faketoken",
          },
          "appDatabaseId",
        ),
      ).rejects.toThrow(errorBuilder.user.notFoundInSql("appDatabaseId"));
    });

    it("should throw error if user not found in firestore", async () => {
      const user = new User();
      user.privateKey = "fake_privateKey";
      user.salt = "fake_salt";
      user.hashedPin = "fake_pin";
      user.id = "fake_uuid";
      mockFindAppUserById.mockReturnValueOnce(user);
      getFirestoreMock.mockImplementationOnce(() => ({
        exists: false,
        data: dataFirestoreMock,
      }));

      expect(
        service.initialize(
          {
            token: "faketoken",
          },
          "appDatabaseId",
        ),
      ).rejects.toThrow(errorBuilder.user.notFoundInFirestore("appDatabaseId"));
    });

    it("should set if there is no publickey in user collection in firestore", async () => {
      const user = new User();
      user.privateKey = "fake_privateKey";
      user.salt = "fake_salt";
      user.hashedPin = "fake_pin";
      user.id = "fake_uuid";
      mockFindAppUserById.mockReturnValueOnce(user);
      getFirestoreMock.mockImplementationOnce(() => ({
        exists: true,
        empty: false,
        data: () => ({ id: "123456" }),
        docs: [{ data: docsDataFirestoreMock }],
      }));
      await service.initialize({ token: "faketoken" }, "appDatabaseId");
      expect(setFirestoreMock).toHaveBeenCalledTimes(1);
    });

    it("should not call set if there is publickey in user collection in firestore", async () => {
      const user = new User();
      user.privateKey = "fake_privateKey";
      user.salt = "fake_salt";
      user.hashedPin = "fake_pin";
      user.id = "fake_uuid";
      mockFindAppUserById.mockReturnValueOnce(user);
      getFirestoreMock.mockImplementationOnce(() => ({
        exists: true,
        empty: false,
        data: () => ({ id: "123456", publicKey: "fake_public_key" }),
        docs: [{ data: docsDataFirestoreMock }],
      }));
      await service.initialize({ token: "faketoken" }, "appDatabaseId");
      expect(setFirestoreMock).not.toHaveBeenCalled(); // means not set for user collection itself
    });

    // commented out because it is not used right now, and it will be used in the future and will be changed
    // it("should call set to copy new campaign to firestore", async () => {
    //   const user = new User();
    //   user.privateKey = "fake_privateKey";
    //   user.salt = "fake_salt";
    //   user.hashedPin = "fake_pin";
    //   user.id = "fake_uuid";
    //   mockFindAppUserById.mockReturnValueOnce(user);
    //   getFirestoreMock.mockImplementationOnce(() => ({
    //     exists: true,
    //     empty: false,
    //     data: () => ({ id: "123456", publicKey: "fake_public_key" }),
    //     docs: [{ data: docsDataFirestoreMock }],
    //   }));
    //   mockGetRawMany.mockReturnValueOnce([{ id: "fake_campaign_1" }, { id: "fake_campaign_2" }]);
    //   docsDataFirestoreMock.mockImplementationOnce(() => {
    //     return { id: "fake_campaign_1" };
    //   });
    //   getFirestoreMock.mockImplementationOnce(() => ({
    //     exists: true,
    //     empty: false,
    //     data: () => ({ id: "fake_campaign_1" }),
    //     docs: [{ data: docsDataFirestoreMock }],
    //   }));
    //   mockGetCount.mockReturnValueOnce(2);
    //   await service.initialize({ token: "faketoken" }, "appDatabaseId");
    //   expect(setFirestoreMock).toHaveBeenCalledTimes(1); //means set for userCampaign collection
    // });
  });
});

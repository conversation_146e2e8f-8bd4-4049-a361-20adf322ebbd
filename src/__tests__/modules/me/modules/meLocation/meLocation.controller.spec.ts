import { Request } from "express";

import { LocationLanguage } from "../../../../../nestJs/modules/location/dto/location.dto";
import { MeLocationController } from "../../../../../nestJs/modules/me/modules/meLocation/meLocation.controller";
import { errorBuilder } from "../../../../../nestJs/modules/utils/utils/error.utils";
import placeAutocompleteResponseMock from "../../../../mockData/google/placeAutocompleteResponse.mock";
import {
  GoogleMapsPlaceAutocompleteMock,
  GoogleMapsReverseGeocodeMock,
} from "../../../../utils/@googlemaps/google-maps-services-js.specs.utils";
import { GoogleMapsPlacesClientV1GetPlace } from "../../../../utils/@googlemaps/places.specs.utils";
import { GoogleMapsRoutingClientV2ComputeRoutes } from "../../../../utils/@googlemaps/routing.specs.utils";
import { newTestMeLocationController } from "../../../../utils/services/TestServices.specs.utils";

import "../../../../initTests";

describe("meLocation.controller.ts", () => {
  let controller: MeLocationController;

  beforeEach(() => {
    controller = newTestMeLocationController();
  });

  describe("placeAutocomplete", () => {
    it("should return the correct response", async () => {
      const result = await controller.placeAutocomplete(
        {
          sessionToken: "123",
          query: "mall",
          language: LocationLanguage.EN,
          index: 0,
        },
        { user: {} } as Request,
      );

      expect(result).toEqual(
        placeAutocompleteResponseMock.predictions.map((prediction) => ({
          placeId: prediction.place_id,
          mainText: prediction.structured_formatting.main_text,
          secondaryText: prediction.structured_formatting.secondary_text.replace(/, Hong Kong/g, ""),
        })),
      );

      expect(GoogleMapsPlaceAutocompleteMock).toHaveBeenCalledWith({
        params: {
          input: "mall",
          key: "GOOGLE_PLACES_KEY",
          language: "en",
          components: ["country:hk"],
        },
      });
    });

    it("should return the correct response with location and radius", async () => {
      const result = await controller.placeAutocomplete(
        {
          sessionToken: "123",
          query: "mall",
          language: LocationLanguage.EN,
          index: 0,
          lat: 22.306779062030994,
          lng: 114.26161142551531,
          radius: 1245,
        },
        { user: {} } as Request,
      );

      expect(result).toEqual(
        placeAutocompleteResponseMock.predictions.map((prediction) => ({
          placeId: prediction.place_id,
          mainText: prediction.structured_formatting.main_text,
          secondaryText: prediction.structured_formatting.secondary_text.replace(/, Hong Kong/g, ""),
        })),
      );

      expect(GoogleMapsPlaceAutocompleteMock).toHaveBeenCalledWith({
        params: {
          input: "mall",
          key: "GOOGLE_PLACES_KEY",
          language: "en",
          location: {
            lat: 22.306779062030994,
            lng: 114.26161142551531,
          },
          radius: 1245,
          components: ["country:hk"],
        },
      });
    });

    it("should throw an error when the query to google is malformed", async () => {
      GoogleMapsPlaceAutocompleteMock.mockRejectedValue({
        response: { data: { error_message: "Some Error!", other: "key - value" } },
      });

      await expect(
        controller.placeAutocomplete(
          {
            sessionToken: "123",
            query: "mall",
            language: LocationLanguage.EN,
            index: 0,
          },
          { user: {} } as Request,
        ),
      ).rejects.toThrow(
        errorBuilder.location.placeAutocomplete.dependencyFailed(
          { error_message: "Some Error!", other: "key - value" },
          "Some Error!",
        ),
      );

      expect(GoogleMapsPlaceAutocompleteMock).toHaveBeenCalledWith({
        params: {
          input: "mall",
          key: "GOOGLE_PLACES_KEY",
          language: "en",
          components: ["country:hk"],
        },
      });
    });
  });

  describe("placeDetails", () => {
    it("should return the correct response", async () => {
      const result = await controller.placeDetails(
        {
          sessionToken: "123",
          placeId: "place-id",
          language: LocationLanguage.EN,
          index: 0,
        },
        { user: {} } as Request,
      );

      expect(result).toEqual({
        lat: 22.2795933,
        lng: 114.1839629,
        displayName: "Hysan Place",
        placeId: "place-id",
        formattedAddress: "Lippo Centre Tower 1, Admiralty",
      });

      expect(GoogleMapsPlacesClientV1GetPlace).toHaveBeenCalledWith(
        {
          languageCode: "en",
          name: "places/place-id",
          sessionToken: "123",
        },
        {
          otherArgs: { headers: { "X-Goog-FieldMask": "id,displayName,location,formattedAddress,addressComponents" } },
        },
      );
    });

    it("should throw an error when the query to google is malformed", async () => {
      GoogleMapsPlacesClientV1GetPlace.mockRejectedValue({
        statusDetails: { error_message: "Some Error!", other: "key - value" },
      });

      await expect(
        controller.placeDetails(
          {
            sessionToken: "123",
            placeId: "place-id",
            language: LocationLanguage.EN,
            index: 0,
          },
          { user: {} } as Request,
        ),
      ).rejects.toThrow(
        errorBuilder.location.placeDetails.dependencyFailed(
          { error_message: "Some Error!", other: "key - value" },
          "Dependency failed",
        ),
      );

      expect(GoogleMapsPlacesClientV1GetPlace).toHaveBeenCalledWith(
        {
          languageCode: "en",
          name: "places/place-id",
          sessionToken: "123",
        },
        {
          otherArgs: { headers: { "X-Goog-FieldMask": "id,displayName,location,formattedAddress,addressComponents" } },
        },
      );
    });
  });

  describe("reverseGeocode", () => {
    it("should return the correct response", async () => {
      const result = await controller.reverseGeocode(
        {
          sessionToken: "string",
          language: LocationLanguage.EN,
          index: 0,
          lat: 22.306779062030994,
          lng: 114.26161142551531,
        },
        { user: {} } as Request,
      );

      expect(result).toEqual({
        displayName: "11 Tong Chun Street",
        formattedAddress: "11 Tong Chun St, Tseung Kwan O",
        placeId: "ChIJo2ZWr_IDBDQRNkB-6gQz2Lw",
        pickupPointPolygon: undefined,
      });

      expect(GoogleMapsReverseGeocodeMock).toHaveBeenCalledWith({
        params: {
          key: "GOOGLE_PLACES_KEY",
          language: "en",
          latlng: {
            lat: 22.306779062030994,
            lng: 114.26161142551531,
          },
        },
      });
    });

    it("should throw an error when the query to google is malformed", async () => {
      GoogleMapsReverseGeocodeMock.mockRejectedValue({
        response: { data: { error_message: "Some Error!", other: "key - value" } },
      });

      await expect(
        controller.reverseGeocode(
          {
            sessionToken: "string",
            language: LocationLanguage.EN,
            index: 0,
            lat: 22.306779062030994,
            lng: 114.26161142551531,
          },
          { user: {} } as Request,
        ),
      ).rejects.toThrow(
        errorBuilder.location.reverseGeocode.dependencyFailed(
          { error_message: "Some Error!", other: "key - value" },
          "Some Error!",
        ),
      );

      expect(GoogleMapsReverseGeocodeMock).toHaveBeenCalledWith({
        params: {
          key: "GOOGLE_PLACES_KEY",
          language: "en",
          latlng: {
            lat: 22.306779062030994,
            lng: 114.26161142551531,
          },
        },
      });
    });
  });

  describe("computeRoutes", () => {
    it("should return the correct response", async () => {
      const result = await controller.computeRoutes(
        {
          language: LocationLanguage.EN,
          originPlaceId: "ChIJuwh9w1YABDQRaRbZ1DgateI",
          destinationPlaceId: "ChIJj6ZHpA4DBDQRD99EOoiAGwo",
        },
        { user: {} } as Request,
      );

      expect(result).toEqual({
        distanceMeters: 14251,
        durationSeconds: 1336,
        destinationPlaceDetails: {
          displayName: "Hysan Place",
          formattedAddress: "Lippo Centre Tower 1, Admiralty",
          lat: 22.2795933,
          lng: 114.1839629,
          placeId: "ChIJj6ZHpA4DBDQRD99EOoiAGwo",
        },
        originPlaceDetails: {
          displayName: "Hysan Place",
          formattedAddress: "Lippo Centre Tower 1, Admiralty",
          lat: 22.2795933,
          lng: 114.1839629,
          placeId: "ChIJuwh9w1YABDQRaRbZ1DgateI",
        },
        encodedPolyline: "",
      });

      expect(GoogleMapsRoutingClientV2ComputeRoutes).toHaveBeenCalledWith(
        {
          computeAlternativeRoutes: false,
          destination: {
            placeId: "ChIJj6ZHpA4DBDQRD99EOoiAGwo",
          },
          languageCode: "en",
          origin: {
            placeId: "ChIJuwh9w1YABDQRaRbZ1DgateI",
          },
          routeModifiers: {
            avoidFerries: false,
            avoidHighways: false,
            avoidTolls: false,
          },
          routingPreference: "TRAFFIC_AWARE",
          travelMode: "DRIVE",
          units: "METRIC",
        },
        {
          otherArgs: {
            headers: {
              "X-Goog-FieldMask":
                "routes.duration,routes.staticDuration,routes.distanceMeters,routes.polyline.encodedPolyline",
            },
          },
        },
      );
    });

    it("should throw an error when the query to google is malformed", async () => {
      GoogleMapsRoutingClientV2ComputeRoutes.mockRejectedValue({
        response: { data: { error_message: "Some Error!", other: "key - value" } },
      });

      await expect(
        controller.computeRoutes(
          {
            language: LocationLanguage.EN,
            originPlaceId: "ChIJuwh9w1YABDQRaRbZ1DgateI",
            destinationPlaceId: "ChIJj6ZHpA4DBDQRD99EOoiAGwo",
          },
          { user: {} } as Request,
        ),
      ).rejects.toThrow(
        errorBuilder.location.computeRoutes.dependencyFailed(
          { error_message: "Some Error!", other: "key - value" },
          "Some Error!",
        ),
      );

      expect(GoogleMapsRoutingClientV2ComputeRoutes).toHaveBeenCalledWith(
        {
          computeAlternativeRoutes: false,
          destination: {
            placeId: "ChIJj6ZHpA4DBDQRD99EOoiAGwo",
          },
          languageCode: "en",
          origin: {
            placeId: "ChIJuwh9w1YABDQRaRbZ1DgateI",
          },
          routeModifiers: {
            avoidFerries: false,
            avoidHighways: false,
            avoidTolls: false,
          },
          routingPreference: "TRAFFIC_AWARE",
          travelMode: "DRIVE",
          units: "METRIC",
        },
        {
          otherArgs: {
            headers: {
              "X-Goog-FieldMask":
                "routes.duration,routes.staticDuration,routes.distanceMeters,routes.polyline.encodedPolyline",
            },
          },
        },
      );
    });
  });
});

import { IsNull } from "typeorm";

import { PaymentGatewayTypes } from "@nest/modules/payment/dto/paymentGatewayTypes.dto";

import PaymentInstrument from "../../../../../nestJs/modules/database/entities/paymentInstrument.entity";
import { MePaymentInstrumentService } from "../../../../../nestJs/modules/me/modules/mePaymentInstrument/mePaymentInstrument.service";
import {
  PaymentInstrumentState,
  PaymentInstrumentType,
} from "../../../../../nestJs/modules/payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { errorBuilder } from "../../../../../nestJs/modules/utils/utils/error.utils";
import {
  deleteFirestoreMock,
  docFirestoreMock,
  getFirestoreMock,
  setFirestoreMock,
} from "../../../../utils/firebase-admin/firestoreMock.specs.utils";
import {
  mockAppByNameOrCreate,
  mockCreate,
  mockFind,
  mockFindOne,
  mockSave,
  mockSoftDelete,
  mockUpdate,
} from "../../../../utils/services/FakeRepository.specs.utils";
import { newTestMePaymentInstrumentService } from "../../../../utils/services/TestServices.specs.utils";

import "../../../../initTests";

const generatePaymentInstrument = (id: string, isPreferred = false) => {
  const pi = new PaymentInstrument();
  pi.id = id;
  pi.isPreferred = isPreferred;
  pi.cardType = PaymentInstrumentType.MASTERCARD;
  pi.paymentGateway = PaymentGatewayTypes.GLOBAL_PAYMENTS;
  return pi;
};

describe("mePaymentInstrument.service", () => {
  describe("setPreferredPaymentInstrument", () => {
    let service: MePaymentInstrumentService;

    const paymentInstruments = [
      generatePaymentInstrument("123"),
      generatePaymentInstrument("456"),
      generatePaymentInstrument("789"),
    ];

    beforeEach(() => {
      service = newTestMePaymentInstrumentService();

      mockFindOne.mockImplementation(() => ({ id: "123456" }));
      mockFind.mockImplementation(() => paymentInstruments);

      mockSave.mockImplementation(() => paymentInstruments);

      mockAppByNameOrCreate.mockImplementation(() => ({ id: "123" }));
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it("should set preferred payment instrument", async () => {
      const result = await service.setPreferredPaymentInstrument(
        {
          paymentInstrumentId: "456",
        },
        "user123",
      );

      expect(result).toMatchObject({
        id: "456",
        isPreferred: true,
        cardType: "MASTERCARD",
      });

      expect(mockFindOne).toHaveBeenCalledWith({ where: { appDatabaseId: "user123" } });

      expect(mockFind).toHaveBeenCalledWith({ where: { user: { id: "123456" } } });

      expect(mockSave).toHaveBeenCalledTimes(1);
      expect(mockSave).toHaveBeenCalledWith(
        paymentInstruments.map((pi) => expect.objectContaining({ id: pi.id, isPreferred: pi.id === "456" })),
      );

      expect(setFirestoreMock).toHaveBeenCalledTimes(3);
      expect(setFirestoreMock).toHaveBeenNthCalledWith(1, expect.objectContaining({ id: "123", isPreferred: false }), {
        merge: true,
      });
      expect(setFirestoreMock).toHaveBeenNthCalledWith(2, expect.objectContaining({ id: "456", isPreferred: true }), {
        merge: true,
      });
      expect(setFirestoreMock).toHaveBeenNthCalledWith(3, expect.objectContaining({ id: "789", isPreferred: false }), {
        merge: true,
      });
    });

    it("should throw an error when the favorite payment method id is not found", async () => {
      await expect(
        service.setPreferredPaymentInstrument(
          {
            paymentInstrumentId: "345",
          },
          "user123",
        ),
      ).rejects.toThrow(errorBuilder.payment.instrument.notFound("345"));

      expect(mockFindOne).toHaveBeenCalledWith({ where: { appDatabaseId: "user123" } });

      expect(mockFind).toHaveBeenCalledWith({ where: { user: { id: "123456" } } });

      expect(mockSave).not.toHaveBeenCalled();
      expect(setFirestoreMock).not.toHaveBeenCalled();
    });

    it("should throw an error when the user is not found in sql", async () => {
      mockFindOne.mockImplementation(() => undefined);

      await expect(
        service.setPreferredPaymentInstrument(
          {
            paymentInstrumentId: "456",
          },
          "user123",
        ),
      ).rejects.toThrow(errorBuilder.user.notFoundInSql("user123"));

      expect(mockFindOne).toHaveBeenCalledWith({ where: { appDatabaseId: "user123" } });

      expect(mockFind).not.toHaveBeenCalled();
      expect(mockSave).not.toHaveBeenCalled();
      expect(setFirestoreMock).not.toHaveBeenCalled();
    });
  });

  describe("delete", () => {
    let service: MePaymentInstrumentService;

    const paymentInstruments = [
      generatePaymentInstrument("123"),
      generatePaymentInstrument("456", true),
      generatePaymentInstrument("789"),
    ];

    beforeEach(() => {
      service = newTestMePaymentInstrumentService();

      mockFindOne.mockImplementationOnce(() => ({ id: "123456" }));
      mockFindOne.mockImplementationOnce(() => paymentInstruments[1]);

      mockSoftDelete.mockImplementation(() => ({
        affected: 1,
      }));

      mockFindOne.mockImplementationOnce(() => paymentInstruments[2]);
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it("should set preferred payment instrument", async () => {
      const result = await service.delete(
        {
          paymentInstrumentId: "456",
        },
        "user123",
      );

      expect(result).toMatchObject({
        id: "456",
        isPreferred: true,
        cardType: "MASTERCARD",
      });

      expect(mockFindOne).toHaveBeenCalledTimes(3);
      expect(mockFindOne).toHaveBeenNthCalledWith(1, { where: { appDatabaseId: "user123" } });
      expect(mockFindOne).toHaveBeenNthCalledWith(2, {
        where: { id: "456", user: { id: "123456" } },
      });
      expect(mockFindOne).toHaveBeenNthCalledWith(3, PaymentInstrument, {
        where: {
          user: { id: "123456" },
          state: PaymentInstrumentState.VERIFIED,
          isPayerAuthEnroled: true,
          expirationDate: expect.objectContaining({
            _type: "moreThan",
            _value: expect.any(Date),
          }),
        },
      });

      expect(mockSoftDelete).toHaveBeenCalledTimes(1);
      expect(mockSoftDelete).toHaveBeenCalledWith(PaymentInstrument, {
        id: "456",
        user: { id: "123456" },
        deletedAt: IsNull(),
      });

      expect(mockUpdate).toHaveBeenCalledTimes(2);
      expect(mockUpdate).toHaveBeenNthCalledWith(
        1,
        PaymentInstrument,
        { user: { id: "123456" } },
        { isPreferred: false },
      );
      expect(mockUpdate).toHaveBeenNthCalledWith(
        2,
        PaymentInstrument,
        { id: paymentInstruments[2].id },
        { isPreferred: true },
      );
    });

    it("should throw an error when delete fails in firestore", async () => {
      deleteFirestoreMock.mockRejectedValue(new Error("Firestore error"));

      await expect(
        service.delete(
          {
            paymentInstrumentId: "456",
          },
          "user123",
        ),
      ).rejects.toThrow(errorBuilder.payment.instrument.couldNotDelete("456", new Error("Firestore error")));

      expect(mockFindOne).toHaveBeenCalledTimes(3);
      expect(mockFindOne).toHaveBeenNthCalledWith(1, { where: { appDatabaseId: "user123" } });
      expect(mockFindOne).toHaveBeenNthCalledWith(2, {
        where: { id: "456", user: { id: "123456" } },
      });
      expect(mockFindOne).toHaveBeenNthCalledWith(3, PaymentInstrument, {
        where: {
          user: { id: "123456" },
          state: PaymentInstrumentState.VERIFIED,
          isPayerAuthEnroled: true,
          expirationDate: expect.objectContaining({
            _type: "moreThan",
            _value: expect.any(Date),
          }),
        },
      });

      expect(mockSoftDelete).toHaveBeenCalledTimes(1);
      expect(mockSoftDelete).toHaveBeenCalledWith(PaymentInstrument, {
        id: "456",
        user: { id: "123456" },
        deletedAt: IsNull(),
      });

      expect(mockUpdate).toHaveBeenCalledTimes(2);
      expect(mockUpdate).toHaveBeenNthCalledWith(
        1,
        PaymentInstrument,
        { user: { id: "123456" } },
        { isPreferred: false },
      );
      expect(mockUpdate).toHaveBeenNthCalledWith(
        2,
        PaymentInstrument,
        { id: paymentInstruments[2].id },
        { isPreferred: true },
      );
    });

    it("should throw an error when delete fails in sql", async () => {
      mockSoftDelete.mockImplementation(() => ({
        affected: 0,
      }));

      await expect(
        service.delete(
          {
            paymentInstrumentId: "456",
          },
          "user123",
        ),
      ).rejects.toThrow(
        errorBuilder.payment.instrument.couldNotDelete("456", errorBuilder.payment.instrument.notFound("456")),
      );

      expect(mockFindOne).toHaveBeenCalledTimes(2);
      expect(mockFindOne).toHaveBeenNthCalledWith(1, { where: { appDatabaseId: "user123" } });
      expect(mockFindOne).toHaveBeenNthCalledWith(2, {
        where: { id: "456", user: { id: "123456" } },
      });

      expect(mockSoftDelete).toHaveBeenCalledTimes(1);
      expect(mockSoftDelete).toHaveBeenCalledWith(PaymentInstrument, {
        id: "456",
        user: { id: "123456" },
        deletedAt: IsNull(),
      });

      expect(mockUpdate).not.toHaveBeenCalled();
    });

    it("should throw an error when payment instrument is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockImplementationOnce(() => ({ id: "123456" }));
      mockFindOne.mockImplementationOnce(() => undefined);

      await expect(
        service.delete(
          {
            paymentInstrumentId: "456",
          },
          "user123",
        ),
      ).rejects.toThrow(errorBuilder.payment.instrument.notFound("456"));

      expect(mockFindOne).toHaveBeenCalledTimes(2);
      expect(mockFindOne).toHaveBeenNthCalledWith(1, { where: { appDatabaseId: "user123" } });
      expect(mockFindOne).toHaveBeenNthCalledWith(2, {
        where: { id: "456", user: { id: "123456" } },
      });

      expect(mockSoftDelete).not.toHaveBeenCalled();
      expect(mockUpdate).not.toHaveBeenCalled();
    });

    it("should throw an error when user is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockImplementationOnce(() => undefined);

      await expect(
        service.delete(
          {
            paymentInstrumentId: "456",
          },
          "user123",
        ),
      ).rejects.toThrow(errorBuilder.user.notFoundInSql("user123"));

      expect(mockFindOne).toHaveBeenCalledTimes(1);
      expect(mockFindOne).toHaveBeenNthCalledWith(1, { where: { appDatabaseId: "user123" } });

      expect(mockSoftDelete).not.toHaveBeenCalled();
      expect(mockUpdate).not.toHaveBeenCalled();
    });
  });

  describe("update", () => {
    let service: MePaymentInstrumentService;

    const paymentInstruments = [
      generatePaymentInstrument("123"),
      generatePaymentInstrument("456", true),
      generatePaymentInstrument("789"),
    ];

    beforeEach(() => {
      service = newTestMePaymentInstrumentService();

      mockFindOne.mockImplementationOnce(() => ({ id: "123456" }));
      mockFindOne.mockImplementationOnce(() => paymentInstruments[1]);
      mockFindOne.mockImplementationOnce(() => paymentInstruments[1]);

      mockCreate.mockImplementation(() => ({
        id: "123",
        metadata: {},
      }));

      setFirestoreMock.mockImplementation(() => paymentInstruments[1]);
      getFirestoreMock.mockImplementationOnce(() => ({
        exists: true,
        empty: false,
        data: () => ({ migratingPaymentToKraken: false }),
      }));
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it("should update the payment instrument", async () => {
      const result = await service.update(
        {
          paymentInstrumentId: "456",
        },
        {
          expirationYear: "2043",
          expirationMonth: "01",
          cardHolderName: "Kakarot",
          securityCode: "777",
        },
        "user123",
      );

      expect(result).toMatchObject({
        id: "456",
        token: "123",
        state: PaymentInstrumentState.VERIFIED,
        isPreferred: true,
        cardHolderName: "Kakarot",
        expirationDate: expect.any(Date),
        cardType: PaymentInstrumentType.MASTERCARD,
      });

      expect(mockFindOne).toHaveBeenCalledTimes(3);
      expect(mockFindOne).toHaveBeenNthCalledWith(1, { where: { appDatabaseId: "user123" } });
      expect(mockFindOne).toHaveBeenNthCalledWith(2, {
        where: { id: "456", user: { id: "123456" } },
      });

      expect(mockSave).toHaveBeenCalledTimes(7);

      expect(docFirestoreMock).toHaveBeenCalledTimes(2);
      expect(docFirestoreMock).toHaveBeenCalledWith("456");

      expect(setFirestoreMock).toHaveBeenCalledTimes(1);
      expect(setFirestoreMock).toHaveBeenCalledWith(
        expect.objectContaining({
          id: "456",
          expirationYear: "2043",
          expirationMonth: "01",
          cardHolderName: "Kakarot",
          cardType: PaymentInstrumentType.MASTERCARD,
        }),
        {
          merge: true,
        },
      );
    });

    it("should throw an error when transaction not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockImplementationOnce(() => ({ id: "123456" }));
      mockFindOne.mockImplementationOnce(() => undefined);

      await expect(
        service.update(
          {
            paymentInstrumentId: "456",
          },
          {
            expirationYear: "2043",
            expirationMonth: "01",
            cardHolderName: "Kakarot",
            securityCode: "777",
          },
          "user123",
        ),
      ).rejects.toThrow(errorBuilder.payment.instrument.notFound("456"));

      expect(mockFindOne).toHaveBeenCalledTimes(2);
      expect(mockFindOne).toHaveBeenNthCalledWith(1, { where: { appDatabaseId: "user123" } });
      expect(mockFindOne).toHaveBeenNthCalledWith(2, {
        where: { id: "456", user: { id: "123456" } },
      });

      expect(mockSave).not.toHaveBeenCalled();
      expect(docFirestoreMock).not.toHaveBeenCalled();
      expect(setFirestoreMock).not.toHaveBeenCalled();
    });

    it("should throw an error when user is not found", async () => {
      mockFindOne.mockReset();
      mockFindOne.mockImplementationOnce(() => undefined);

      await expect(
        service.update(
          {
            paymentInstrumentId: "456",
          },
          {
            expirationYear: "2043",
            expirationMonth: "01",
            cardHolderName: "Kakarot",
            securityCode: "777",
          },
          "user123",
        ),
      ).rejects.toThrow(errorBuilder.user.notFoundInSql("user123"));

      expect(mockFindOne).toHaveBeenCalledTimes(1);
      expect(mockFindOne).toHaveBeenNthCalledWith(1, { where: { appDatabaseId: "user123" } });

      expect(mockSave).not.toHaveBeenCalled();
      expect(docFirestoreMock).not.toHaveBeenCalled();
      expect(setFirestoreMock).not.toHaveBeenCalled();
    });
  });
});

import User from "../../../../../nestJs/modules/database/entities/user.entity";
import { MeNotificationTokenService } from "../../../../../nestJs/modules/me/modules/meNotificationToken/meNotificationToken.service";
import { mockUpsert } from "../../../../utils/services/FakeRepository.specs.utils";
import { newTestMeNotificationTokenService } from "../../../../utils/services/TestServices.specs.utils";

describe("meNotificationToken.service", () => {
  describe("upsertNotificationToken", () => {
    let service: MeNotificationTokenService;

    beforeEach(() => {
      service = newTestMeNotificationTokenService();

      mockUpsert.mockImplementation(() => ({
        saved: true,
      }));
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it("should upsert the notification token", async () => {
      const user = new User();
      user.id = "user123-db";
      const result = await service.upsertNotificationToken(
        {
          token: "456",
        },
        user,
      );

      expect(result).toEqual({
        saved: true,
      });

      expect(mockUpsert).toHaveBeenCalledWith(
        { token: "456", lastUpdateDate: expect.any(Date), user: { id: "user123-db" } },
        ["token"],
      );
    });
  });
});

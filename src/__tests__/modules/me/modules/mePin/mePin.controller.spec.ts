import { Request } from "express";

import User from "../../../../../nestJs/modules/database/entities/user.entity";
import { UpdateUserPinBody } from "../../../../../nestJs/modules/me/modules/mePin/dto/pin.dto";
import { MePinController } from "../../../../../nestJs/modules/me/modules/mePin/mePin.controller";
import { errorBuilder } from "../../../../../nestJs/modules/utils/utils/error.utils";
import { newTestMePinController } from "../../../../utils/services/TestController.specs.utils";

describe("mePin.controller", () => {
  let controller: MePinController;

  beforeEach(() => {
    jest.clearAllMocks();

    controller = newTestMePinController();
  });

  describe("updateUserPin", () => {
    it("throw error if old pin is not verified", async () => {
      // mockVerifyUserPin.mockResolvedValue({ verified: false, user: new User() });
      const body = new UpdateUserPinBody();
      body.pin = "oldPin";
      body.newPin = "newPin";
      const user_id = "qwertyuiop123456789";
      const req = {
        body: {},
        user: {
          user_id,
        },
        verifyPinResult: {
          verified: false,
          user: new User(),
        },
      } as unknown as Request;
      await expect(controller.updateUserPin(body, req)).rejects.toThrow(
        errorBuilder.user.pin.updatePinFailedWithWrongOldPin(user_id),
      );
    });
  });
});

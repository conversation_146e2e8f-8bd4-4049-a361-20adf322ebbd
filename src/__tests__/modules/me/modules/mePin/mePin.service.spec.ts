import User from "../../../../../nestJs/modules/database/entities/user.entity";
import { MePinService } from "../../../../../nestJs/modules/me/modules/mePin/mePin.service";
import { AppUser } from "../../../../../nestJs/modules/user/dto/user.dto";
import { errorBuilder } from "../../../../../nestJs/modules/utils/utils/error.utils";
import { mockHash } from "../../../../utils/bcrypt.specs.utils";
import { mockPrivateDecrypt } from "../../../../utils/crypto.specs.utils";
import { mockFindAppUserById, mockSave } from "../../../../utils/services/FakeRepository.specs.utils";
import { newTestMePinService } from "../../../../utils/services/TestServices.specs.utils";

jest.mock("typeorm/repository/Repository");
jest.mock("bcrypt");
jest.mock("crypto");

describe("mePin.service", () => {
  let mePinService: MePinService;
  const fakePin =
    "lqP3FsnwWnEZkYiKRbNMp6JIuafGv4p0jc0h5cAN9LSz5ISxwxgpxq1hCZfCtvD1OPARhDjwHepXaK6IvPkvkHw0hy/ru+8zUF9gTogd13nKleD4Rzskp/GJLhVrPMFO4Mxc7kkhNtMhnXzev4ojP7e7nF380X1YSH2xmFB8BI7apkivTjggcfClgT43//00Q4P1IuwdN1z2i5dEBVFkGxxjdPdQZ/ktNAnjCo3waT9wLmzP8M78wsSN0t9ICNxY/rFjp8ucKE0kfCMxiYlDksmgO+sr7H8i6IwJkAggMIm86WTsC+CkdwdQ73MUNPz/uWUpEzFdu6Eys03aQGI8Pg==";

  beforeEach(async () => {
    mePinService = newTestMePinService();
    mockSave.mockClear();
  });

  describe("createUserPin", () => {
    it("throw error if not find user in sql db", async () => {
      const error = new Error("App User: qwertyuiop123456789 not found in SQL");
      mockFindAppUserById.mockRejectedValue(error);

      await expect(mePinService.createUserPin(fakePin, "qwertyuiop123456789")).rejects.toThrow(
        errorBuilder.user.notFoundInSql("qwertyuiop123456789"),
      );
    });
    it("throw error if there is pin in user from sql db", async () => {
      const user = new User();
      user.privateKey = "fake_privateKey";
      user.salt = "fake_salt";
      user.hashedPin = "fake_pin";
      mockFindAppUserById.mockReturnValue(user);

      await expect(mePinService.createUserPin(fakePin, "qwertyuiop123456789")).rejects.toThrow(
        errorBuilder.user.pin.createPinFailedWithExistingPin("qwertyuiop123456789"),
      );
    });
    it("should save hashedPin to sql", async () => {
      const user = new User();
      user.privateKey = "fake_privateKey";
      user.salt = "fake_salt";
      mockFindAppUserById.mockReturnValue(user);

      mockPrivateDecrypt.mockResolvedValue(Buffer.from(""));
      mockHash.mockResolvedValue("fake_hashed_pin");

      await mePinService.createUserPin(fakePin, "qwertyuiop123456789");
      expect(mockSave).toHaveBeenCalledWith({
        privateKey: "fake_privateKey",
        salt: "fake_salt",
        hashedPin: "fake_hashed_pin",
        pinErrorCount: 0,
      });
    });
  });

  describe("updateUserPin", () => {
    it("should update hashedPin to sql", async () => {
      const user = new AppUser();
      user.privateKey = "fake_privateKey";
      user.salt = "fake_salt";
      user.hashedPin = "fake_hashed_pin_old";

      mockPrivateDecrypt.mockResolvedValue(Buffer.from(""));
      mockHash.mockResolvedValue("fake_hashed_pin_new");

      await mePinService.updateUserPin(user, fakePin);
      expect(mockSave).toHaveBeenCalledWith({
        privateKey: "fake_privateKey",
        salt: "fake_salt",
        hashedPin: "fake_hashed_pin_new",
        pinErrorCount: 0,
      });
    });
  });
});

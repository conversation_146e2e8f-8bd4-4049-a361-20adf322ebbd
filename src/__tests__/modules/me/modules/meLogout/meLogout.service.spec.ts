import { MeLogoutService } from "../../../../../nestJs/modules/me/modules/meLogout/meLogout.service";
import { AppUser } from "../../../../../nestJs/modules/user/dto/user.dto";
import { errorBuilder } from "../../../../../nestJs/modules/utils/utils/error.utils";
import { mockDelete, mockFindOne } from "../../../../utils/services/FakeRepository.specs.utils";
import { newTestMeLogoutService } from "../../../../utils/services/TestServices.specs.utils";

describe("meLogout.service", () => {
  describe("logout", () => {
    let service: MeLogoutService;

    beforeEach(() => {
      service = newTestMeLogoutService();
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it("should remove specific notification token for user", async () => {
      const user = new AppUser();
      user.id = "user123-db";
      user.appDatabaseId = "app123";
      user.phoneNumber = "+1234567890";
      user.salt = "salt123";
      user.publicKey = "publicKey123";
      user.privateKey = "privateKey123";
      jest.spyOn(service["userRepository"], "findAppUserById").mockResolvedValue(user);
      mockFindOne.mockResolvedValue({ id: "token123", token: "token123", user: { id: "user123-db" } });

      await service.logout("app123", "token123");

      expect(mockDelete).toHaveBeenCalledWith("token123");
    });

    it("should throw error if user not found", async () => {
      jest
        .spyOn(service["userRepository"], "findAppUserById")
        .mockRejectedValue(errorBuilder.user.notFoundInSql("app123"));

      await expect(service.logout("app123", "token123")).rejects.toThrow(errorBuilder.user.notFoundInSql("app123"));
      expect(mockDelete).not.toHaveBeenCalled();
    });
  });
});

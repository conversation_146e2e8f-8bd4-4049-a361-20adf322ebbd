import { <PERSON><PERSON><PERSON> } from "typeorm";

import { PAYMENT_MERCHANT_INFORMATION_MERCHANT_DESCRIPTOR_AUTH } from "@nest/modules/payment/modules/paymentInstrument/modules/globalPayment/dto/globalPayment.dto";

import { LocalizedLanguage } from "../../../../../nestJs/modules/location/dto/location.dto";
import { MeQrCodeService } from "../../../../../nestJs/modules/me/modules/meQrCode/meQrCode.service";
import { PaymentGatewayTypes } from "../../../../../nestJs/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentInstrumentState } from "../../../../../nestJs/modules/payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { QrCodeDecodedDataType, QrCodeDecodedDataVersion } from "../../../../../nestJs/modules/qrCode/dto/qrCode.dto";
import { TxTypes } from "../../../../../nestJs/modules/transaction/dto/txType.dto";
import { errorBuilder } from "../../../../../nestJs/modules/utils/utils/error.utils";
import defaultCreatePaymentResponseMock from "../../../../mockData/globalPayment/defaultCreatePaymentResponse.mock";
import {
  PaymentsApiMock,
  bindingMock,
  createPaymentMock,
} from "../../../../utils/cybersource-rest-client/cybersourceRestClientMocks.specs.utils";
import expects from "../../../../utils/expects.specs.utils";
import {
  createFirestoreMock,
  dataFirestoreMock,
  docFirestoreMock,
  docsDataFirestoreMock,
  getFirestoreMock,
  setFirestoreMock,
  whereFirestoreMock,
} from "../../../../utils/firebase-admin/firestoreMock.specs.utils";
import { mockFindOne, mockSave } from "../../../../utils/services/FakeRepository.specs.utils";
import { newTestMeQrCodeService } from "../../../../utils/services/TestServices.specs.utils";

import "../../../../initTests";

describe("meQrCode.service.pair", () => {
  let meQrCodeService: MeQrCodeService;

  beforeEach(() => {
    meQrCodeService = newTestMeQrCodeService();

    mockFindOne.mockImplementationOnce(() => ({
      id: "123456",
      type: TxTypes.TRIP,
      metadata: {
        licensePlate: "PLATE_1",
      },
    }));

    mockFindOne.mockImplementationOnce(() => ({
      id: "654321",
      paymentGateway: PaymentGatewayTypes.GLOBAL_PAYMENTS,
      user: {
        id: "user123",
        maskedPhoneNumber: "***1234",
      },
    }));

    dataFirestoreMock.mockImplementation(() => ({
      transactionId: "123456",
    }));
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it("should pair", async () => {
    getFirestoreMock.mockImplementationOnce(() => ({
      exists: true,
      empty: false,
      data: () => ({ qrCodeId: "fake_qr_id", transactionId: "123456" }),
    }));
    getFirestoreMock.mockImplementationOnce(() => ({
      exists: true,
      empty: false,
      data: () => ({ id: "123456" }),
      docs: [{ data: docsDataFirestoreMock }],
    }));
    getFirestoreMock.mockImplementationOnce(() => ({
      exists: true,
      empty: false,
      data: () => ({ migratingPaymentToKraken: false }),
    }));
    getFirestoreMock.mockImplementationOnce(() => ({
      exists: true,
      empty: false,
      data: () => ({ id: "fake_lock_id" }),
      docs: [{ data: docsDataFirestoreMock }],
    }));
    getFirestoreMock.mockImplementationOnce(() => ({
      exists: false,
      empty: true,
      data: undefined,
      docs: [{ data: undefined }],
    }));
    setFirestoreMock.mockResolvedValueOnce({
      writeTime: new Date("2024-01-01T00:00:01.000Z"),
    });
    mockSave.mockResolvedValueOnce({
      id: "fake_paymentTx_id",
    });
    mockSave.mockResolvedValueOnce({
      id: "123456",
      type: TxTypes.TRIP,
      metadata: {
        licensePlate: "PLATE_1",
        user: {
          id: "user123",
          phone: "***1234",
        },
      },
    });
    mockSave.mockImplementation(() => ({
      id: "123456",
      type: TxTypes.TRIP,
      metadata: {
        licensePlate: "PLATE_1",
        user: {
          id: "user123",
          phone: "***1234",
        },
      },
    }));
    const result = await meQrCodeService.pair(
      {
        baseUrl: "QR_CODE_URL_DASH",
        version: QrCodeDecodedDataVersion.ONE,
        type: QrCodeDecodedDataType.T2,
        qrCodeId: "SOME_ID",
        contextualCustomData: "data",
      },
      "user123",
      "1234",
      LocalizedLanguage.EN,
    );

    expect(result).toEqual({
      id: "123456",
      metadata: {
        licensePlate: "PLATE_1",
        user: {
          id: "user123",
          phone: "***1234",
        },
      },
      type: "TRIP",
      user: {
        id: "user123",
        maskedPhoneNumber: "***1234",
      },
    });

    expect(docFirestoreMock).toHaveBeenCalledTimes(6);
    expect(docFirestoreMock).toHaveBeenNthCalledWith(1, "SOME_ID");
    expect(docFirestoreMock).toHaveBeenNthCalledWith(2, expect.stringMatching(expects.uuid));
    expect(docFirestoreMock).toHaveBeenNthCalledWith(3, "server");
    expect(docFirestoreMock).toHaveBeenNthCalledWith(4, "123456");
    expect(docFirestoreMock).toHaveBeenNthCalledWith(5, "123456");

    expect(mockFindOne).toHaveBeenCalledTimes(2);
    expect(mockFindOne).toHaveBeenNthCalledWith(1, {
      where: {
        id: "123456",
      },
      relations: ["user"],
    });
    expect(mockFindOne).toHaveBeenNthCalledWith(2, {
      where: {
        id: "1234",
        state: PaymentInstrumentState.VERIFIED,
        expirationDate: MoreThan(expect.any(Date)),
        user: {
          appDatabaseId: "user123",
        },
      },
      relations: ["user"],
    });

    expect(whereFirestoreMock).toHaveBeenCalledTimes(2);
    expect(whereFirestoreMock).toHaveBeenNthCalledWith(1, "expired_at", ">", expect.any(Date));
    expect(whereFirestoreMock).toHaveBeenNthCalledWith(2, "unlocked_at", "==", null);

    expect(createFirestoreMock).toHaveBeenCalledWith({
      createdAt: expect.any(Date),
      createdBy: "user123",
      expiredAt: expect.any(Date),
      id: expect.stringMatching(expects.uuid),
      unlockedAt: null,
    });

    expect(createPaymentMock).toHaveBeenCalledWith(
      {
        clientReferenceInformation: {
          code: undefined,
        },
        orderInformation: {
          amountDetails: {
            currency: "HKD",
            totalAmount: "0",
          },
        },
        paymentInformation: {
          paymentInstrument: {
            id: undefined,
          },
        },
        processingInformation: {
          capture: false,
        },
        merchantInformation: {
          merchantDescriptor: { name: PAYMENT_MERCHANT_INFORMATION_MERCHANT_DESCRIPTOR_AUTH },
        },
      },
      expect.any(Function),
    );
  });

  it("should throw an error when the 0$ auth failed", async () => {
    PaymentsApiMock.mockImplementation(() => {
      return {
        createPayment: bindingMock(
          createPaymentMock.mockImplementation((...args: any[]) =>
            /// get last arg
            args[args.length - 1](null, { ...defaultCreatePaymentResponseMock, status: "ERROR" }),
          ),
        ),
      };
    });
    setFirestoreMock.mockResolvedValueOnce({
      writeTime: new Date("2024-01-01T00:00:01.000Z"),
    });
    await expect(
      meQrCodeService.pair(
        {
          baseUrl: "QR_CODE_URL_DASH",
          version: QrCodeDecodedDataVersion.ONE,
          type: QrCodeDecodedDataType.T2,
          qrCodeId: "SOME_ID",
          contextualCustomData: "data",
        },
        "user123",
        "1234",
        LocalizedLanguage.EN,
      ),
    ).rejects.toThrow(errorBuilder.payment.authFailed(expect.any(Object)));

    expect(docFirestoreMock).toHaveBeenCalledTimes(3);
    expect(docFirestoreMock).toHaveBeenNthCalledWith(1, "SOME_ID");
    expect(docFirestoreMock).toHaveBeenNthCalledWith(2, expect.stringMatching(expects.uuid));

    expect(mockFindOne).toHaveBeenCalledTimes(2);
    expect(mockFindOne).toHaveBeenNthCalledWith(1, {
      where: {
        id: "123456",
      },
      relations: ["user"],
    });
    expect(mockFindOne).toHaveBeenNthCalledWith(2, {
      where: {
        id: "1234",
        state: PaymentInstrumentState.VERIFIED,
        expirationDate: MoreThan(expect.any(Date)),
        user: {
          appDatabaseId: "user123",
        },
      },
      relations: ["user"],
    });

    expect(whereFirestoreMock).toHaveBeenCalledTimes(2);
    expect(whereFirestoreMock).toHaveBeenNthCalledWith(1, "expired_at", ">", expect.any(Date));
    expect(whereFirestoreMock).toHaveBeenNthCalledWith(2, "unlocked_at", "==", null);

    expect(createFirestoreMock).toHaveBeenCalledWith({
      createdAt: expect.any(Date),
      createdBy: "user123",
      expiredAt: expect.any(Date),
      id: expect.stringMatching(expects.uuid),
      unlockedAt: null,
    });

    expect(createPaymentMock).toHaveBeenCalledWith(
      {
        clientReferenceInformation: {
          code: undefined,
        },
        orderInformation: {
          amountDetails: {
            currency: "HKD",
            totalAmount: "0",
          },
        },
        paymentInformation: {
          paymentInstrument: {
            id: undefined,
          },
        },
        processingInformation: {
          capture: false,
        },
        merchantInformation: {
          merchantDescriptor: { name: PAYMENT_MERCHANT_INFORMATION_MERCHANT_DESCRIPTOR_AUTH },
        },
      },
      expect.any(Function),
    );
  });

  it("should throw an error when there is an activeLock", async () => {
    docsDataFirestoreMock.mockImplementation(() => {
      return {};
    });

    await expect(
      meQrCodeService.pair(
        {
          baseUrl: "QR_CODE_URL_DASH",
          version: QrCodeDecodedDataVersion.ONE,
          type: QrCodeDecodedDataType.T2,
          qrCodeId: "SOME_ID",
          contextualCustomData: "data",
        },
        "user123",
        "1234",
        LocalizedLanguage.EN,
      ),
    ).rejects.toThrow(errorBuilder.transaction.alreadyLocked("123456"));

    expect(docFirestoreMock).toHaveBeenCalledTimes(1);
    expect(docFirestoreMock).toHaveBeenCalledWith("SOME_ID");

    expect(mockFindOne).toHaveBeenCalledTimes(2);
    expect(mockFindOne).toHaveBeenNthCalledWith(1, {
      where: {
        id: "123456",
      },
      relations: ["user"],
    });
    expect(mockFindOne).toHaveBeenNthCalledWith(2, {
      where: {
        id: "1234",
        state: PaymentInstrumentState.VERIFIED,
        expirationDate: MoreThan(expect.any(Date)),
        user: {
          appDatabaseId: "user123",
        },
      },
      relations: ["user"],
    });

    expect(whereFirestoreMock).toHaveBeenCalledTimes(2);
    expect(whereFirestoreMock).toHaveBeenNthCalledWith(1, "expired_at", ">", expect.any(Date));
    expect(whereFirestoreMock).toHaveBeenNthCalledWith(2, "unlocked_at", "==", null);

    expect(createFirestoreMock).not.toHaveBeenCalled();
  });

  it("should throw an error when there is no licence plate", async () => {
    mockFindOne.mockReset();
    mockFindOne.mockImplementationOnce(() => ({
      id: "123456",
      type: TxTypes.TRIP,
      metadata: {},
    }));
    mockFindOne.mockImplementationOnce(() => ({}));

    await expect(
      meQrCodeService.pair(
        {
          baseUrl: "QR_CODE_URL_DASH",
          version: QrCodeDecodedDataVersion.ONE,
          type: QrCodeDecodedDataType.T2,
          qrCodeId: "SOME_ID",
          contextualCustomData: "data",
        },
        "user123",
        "1234",
        LocalizedLanguage.EN,
      ),
    ).rejects.toThrow(errorBuilder.transaction.trip.missingLicencePlate("123456"));

    expect(docFirestoreMock).toHaveBeenCalledTimes(1);
    expect(docFirestoreMock).toHaveBeenCalledWith("SOME_ID");

    expect(mockFindOne).toHaveBeenCalledTimes(2);
    expect(mockFindOne).toHaveBeenNthCalledWith(1, {
      where: {
        id: "123456",
      },
      relations: ["user"],
    });
    expect(mockFindOne).toHaveBeenNthCalledWith(2, {
      where: {
        id: "1234",
        state: PaymentInstrumentState.VERIFIED,
        expirationDate: MoreThan(expect.any(Date)),
        user: {
          appDatabaseId: "user123",
        },
      },
      relations: ["user"],
    });

    expect(whereFirestoreMock).not.toHaveBeenCalled();
    expect(createFirestoreMock).not.toHaveBeenCalled();
  });

  it("should throw an error when the tx is not a trip type", async () => {
    mockFindOne.mockReset();
    mockFindOne.mockImplementationOnce(() => ({
      id: "123456",
      type: TxTypes.TX_ADJUSTMENT,
      metadata: {},
    }));
    mockFindOne.mockImplementationOnce(() => ({}));

    await expect(
      meQrCodeService.pair(
        {
          baseUrl: "QR_CODE_URL_DASH",
          version: QrCodeDecodedDataVersion.ONE,
          type: QrCodeDecodedDataType.T2,
          qrCodeId: "SOME_ID",
          contextualCustomData: "data",
        },
        "user123",
        "1234",
        LocalizedLanguage.EN,
      ),
    ).rejects.toThrow(errorBuilder.transaction.wrongImplement(TxTypes.TX_ADJUSTMENT, "TripService/isTxAbleToPair"));

    expect(docFirestoreMock).toHaveBeenCalledTimes(1);
    expect(docFirestoreMock).toHaveBeenCalledWith("SOME_ID");

    expect(mockFindOne).toHaveBeenCalledTimes(1);
    expect(mockFindOne).toHaveBeenNthCalledWith(1, {
      where: {
        id: "123456",
      },
      relations: ["user"],
    });

    expect(whereFirestoreMock).not.toHaveBeenCalled();
    expect(createFirestoreMock).not.toHaveBeenCalled();
  });

  it("should throw an error when the tx is not found", async () => {
    mockFindOne.mockReset();
    mockFindOne.mockImplementation(() => undefined);

    await expect(
      meQrCodeService.pair(
        {
          baseUrl: "QR_CODE_URL_DASH",
          version: QrCodeDecodedDataVersion.ONE,
          type: QrCodeDecodedDataType.T2,
          qrCodeId: "SOME_ID",
          contextualCustomData: "data",
        },
        "user123",
        "1234",
        LocalizedLanguage.EN,
      ),
    ).rejects.toThrow(errorBuilder.transaction.notFound("123456"));

    expect(docFirestoreMock).toHaveBeenCalledTimes(1);
    expect(docFirestoreMock).toHaveBeenCalledWith("SOME_ID");

    expect(mockFindOne).toHaveBeenCalledTimes(1);
    expect(mockFindOne).toHaveBeenNthCalledWith(1, {
      where: {
        id: "123456",
      },
      relations: ["user"],
    });

    expect(whereFirestoreMock).not.toHaveBeenCalled();
    expect(createFirestoreMock).not.toHaveBeenCalled();
  });

  it("should throw an error when the tx is already paired", async () => {
    mockFindOne.mockReset();
    mockFindOne.mockImplementation(() => ({
      type: TxTypes.TRIP,
      user: { id: "1234" },
      metadata: { user: { id: "SOME_ID" } },
    }));

    await expect(
      meQrCodeService.pair(
        {
          baseUrl: "QR_CODE_URL_DASH",
          version: QrCodeDecodedDataVersion.ONE,
          type: QrCodeDecodedDataType.T2,
          qrCodeId: "SOME_ID",
          contextualCustomData: "data",
        },
        "user123",
        "1234",
        LocalizedLanguage.EN,
      ),
    ).rejects.toThrow(errorBuilder.qrcode.alreadyPaired("SOME_ID"));

    expect(docFirestoreMock).toHaveBeenCalledTimes(1);
    expect(docFirestoreMock).toHaveBeenCalledWith("SOME_ID");

    expect(mockFindOne).toHaveBeenCalledTimes(1);
    expect(mockFindOne).toHaveBeenNthCalledWith(1, {
      where: {
        id: "123456",
      },
      relations: ["user"],
    });

    expect(whereFirestoreMock).not.toHaveBeenCalled();
    expect(createFirestoreMock).not.toHaveBeenCalled();
  });

  it("should throw an error when the QR Code is not found", async () => {
    dataFirestoreMock.mockImplementation(() => undefined);

    await expect(
      meQrCodeService.pair(
        {
          baseUrl: "QR_CODE_URL_DASH",
          version: QrCodeDecodedDataVersion.ONE,
          type: QrCodeDecodedDataType.T2,
          qrCodeId: "SOME_ID",
          contextualCustomData: "data",
        },
        "user123",
        "1234",
        LocalizedLanguage.EN,
      ),
    ).rejects.toThrow(errorBuilder.qrcode.notFound("SOME_ID"));

    expect(docFirestoreMock).toHaveBeenCalledTimes(1);
    expect(docFirestoreMock).toHaveBeenCalledWith("SOME_ID");

    expect(mockFindOne).not.toHaveBeenCalled();
    expect(whereFirestoreMock).not.toHaveBeenCalled();
    expect(createFirestoreMock).not.toHaveBeenCalled();
  });
});

import { FcmService } from "../../../nestJs/modules/fcm/fcm.service";
import {
  NotificationRecipientType,
  NotificationTemplate,
  NotificationTriggerEventType,
} from "../../../nestJs/modules/fcm/types";
import { PreferredLanguageType } from "../../../nestJs/modules/identity/dto/user.dto";
import { errorBuilder } from "../../../nestJs/modules/utils/utils/error.utils";
import {
  CollectionReferenceMock,
  docFirestoreMock,
  getFirestoreMock,
} from "../../utils/firebase-admin/firestoreMock.specs.utils";
import { newTestFcmService } from "../../utils/services/TestServices.specs.utils";

describe("fcm.service", () => {
  let fcmService: FcmService;
  beforeEach(async () => {
    fcmService = newTestFcmService();
    jest.clearAllMocks();
    // mockSave.mockClear();
    // mockFindOne.mockClear();
  });

  describe("getTemplateFromFirestore", () => {
    beforeEach(() => {
      docFirestoreMock.mockImplementationOnce(() => ({
        get: getFirestoreMock,
      }));
      CollectionReferenceMock.mockImplementation(() => ({
        doc: docFirestoreMock,
      }));
    });
    it("throw error if not find notification template with event type in firestore", async () => {
      const fakeTemplates: NotificationTemplate[] = [];
      const fakeData = {
        notificationTemplates: fakeTemplates,
      };

      getFirestoreMock.mockImplementationOnce(() => ({
        exists: true,
        empty: false,
        data: () => fakeData,
        docs: { notificationTemplates: [] },
      }));

      await expect(
        fcmService.getTemplateMessageFromFirestore(
          NotificationTriggerEventType.RT2_ORDER_TIME_OUT,
          NotificationRecipientType.RIDER,
          PreferredLanguageType.EN,
        ),
      ).rejects.toThrow(errorBuilder.pushNotification.templateMissing(NotificationTriggerEventType.RT2_ORDER_TIME_OUT));
    });
    it("throw error if find notification template with event type but with wrong language", async () => {
      const fakeTemplates: NotificationTemplate[] = [
        {
          event: NotificationTriggerEventType.RT2_ORDER_TIME_OUT,
          templates: [
            {
              recipient: NotificationRecipientType.RIDER,
              messages: [
                {
                  body: "fake_body",
                  title: "fake_title",
                  language: PreferredLanguageType.ZHHK,
                },
              ],
            },
          ],
        },
      ];
      const fakeData = {
        notificationTemplates: fakeTemplates,
      };

      getFirestoreMock.mockImplementationOnce(() => ({
        exists: true,
        empty: false,
        data: () => fakeData,
        docs: { notificationTemplates: [] },
      }));
      await expect(
        fcmService.getTemplateMessageFromFirestore(
          NotificationTriggerEventType.RT2_ORDER_TIME_OUT,
          NotificationRecipientType.RIDER,
          PreferredLanguageType.EN,
        ),
      ).rejects.toThrow(errorBuilder.pushNotification.wrongTemplate(NotificationTriggerEventType.RT2_ORDER_TIME_OUT));
    });
    it("should get correct template", async () => {
      const fakeTemplates: NotificationTemplate[] = [
        {
          event: NotificationTriggerEventType.RT2_ORDER_TIME_OUT,
          templates: [
            {
              recipient: NotificationRecipientType.RIDER,
              messages: [
                {
                  body: "fake_body",
                  title: "fake_title",
                  language: PreferredLanguageType.EN,
                },
              ],
            },
          ],
        },
      ];
      const fakeData = {
        notificationTemplates: fakeTemplates,
      };
      getFirestoreMock.mockImplementationOnce(() => ({
        exists: true,
        empty: false,
        data: () => fakeData,
        docs: { notificationTemplates: [] },
      }));
      const result = await fcmService.getTemplateMessageFromFirestore(
        NotificationTriggerEventType.RT2_ORDER_TIME_OUT,
        NotificationRecipientType.RIDER,
        PreferredLanguageType.EN,
      );
      expect(result.body).toEqual("fake_body");
      expect(result.title).toEqual("fake_title");
    });
  });
});

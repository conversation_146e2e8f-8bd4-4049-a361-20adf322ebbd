import { qrCodeLinkTransform } from "../../../nestJs/modules/qrCode/dto/qrCode.dto";

const tests = [
  {
    url: "https://dash.com/?t=1T20SClCNCCDJTqGzxIrLddDASH02T",
    expected: {
      baseUrl: "dash.com",
      version: "1",
      type: "T2",
      qrCodeId: "0SClCNCCDJTqGzxIrLdd",
      contextualCustomData: "DASH02T",
    },
  },
  {
    url: "https://google.com/?t=2C50SClCNCCDJTqGzxIrLddSOME-OTHER-DATA",
    expected: {
      baseUrl: "google.com",
      version: "2",
      type: "C5",
      qrCodeId: "0SClCNCCDJTqGzxIrLdd",
      contextualCustomData: "SOME-OTHER-DATA",
    },
  },
  {
    url: "http://dash.com/?t=1T20SClCNCCDJTqGzxIrLdd",
    expected: {
      baseUrl: "dash.com",
      version: "1",
      type: "T2",
      qrCodeId: "0SClCNCCDJTqGzxIrLdd",
      contextualCustomData: "",
    },
  },
];

describe("qrCodeLinkTransform", () => {
  tests.forEach((test) => {
    it(`should return the correct QrCodeDecodedData for ${test.url}`, () => {
      const result = qrCodeLinkTransform(test.url);
      expect(result).toEqual(test.expected);
    });
  });
});

import QrCode from "../../../nestJs/modules/qrCode/dto/qrCode.dto";
import { QrType } from "../../../nestJs/modules/qrCode/dto/qrType";
import { QrCodeService } from "../../../nestJs/modules/qrCode/qrCode.service";

describe("QrCodeService", () => {
  let qrCodeService: QrCodeService;
  let mockAppDatabaseService: any;
  let mockConfigService: any;

  beforeEach(() => {
    mockAppDatabaseService = {
      qrRepository: jest.fn().mockReturnValue({
        createAndGet: jest.fn(),
      }),
    };
    mockConfigService = {
      getOrThrow: jest.fn(),
    };
    qrCodeService = new QrCodeService(mockAppDatabaseService, mockConfigService);
  });

  it("should create a QR code", async () => {
    const userId = "user1";
    const meterId = "meter1";
    const type = QrType.TRIP;
    const transactionId = "transaction1";
    const qrDocument = {
      transactionId: transactionId,
      metadata: { licensePlate: meterId },
      type: type,
      baseUrl: "testUrl",
      createdAt: expect.any(Date),
      createdBy: userId,
      expireAt: expect.any(Date),
    };

    mockConfigService.getOrThrow.mockReturnValue("testUrl");
    mockAppDatabaseService.qrRepository().createAndGet.mockResolvedValue(qrDocument);

    const result = await qrCodeService.create(userId, meterId, type, transactionId);

    expect(mockAppDatabaseService.qrRepository().createAndGet).toHaveBeenCalledWith(qrDocument);
    expect(result).toEqual(QrCode.fromQrDocument(qrDocument));
  });
});

import { AppController } from "../../nestJs/app.controller";

jest.mock("../../static/info.json", () => ({ commit_hash: "test" }));

describe("app.controller", () => {
  let controller: AppController;

  beforeEach(() => {
    jest.clearAllMocks();
    controller = new AppController();
  });

  describe("info", () => {
    it("should return infoJson", () => {
      const info = controller.info();
      expect(info).toStrictEqual({ commit_hash: "test" });
    });
  });
});

import {
  defaultRuntimeOptionsGen1,
  defaultHttpsOptionsGen2,
  defaultNestHttpsOptions,
  defaultNestOptions,
} from "../../functions/_defaultOptions";

/**
 * _defaultOptions test
 */
describe("_defaultOptions", () => {
  it("should generate the right options for function gen1 runtime options", () => {
    expect(defaultRuntimeOptionsGen1).toStrictEqual({
      minInstances: 2,
    });
  });
  it("should generate the right options for nest event functions", () => {
    expect(defaultNestOptions).toStrictEqual({
      region: "asia-east2",
      memory: "512MiB",
      minInstances: 2,
      maxInstances: 10,
    });
  });
  it("should generate the right options for nest https functions", () => {
    expect(defaultNestHttpsOptions).toStrictEqual({
      region: "asia-east2",
      memory: "512MiB",
      minInstances: 2,
      invoker: "public",
      maxInstances: 10,
    });
  });
  it("should generate the right options for https functions Gen 2", () => {
    expect(defaultHttpsOptionsGen2).toStrictEqual({
      region: "asia-east2",
      minInstances: 2,
      invoker: "public",
      maxInstances: 10,
    });
  });
});

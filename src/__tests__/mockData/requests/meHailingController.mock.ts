import { LocalizedLanguage } from "../../../nestJs/modules/location/dto/location.dto";
import { PartnerKey } from "../../../nestJs/modules/me/modules/meFleetTaxi/meFleetTaxi.interface";
import { HailingCreateOrderBody, PlatformType } from "../../../nestJs/modules/me/modules/meHailing/dto/meHailing.dto";

const meHailingControllerRequestMock: HailingCreateOrderBody = {
  platformType: PlatformType.DASH,
  fleetPartnerKey: PartnerKey.SYNCAB,
  fleetVehicleClass: {
    COMFORT: ["1", "2"],
    LUXURY: ["2"],
    STANDARD: ["1"],
  },
  itinerary: [
    {
      displayName: "TKO Plaza",
      formattedAddress: "Tseung Kwan O Plaza Club House, 1 Tong Tak St, Tseung Kwan O",
      index: 0,
      lat: 22.306779062030994,
      lng: 114.26161142551531,
      placeId: "ChIJE-FWMvMDBDQRBCxQzoVA-Ao",
    },
    {
      displayName: "Hysan Place",
      formattedAddress: "Hysan Place, 500 Hennessy Rd, Causeway Bay",
      index: 1,
      lat: 22.279593203640275,
      lng: 114.18396337853324,
      placeId: "ChIJuwh9w1YABDQRaRbZ1DgateI",
    },
  ],
  language: LocalizedLanguage.EN,
  options: {
    isAssistant: false,
    isPetFriendly: false,
  },
  sessionToken: "string",
  time: new Date(),
  operatingAreas: ["URBAN"],
  doubleTunnelFee: false,
};

export default meHailingControllerRequestMock;

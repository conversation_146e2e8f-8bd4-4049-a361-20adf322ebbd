import { randomUUID } from "crypto";

export default {
  _links: {
    self: {
      href: "string",
      method: "string",
    },
  },
  id: randomUUID(),
  submitTimeUtc: new Date().toISOString(),
  status: "AUTHORIZED",
  reconciliationId: "string",
  clientReferenceInformation: {
    code: "string",
    pausedRequestId: "string",
    comments: "string",
    partner: "string",
  },
  processorInformation: {
    approvalCode: "string",
    transactionId: "string",
    networkTransactionId: "string",
    responseCode: "string",
    responseDetails: "string",
    avs: {
      code: "string",
      codeRaw: "string",
    },
    merchantAdvice: {
      code: "string",
      codeRaw: "string",
    },
    consumerAuthenticationResponse: {
      code: "string",
      codeRaw: "string",
    },
    systemTraceAuditNumber: "string",
    retrievalReferenceNumber: "string",
  },
  paymentAccountInformation: {
    card: {
      type: "001",
    },
  },
  paymentInformation: {
    card: {
      number: "string",
      expirationMonth: "string",
      expirationYear: "string",
      securityCode: "string",
    },
    tokenizedCard: {
      number: "string",
      expirationMonth: "string",
      expirationYear: "string",
      securityCode: "string",
    },
    paymentInstrument: {
      id: "string",
    },
    instrumentIdentifier: {
      _links: [],
      id: "string",
      object: "string",
      state: "string",
      card: [],
      processingInformation: [],
      metadata: [],
    },
  },
  orderInformation: {
    amountDetails: {
      totalAmount: "string",
      authorizedAmount: "10",
      currency: "string",
    },
  },
};

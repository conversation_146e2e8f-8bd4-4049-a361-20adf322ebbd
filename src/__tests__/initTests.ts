import defaultCreatePaymentResponseMock from "./mockData/globalPayment/defaultCreatePaymentResponse.mock";
import computeRoutesRoutesApiResponseMock from "./mockData/google/computeRoutesRoutesApiResponse.mock";
import placeAutocompleteResponseMock from "./mockData/google/placeAutocompleteResponse.mock";
import placeDetailsNewApiResponseMock from "./mockData/google/placeDetailsNewApiResponse.mock";
import reverseGeocodeCoordResponseMock from "./mockData/google/reverseGeocodeCoordResponse.mock";
import textAutocompleteResponseMock from "./mockData/google/textAutocompleteResponse.mock";
import textSearchResponseMock from "./mockData/google/textSearchResponse.mock";
import { resetPubsubMocks } from "./utils/@google-cloud/pubsub.specs.utils";
import {
  GoogleMapsFindPlaceFromTextMock,
  GoogleMapsPlaceAutocompleteMock,
  GoogleMapsReverseGeocodeMock,
  GoogleMapsTextSearchMock,
} from "./utils/@googlemaps/google-maps-services-js.specs.utils";
import { GoogleMapsPlacesClientV1GetPlace } from "./utils/@googlemaps/places.specs.utils";
import { GoogleMapsRoutingClientV2ComputeRoutes } from "./utils/@googlemaps/routing.specs.utils";
import {
  axiosCreateMock,
  axiosDeleteMock,
  axiosGetMock,
  axiosPatchMock,
  axiosPostMock,
  axiosPutMock,
} from "./utils/axios/axiosMock.specs.utils";
import {
  CaptureApiMock,
  InstrumentIdentifierApiMock,
  PayerAuthenticationApiMock,
  PaymentInstrumentApiMock,
  PaymentsApiMock,
  VoidApiMock,
  bindingMock,
  createPaymentMock,
  promisifyMock,
} from "./utils/cybersource-rest-client/cybersourceRestClientMocks.specs.utils";
import {
  CollectionReferenceMock,
  collectionFirestoreMock,
  createFirestoreMock,
  dataFirestoreMock,
  deleteFirestoreMock,
  docFirestoreMock,
  docsDataFirestoreMock,
  getFirestoreMock,
  setFirestoreMock,
  updateFirestoreMock,
  whereFirestoreMock,
} from "./utils/firebase-admin/firestoreMock.specs.utils";
import { mockget, mockgetOrThrow } from "./utils/services/FakeConfigService.specs.utils";
import {
  mockAddGroupBy,
  mockAndWhere,
  mockCreateQueryBuilder,
  mockExecute,
  mockLeftJoinAndSelect,
  mockLimit,
  mockOffset,
  mockOrderBy,
  mockSelect,
  mockSet,
  mockUpdate,
  mockWhere,
  mockWithDeleted,
  mockTransaction,
  mockGetOne,
  mockGetRawMany,
  mockGetCount,
  mockSkip,
  mockTake,
  mockGetManyAndCount,
  mockOrWhere,
  mockCount,
  mockCreate,
  mockSave,
} from "./utils/services/FakeRepository.specs.utils";
import {
  andWhereTypeormMock,
  createQueryBuilderTypeormMock,
  createTypeormMock,
  executeTypeormMock,
  getOneTypeormMock,
  setTypeormMock,
  updateTypeormMock,
  whereTypeormMock,
} from "./utils/typeorm/repository/Repository.specs.utils";

beforeEach(() => {
  mockgetOrThrow.mockImplementation((key) => {
    return key;
  });

  mockget.mockImplementation((key) => {
    return process.env[key];
  });

  /**
   * Firestore Mocks
   */
  getFirestoreMock.mockImplementation(() => ({
    exists: true,
    empty: false,
    data: dataFirestoreMock,
    docs: [{ data: docsDataFirestoreMock }],
  }));

  createFirestoreMock.mockImplementation(() => new Promise((resolve) => resolve(1)));

  whereFirestoreMock.mockImplementation(CollectionReferenceMock);

  docFirestoreMock.mockImplementation(() => ({
    get: getFirestoreMock,
    update: updateFirestoreMock,
    set: setFirestoreMock,
    delete: deleteFirestoreMock,
    collection: collectionFirestoreMock,
    withConverter: CollectionReferenceMock,
  }));

  collectionFirestoreMock.mockImplementation(() => ({
    doc: docFirestoreMock,
    where: whereFirestoreMock,
  }));

  CollectionReferenceMock.mockImplementation(() => ({
    doc: docFirestoreMock,
    get: getFirestoreMock,
    update: updateFirestoreMock,
    set: setFirestoreMock,
    create: createFirestoreMock,
    collection: collectionFirestoreMock,
    where: whereFirestoreMock,
  }));

  /**
   * Typeorm Mocks
   */
  createQueryBuilderTypeormMock.mockImplementation(() => ({
    update: updateTypeormMock,
    set: setTypeormMock,
    where: whereTypeormMock,
    andWhere: andWhereTypeormMock,
    execute: executeTypeormMock,
    getOne: getOneTypeormMock,
    create: createTypeormMock,
  }));

  updateTypeormMock.mockReturnThis();
  setTypeormMock.mockReturnThis();
  whereTypeormMock.mockReturnThis();
  andWhereTypeormMock.mockReturnThis();
  executeTypeormMock.mockReturnThis();
  getOneTypeormMock.mockReturnThis();
  createTypeormMock.mockImplementation(() => ({ id: "123" }));
  mockCreate.mockImplementation(() => ({ id: "123" }));
  mockSave.mockImplementation(() => ({ id: "123" }));

  /**
   * Cybersource Rest Client Mocks
   */
  InstrumentIdentifierApiMock.mockImplementation(() => {
    return {
      postInstrumentIdentifier: bindingMock(promisifyMock),
    };
  });

  PayerAuthenticationApiMock.mockImplementation(() => {
    return {
      payerAuthSetup: bindingMock(promisifyMock),
      validateAuthenticationResults: bindingMock(promisifyMock),
      checkPayerAuthEnrollment: bindingMock(promisifyMock),
    };
  });

  PaymentsApiMock.mockImplementation(() => {
    return {
      createPayment: bindingMock(
        createPaymentMock.mockImplementation((...args: any[]) =>
          /// get last arg
          args[args.length - 1](null, defaultCreatePaymentResponseMock),
        ),
      ),
    };
  });

  PaymentInstrumentApiMock.mockImplementation(() => {
    return {
      postPaymentInstrument: bindingMock(promisifyMock),
    };
  });

  CaptureApiMock.mockImplementation(() => {
    return {
      capturePayment: bindingMock((...args: any[]) =>
        /// get last arg
        args[args.length - 1](null, {
          id: "123",
          state: "ACTIVE",
          status: "PENDING",
        }),
      ),
    };
  });

  VoidApiMock.mockImplementation(() => {
    return {
      voidPayment: bindingMock((...args: any[]) =>
        /// get last arg
        args[args.length - 1](null, {
          id: "123",
          status: "REVERSED",
          submitTimeUtc: "2021-01-01T00:00:00Z",
        }),
      ),
    };
  });

  /**
   * Fake Repository Mocks
   */
  mockCreateQueryBuilder.mockImplementation(() => ({
    withDeleted: mockWithDeleted,
    leftJoinAndSelect: mockLeftJoinAndSelect,
    addGroupBy: mockAddGroupBy,
    select: mockSelect,
    update: mockUpdate,
    set: mockSet,
    where: mockWhere,
    andWhere: mockAndWhere,
    orWhere: mockOrWhere,
    orderBy: mockOrderBy,
    limit: mockLimit,
    offset: mockOffset,
    execute: mockExecute,
    getOne: mockGetOne,
    getRawMany: mockGetRawMany,
    getCount: mockGetCount,
    skip: mockSkip,
    take: mockTake,
    count: mockCount,
    getManyAndCount: mockGetManyAndCount,
    create: mockCreate,
  }));

  mockUpdate.mockReturnThis();
  mockWithDeleted.mockReturnThis();
  mockLeftJoinAndSelect.mockReturnThis();
  mockAddGroupBy.mockReturnThis();
  mockSelect.mockReturnThis();
  mockSet.mockReturnThis();
  mockWhere.mockReturnThis();
  mockAndWhere.mockReturnThis();
  mockOrWhere.mockReturnThis();
  mockOrderBy.mockReturnThis();
  mockLimit.mockReturnThis();
  mockOffset.mockReturnThis();
  mockSkip.mockReturnThis();
  mockTake.mockReturnThis();
  mockGetManyAndCount.mockReturnThis();
  mockCount.mockImplementation(() => Promise.resolve(0));
  mockCreate.mockReturnThis();

  mockTransaction.mockImplementation((callback) => {
    return callback();
  });

  /**
   * Google
   */
  GoogleMapsFindPlaceFromTextMock.mockImplementation(() => {
    return new Promise((resolve) =>
      resolve({
        data: textAutocompleteResponseMock,
      }),
    );
  });
  GoogleMapsReverseGeocodeMock.mockImplementation(() => {
    return new Promise((resolve) =>
      resolve({
        data: reverseGeocodeCoordResponseMock,
      }),
    );
  });
  GoogleMapsTextSearchMock.mockImplementation(() => {
    return new Promise((resolve) =>
      resolve({
        data: textSearchResponseMock,
      }),
    );
  });
  GoogleMapsPlaceAutocompleteMock.mockImplementation(() => {
    return new Promise((resolve) =>
      resolve({
        data: placeAutocompleteResponseMock,
      }),
    );
  });
  GoogleMapsPlacesClientV1GetPlace.mockImplementation(() => {
    return new Promise((resolve) => resolve(placeDetailsNewApiResponseMock));
  });
  GoogleMapsRoutingClientV2ComputeRoutes.mockImplementation(() => {
    return new Promise((resolve) => resolve(computeRoutesRoutesApiResponseMock));
  });

  /**
   * Axios
   */
  axiosCreateMock.mockImplementation(() => Promise.resolve({ data: {} }));
  axiosGetMock.mockImplementation(() => Promise.resolve({ data: {} }));
  axiosPostMock.mockImplementation(() => Promise.resolve({ data: {} }));
  axiosPutMock.mockImplementation(() => Promise.resolve({ data: {} }));
  axiosPatchMock.mockImplementation(() => Promise.resolve({ data: {} }));
  axiosDeleteMock.mockImplementation(() => Promise.resolve({ data: {} }));

  /**
   * Csv Writer
   */
  jest.mock("csv-writer", () => {
    return {
      createObjectCsvWriter: jest.fn().mockImplementation(() => {
        return {
          writeRecords: jest.fn().mockImplementation(() => Promise.resolve()),
        };
      }),
    };
  });

  /**
   * Archiver
   */
  jest.mock("archiver", () => {
    return {
      create: jest.fn().mockReturnValue({
        pipe: jest.fn(),
        file: jest.fn(),
        finalize: jest.fn(),
        on: jest.fn((event, callback) => {
          if (event === "close") {
            callback();
          }
        }),
      }),
      registerFormat: jest.fn(),
    };
  });

  resetPubsubMocks();
});

afterEach(() => {
  jest.clearAllMocks();
});

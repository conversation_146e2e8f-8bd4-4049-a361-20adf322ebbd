import * as admin from "firebase-admin";
import firebaseFunctionTest from "firebase-functions-test";
const testEnv = firebaseFunctionTest({
  projectId: "dash-dev2-edcb3",
});

describe("functions", () => {
  let adminStub: any;
  let api: any;

  beforeAll(() => {
    // you can use `sinon.stub` instead
    adminStub = jest.spyOn(admin, "initializeApp");

    // after initializeApp call, we load our functions
    api = require("./index");
  });

  test("logstore", () => {
    console.log(adminStub);
    console.log(api);
    console.log("empty");
  });

  afterAll(() => {
    testEnv.cleanup();
  });
});

export const initMock = jest.fn();
export const useGlobalFiltersMock = jest.fn();
export const enableCorsMock = jest.fn();
export const getMock = jest.fn().mockImplementation((p) => new p());

export const NestFactoryMock = {
  create: jest.fn().mockImplementation(() => ({
    init: initMock.mockImplementation(() => "success"),
    useGlobalFilters: useGlobalFiltersMock,
    enableCors: enableCorsMock,
    get: getMock,
  })),
};

const createDocumentMock = jest.fn();
const setupMock = jest.fn();

export const SwaggerModuleMock = {
  createDocument: createDocumentMock,
  setup: setupMock,
};

export const setTitleMock = jest.fn();
export const setVersionMock = jest.fn();
export const addTagMock = jest.fn();
export const buildMock = jest.fn();
export const addServerMock = jest.fn();
export const addBearerAuthMock = jest.fn();

export class DocumentBuilderMock {
  setTitle = setTitleMock.mockReturnThis();
  setVersion = setVersionMock.mockReturnThis();
  addTag = addTagMock.mockReturnThis();
  build = buildMock.mockReturnThis();
  addServer = addServerMock.mockReturnThis();
  addBearerAuth = addBearerAuthMock.mockReturnThis();
}

export const ApiTagsMock = () => jest.fn();
export const ApiPropertyMock = () => jest.fn();
export const getSchemaPathMock = () => jest.fn();
export const ApiResponseMock = () => jest.fn();
export const ApiOperationMock = () => jest.fn();
export const ApiPropertyOptionalMock = () => jest.fn();
export const ApiOkResponseMock = () => jest.fn();
export const ApiServiceUnavailableResponseMock = () => jest.fn();
export const ApiHeaderMock = () => jest.fn();
export const ApiBearerAuthMock = () => jest.fn();
export const ApiNotFoundResponseMock = () => jest.fn();
export const ApiBadRequestResponseMock = () => jest.fn();
export const ApiInternalServerErrorResponseMock = () => jest.fn();
export const ApiNotImplementedResponseMock = () => jest.fn();
export const ApiUnauthorizedResponseMock = () => jest.fn();
export const ApiConflictResponseMock = () => jest.fn();
export const ApiUnprocessableEntityResponseMock = () => jest.fn();
export const ApiExtraModelsMock = () => jest.fn();
export const ApiBodyMock = () => jest.fn();
export const refsMock = jest.fn();

export const ApiClientMock = jest.fn();
export const ApiConfigMock = jest.fn();
export const PostInstrumentIdentifierRequestMock = jest.fn();
export const PayerAuthSetupRequestMock = jest.fn();
export const VoidPaymentRequestMock = jest.fn();
export const CreatePaymentRequestMock = jest.fn();
export const Tmsv2customersEmbeddedDefaultPaymentInstrumentEmbeddedInstrumentIdentifierCardMock = jest.fn();
export const Riskv1decisionsClientReferenceInformationMock = jest.fn();
export const Riskv1authenticationsetupsPaymentInformationMock = jest.fn();
export const Riskv1authenticationsetupsPaymentInformationTokenizedCardMock = jest.fn();
export const Ptsv2paymentsPaymentInformationInstrumentIdentifierMock = jest.fn();
export const Ptsv2paymentsClientReferenceInformationMock = jest.fn();
export const Ptsv2paymentsProcessingInformationMock = jest.fn();
export const Ptsv2paymentsPaymentInformationMock = jest.fn();
export const Ptsv2paymentsPaymentInformationCardMock = jest.fn();
export const Ptsv2paymentsOrderInformationMock = jest.fn();
export const Ptsv2paymentsOrderInformationAmountDetailsMock = jest.fn();
export const Ptsv2paymentsConsumerAuthenticationInformationMock = jest.fn();
export const Ptsv2paymentsOrderInformationBillToMock = jest.fn();
export const Ptsv2paymentsDeviceInformationMock = jest.fn();
export const ValidateRequestMock = jest.fn();
export const Riskv1authenticationresultsPaymentInformationCardMock = jest.fn();
export const Riskv1authenticationresultsPaymentInformationMock = jest.fn();
export const Riskv1authenticationresultsConsumerAuthenticationInformationMock = jest.fn();
export const Riskv1authenticationsOrderInformationMock = jest.fn();
export const CheckPayerAuthEnrollmentRequestMock = jest.fn();
export const Riskv1authenticationsOrderInformationAmountDetailsMock = jest.fn();
export const Riskv1authenticationsOrderInformationBillToMock = jest.fn();
export const Riskv1authenticationsPaymentInformationMock = jest.fn();
export const Riskv1authenticationsPaymentInformationCardMock = jest.fn();
export const PostPaymentInstrumentRequestMock = jest.fn();
export const Tmsv2customersEmbeddedDefaultPaymentInstrumentCardMock = jest.fn();
export const Tmsv2customersEmbeddedDefaultPaymentInstrumentBillToMock = jest.fn();
export const Tmsv2customersEmbeddedDefaultPaymentInstrumentInstrumentIdentifierMock = jest.fn();
export const Ptsv2paymentsPaymentInformationCustomerMock = jest.fn();
export const Ptsv2paymentsPaymentInformationPaymentInstrumentMock = jest.fn();
export const Riskv1authenticationsetupsPaymentInformationCustomerMock = jest.fn();
export const CapturePaymentRequestMock = jest.fn();
export const Ptsv2paymentsidcapturesOrderInformationMock = jest.fn();
export const Ptsv2paymentsidcapturesOrderInformationAmountDetailsMock = jest.fn();
export const Ptsv2paymentsMerchantInformationMock = jest.fn();
export const Ptsv2paymentsMerchantInformationMerchantDescriptorMock = jest.fn();
export const promisifyMock = (...args: any[]) =>
  /// get last arg
  args[args.length - 1](null, {
    id: "123",
    state: "ACTIVE",
    status: "COMPLETED",
    consumerAuthenticationInformation: {
      accessToken: "accessToken",
      referenceId: "referenceId",
      deviceDataCollectionUrl: "deviceDataCollectionUrl",
    },
  });

export const bindingMock = (mock: any) => ({
  bind: () => mock,
});
export const InstrumentIdentifierApiMock = jest.fn();
export const PayerAuthenticationApiMock = jest.fn();
export const PaymentsApiMock = jest.fn();
export const createPaymentMock = jest.fn();
export const PaymentInstrumentApiMock = jest.fn();
export const CaptureApiMock = jest.fn();
export const VoidApiMock = jest.fn();

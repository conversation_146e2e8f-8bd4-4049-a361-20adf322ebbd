export const findOneTypeormMock = jest.fn();
export const saveTypeormMock = jest.fn();
export const softDeleteTypeormMock = jest.fn();
export const upsertTypeormMock = jest.fn();
export const updateTypeormMock = jest.fn();
export const setTypeormMock = jest.fn();
export const whereTypeormMock = jest.fn();
export const andWhereTypeormMock = jest.fn();
export const executeTypeormMock = jest.fn();
export const getOneTypeormMock = jest.fn();
export const createTypeormMock = jest.fn();
export const createQueryBuilderTypeormMock = jest.fn();

export class RepositoryTypeormMock {
  findOne = findOneTypeormMock;
  save = saveTypeormMock;
  softDelete = softDeleteTypeormMock;
  upsert = upsertTypeormMock;
  createQueryBuilder = createQueryBuilderTypeormMock;
  create = createTypeormMock;
}

export const createDateColumnMock = () => jest.fn();
export const updateDateColumnMock = () => jest.fn();
export const primaryGeneratedColumnMock = () => jest.fn();
export const indexMock = () => jest.fn();
export const columnMock = () => jest.fn();
export const deleteDateColumnMock = () => jest.fn();
export const oneToManyMock = () => jest.fn();
export const entityMock = () => jest.fn();
export const manyToOneMock = () => jest.fn();
export const joinColumnMock = () => jest.fn();
export const primaryColumnMock = () => jest.fn();
export const afterInsertMock = () => jest.fn();
export const getMock = () => jest.fn();

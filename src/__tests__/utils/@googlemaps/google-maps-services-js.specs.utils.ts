export const GoogleMapsFindPlaceFromTextMock = jest.fn();
export const GoogleMapsReverseGeocodeMock = jest.fn();
export const GoogleMapsTextSearchMock = jest.fn();
export const GoogleMapsPlaceAutocompleteMock = jest.fn();

export class GoogleMapsClientMock {
  findPlaceFromText = GoogleMapsFindPlaceFromTextMock;
  reverseGeocode = GoogleMapsReverseGeocodeMock;
  textSearch = GoogleMapsTextSearchMock;
  placeAutocomplete = GoogleMapsPlaceAutocompleteMock;
}
export const GoogleMapsLanguageMock = {
  en: "en",
  zh_TW: "zh_TW",
  "zh-hk": "zh-hk",
};
export const GoogleMapsPlaceInputTypeMock = {
  textQuery: "textQuery",
};
export const GoogleMapsAddressTypeMock = {
  premise: "premise",
  neighborhood: "neighborhood",
  street_address: "street_address",
  administrative_area_level_1: "administrative_area_level_1",
  route: "route",
  street_number: "street_number",
  point_of_interest: "point_of_interest",
  plus_code: "plus_code",
};
export const GeocodingAddressComponentTypeMock = {
  street_number: "street_number",
  route: "route",
  point_of_interest: "point_of_interest",
  establishment: "establishment",
};

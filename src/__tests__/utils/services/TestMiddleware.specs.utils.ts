import FakeRepository from "./FakeRepository.specs.utils";
import { ProfileAuditRepository } from "../../../nestJs/modules/database/repositories/profileAudit.repository";
import { UserRepository } from "../../../nestJs/modules/database/repositories/user.repository";
import { VerifyPinMiddleware } from "../../../nestJs/modules/me/middlewares/verifyPin.middleware";

export const newTestVerifyPinMiddleware = () => {
  return new VerifyPinMiddleware(
    new FakeRepository() as unknown as UserRepository,
    new FakeRepository() as unknown as ProfileAuditRepository,
  );
};

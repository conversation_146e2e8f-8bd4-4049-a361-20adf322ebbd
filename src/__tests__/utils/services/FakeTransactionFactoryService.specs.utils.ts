export const mockPostPaymentProcess = jest.fn();
export const mockIsTxCalculationCorrect = jest.fn();
export const mockPrePaymentProcess = jest.fn();
export const mockSendReceipt = jest.fn();
export const mockHasCustomerInformation = jest.fn();
export const mockGetTxMonetary = jest.fn();
export const mockIsPayWithDash = jest.fn();
export const mockGetExpiresAt = jest.fn();
export const mockPostCashPaymentProcess = jest.fn();
export const mockPostTxEndProcess = jest.fn();
export const mockShouldUpdateTxMetadata = jest.fn();
export const mockGetTxsToPayout = jest.fn();
export const mockPostPayoutProcess = jest.fn();
export const mockUpdateMetadata = jest.fn();
export const mockLock = jest.fn();
export const mockPostAdjustmentProcess = jest.fn();

mockIsTxCalculationCorrect.mockImplementation(() => {
  return { result: true, reason: "OK!" };
});
mockHasCustomerInformation.mockImplementation(() => {
  return true;
});
mockGetTxMonetary.mockImplementation(() => {
  return { total: 100, dashFee: 10 };
});
mockIsPayWithDash.mockImplementation(() => {
  return true;
});
mockShouldUpdateTxMetadata.mockImplementation(() => {
  return true;
});
mockPostAdjustmentProcess.mockImplementation(() => {
  return { ok: "OK!" };
});
class FakeTransactionFactoryService {
  postAdjustmentProcess = mockPostAdjustmentProcess;
  postPaymentProcess = mockPostPaymentProcess;
  isTxCalculationCorrect = mockIsTxCalculationCorrect;
  prePaymentProcess = mockPrePaymentProcess;
  sendReceipt = mockSendReceipt;
  getTxMonetary = mockGetTxMonetary;
  getTotalAmountToCapture() {
    return 100;
  }
  getTxsToPayout = mockGetTxsToPayout;
  hasCustomerInformation = mockHasCustomerInformation;
  isPayWithDash = mockIsPayWithDash;
  getExpiresAt = mockGetExpiresAt;
  postCashPaymentProcess = mockPostCashPaymentProcess;
  postTxEndProcess = mockPostTxEndProcess;
  shouldUpdateTxMetadata = mockShouldUpdateTxMetadata;
  postPayoutProcess = mockPostPayoutProcess;
  updateMetadata = mockUpdateMetadata;
  lock = mockLock;
}

export default FakeTransactionFactoryService;

import { AdminNotificationService } from "../../../nestJs/modules/admin/adminNotification/admin-notification.service";
import {
  CreateManyNotificationRequestDto,
  UpdateManyNotificationRequestDto,
} from "../../../nestJs/modules/cloud-task-notification-handler/dto/request.dto";
import NotificationTask from "../../../nestJs/modules/database/entities/notificationTask.entity";
import { NotificationTaskFilterDto } from "../../../nestJs/modules/notification/dto/notification-filters.dto";
import { PaginatedResponseDto } from "../../../nestJs/modules/utils/paginated.dto";

export default class FakeAdminNotificationService {
  createNotifications = jest.fn<Promise<NotificationTask[]>, [CreateManyNotificationRequestDto, string]>();
  updateNotifications = jest.fn<Promise<NotificationTask>, [string, string, UpdateManyNotificationRequestDto]>();
  deleteNotifications = jest.fn<Promise<NotificationTask>, [string, string]>();
  getNotifications = jest.fn<Promise<PaginatedResponseDto<NotificationTask>>, [NotificationTaskFilterDto]>();
}

export const newFakeAdminNotificationService = () => {
  return new FakeAdminNotificationService() as unknown as AdminNotificationService;
};

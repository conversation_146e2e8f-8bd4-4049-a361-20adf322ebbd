export const registerWebhookMock = jest.fn();
export const processWebhookMessageMock = jest.fn();
export const getMeterHeartbeatWebhookCacheKeyMock = jest.fn();
export const getMeterSessionStartWebhookCacheKeyMock = jest.fn();
export const getMeterSessionEndWebhookCacheKeyMock = jest.fn();

class FakeWebhookService {
  registerWebhook = registerWebhookMock;
  processWebhookMessage = processWebhookMessageMock;
  getMeterHeartbeatWebhookCacheKey = getMeterHeartbeatWebhookCacheKeyMock;
  getMeterSessionStartWebhookCacheKey = getMeterSessionStartWebhookCacheKeyMock;
  getMeterSessionEndWebhookCacheKey = getMeterSessionEndWebhookCacheKeyMock;
}

export default FakeWebhookService;

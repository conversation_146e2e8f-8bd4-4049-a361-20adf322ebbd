export const mockSave = jest.fn();
export const mockFindOne = jest.fn();
export const mockFindOneBy = jest.fn();
export const mockUpsertTxAndUpdatePaymentTx = jest.fn();
export const mockUpsert = jest.fn();
export const mockAddTagsToTx = jest.fn();
export const mockFind = jest.fn();
export const mockDelete = jest.fn();
export const mockSoftDelete = jest.fn();
export const mockUpdate = jest.fn();
export const mockSkip = jest.fn();
export const mockTake = jest.fn();
export const mockGetManyAndCount = jest.fn();
export const mockUpsertPaymentTx = jest.fn();
export const mockAppByNameOrCreate = jest.fn();
export const mockFindAppUserById = jest.fn();
export const mockCreateProfileAudit = jest.fn();
export const mockTransaction = jest.fn();
export const mockGetRawMany = jest.fn();
export const mockGetCount = jest.fn();
export const mockGetOne = jest.fn();
export const mockUpdatePayoutAndGetMerchantsData = jest.fn();
export const mockClearNotificationToken = jest.fn();

export const mockCreateQueryBuilder = jest.fn();
export const mockFindRegisteredUserById = jest.fn();
export const mockCount = jest.fn();
export const mockWithDeleted = jest.fn();
export const mockLeftJoinAndSelect = jest.fn();
export const mockAddGroupBy = jest.fn();
export const mockSelect = jest.fn();
export const mockSet = jest.fn();
export const mockWhere = jest.fn();
export const mockAndWhere = jest.fn();
export const mockOrWhere = jest.fn();
export const mockOrderBy = jest.fn();
export const mockLimit = jest.fn();
export const mockOffset = jest.fn();
export const mockExecute = jest.fn();
export const mockInsert = jest.fn();
export const mockCreateTxAdjustment = jest.fn();
export const mockCreate = jest.fn();
export const mockExists = jest.fn();

class FakeRepository {
  save = mockSave;
  findOne = mockFindOne;
  findOneBy = mockFindOneBy;
  upsert = mockUpsert;
  upsertTxAndUpdatePaymentTx = mockUpsertTxAndUpdatePaymentTx;
  addTagsToTx = mockAddTagsToTx;
  find = mockFind;
  update = mockUpdate;
  insert = mockInsert;
  upsertPaymentTx = mockUpsertPaymentTx;
  appByNameOrCreate = mockAppByNameOrCreate;
  delete = mockDelete;
  findAppUserById = mockFindAppUserById;
  createProfileAudit = mockCreateProfileAudit;
  createQueryBuilder = mockCreateQueryBuilder;
  softDelete = mockSoftDelete;
  findRegisteredUserById = mockFindRegisteredUserById;
  count = mockCount;
  updatePayoutAndGetMerchantsData = mockUpdatePayoutAndGetMerchantsData;
  createTxAdjustment = mockCreateTxAdjustment;
  create = mockCreate;
  clearNotificationToken = mockClearNotificationToken;
  exists = mockExists;

  manager = {
    transaction: async (callback: (transactionalEntityManager: FakeRepository) => void) => {
      return Promise.resolve(callback(new FakeRepository()));
    },
  };
}

export default FakeRepository;

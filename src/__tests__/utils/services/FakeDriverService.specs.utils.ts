export const ingestFromBucketFileMock = jest.fn();
export const saveMerchantMock = jest.fn();
export const upsertMerchantsMock = jest.fn();
export const driverTripChangeMock = jest.fn();
class FakeDriverService {
  ingestFromBucketFile = ingestFromBucketFileMock;
  saveMerchant = saveMerchantMock;
  upsertMerchants = upsertMerchantsMock;
  driverTripChanged = driverTripChangeMock;
}

export default FakeDriverService;

import { of } from "rxjs";

export const mockPost = jest.fn();
export const mockGet = jest.fn();

export const resetHttpServiceMock = (data: any) => {
  mockPost.mockImplementation(() => {
    return of({ data });
  });
  mockGet.mockImplementation(() => {
    return of({ data });
  });
};

class FakeHttpService {
  post = mockPost;
  get = mockGet;
}

resetHttpServiceMock({});

export default FakeHttpService;

import { Buckets } from "../../../nestJs/modules/storage/storage.service";

export const readFileFromBucketMock = jest.fn();
export const savePayoutFileMock = jest.fn();
export const getXlsxFileContentFromBucketFileMock = jest.fn();
export const moveFileMock = jest.fn();

class FakeStorageService {
  bucketsNames = {
    [Buckets.PAYOUT]: "fake-bucket-name",
    [Buckets.PAYOUT_RESPONSE]: "fake-bucket-name",
  };
  readFileFromBucket = readFileFromBucketMock;
  savePayoutFile = savePayoutFileMock;
  getXlsxFileContentFromBucketFile = getXlsxFileContentFromBucketFileMock;
  moveFile = moveFileMock;
}

export default FakeStorageService;

export const transactionMock = jest.fn();
export const findOneMock = jest.fn();
export const insertMock = jest.fn();
export const findMock = jest.fn();
export const saveMock = jest.fn();
export const updateMock = jest.fn();

transactionMock.mockImplementation((callback) => {
  return callback(fakeEntityManager);
});

const fakeEntityManager = {
  transaction: transactionMock,
  findOne: findOneMock,
  insert: insertMock,
  find: findMock,
  save: saveMock,
  update: updateMock,
};

export default fakeEntityManager;

import PaymentTx from "../../../nestJs/modules/database/entities/paymentTx.entity";
import Tx from "../../../nestJs/modules/database/entities/tx.entity";
import { PaymentInformationStatus } from "../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../nestJs/modules/payment/dto/paymentInformationType.dto";
export const mockSendReceipt = jest.fn();
export const mockProcessCapture = jest.fn();
mockProcessCapture.mockImplementation((paymentTx: PaymentTx) => {
  const newPaymentTx = Object.create(PaymentTx.prototype);
  Object.assign(newPaymentTx, paymentTx);
  newPaymentTx.status = PaymentInformationStatus.SUCCESS;
  newPaymentTx.type = PaymentInformationType.CAPTURE;
  return new Promise((resolve) => resolve(newPaymentTx));
});

export const mockProcessAuthsToVoid = jest.fn();
export const mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid = jest.fn();
export const mockFindLastSuccessSaleWithoutVoid = jest.fn();
export const mockFindAllSalesInProcessingStatus = jest.fn();

export const mockProcessSale = jest.fn();
mockProcessSale.mockImplementation((foundTx: Tx, paymentInstrumentId: string, overwriteAmount?: number) => {
  const newPaymentTx = new PaymentTx();
  newPaymentTx.tx = foundTx;
  newPaymentTx.status = PaymentInformationStatus.SUCCESS;
  newPaymentTx.type = PaymentInformationType.SALE;
  return new Promise((resolve) => resolve(newPaymentTx));
});
class MockPaymentService {
  processCapture = mockProcessCapture;
  findLastSuccessAuthWithoutCaptureOrVoid(paymentTxs: PaymentTx[]) {
    return paymentTxs[0];
  }
  // processVoid = mockProcessVoid;
  sendReceipt = mockSendReceipt;
  findAllPreviousAuthAndSaleWithoutCaptureAndVoid = mockfindAllPreviousAuthAndSaleWithoutCaptureAndVoid;
  processAuthsToVoid = mockProcessAuthsToVoid;
  findLastSuccessSaleWithoutVoid = mockFindLastSuccessSaleWithoutVoid;
  findAllSalesInProcessingStatus = mockFindAllSalesInProcessingStatus;
  processSale = mockProcessSale;
}

export default MockPaymentService;

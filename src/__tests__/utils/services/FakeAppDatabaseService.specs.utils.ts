export const createMock = jest.fn();
export const findOneByIdMock = jest.fn();
export const getSessionMock = jest.fn();
export const removeDriverAndSessionMock = jest.fn();
export const saveTripMock = jest.fn();
export const saveSessionTripMock = jest.fn();
export const updateTripMock = jest.fn();
export const updateSessionTripMock = jest.fn();
export const getCashTripTTLMock = jest.fn();
export const getDashTripTTLMock = jest.fn();
export const getHailConfigMock = jest.fn();
export const addMock = jest.fn();
export const getReceiptFromTripMock = jest.fn();
export const updateMeterTripTipMock = jest.fn();
export const updateMeterTripMock = jest.fn();
export const updateDriverTripWithObjectMock = jest.fn();
export const updateSessionTripWithObjectMock = jest.fn();
export const getActiveLockMock = jest.fn();
export const createLockMock = jest.fn();
export const setCurrentTripMock = jest.fn();
export const getDriverMock = jest.fn();
export const getAuthByEmailMock = jest.fn();
export const getAuthByValidResetTokenMock = jest.fn();
export const saveAuthMock = jest.fn();
export const findLatestSecurityByTypeMock = jest.fn();
export const findMock = jest.fn();

export const setMock = jest.fn().mockImplementation(() => {
  return Promise.resolve();
});

export const runTransactionMock = jest.fn().mockImplementation(() => {
  return Promise.resolve();
});

const baseRepositoryMock = {
  set: setMock,
  create: createMock,
  findOneById: findOneByIdMock,
  find: findMock,
  collection: {
    add: addMock,
  },
};

class MockAppDatabaseService {
  runTransaction = runTransactionMock;
  tripRepository = () => ({
    ...baseRepositoryMock,
    getReceiptFromTrip: getReceiptFromTripMock,
  });
  meterTripRepository = (meterId: string) => ({
    ...baseRepositoryMock,
    removeDriverAndSession: removeDriverAndSessionMock,
    updateMeterTripTip: updateMeterTripTipMock,
    updateTrip: updateMeterTripMock,
  });

  sessionRepository = () => ({
    ...baseRepositoryMock,
    getSession: getSessionMock,
  });

  driverRepository = () => ({
    ...baseRepositoryMock,
    getDriver: getDriverMock,
  });

  driverTripRepository = () => ({
    ...baseRepositoryMock,
    saveTrip: saveTripMock,
    updateTrip: updateTripMock,
    updateTripWithObject: updateDriverTripWithObjectMock,
  });

  driverSessionTripRepository = () => ({
    ...baseRepositoryMock,
    saveSessionTrip: saveSessionTripMock,
    updateSessionTrip: updateSessionTripMock,
    updateSessionTripWithObject: updateSessionTripWithObjectMock,
  });

  configurationRepository = () => ({
    ...baseRepositoryMock,
    getCashTripTTL: getCashTripTTLMock,
    getDashTripTTL: getDashTripTTLMock,
    getHailConfig: getHailConfigMock,
  });

  driverNotificationsRepository = (driverId: string) => ({
    ...baseRepositoryMock,
  });

  userPaymentInstrumentRepository = (userId: string) => ({
    ...baseRepositoryMock,
  });

  userRepository = () => ({
    ...baseRepositoryMock,
  });
  meterTripLockRepository = () => ({
    ...baseRepositoryMock,
    getActiveLock: getActiveLockMock,
    createLock: createLockMock,
  });

  meterSecurityRepository = (meterId: string) => ({
    ...baseRepositoryMock,
    findLatestSecurityByType: findLatestSecurityByTypeMock,
  });

  qrRepository = () => ({
    ...baseRepositoryMock,
  });
  userTripRepository = () => ({
    ...baseRepositoryMock,
    setCurrentTrip: setCurrentTripMock,
  });

  authRepository = () => ({
    ...baseRepositoryMock,
    getAuthByEmail: getAuthByEmailMock,
    getAuthByValidResetToken: getAuthByValidResetTokenMock,
    saveAuth: saveAuthMock,
  });

  meterRepository = () => ({
    ...baseRepositoryMock,
  });

  fleetRepository = () => ({
    ...baseRepositoryMock,
  });
}

export default MockAppDatabaseService;

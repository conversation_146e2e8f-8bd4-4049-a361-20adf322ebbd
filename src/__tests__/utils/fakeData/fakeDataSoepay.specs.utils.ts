import {
  SoePayAuthFailureResponse,
  SoePayAuthSuccessResponse,
  SoePayFailureResponse,
  SoePayCaptureSuccessResponse,
  SoePayTranStatus,
  SoePayTranType,
  SoePayVoidSuccessResponse,
  SoePaySaleSuccessResponse,
} from "../../../nestJs/modules/payment/paymentFactory/modules/soepay/dto/soepay.model";

export const soepayAuthSuccess: SoePayAuthSuccessResponse = {
  baseAmount: 23.5,
  batch: 1,
  cancelable: true,
  capturable: true,
  cardData: { aid: "A0000000031010", appName: "Visa Credit", tc: "AA338D0C4743D8A9", tsi: "0000", tvr: "0000000000" },
  createById: "425954b17f0bbe2233a5a88fc21ee8400cfa0c20",
  createByName: "SOFTPOS",
  createTime: "2023-05-22T22:49:12+0000",
  entryMode: "CONTACTLESS",
  ip: "*************",
  lastUpdateTime: "2023-05-22T22:49:12+0000",
  lineItem: false,
  mappingId: "",
  messageId: "9081f05c-4ffd-49a8-980f-ad4b6f852989",
  payment: {
    adjustable: false,
    baseAmount: 23.5,
    cancelable: true,
    createTime: "2023-05-22T22:49:12+0000",
    directMode: false,
    lastUpdateTime: "2023-05-22T22:49:12+0000",
    netAmount: 0,
    orderId: "82b973592b327bc763778fabb511aa92",
    organize: { address: "<EMAIL>", name: "Vis-Mobility Dev Shop" },
    originTranType: "AUTH",
    payer: "************8868",
    payerFirst: "436605",
    payerLast: "8868",
    paymentId: "5ca5c6be5bae19e4fbe777f7f1efac68a5ab2d2c",
    paymentMethod: "VISA",
    paymentTrace: 72343,
    refundable: false,
    tipAmount: 0,
  },
  processorReference: "230522224912",
  processorResult: "00",
  receiptOffline: false,
  receiptOnline: true,
  refundable: false,
  requireSignature: false,
  settled: false,
  tipAmount: 0,
  totalAmount: 23.5,
  trace: 76647,
  tranId: "4b47b9d8820a0b4400440a46e49a3c4d7ad8e9c0",
  tranStatus: SoePayTranStatus.APPORVED,
  tranType: SoePayTranType.AUTH,
};

export const soepayCaptureSuccess: SoePayCaptureSuccessResponse = {
  data: {
    tranId: "5a04256cb293686739a252e052e898aaf321decf",
    tranStatus: SoePayTranStatus.APPORVED,
    totalAmount: 100,
    payment: {
      payerFirst: "123456",
      payerLast: "7890",
      paymentMethod: "VISA",
      createTime: "",
      lastUpdateTime: "",
      organize: {
        name: "",
        address: "",
        phone: "",
        autoSettlement: false,
        settlementTime: "",
        organizeId: "",
      },
      currency: "",
      baseAmount: 0,
      tipAmount: 0,
      originTranType: "",
      netAmount: 0,
      directMode: false,
      processor: "",
      status: "",
      orderId: "",
      cancelable: false,
      refundable: false,
      capturable: false,
      adjustable: false,
      paymentTrace: 0,
      paymentId: "",
      payer: "",
    },
    createTime: "",
    lastUpdateTime: "",
    receiptOffline: false,
    messageId: "",
    entryMode: "",
    commitTime: "",
    ip: "",
    outTranId: "",
    approvalCode: "",
    stan: "",
    mid: "",
    tid: "",
    processorReference: "",
    processorResult: "",
    cardholderVerification: "",
    batch: "",
    lineItem: false,
    requireSignature: false,
    cardData: {
      aid: "",
      approvalCode: "",
      localTranDateTime: "",
      stan: "",
      mid: "",
      tid: "",
    },
    app: {
      appType: "",
      equipment: {
        createTime: "",
        name: "",
        model: "",
        os: "",
        id: "",
      },
      id: "",
    },
    signatureOnPaper: false,
    createByName: "",
    createById: "",
    cancelable: false,
    refundable: false,
    capturable: false,
    createByType: "",
    tipAmount: 0,
    receiptOnline: false,
    settled: false,
    trace: 0,
    tranType: SoePayTranType.AUTH_COMPLETE,
    baseAmount: 0,
    tranParent: {
      trace: 0,
      tranId: "",
      tranType: "",
    },
    mdr: 0,
    tranParentId: "",
  },
  result: 0,
  message: "SUCCESS",
};

export const soepayCaptureFailure: SoePayFailureResponse = {
  result: 123,
  message: "",
};

export const soepayVoidFailure: SoePayFailureResponse = {
  result: 123,
  message: "",
};

export const soepayAuthFailure: SoePayAuthFailureResponse = {
  baseAmount: 150.0,
  errorCode: 3010,
  lineItem: false,
  mappingId: "",
  messageId: "fbd21121-9f67-4d7e-828c-ac6fb965a6f9",
  payment: { paymentId: "" },
  processorMessage: "Transaction aborted",
  receiptOnline: false,
  trace: 0,
  tranId: "",
  tranStatus: SoePayTranStatus.ERROR,
  tranType: SoePayTranType.AUTH,
};

export const soepayVoidSuccess: SoePayVoidSuccessResponse = {
  data: {
    tranId: "5a04256cb293686739a252e052e898aaf321decf",
    tranStatus: SoePayTranStatus.APPORVED,
    totalAmount: 100,
    payment: {
      payerFirst: "123456",
      payerLast: "7890",
      paymentMethod: "VISA",
      createTime: "",
      lastUpdateTime: "",
      organize: {
        name: "",
        address: "",
        phone: "",
        autoSettlement: false,
        settlementTime: "",
        organizeId: "",
      },
      currency: "",
      baseAmount: 0,
      tipAmount: 0,
      originTranType: "",
      netAmount: 0,
      directMode: false,
      processor: "",
      status: "",
      orderId: "",
      cancelable: false,
      refundable: false,
      capturable: false,
      adjustable: false,
      paymentTrace: 0,
      paymentId: "",
      payer: "",
    },
    createTime: "",
    lastUpdateTime: "",
    receiptOffline: false,
    messageId: "",
    entryMode: "",
    commitTime: "",
    ip: "",
    outTranId: "",
    approvalCode: "",
    stan: "",
    mid: "",
    tid: "",
    processorReference: "",
    processorResult: "",
    cardholderVerification: "",
    batch: "",
    lineItem: false,
    requireSignature: false,
    cardData: {
      aid: "",
      approvalCode: "",
      localTranDateTime: "",
      stan: "",
      mid: "",
      tid: "",
    },
    app: {
      appType: "",
      equipment: {
        createTime: "",
        name: "",
        model: "",
        os: "",
        id: "",
      },
      id: "",
    },
    signatureOnPaper: false,
    createByName: "",
    createById: "",
    cancelable: false,
    refundable: false,
    capturable: false,
    createByType: "",
    tipAmount: 0,
    receiptOnline: false,
    settled: false,
    trace: 0,
    tranType: SoePayTranType.VOID,
    baseAmount: 0,
    tranParent: {
      trace: 0,
      tranId: "",
      tranType: "",
    },
    mdr: 0,
    tranParentId: "",
  },
  result: 0,
  message: "SUCCESS",
};

export const soepaySaleSuccess: SoePaySaleSuccessResponse = {
  baseAmount: 23.5,
  batch: 1,
  cancelable: true,
  capturable: true,
  cardData: { aid: "A0000000031010", appName: "Visa Credit", tc: "AA338D0C4743D8A9", tsi: "0000", tvr: "0000000000" },
  createById: "425954b17f0bbe2233a5a88fc21ee8400cfa0c20",
  createByName: "SOFTPOS",
  createTime: "2023-05-22T22:49:12+0000",
  entryMode: "CONTACTLESS",
  ip: "*************",
  lastUpdateTime: "2023-05-22T22:49:12+0000",
  lineItem: false,
  mappingId: "",
  messageId: "9081f05c-4ffd-49a8-980f-ad4b6f852989",
  payment: {
    adjustable: false,
    baseAmount: 23.5,
    cancelable: true,
    createTime: "2023-05-22T22:49:12+0000",
    directMode: false,
    lastUpdateTime: "2023-05-22T22:49:12+0000",
    netAmount: 0,
    orderId: "82b973592b327bc763778fabb511aa92",
    organize: { address: "<EMAIL>", name: "Vis-Mobility Dev Shop" },
    originTranType: "AUTH",
    payer: "************8868",
    payerFirst: "436605",
    payerLast: "8868",
    paymentId: "5ca5c6be5bae19e4fbe777f7f1efac68a5ab2d2c",
    paymentMethod: "VISA",
    paymentTrace: 72343,
    refundable: false,
    tipAmount: 0,
  },
  processorReference: "230522224912",
  processorResult: "00",
  receiptOffline: false,
  receiptOnline: true,
  refundable: false,
  requireSignature: false,
  settled: false,
  tipAmount: 0,
  totalAmount: 23.5,
  trace: 76647,
  tranId: "4b47b9d8820a0b4400440a46e49a3c4d7ad8e9c0",
  tranStatus: SoePayTranStatus.APPORVED,
  tranType: SoePayTranType.SALE,
};

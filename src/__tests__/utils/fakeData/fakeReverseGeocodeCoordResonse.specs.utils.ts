export const test1 = {
  plus_code: {
    compound_code: "5678+CD Example Avenue, Sample City",
    global_code: "7PCD5678+CD",
  },
  results: [
    {
      address_components: [
        {
          long_name: "Sample Cafe",
          short_name: "Sample Cafe",
          types: ["establishment"],
        },
        {
          long_name: "10号",
          short_name: "10",
          types: ["street_number"],
        },
        {
          long_name: "Example Road",
          short_name: "Example Rd",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "10 Example Rd, Sample Neighborhood, Sample City",
      geometry: {
        location: {
          lat: 12.346789,
          lng: 98.765123,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.347789,
            lng: 98.766123,
          },
          southwest: {
            lat: 12.345789,
            lng: 98.764123,
          },
        },
      },
      place_id: "ChIJ1234567890EFGH",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "20号",
          short_name: "20",
          types: ["street_number"],
        },
        {
          long_name: "Example Street",
          short_name: "Example St",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "20 Example St, Sample Neighborhood, Sample City",
      geometry: {
        location: {
          lat: 12.34789,
          lng: 98.766789,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.34889,
            lng: 98.767789,
          },
          southwest: {
            lat: 12.34689,
            lng: 98.765789,
          },
        },
      },
      place_id: "ChIJ1234567890IJKL",
      types: ["neighborhood"],
    },
    {
      address_components: [
        {
          long_name: "Sample Restaurant",
          short_name: "Sample Restaurant",
          types: ["establishment"],
        },
        {
          long_name: "15号",
          short_name: "15",
          types: ["street_number"],
        },
        {
          long_name: "Another Road",
          short_name: "Another Rd",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "15 Another Rd, Sample Neighborhood, Sample City",
      geometry: {
        location: {
          lat: 12.348901,
          lng: 98.76789,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.349901,
            lng: 98.76889,
          },
          southwest: {
            lat: 12.347901,
            lng: 98.76689,
          },
        },
      },
      place_id: "ChIJ1234567890MNOP",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "5号",
          short_name: "5",
          types: ["street_number"],
        },
        {
          long_name: "Example Avenue",
          short_name: "Example Ave",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
        {
          long_name: "Sample City",
          short_name: "Sample City",
          types: ["premise"],
        },
      ],
      formatted_address: "5 Example Ave, Sample Neighborhood, Sample City",
      geometry: {
        location: {
          lat: 12.345678,
          lng: 98.765432,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.346678,
            lng: 98.766432,
          },
          southwest: {
            lat: 12.344678,
            lng: 98.764432,
          },
        },
      },
      place_id: "ChIJ1234567890ABCD",
      types: ["premise"],
    },
    {
      address_components: [
        {
          long_name: "30号",
          short_name: "30",
          types: ["street_number"],
        },
        {
          long_name: "Sample Boulevard",
          short_name: "Sample Blvd",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "30 Sample Blvd, Sample Neighborhood, Sample City",
      geometry: {
        location: {
          lat: 12.349012,
          lng: 98.768901,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.350012,
            lng: 98.769901,
          },
          southwest: {
            lat: 12.348012,
            lng: 98.767901,
          },
        },
      },
      place_id: "ChIJ1234567890QRST",
      types: ["street_address"],
    },
    {
      address_components: [
        {
          long_name: "Sample Store",
          short_name: "Sample Store",
          types: ["establishment"],
        },
        {
          long_name: "25号",
          short_name: "25",
          types: ["street_number"],
        },
        {
          long_name: "Example Avenue",
          short_name: "Example Ave",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "25 Example Ave, Sample Neighborhood, Sample City",
      geometry: {
        location: {
          lat: 12.350123,
          lng: 98.769012,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.351123,
            lng: 98.770012,
          },
          southwest: {
            lat: 12.349123,
            lng: 98.768012,
          },
        },
      },
      place_id: "ChIJ1234567890UVWX",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "40号",
          short_name: "40",
          types: ["street_number"],
        },
        {
          long_name: "Sample Parade",
          short_name: "Sample Pde",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "40 Sample Pde, Sample Neighborhood, Sample City",
      geometry: {
        location: {
          lat: 12.351234,
          lng: 98.770123,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.352234,
            lng: 98.771123,
          },
          southwest: {
            lat: 12.350234,
            lng: 98.769123,
          },
        },
      },
      place_id: "ChIJ1234567890YZAB",
      types: ["street_address"],
    },
    {
      address_components: [
        {
          long_name: "Sample Bakery",
          short_name: "Sample Bakery",
          types: ["establishment"],
        },
        {
          long_name: "35号",
          short_name: "35",
          types: ["street_number"],
        },
        {
          long_name: "Another Avenue",
          short_name: "Another Ave",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "35 Another Ave, Sample Neighborhood, Sample City",
      geometry: {
        location: {
          lat: 12.352345,
          lng: 98.771234,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.353345,
            lng: 98.772234,
          },
          southwest: {
            lat: 12.351345,
            lng: 98.770234,
          },
        },
      },
      place_id: "ChIJ1234567890CDEF",
      types: ["point_of_interest"],
    },
  ],
  status: "OK",
};

export const test2 = {
  plus_code: {
    compound_code: "7890+EF Sample Lane, Example Town",
    global_code: "7PEF7890+EF",
  },
  results: [
    {
      address_components: [
        {
          long_name: "8号",
          short_name: "8",
          types: ["street_number"],
        },
        {
          long_name: "Sample Lane",
          short_name: "Sample Ln",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
        {
          long_name: "Example Town",
          short_name: "Example Town",
          types: ["premise"],
        },
      ],
      formatted_address: "8 Sample Ln, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.56789,
          lng: -123.456789,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.56889,
            lng: -123.455789,
          },
          southwest: {
            lat: 34.56689,
            lng: -123.457789,
          },
        },
      },
      place_id: "ChIJ9876543210ABCDE",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "Sample Library",
          short_name: "Sample Library",
          types: ["establishment"],
        },
        {
          long_name: "12号",
          short_name: "12",
          types: ["street_number"],
        },
        {
          long_name: "Sample Boulevard",
          short_name: "Sample Blvd",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "12 Sample Blvd, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.568901,
          lng: -123.455012,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.569901,
            lng: -123.454012,
          },
          southwest: {
            lat: 34.567901,
            lng: -123.456012,
          },
        },
      },
      place_id: "ChIJ9876543210FGHIJ",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "25号",
          short_name: "25",
          types: ["street_number"],
        },
        {
          long_name: "Another Sample Street",
          short_name: "Another Sample St",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "25 Another Sample St, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.56989,
          lng: -123.454789,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.57089,
            lng: -123.453789,
          },
          southwest: {
            lat: 34.56889,
            lng: -123.455789,
          },
        },
      },
      place_id: "ChIJ9876543210JKLMN",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "Sample Park",
          short_name: "Sample Park",
          types: ["establishment"],
        },
        {
          long_name: "30号",
          short_name: "30",
          types: ["street_number"],
        },
        {
          long_name: "Sample Parkway",
          short_name: "Sample Pkwy",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "30 Sample Pkwy, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.570123,
          lng: -123.453456,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.571123,
            lng: -123.452456,
          },
          southwest: {
            lat: 34.569123,
            lng: -123.454456,
          },
        },
      },
      place_id: "ChIJ9876543210OPQRS",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "40号",
          short_name: "40",
          types: ["street_number"],
        },
        {
          long_name: "Sample Drive",
          short_name: "Sample Dr",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "40 Sample Dr, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.571234,
          lng: -123.452123,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.572234,
            lng: -123.451123,
          },
          southwest: {
            lat: 34.570234,
            lng: -123.453123,
          },
        },
      },
      place_id: "ChIJ9876543210TUVWX",
      types: ["premise"],
    },
    {
      address_components: [
        {
          long_name: "Sample Deli",
          short_name: "Sample Deli",
          types: ["establishment"],
        },
        {
          long_name: "50号",
          short_name: "50",
          types: ["street_number"],
        },
        {
          long_name: "Example Court",
          short_name: "Example Ct",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "50 Example Ct, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.572345,
          lng: -123.450234,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.573345,
            lng: -123.449234,
          },
          southwest: {
            lat: 34.571345,
            lng: -123.451234,
          },
        },
      },
      place_id: "ChIJ9876543210YZABC",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "Sample Museum",
          short_name: "Sample Museum",
          types: ["establishment"],
        },
        {
          long_name: "60号",
          short_name: "60",
          types: ["street_number"],
        },
        {
          long_name: "Sample Way",
          short_name: "Sample Way",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "60 Sample Way, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.573456,
          lng: -123.449012,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.574456,
            lng: -123.448012,
          },
          southwest: {
            lat: 34.572456,
            lng: -123.450012,
          },
        },
      },
      place_id: "ChIJ9876543210DEFGH",
      types: ["point_of_interest"],
    },
  ],
  status: "OK",
};

export const test3 = {
  plus_code: {
    compound_code: "1234+AB Sample Road, Testville",
    global_code: "7PAB1234+AB",
  },
  results: [
    {
      address_components: [
        {
          long_name: "1号",
          short_name: "1",
          types: ["street_number"],
        },
        {
          long_name: "Sample Road",
          short_name: "Sample Rd",
          types: ["route"],
        },
        {
          long_name: "Test Neighborhood",
          short_name: "Test Neighborhood",
          types: ["neighborhood"],
        },
        {
          long_name: "Testville",
          short_name: "Testville",
          types: ["premise"],
        },
      ],
      formatted_address: "1 Sample Rd, Test Neighborhood, Testville",
      geometry: {
        location: {
          lat: 23.456789,
          lng: -45.678901,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 23.457789,
            lng: -45.677901,
          },
          southwest: {
            lat: 23.455789,
            lng: -45.679901,
          },
        },
      },
      place_id: "ChIJ1234567890ABCDE",
      types: ["neighborhood"],
    },
    {
      address_components: [
        {
          long_name: "Test Library",
          short_name: "Test Library",
          types: ["establishment"],
        },
        {
          long_name: "5号",
          short_name: "5",
          types: ["street_number"],
        },
        {
          long_name: "Demo Avenue",
          short_name: "Demo Ave",
          types: ["route"],
        },
        {
          long_name: "Test Neighborhood",
          short_name: "Test Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "5 Demo Ave, Test Neighborhood, Testville",
      geometry: {
        location: {
          lat: 23.45789,
          lng: -45.67789,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 23.45889,
            lng: -45.67689,
          },
          southwest: {
            lat: 23.45689,
            lng: -45.67889,
          },
        },
      },
      place_id: "ChIJ1234567890FGHIJ",
      types: ["neighborhood"],
    },
    {
      address_components: [
        {
          long_name: "10号",
          short_name: "10",
          types: ["street_number"],
        },
        {
          long_name: "Another Sample Blvd",
          short_name: "Another Sample Blvd",
          types: ["route"],
        },
        {
          long_name: "Test Neighborhood",
          short_name: "Test Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "10 Another Sample Blvd, Test Neighborhood, Testville",
      geometry: {
        location: {
          lat: 23.458901,
          lng: -45.676789,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 23.459901,
            lng: -45.675789,
          },
          southwest: {
            lat: 23.457901,
            lng: -45.677789,
          },
        },
      },
      place_id: "ChIJ1234567890JKLMN",
      types: ["neighborhood"],
    },
    {
      address_components: [
        {
          long_name: "Test Restaurant",
          short_name: "Test Restaurant",
          types: ["establishment"],
        },
        {
          long_name: "15号",
          short_name: "15",
          types: ["street_number"],
        },
        {
          long_name: "Sample Boulevard",
          short_name: "Sample Blvd",
          types: ["route"],
        },
        {
          long_name: "Test Neighborhood",
          short_name: "Test Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "15 Sample Blvd, Test Neighborhood, Testville",
      geometry: {
        location: {
          lat: 23.459012,
          lng: -45.675678,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 23.460012,
            lng: -45.674678,
          },
          southwest: {
            lat: 23.458012,
            lng: -45.676678,
          },
        },
      },
      place_id: "ChIJ1234567890OPQRS",
      types: ["neighborhood"],
    },
    {
      address_components: [
        {
          long_name: "20号",
          short_name: "20",
          types: ["street_number"],
        },
        {
          long_name: "Test Parkway",
          short_name: "Test Pkwy",
          types: ["route"],
        },
        {
          long_name: "Test Neighborhood",
          short_name: "Test Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "20 Test Pkwy, Test Neighborhood, Testville",
      geometry: {
        location: {
          lat: 23.460123,
          lng: -45.674567,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 23.461123,
            lng: -45.673567,
          },
          southwest: {
            lat: 23.459123,
            lng: -45.675567,
          },
        },
      },
      place_id: "ChIJ1234567890TUVWX",
      types: ["neighborhood"],
    },
    {
      address_components: [
        {
          long_name: "25号",
          short_name: "25",
          types: ["street_number"],
        },
        {
          long_name: "Demo Way",
          short_name: "Demo Way",
          types: ["route"],
        },
        {
          long_name: "Test Neighborhood",
          short_name: "Test Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "25 Demo Way, Test Neighborhood, Testville",
      geometry: {
        location: {
          lat: 23.461234,
          lng: -45.673456,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 23.462234,
            lng: -45.672456,
          },
          southwest: {
            lat: 23.460234,
            lng: -45.674456,
          },
        },
      },
      place_id: "ChIJ1234567890YZABC",
      types: ["neighborhood"],
    },
    {
      address_components: [
        {
          long_name: "30号",
          short_name: "30",
          types: ["street_number"],
        },
        {
          long_name: "Sample Court",
          short_name: "Sample Ct",
          types: ["route"],
        },
        {
          long_name: "Test Neighborhood",
          short_name: "Test Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "30 Sample Ct, Test Neighborhood, Testville",
      geometry: {
        location: {
          lat: 23.462345,
          lng: -45.672345,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 23.463345,
            lng: -45.671345,
          },
          southwest: {
            lat: 23.461345,
            lng: -45.673345,
          },
        },
      },
      place_id: "ChIJ1234567890CDEF",
      types: ["neighborhood"],
    },
  ],
  status: "OK",
};

export const test4 = {
  plus_code: {
    compound_code: "5678+JK Example Drive, Sampletown",
    global_code: "7PJK5678+JK",
  },
  results: [
    {
      address_components: [
        {
          long_name: "2号",
          short_name: "2",
          types: ["street_number"],
        },
        {
          long_name: "Example Drive",
          short_name: "Example Dr",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
        {
          long_name: "Sampletown",
          short_name: "Sampletown",
          types: ["premise"],
        },
      ],
      formatted_address: "2 Example Dr, Sample Neighborhood, Sampletown",
      geometry: {
        location: {
          lat: 12.345678,
          lng: -98.765432,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.346678,
            lng: -98.764432,
          },
          southwest: {
            lat: 12.344678,
            lng: -98.766432,
          },
        },
      },
      place_id: "ChIJ2345678901ABCDE",
      types: ["premise"],
    },
    {
      address_components: [
        {
          long_name: "Sample Park",
          short_name: "Sample Park",
          types: ["establishment"],
        },
        {
          long_name: "6号",
          short_name: "6",
          types: ["street_number"],
        },
        {
          long_name: "Sample Avenue",
          short_name: "Sample Ave",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "6 Sample Ave, Sample Neighborhood, Sampletown",
      geometry: {
        location: {
          lat: 12.346789,
          lng: -98.764321,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.347789,
            lng: -98.763321,
          },
          southwest: {
            lat: 12.345789,
            lng: -98.765321,
          },
        },
      },
      place_id: "ChIJ2345678901FGHIJ",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "12号",
          short_name: "12",
          types: ["floor"],
        },
        {
          long_name: "Another Example Road",
          short_name: "Another Example Rd",
          types: ["floor"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["floor"],
        },
      ],
      formatted_address: "12 Another Example Rd, Sample Neighborhood, Sampletown",
      geometry: {
        location: {
          lat: 12.34789,
          lng: -98.763456,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.34889,
            lng: -98.762456,
          },
          southwest: {
            lat: 12.34689,
            lng: -98.764456,
          },
        },
      },
      place_id: "ChIJ2345678901JKLMN",
      types: ["street_address"],
    },
    {
      address_components: [
        {
          long_name: "Sample Bakery",
          short_name: "Sample Bakery",
          types: ["establishment"],
        },
        {
          long_name: "18号",
          short_name: "18",
          types: ["street_number"],
        },
        {
          long_name: "Example Boulevard",
          short_name: "Example Blvd",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "18 Example Blvd, Sample Neighborhood, Sampletown",
      geometry: {
        location: {
          lat: 12.348901,
          lng: -98.762345,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.349901,
            lng: -98.761345,
          },
          southwest: {
            lat: 12.347901,
            lng: -98.763345,
          },
        },
      },
      place_id: "ChIJ2345678901OPQRS",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "24号",
          short_name: "24",
          types: ["street_number"],
        },
        {
          long_name: "Sample Parkway",
          short_name: "Sample Pkwy",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "24 Sample Pkwy, Sample Neighborhood, Sampletown",
      geometry: {
        location: {
          lat: 12.349012,
          lng: -98.761234,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.350012,
            lng: -98.760234,
          },
          southwest: {
            lat: 12.348012,
            lng: -98.762234,
          },
        },
      },
      place_id: "ChIJ2345678901TUVWX",
      types: ["street_address"],
    },
    {
      address_components: [
        {
          long_name: "30号",
          short_name: "30",
          types: ["street_number"],
        },
        {
          long_name: "Demo Road",
          short_name: "Demo Rd",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "30 Demo Rd, Sample Neighborhood, Sampletown",
      geometry: {
        location: {
          lat: 12.350123,
          lng: -98.760123,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.351123,
            lng: -98.759123,
          },
          southwest: {
            lat: 12.349123,
            lng: -98.761123,
          },
        },
      },
      place_id: "ChIJ2345678901YZABC",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "36号",
          short_name: "36",
          types: ["street_number"],
        },
        {
          long_name: "Sample Way",
          short_name: "Sample Way",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "36 Sample Way, Sample Neighborhood, Sampletown",
      geometry: {
        location: {
          lat: 12.351234,
          lng: -98.759012,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.352234,
            lng: -98.758012,
          },
          southwest: {
            lat: 12.350234,
            lng: -98.760012,
          },
        },
      },
      place_id: "ChIJ2345678901CDEF",
      types: ["point_of_interest"],
    },
  ],
  status: "OK",
};

export const test5 = {
  plus_code: {
    compound_code: "7890+EF Sample Lane, Example Town",
    global_code: "7PEF7890+EF",
  },
  results: [
    {
      address_components: [
        {
          long_name: "8号",
          short_name: "8",
          types: ["street_number"],
        },
        {
          long_name: "Sample Lane",
          short_name: "Sample Ln",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
        {
          long_name: "Example Town",
          short_name: "Example Town",
          types: ["premise"],
        },
      ],
      formatted_address: "8 Sample Ln, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.56789,
          lng: -123.456789,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.56889,
            lng: -123.455789,
          },
          southwest: {
            lat: 34.56689,
            lng: -123.457789,
          },
        },
      },
      place_id: "ChIJ9876543210ABCDE",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "Sample Library",
          short_name: "Sample Library",
          types: ["establishment"],
        },
        {
          long_name: "12号",
          short_name: "12",
          types: ["street_number"],
        },
        {
          long_name: "Sample Boulevard",
          short_name: "Sample Blvd",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "12 Sample Blvd, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.568901,
          lng: -123.455012,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.569901,
            lng: -123.454012,
          },
          southwest: {
            lat: 34.567901,
            lng: -123.456012,
          },
        },
      },
      place_id: "ChIJ9876543210FGHIJ",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "25号",
          short_name: "25",
          types: ["street_number"],
        },
        {
          long_name: "Another Sample Street",
          short_name: "Another Sample St",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "25 Another Sample St, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.56989,
          lng: -123.454789,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.57089,
            lng: -123.453789,
          },
          southwest: {
            lat: 34.56889,
            lng: -123.455789,
          },
        },
      },
      place_id: "ChIJ9876543210JKLMN",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "Sample Park",
          short_name: "Sample Park",
          types: ["establishment"],
        },
        {
          long_name: "30号",
          short_name: "30",
          types: ["street_number"],
        },
        {
          long_name: "Sample Parkway",
          short_name: "Sample Pkwy",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "30 Sample Pkwy, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.570123,
          lng: -123.453456,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.571123,
            lng: -123.452456,
          },
          southwest: {
            lat: 34.569123,
            lng: -123.454456,
          },
        },
      },
      place_id: "ChIJ9876543210OPQRS",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "Sample Drive",
          short_name: "Sample Dr",
          types: ["route"],
        },
        {
          long_name: "40号",
          short_name: "40",
          types: ["street_number"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "40 Sample Dr, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.571234,
          lng: -123.452123,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.572234,
            lng: -123.451123,
          },
          southwest: {
            lat: 34.570234,
            lng: -123.453123,
          },
        },
      },
      place_id: "ChIJ9876543210TUVWX",
      types: ["premise"],
    },
    {
      address_components: [
        {
          long_name: "Sample Deli",
          short_name: "Sample Deli",
          types: ["establishment"],
        },
        {
          long_name: "50号",
          short_name: "50",
          types: ["street_number"],
        },
        {
          long_name: "Example Court",
          short_name: "Example Ct",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "50 Example Ct, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.572345,
          lng: -123.450234,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.573345,
            lng: -123.449234,
          },
          southwest: {
            lat: 34.571345,
            lng: -123.451234,
          },
        },
      },
      place_id: "ChIJ9876543210YZABC",
      types: ["point_of_interest"],
    },
    {
      address_components: [
        {
          long_name: "Sample Museum",
          short_name: "Sample Museum",
          types: ["establishment"],
        },
        {
          long_name: "60号",
          short_name: "60",
          types: ["street_number"],
        },
        {
          long_name: "Sample Way",
          short_name: "Sample Way",
          types: ["route"],
        },
        {
          long_name: "Example Neighborhood",
          short_name: "Example Neighborhood",
          types: ["neighborhood"],
        },
      ],
      formatted_address: "60 Sample Way, Example Neighborhood, Example Town",
      geometry: {
        location: {
          lat: 34.573456,
          lng: -123.449012,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 34.574456,
            lng: -123.448012,
          },
          southwest: {
            lat: 34.572456,
            lng: -123.450012,
          },
        },
      },
      place_id: "ChIJ9876543210DEFGH",
      types: ["point_of_interest"],
    },
  ],
  status: "OK",
};

export const test6 = {
  plus_code: {
    compound_code: "C6G5+W5P Pak Shek Kok, Hong Kong",
    global_code: "7PJPC6G5+W5",
  },
  results: [
    {
      address_components: [
        {
          long_name: "C6G5+W5",
          short_name: "C6G5+W5",
          types: ["plus_code"],
        },
        {
          long_name: "Pak Shek Kok",
          short_name: "Pak Shek Kok",
          types: ["neighborhood", "political"],
        },
        {
          long_name: "New Territories",
          short_name: "New Territories",
          types: ["administrative_area_level_1", "political"],
        },
        {
          long_name: "Hong Kong",
          short_name: "HK",
          types: ["country", "political"],
        },
      ],
      formatted_address: "C6G5+W5 Pak Shek Kok, Hong Kong",
      geometry: {
        bounds: {
          northeast: { lat: 22.427375, lng: 114.208 },
          southwest: { lat: 22.42725, lng: 114.207875 },
        },
        location: { lat: 22.4273384, lng: 114.2079403 },
        location_type: "GEOMETRIC_CENTER",
        viewport: {
          northeast: { lat: 22.4286614802915, lng: 114.2092864802915 },
          southwest: { lat: 22.4259635197085, lng: 114.2065885197085 },
        },
      },
      place_id: "GhIJMVOkDGZtNkARTwHV5E6NXEA",
      plus_code: {
        compound_code: "C6G5+W5 Pak Shek Kok, Hong Kong",
        global_code: "7PJPC6G5+W5",
      },
      types: ["plus_code"],
    },
    {
      address_components: [
        {
          long_name: "Fo Yin Road",
          short_name: "Fo Yin Rd",
          types: ["route"],
        },
        {
          long_name: "Science Park",
          short_name: "Science Park",
          types: ["neighborhood", "political"],
        },
        {
          long_name: "New Territories",
          short_name: "New Territories",
          types: ["administrative_area_level_1", "political"],
        },
        {
          long_name: "Hong Kong",
          short_name: "HK",
          types: ["country", "political"],
        },
      ],
      formatted_address: "Fo Yin Rd, Science Park, Hong Kong",
      geometry: {
        bounds: {
          northeast: { lat: 22.4275325, lng: 114.2078161 },
          southwest: { lat: 22.4273265, lng: 114.2076536 },
        },
        location: { lat: 22.4274295, lng: 114.2077348 },
        location_type: "GEOMETRIC_CENTER",
        viewport: {
          northeast: { lat: 22.4287784802915, lng: 114.2090838302915 },
          southwest: { lat: 22.4260805197085, lng: 114.2063858697085 },
        },
      },
      place_id: "ChIJgVr06ZAIBDQRoP5TSbOkhUQ",
      types: ["route"],
    },
    {
      address_components: [
        {
          long_name: "Science Park",
          short_name: "Science Park",
          types: ["neighborhood", "political"],
        },
        {
          long_name: "New Territories",
          short_name: "New Territories",
          types: ["administrative_area_level_1", "political"],
        },
        {
          long_name: "Hong Kong",
          short_name: "HK",
          types: ["country", "political"],
        },
      ],
      formatted_address: "Science Park, Hong Kong",
      geometry: {
        bounds: {
          northeast: { lat: 22.4311816, lng: 114.215094 },
          southwest: { lat: 22.4221368, lng: 114.2027736 },
        },
        location: { lat: 22.4290682, lng: 114.2085857 },
        location_type: "APPROXIMATE",
        viewport: {
          northeast: { lat: 22.4311816, lng: 114.215094 },
          southwest: { lat: 22.4221368, lng: 114.2027736 },
        },
      },
      place_id: "ChIJJYNy7JAIBDQRrPnV1FQwWZQ",
      types: ["neighborhood", "political"],
    },
    {
      address_components: [
        {
          long_name: "Tai Po District",
          short_name: "Tai Po District",
          types: ["administrative_area_level_2", "political"],
        },
        {
          long_name: "New Territories",
          short_name: "New Territories",
          types: ["administrative_area_level_1", "political"],
        },
        {
          long_name: "Hong Kong",
          short_name: "HK",
          types: ["country", "political"],
        },
      ],
      formatted_address: "Tai Po District, Hong Kong",
      geometry: {
        bounds: {
          northeast: { lat: 22.549616, lng: 114.336605 },
          southwest: { lat: 22.403584, lng: 114.103963 },
        },
        location: { lat: 22.4423282, lng: 114.165521 },
        location_type: "APPROXIMATE",
        viewport: {
          northeast: { lat: 22.549616, lng: 114.336605 },
          southwest: { lat: 22.403584, lng: 114.103963 },
        },
      },
      place_id: "ChIJz3uR-ML3AzQRMJDlfs07kMg",
      types: ["administrative_area_level_2", "political"],
    },
    {
      address_components: [
        {
          long_name: "New Territories",
          short_name: "New Territories",
          types: ["administrative_area_level_1", "political"],
        },
        {
          long_name: "Hong Kong",
          short_name: "HK",
          types: ["country", "political"],
        },
      ],
      formatted_address: "New Territories, Hong Kong",
      geometry: {
        bounds: {
          northeast: { lat: 22.5619485, lng: 114.441442 },
          southwest: { lat: 22.153415, lng: 113.835078 },
        },
        location: { lat: 22.3704243, lng: 114.1234149 },
        location_type: "APPROXIMATE",
        viewport: {
          northeast: { lat: 22.5619485, lng: 114.441442 },
          southwest: { lat: 22.153415, lng: 113.835078 },
        },
      },
      place_id: "ChIJt8ZyJegABDQRyJEH8bJo_iI",
      types: ["administrative_area_level_1", "political"],
    },
    {
      address_components: [
        {
          long_name: "Hong Kong",
          short_name: "Hong Kong",
          types: ["locality", "political"],
        },
        {
          long_name: "Hong Kong",
          short_name: "HK",
          types: ["country", "political"],
        },
      ],
      formatted_address: "Hong Kong",
      geometry: {
        bounds: {
          northeast: { lat: 22.5619475, lng: 114.4294999 },
          southwest: { lat: 22.1435, lng: 113.8259001 },
        },
        location: { lat: 22.3193292, lng: 114.1694229 },
        location_type: "APPROXIMATE",
        viewport: {
          northeast: { lat: 22.5619475, lng: 114.4294999 },
          southwest: { lat: 22.1435, lng: 113.8259001 },
        },
      },
      place_id: "ChIJByjqov3-AzQR2pT0dDW0bUg",
      types: ["locality", "political"],
    },
    {
      address_components: [
        {
          long_name: "Hong Kong",
          short_name: "HK",
          types: ["country", "political"],
        },
      ],
      formatted_address: "Hong Kong",
      geometry: {
        bounds: {
          northeast: { lat: 22.5619485, lng: 114.4528999 },
          southwest: { lat: 22.1435, lng: 113.8259001 },
        },
        location: { lat: 22.3193039, lng: 114.1693611 },
        location_type: "APPROXIMATE",
        viewport: {
          northeast: { lat: 22.5619485, lng: 114.4528999 },
          southwest: { lat: 22.1435, lng: 113.8259001 },
        },
      },
      place_id: "ChIJD5gyo-3iAzQRfMnq27qzivA",
      types: ["country", "political"],
    },
  ],
  status: "OK",
};

export const exampleEnglish = {
  plus_code: {
    compound_code: "5678+CD Example Avenue, Sample City",
    global_code: "7PCD5678+CD",
  },
  results: [
    {
      address_components: [
        {
          long_name: "Sample Cafe",
          short_name: "Sample Cafe",
          types: ["establishment"],
        },
        {
          long_name: "10",
          short_name: "10",
          types: ["street_number"],
        },
        {
          long_name: "Example Road",
          short_name: "Example Rd",
          types: ["route"],
        },
        {
          long_name: "Sample Neighborhood",
          short_name: "Sample Neighborhood",
          types: ["neighborhood"],
        },
        {
          long_name: "Sample City",
          short_name: "Sample City",
          types: ["locality"],
        },
        {
          long_name: "Sample State",
          short_name: "Sample State",
          types: ["administrative_area_level_1"],
        },
        {
          long_name: "Sample Country",
          short_name: "Sample Country",
          types: ["country"],
        },
        {
          long_name: "12345",
          short_name: "12345",
          types: ["postal_code"],
        },
      ],
      formatted_address: "10 Example Rd, Sample Neighborhood, Hong Kong",
      geometry: {
        location: {
          lat: 12.346789,
          lng: 98.765123,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.347789,
            lng: 98.766123,
          },
          southwest: {
            lat: 12.345789,
            lng: 98.764123,
          },
        },
      },
      place_id: "ChIJ1234567890EFGH",
      types: ["point_of_interest"],
    },
  ],
  status: "OK",
};

export const exampleChinese = {
  plus_code: {
    compound_code: "5678+CD 示例大道, 示例市",
    global_code: "7PCD5678+CD",
  },
  results: [
    {
      address_components: [
        {
          long_name: "示例咖啡馆",
          short_name: "示例咖啡馆",
          types: ["establishment"],
        },
        {
          long_name: "10",
          short_name: "10",
          types: ["street_number"],
        },
        {
          long_name: "示例路",
          short_name: "示例路",
          types: ["route"],
        },
        {
          long_name: "示例社区",
          short_name: "示例社区",
          types: ["neighborhood"],
        },
        {
          long_name: "示例市",
          short_name: "示例市",
          types: ["locality"],
        },
        {
          long_name: "示例省",
          short_name: "示例省",
          types: ["administrative_area_level_1"],
        },
        {
          long_name: "示例国家",
          short_name: "示例国家",
          types: ["country"],
        },
        {
          long_name: "12345",
          short_name: "12345",
          types: ["postal_code"],
        },
      ],
      formatted_address: "示例省示例市示例社区示例路10号, 12345",
      geometry: {
        location: {
          lat: 12.346789,
          lng: 98.765123,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 12.347789,
            lng: 98.766123,
          },
          southwest: {
            lat: 12.345789,
            lng: 98.764123,
          },
        },
      },
      place_id: "ChIJ1234567890EFGH",
      types: ["point_of_interest"],
    },
  ],
  status: "OK",
};

export const examplePremisePlusCode = {
  plus_code: {
    compound_code: "7X5V+MGH Mui Wo, Hong Kong",
    global_code: "7PJM7X5V+MGH",
  },
  results: [
    {
      address_components: [
        {
          long_name: "Lai Chi Yuen Cemetery",
          short_name: "Lai Chi Yuen Cemetery",
          types: ["establishment", "point_of_interest", "transit_station"],
        },
        {
          long_name: "Mui Wo",
          short_name: "Mui Wo",
          types: ["neighborhood", "political"],
        },
        {
          long_name: "New Territories",
          short_name: "New Territories",
          types: ["administrative_area_level_1", "political"],
        },
        {
          long_name: "Hong Kong",
          short_name: "HK",
          types: ["country", "political"],
        },
      ],
      formatted_address: "Lai Chi Yuen Cemetery, Mui Wo, Hong Kong",
      geometry: {
        location: {
          lat: 22.259225,
          lng: 113.993563,
        },
        location_type: "GEOMETRIC_CENTER",
        viewport: {
          northeast: {
            lat: 22.2605739802915,
            lng: 113.9949119802915,
          },
          southwest: {
            lat: 22.2578760197085,
            lng: 113.9922140197085,
          },
        },
      },
      navigation_points: [
        {
          location: {
            latitude: 22.2592354,
            longitude: 113.993563,
          },
        },
      ],
      place_id: "ChIJMYBx6dFXATQRzGkJ-VS_9sY",
      plus_code: {
        compound_code: "7X5V+MC Mui Wo, Hong Kong",
        global_code: "7PJM7X5V+MC",
      },
      types: ["establishment", "point_of_interest", "transit_station"],
    },
    {
      address_components: [
        {
          long_name: "7X6V+J2",
          short_name: "7X6V+J2",
          types: ["plus_code"],
        },
        {
          long_name: "Mui Wo",
          short_name: "Mui Wo",
          types: ["neighborhood", "political"],
        },
        {
          long_name: "New Territories",
          short_name: "New Territories",
          types: ["administrative_area_level_1", "political"],
        },
        {
          long_name: "Hong Kong",
          short_name: "HK",
          types: ["country", "political"],
        },
      ],
      formatted_address: "7X6V+J2, Mui Wo, Hong Kong",
      geometry: {
        bounds: {
          northeast: {
            lat: 22.2616169,
            lng: 113.9927014,
          },
          southwest: {
            lat: 22.2614854,
            lng: 113.9925763,
          },
        },
        location: {
          lat: 22.261545,
          lng: 113.9926421,
        },
        location_type: "ROOFTOP",
        viewport: {
          northeast: {
            lat: 22.2629001302915,
            lng: 113.9939878302915,
          },
          southwest: {
            lat: 22.2602021697085,
            lng: 113.9912898697085,
          },
        },
      },
      navigation_points: [
        {
          location: {
            latitude: 22.2617444,
            longitude: 113.992742,
          },
        },
      ],
      place_id: "ChIJv8IoAdFXATQRASF7AVCMW_I",
      types: ["premise"],
    },
  ],
  status: "OK",
};

import { StorageObjectData } from "firebase-functions/v2/storage";

import PaymentTx from "../../../nestJs/modules/database/entities/paymentTx.entity";
import { PaymentGatewayTypes } from "../../../nestJs/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../nestJs/modules/payment/dto/paymentInformationType.dto";
import { PaymentStatus } from "../../../nestJs/modules/payment/dto/paymentStatus.dto";

export const fakePaymentTx = (): PaymentTx =>
  PaymentTx.fromJson(
    {
      id: "5a04256cb293686739a252e052e898aaf321decf",
      gateway: PaymentGatewayTypes.SOEPAY,
      createdAt: new Date(),
      type: PaymentInformationType.CAPTURE,
      gatewayResponses: [
        {
          payment: {
            payerFirst: "123456",
            payerLast: "7890",
            paymentMethod: "VISA",
          },
          totalAmount: 100,
          tranId: "5a04256cb293686739a252e052e898aaf321decf",
          tranStatus: "SUCCESS",
        },
        {
          payment: {
            payerFirst: "123456",
            payerLast: "7890",
            paymentMethod: "VISA",
          },
          totalAmount: 100,
          tranId: "5a04256cb293686739a252e052e898aaf321decf",
          tranStatus: "SUCCESS",
        },
      ],
      status: PaymentStatus.CAPTURED,
      amount: 123,
      gatewayTransactionId: "5a04256cb293686739a252e052e898aaf321decf",
      cardNumber: "123456******7890",
      paymentMethod: "VISA",
    },
    "5a04256cb293686739a252e052e898aaf321decf",
  );

export const fakeObjectMetadata = jest.fn();
fakeObjectMetadata.mockImplementation(() => {
  const objectMetadata: StorageObjectData = {
    kind: "storage#object",
    name: "bucket/dir1/fileName.json",
    id: "bucket/object",
    bucket: "bucket",
    storageClass: "STANDARD",
    size: 1024,
    timeCreated: "2019-12-05T10:27:31.000Z",
    updated: "2019-12-05T10:27:31.000Z",
    generation: 0,
    metageneration: 0,
  };
  return objectMetadata;
});
export const fakeAuthFileContent = {
  payload: {
    id: "1234",
    amount: 100,
    gateway: PaymentGatewayTypes.SOEPAY,
    gatewayResponse: {},
    gatewayTransactionId: "1234",
    cardNumber: "8464*****212341",
    paymentMethod: "VISA",
    status: PaymentInformationStatus.SUCCESS,
    type: PaymentInformationType.AUTH,
    createdAt: new Date("2019-12-05T10:27:31.000Z"),
  },
};

export const fakeCaptureFileContent = {
  payload: {
    id: "1234",
    amount: 100,
    gateway: PaymentGatewayTypes.SOEPAY,
    gatewayResponse: {},
    gatewayTransactionId: "1234",
    cardNumber: "8464*****212341",
    paymentMethod: "VISA",
    status: PaymentInformationStatus.SUCCESS,
    type: PaymentInformationType.CAPTURE,
    createdAt: new Date("2019-12-05T10:27:31.000Z"),
  },
};

export const generateFakePaymentTx = jest.fn();

generateFakePaymentTx.mockImplementation(() => {
  return PaymentTx.fromJson(
    {
      id: "1233",
      gatewayResponse: {},
      tx: "1234",
      amount: 150,
      gatewayTransactionId: "5a04256cb293686739a252e052e898aaf321decf",
      cardNumber: "8464*****212341",
      paymentMethod: "VISA",
      sequenceId: undefined,
      gateway: "SOEPAY",
      createdAt: new Date("2019-12-05T10:27:31.000Z"),
      status: "SUCCESS",
      type: "AUTH",
    },
    "1234",
  );
});

export const generateFakePaymentTxAuthAndCapture = jest.fn();

generateFakePaymentTxAuthAndCapture.mockImplementation(() => {
  return [
    PaymentTx.fromJson(
      {
        id: "1233",
        gatewayResponse: {},
        tx: "1234",
        amount: 150,
        gatewayTransactionId: "5a04256cb293686739a252e052e898aaf321decf",
        cardNumber: "8464*****212341",
        paymentMethod: "VISA",
        sequenceId: undefined,
        gateway: "SOEPAY",
        createdAt: new Date("2019-12-05T10:27:31.000Z"),
        status: "SUCCESS",
        type: "AUTH",
      },
      "1234",
    ),
    PaymentTx.fromJson(
      {
        id: "1233",
        gatewayResponse: {},
        tx: "1234",
        amount: 150,
        gatewayTransactionId: "5a04256cb293686739a252e052e898aaf321decf",
        cardNumber: "8464*****212341",
        paymentMethod: "VISA",
        sequenceId: undefined,
        gateway: "SOEPAY",
        createdAt: new Date("2019-12-05T10:27:31.000Z"),
        status: "SUCCESS",
        type: "CAPTURE",
      },
      "1234",
    ),
  ];
});

export const generateFakePaymentTxSale = jest.fn();

generateFakePaymentTxSale.mockImplementation(() => {
  return [
    PaymentTx.fromJson(
      {
        id: "1233",
        gatewayResponse: {},
        tx: "1234",
        amount: 150,
        gatewayTransactionId: "5a04256cb293686739a252e052e898aaf321decf",
        cardNumber: "8464*****212341",
        paymentMethod: "VISA",
        sequenceId: undefined,
        gateway: "SOEPAY",
        createdAt: new Date("2019-12-05T10:27:31.000Z"),
        status: "SUCCESS",
        type: "SALE",
      },
      "1234",
    ),
  ];
});

export const generateFakePaymentTxAlipaySale = jest.fn();

generateFakePaymentTxAlipaySale.mockImplementation(() => {
  return [
    PaymentTx.fromJson(
      {
        id: "1233",
        gatewayResponse: {},
        tx: "1234",
        amount: 150,
        gatewayTransactionId: "5a04256cb293686739a252e052e898aaf321decf",
        cardNumber: "8464*****212341",
        paymentMethod: "ALIPAY",
        sequenceId: undefined,
        gateway: "SOEPAY",
        createdAt: new Date("2019-12-05T10:27:31.000Z"),
        status: "SUCCESS",
        type: "SALE",
      },
      "1234",
    ),
  ];
});

const createPaymentTxItem = (
  type: string,
  status: string,
  createdAt: Date,
  id: string,
  parent?: PaymentTx,
): PaymentTx => {
  const paymentTx = PaymentTx.fromJson(
    {
      id: id,
      gatewayResponse: {},
      amount: 29.1,
      gatewayTransactionId: "5a04256cb293686739a252e052e898aaf321dec0",
      cardNumber: "8464*****212341",
      paymentMethod: "VISA",
      gateway: "SOEPAY",
      createdAt: createdAt,
      status: status,
      type: type,
    },
    "1234",
  );
  if (parent) {
    paymentTx.parent = parent;
  }
  return paymentTx;
};

const paymentTxItem0 = createPaymentTxItem("AUTH", "SUCCESS", new Date("2023-02-05T10:27:31.000Z"), "0");

const paymentTxItem0_void_fail = createPaymentTxItem(
  "VOID",
  "FAILURE",
  new Date("2023-02-05T10:28:31.000Z"),
  "0-1",
  paymentTxItem0,
);
const paymentTxItem0_void_success = createPaymentTxItem(
  "VOID",
  "SUCCESS",
  new Date("2023-02-05T10:28:31.000Z"),
  "0-2",
  paymentTxItem0,
);

const paymentTxItem1 = createPaymentTxItem("AUTH", "SUCCESS", new Date("2023-02-05T10:30:31.000Z"), "1");

const paymentTxItem1_capture_fail = createPaymentTxItem(
  "CAPTURE",
  "FAILURE",
  new Date("2023-02-05T10:31:31.000Z"),
  "1-1",
  paymentTxItem1,
);
const paymentTxItem1_capture_success = createPaymentTxItem(
  "CAPTURE",
  "SUCCESS",
  new Date("2023-02-05T10:32:31.000Z"),
  "1-2",
  paymentTxItem1,
);

const paymentTxItem5 = createPaymentTxItem("AUTH", "SUCCESS", new Date("2023-02-05T10:50:31.000Z"), "5");

const paymentTxItem6 = createPaymentTxItem("SALE", "SUCCESS", new Date("2023-02-05T10:27:31.000Z"), "0");
const paymentTxItem6_void_success = createPaymentTxItem(
  "VOID",
  "SUCCESS",
  new Date("2023-02-05T10:28:31.000Z"),
  "0-2",
  paymentTxItem6,
);
export const paymentTxItem61 = createPaymentTxItem("SALE", "SUCCESS", new Date("2023-02-05T10:27:31.000Z"), "6");

//auth success 0; void fail 0; auth success 1;
export const fakePaymentTxs_1 = [paymentTxItem0, paymentTxItem0_void_fail, paymentTxItem5];
//auth success 0;
export const expectedFakePaymentTxs_1 = [paymentTxItem0];

// auth success 1; auth success 0; void fail 0;
export const fakePaymentTxs_2 = [paymentTxItem5, paymentTxItem0, paymentTxItem0_void_fail];
//auth success 0;
export const expectedFakePaymentTxs_2 = [paymentTxItem0];

//auth success 0; void success 0; auth success 1;
export const fakePaymentTxs_3 = [paymentTxItem0, paymentTxItem0_void_success, paymentTxItem5];
//[];
export const expectedFakePaymentTxs_3 = [];

//auth success 1; capture fail 1; capture success 1; auth success 5;
export const fakePaymentTxs_4 = [
  paymentTxItem1,
  paymentTxItem1_capture_fail,
  paymentTxItem1_capture_success,
  paymentTxItem5,
];
//[];
export const expectedFakePaymentTxs_4 = [];

//auth success 1;  void fail 0; auth success 0; capture fail 1; auth success 5
export const fakePaymentTxs_5 = [
  paymentTxItem1,
  paymentTxItem0_void_fail,
  paymentTxItem0,
  paymentTxItem1_capture_fail,
  paymentTxItem5,
];
//auth success 0; auth success 1;
export const expectedFakePaymentTxs_5 = [paymentTxItem0, paymentTxItem1];

//sale success 0; void success 0; sale success 1;
export const fakePaymentTxs_6 = [paymentTxItem6, paymentTxItem6_void_success, paymentTxItem61];
//[];
export const expectedFakePaymentTxs_6 = [];

//sale success 0; void success 0;
export const fakePaymentTxs_7 = [paymentTxItem6, paymentTxItem6_void_success];

export const fakePaymentTx_Auth_Fail = createPaymentTxItem(
  "AUTH",
  "FAILURE",
  new Date("2023-02-05T10:27:31.000Z"),
  "0",
);
export const fakePaymentTx_Auth_Fail_1 = createPaymentTxItem(
  "AUTH",
  "FAILURE",
  new Date("2023-02-05T11:27:31.000Z"),
  "1",
);
export const fakePaymentTx_Auth_SUCCESS_1 = createPaymentTxItem(
  "AUTH",
  "SUCCESS",
  new Date("2023-02-05T10:27:31.000Z"),
  "123",
);
export const fakePaymentTx_VOID_SUCCESS_1 = createPaymentTxItem(
  "VOID",
  "SUCCESS",
  new Date("2023-02-05T10:28:31.000Z"),
  "0-2",
  fakePaymentTx_Auth_SUCCESS_1,
);
export const fakePaymentTx_CAPTURE_SUCCESS_1 = createPaymentTxItem(
  "CAPTURE",
  "SUCCESS",
  new Date("2023-02-05T10:29:31.000Z"),
  "0-3",
  fakePaymentTx_Auth_SUCCESS_1,
);
export const fakePaymentTx_VOID_FAIL_1 = createPaymentTxItem(
  "VOID",
  "FAILURE",
  new Date("2023-02-05T10:28:31.000Z"),
  "0-2",
  fakePaymentTx_Auth_SUCCESS_1,
);
export const fakePaymentTx_Auth_SUCCESS_2 = createPaymentTxItem(
  "AUTH",
  "SUCCESS",
  new Date("2023-02-05T10:27:31.000Z"),
  "123",
);

export const fakePaymentTx_SALE_SUCCESS_1 = createPaymentTxItem(
  "SALE",
  "SUCCESS",
  new Date("2023-02-05T10:27:31.000Z"),
  "123",
);
export const fakePaymentTx_VOID_SALE_SUCCESS_1 = createPaymentTxItem(
  "VOID",
  "SUCCESS",
  new Date("2023-02-05T10:28:31.000Z"),
  "0-2",
  fakePaymentTx_SALE_SUCCESS_1,
);
export const fakePaymentTx_SALE_SUCCESS_2 = createPaymentTxItem(
  "SALE",
  "SUCCESS",
  new Date("2023-02-05T10:27:31.000Z"),
  "123",
);

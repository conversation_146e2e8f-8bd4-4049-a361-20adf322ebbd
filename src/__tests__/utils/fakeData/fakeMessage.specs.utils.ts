import { ChannelTypes } from "../../../nestJs/modules/message/dto/channelType.dto";
import { TemplateTypesText } from "../../../nestJs/modules/message/dto/templateType.dto";
import {
  PublishMessageForMessageProcessingParamsSms,
  PublishMessageForMessageProcessingParamsWhatsApp,
} from "../../../nestJs/modules/pubsub/dto/publishMessageForMessageProcessing.dto";
import { ReceiptLanguageType } from "../../../nestJs/modules/transaction/dto/txReceipt.dto";
import { LanguageOption } from "../../../nestJs/modules/validation/dto/language.dto";

export const expectedPublishWhatsappMessage: PublishMessageForMessageProcessingParamsWhatsApp = {
  metadata: {
    schemeVersion: "1.3",
    createdAt: new Date("2023-01-01T00:00:01.000Z"),
  },
  recipient: {
    phone: "+85212345678",
  },
  tranId: "5a04256cb293686739a252e052e898aaf321decf",
  channel: ChannelTypes.WHATSAPP,
  language: LanguageOption.EN,
  params: {
    receiptLink: `RECEIPT_URL/043f160c-792e-4f91-8031-4b786bbf2ce9?lang=${ReceiptLanguageType.ENHK}`,
    licensePlate: "TEST1234",
    tripTotal: 150,
    tripEndTime: "2023-01-01 00:01:00",
  },
  messageId: "1233",
  template: TemplateTypesText.RECEIPT,
};

export const expectedPublishSMSMessageAfterSendWhatsappFail: PublishMessageForMessageProcessingParamsSms = {
  metadata: {
    schemeVersion: "1.3",
    createdAt: new Date("2023-01-01T00:00:01.000Z"),
  },
  recipient: {
    phone: "+85212345678",
  },
  tranId: "5a04256cb293686739a252e052e898aaf321decf",
  channel: ChannelTypes.SMS,
  language: LanguageOption.ZHHK,
  params: {
    receiptLink: "RECEIPT_URL/043f160c-792e-4f91-8031-4b786bbf2ce9",
    licensePlate: "TEST1234",
    tripTotal: 150,
    tripEndTime: "2023-01-01 00:01:00",
  },
  messageId: "1233",
  template: TemplateTypesText.RECEIPT,
};

export const expectedPublishSMSMessage: PublishMessageForMessageProcessingParamsSms = {
  metadata: {
    schemeVersion: "1.3",
    createdAt: new Date("2023-01-01T00:00:01.000Z"),
  },
  recipient: {
    phone: "+85212345678",
  },
  tranId: "5a04256cb293686739a252e052e898aaf321decf",
  channel: ChannelTypes.SMS,
  language: LanguageOption.ZHHK,
  params: {
    receiptLink: "RECEIPT_URL/043f160c-792e-4f91-8031-4b786bbf2ce9",
    licensePlate: "TEST1234",
    tripTotal: 150,
    tripEndTime: "2023-01-01T00:01:00.000Z",
  },
  messageId: "1234",
  template: TemplateTypesText.RECEIPT,
};

export const expectedSaveWhatsappMessage = {
  metadata: {
    schemeVersion: "1.3",
    createdAt: new Date("2023-01-01T00:00:01.000Z"),
  },
  user: {
    id: 1234,
    phoneNumber: "+85212345678",
  },
  type: ChannelTypes.WHATSAPP,
  template: TemplateTypesText.RECEIPT,
  language: LanguageOption.ZHHK,
  params: {
    receiptLink: "RECEIPT_URL/043f160c-792e-4f91-8031-4b786bbf2ce9",
    licensePlate: "TEST1234",
    tripTotal: 150,
    tripEndTime: "2023-01-01T00:01:00.000Z",
  },
  messageProviderId: "**********",
};

export const expectedSaveSMSMessage = {
  metadata: {
    schemeVersion: "1.3",
    createdAt: new Date("2023-01-01T00:00:01.000Z"),
  },
  user: {
    id: 1234,
    phoneNumber: "+85212345678",
  },
  type: ChannelTypes.SMS,
  template: TemplateTypesText.RECEIPT,
  language: LanguageOption.ZHHK,
  params: {
    receiptLink: "RECEIPT_URL/043f160c-792e-4f91-8031-4b786bbf2ce9",
    licensePlate: "TEST1234",
    tripTotal: 150,
    tripEndTime: "2023-01-01T00:01:00.000Z",
  },
  messageProviderId: "**********",
};

export const expectedPublishSMSMessageWithoutReceiptLink: PublishMessageForMessageProcessingParamsSms = {
  metadata: {
    schemeVersion: "1.3",
    createdAt: new Date("2023-01-01T00:00:01.000Z"),
  },
  recipient: {
    phone: "+85212345678",
  },
  tranId: "5a04256cb293686739a252e052e898aaf321decf",
  channel: ChannelTypes.SMS,
  language: LanguageOption.ZHHK,
  params: {
    licensePlate: "TEST1234",
    tripTotal: 150,
    tripEndTime: "2023-01-01T00:01:00.000Z",
  },
  messageId: "1234",
  template: TemplateTypesText.RECEIPT,
};
export const expectedPublishWhatsappMessageWithoutReceiptLink: PublishMessageForMessageProcessingParamsWhatsApp = {
  metadata: {
    schemeVersion: "1.3",
    createdAt: new Date("2023-01-01T00:00:01.000Z"),
  },
  recipient: {
    phone: "+85212345678",
  },
  tranId: "5a04256cb293686739a252e052e898aaf321decf",
  channel: ChannelTypes.WHATSAPP,
  language: LanguageOption.ZHHK,
  params: {
    tripTotal: 150,
    tripEndTime: "2023-01-01T00:01:00.000Z",
  },
  messageId: "1233",
  template: TemplateTypesText.RECEIPT,
};
export const expectedPublishSMSMessageWithoutReceiptLinkAfterWhatsappFail: PublishMessageForMessageProcessingParamsSms =
  {
    metadata: {
      schemeVersion: "1.3",
      createdAt: new Date("2023-01-01T00:00:01.000Z"),
    },
    recipient: {
      phone: "+85212345678",
    },
    tranId: "5a04256cb293686739a252e052e898aaf321decf",
    channel: ChannelTypes.SMS,
    language: LanguageOption.ZHHK,
    params: {
      tripTotal: 150,
      tripEndTime: "2023-01-01T00:01:00.000Z",
    },
    messageId: "1233",
    template: TemplateTypesText.RECEIPT,
  };
export const expectedPublishWhatsappMessageWithoutCampaignName: PublishMessageForMessageProcessingParamsWhatsApp = {
  metadata: {
    schemeVersion: "1.3",
    createdAt: new Date("2023-01-01T00:00:01.000Z"),
  },
  recipient: {
    phone: "+85212345678",
  },
  tranId: "5a04256cb293686739a252e052e898aaf321decf",
  channel: ChannelTypes.WHATSAPP,
  language: LanguageOption.ZHHK,
  params: {
    id: "209a1a97-f0bd-480b-b0d6-c889feb0dbc0",
  },
  messageId: "1233",
  template: TemplateTypesText.DISCOUNT,
};

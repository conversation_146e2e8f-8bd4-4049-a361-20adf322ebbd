import { soepayAuthSuccess, soepaySaleSuccess } from "./fakeDataSoepay.specs.utils";
import { fakeTripData } from "./fakeTripData.specs.utils";
import { TripDocument } from "../../../nestJs/modules/appDatabase/documents/trip.document";
import { TxAppsNames } from "../../../nestJs/modules/apps/dto/Apps.dto";
import TxApp from "../../../nestJs/modules/database/entities/app.entity";
import Merchant from "../../../nestJs/modules/database/entities/merchant.entity";
import PaymentTx from "../../../nestJs/modules/database/entities/paymentTx.entity";
import Tx from "../../../nestJs/modules/database/entities/tx.entity";
import { TxTypes } from "../../../nestJs/modules/transaction/dto/txType.dto";

export const expectedResultSavedTx = {
  id: "07aa2173-a3d9-45aa-be74-61e359761766",
  txApp: TxApp.fromJson({ id: "1234", name: TxAppsNames.TAPXI }),
  type: TxTypes.TRIP,
  metadata: {
    ...fakeTripData,
    creation_time: fakeTripData.creation_time.toDate().toISOString(),
    last_update_time: fakeTripData.last_update_time.toDate().toISOString(),
    trip_end: fakeTripData.trip_end.toDate().toISOString(),
    trip_start: fakeTripData.trip_start.toDate().toISOString(),
  },
  dashFee: 10,
  total: 100,
  merchant: { id: "1234", phoneNumber: "+85298765432", name: "Tony", showCashTrip: true },
  paymentTx: [
    {
      id: "9081f05c-4ffd-49a8-980f-ad4b6f852989",
      gatewayResponse: soepayAuthSuccess,
      tx: {
        id: "07aa2173-a3d9-45aa-be74-61e359761766",
        txApp: TxApp.fromJson({ id: "1234", name: TxAppsNames.TAPXI }),
      },
      amount: 23.5,
      gatewayTransactionId: "4b47b9d8820a0b4400440a46e49a3c4d7ad8e9c0",
      cardNumber: "436605******8868",
      paymentMethod: "VISA",
      gateway: "SOEPAY",
      createdAt: new Date("2023-05-22T22:49:12.000Z"),
      status: "SUCCESS",
      type: "AUTH",
    },
  ],
};

export const fakeTx = new Tx();
fakeTx.id = expectedResultSavedTx.id;
fakeTx.type = expectedResultSavedTx.type;
fakeTx.metadata = fakeTripData as unknown as TripDocument;
fakeTx.merchant = expectedResultSavedTx.merchant as Merchant;
fakeTx.paymentTx = expectedResultSavedTx.paymentTx.map((paymentTx) =>
  PaymentTx.fromJson(paymentTx, expectedResultSavedTx.id),
);

export const expectedSaleResultSavedTx = {
  id: "07aa2173-a3d9-45aa-be74-61e359761766",
  txApp: TxApp.fromJson({ id: "1234", name: TxAppsNames.TAPXI }),
  type: TxTypes.TRIP,
  metadata: {
    ...fakeTripData,
    creation_time: fakeTripData.creation_time.toDate().toISOString(),
    last_update_time: fakeTripData.last_update_time.toDate().toISOString(),
    trip_end: fakeTripData.trip_end.toDate().toISOString(),
    trip_start: fakeTripData.trip_start.toDate().toISOString(),
  },
  dashFee: 10,
  total: 100,
  merchant: { id: "1234", phoneNumber: "+85298765432", name: "Tony", showCashTrip: true },
  paymentTx: [
    {
      id: "9081f05c-4ffd-49a8-980f-ad4b6f852989",
      gatewayResponse: soepaySaleSuccess,
      tx: {
        id: "07aa2173-a3d9-45aa-be74-61e359761766",
      },
      amount: 23.5,
      gatewayTransactionId: "4b47b9d8820a0b4400440a46e49a3c4d7ad8e9c0",
      cardNumber: "436605******8868",
      paymentMethod: "VISA",
      gateway: "SOEPAY",
      createdAt: new Date("2023-05-22T22:49:12.000Z"),
      status: "SUCCESS",
      type: "SALE",
    },
  ],
};

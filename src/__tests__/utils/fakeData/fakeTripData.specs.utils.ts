import { Timestamp } from "firebase-admin/firestore";

import { TripDocument } from "../../../nestJs/modules/appDatabase/documents/trip.document";

export const fakeTripData = {
  creation_time: Timestamp.fromDate(new Date("2023-05-22T22:48:45.000Z")),
  fare: 22,
  distance: 0,
  session: { id: "12345678" },
  language: "en",
  dash_fee_rate: 0,
  location_start_address: "天水圍慧景軒3座",
  total: 23.5,
  location_end_address: "天水圍慧景軒3座",
  location_start: { _latitude: 22.46571212, _longitude: 114.00406294 },
  trip_end: Timestamp.fromDate(new Date("2023-05-22T22:49:23.000Z")),
  extra: 0,
  location_end: { _latitude: 22.46569995, _longitude: 114.00413758 },
  id: "07aa2173-a3d9-45aa-be74-61e359761766",
  trip_total: 22,
  wait_time: 0,
  trip_status: "STOP",
  attempted_auth_amount: 23.5,
  last_update_time: Timestamp.fromDate(new Date("2023-05-22T22:49:23.000Z")),
  payment_type: "DASH",
  license_plate: "DF 698",
  await_payment: false,
  driver: { name: "Taxi D", id: "+1231244", name_ch: "" },
  estimatedDashAmount: 23.5,
  trip_start: Timestamp.fromDate(new Date("2023-05-22T22:48:45.000Z")),
  dash_fee_constant: 1.5,
  meter_id: "12897",
  dash_tips_enable: false,
  dash_fee: 12,
  attempted_dash_payment: 3,
  billing: { total: 23.5 },
};

export const fakeTrip = jest.fn();
fakeTrip.mockImplementation(() => {
  const trip = new TripDocument();
  trip.id = "043f160c-792e-4f91-8031-4b786bbf2ce9";
  trip.licensePlate = "TEST1234";
  trip.language = "en";
  trip.passengerInformation = { phone: "+85212345678" };
  trip.tripEnd = new Date("2023-01-01T00:01:00.000Z");
  trip.lastUpdateTime = new Date("2023-01-01T00:00:01.000Z");
  trip.tripStart = new Date("2023-01-01T00:00:00.000Z");
  trip.tripInfoAppVersion = "1.3";
  trip.meterSoftwareVersion = "1.3";
  return trip;
});

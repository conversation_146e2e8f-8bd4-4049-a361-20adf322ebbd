import { StorageObjectData } from "firebase-functions/v2/storage";

import PaymentTx from "../../../nestJs/modules/database/entities/paymentTx.entity";
import { PaymentGatewayTypes } from "../../../nestJs/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../nestJs/modules/payment/dto/paymentInformationType.dto";

export const fakeObjectMetadata = jest.fn();
fakeObjectMetadata.mockImplementation(() => {
  const objectMetadata: StorageObjectData = {
    kind: "storage#object",
    name: "bucket/dir1/fileName.json",
    id: "bucket/object",
    bucket: "bucket",
    storageClass: "STANDARD",
    size: 1024,
    timeCreated: "2019-12-05T10:27:31.000Z",
    updated: "2019-12-05T10:27:31.000Z",
    generation: 0,
    metageneration: 0,
  };
  return objectMetadata;
});
export const fakeAuthFileContent = {
  payload: {
    id: "1234",
    amount: 100,
    gateway: PaymentGatewayTypes.SOEPAY,
    gatewayResponse: {},
    gatewayTransactionId: "1234",
    cardNumber: "8464*****212341",
    paymentMethod: "VISA",
    status: PaymentInformationStatus.SUCCESS,
    type: PaymentInformationType.AUTH,
    createdAt: new Date("2019-12-05T10:27:31.000Z"),
  },
};

export const fakeCaptureFileContent = {
  payload: {
    id: "1234",
    amount: 100,
    gateway: PaymentGatewayTypes.SOEPAY,
    gatewayResponse: {},
    gatewayTransactionId: "1234",
    cardNumber: "8464*****212341",
    paymentMethod: "VISA",
    status: PaymentInformationStatus.SUCCESS,
    type: PaymentInformationType.CAPTURE,
    createdAt: new Date("2019-12-05T10:27:31.000Z"),
  },
};

export const generateFakePaymentTx = jest.fn();

generateFakePaymentTx.mockImplementation(() => {
  return PaymentTx.fromJson(
    {
      id: "1233",
      gatewayResponse: {},
      tx: "1234",
      amount: 150,
      gatewayTransactionId: "5a04256cb293686739a252e052e898aaf321decf",
      cardNumber: "8464*****212341",
      paymentMethod: "VISA",
      gateway: "SOEPAY",
      createdAt: new Date("2019-12-05T10:27:31.000Z"),
      status: "SUCCESS",
      type: "AUTH",
    },
    "1234",
  );
});

const createPaymentTxItem = (type: string, status: string, sequenceId: number, createdAt: Date) => {
  return PaymentTx.fromJson(
    {
      id: "1",
      gatewayResponse: {},
      amount: 29.1,
      gatewayTransactionId: "5a04256cb293686739a252e052e898aaf321dec0",
      cardNumber: "8464*****212341",
      paymentMethod: "VISA",
      sequenceId: sequenceId,
      gateway: "SOEPAY",
      createdAt: createdAt,
      status: status,
      type: type,
    },
    "1234",
  );
};

const paymentTxItem0 = createPaymentTxItem("AUTH", "SUCCESS", 0, new Date("2023-02-05T10:27:31.000Z"));

const paymentTxItem0_void_fail = createPaymentTxItem("VOID", "FAILURE", 0, new Date("2023-02-05T10:28:31.000Z"));
const paymentTxItem0_void_success = createPaymentTxItem("VOID", "SUCCESS", 0, new Date("2023-02-05T10:28:31.000Z"));

const paymentTxItem1 = createPaymentTxItem("AUTH", "SUCCESS", 1, new Date("2023-02-05T10:30:31.000Z"));

const paymentTxItem1_capture_fail = createPaymentTxItem("CAPTURE", "FAILURE", 1, new Date("2023-02-05T10:31:31.000Z"));
const paymentTxItem1_capture_success = createPaymentTxItem(
  "CAPTURE",
  "SUCCESS",
  1,
  new Date("2023-02-05T10:32:31.000Z"),
);

const paymentTxItem5 = createPaymentTxItem("AUTH", "SUCCESS", 5, new Date("2023-02-05T10:50:31.000Z"));

//auth success 0; void fail 0; auth success 1;
export const fakePaymentTxs_1 = [paymentTxItem0, paymentTxItem0_void_fail, paymentTxItem5];
//auth success 0;
export const expectedFakePaymentTxs_1 = [paymentTxItem0];

// auth success 1; auth success 0; void fail 0;
export const fakePaymentTxs_2 = [paymentTxItem5, paymentTxItem0, paymentTxItem0_void_fail];
//auth success 0;
export const expectedFakePaymentTxs_2 = [paymentTxItem0];

//auth success 0; void success 0; auth success 1;
export const fakePaymentTxs_3 = [paymentTxItem0, paymentTxItem0_void_success, paymentTxItem5];
//[];
export const expectedFakePaymentTxs_3 = [];

//auth success 1; capture fail 1; capture success 1; auth success 5;
export const fakePaymentTxs_4 = [
  paymentTxItem1,
  paymentTxItem1_capture_fail,
  paymentTxItem1_capture_success,
  paymentTxItem5,
];
//[];
export const expectedFakePaymentTxs_4 = [];

//auth success 1;  void fail 0; auth success 0; capture fail 1; auth success 5
export const fakePaymentTxs_5 = [
  paymentTxItem1,
  paymentTxItem0_void_fail,
  paymentTxItem0,
  paymentTxItem1_capture_fail,
  paymentTxItem5,
];
//auth success 0; auth success 1;
export const expectedFakePaymentTxs_5 = [paymentTxItem0, paymentTxItem1];

import { PaymentType } from "@nest/modules/payment/dto/paymentType.dto";

import { soepayAuthSuccess } from "./fakeDataSoepay.specs.utils";
import { fakeTripData } from "./fakeTripData.specs.utils";
import { TxAppsNames } from "../../../nestJs/modules/apps/dto/Apps.dto";
import TxApp from "../../../nestJs/modules/database/entities/app.entity";
import PaymentTx from "../../../nestJs/modules/database/entities/paymentTx.entity";
import Tx from "../../../nestJs/modules/database/entities/tx.entity";
import { ChannelTypes } from "../../../nestJs/modules/message/dto/channelType.dto";
import { TemplateTypesText } from "../../../nestJs/modules/message/dto/templateType.dto";
import { PaymentGatewayTypes } from "../../../nestJs/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../nestJs/modules/payment/dto/paymentInformationType.dto";
import { PublishMessageForCaptureProcessingParams } from "../../../nestJs/modules/pubsub/dto/publishMessageForCaptureProcessingParams.dto";
import { PublishMessageForCopyTripToDriverProcessingParams } from "../../../nestJs/modules/pubsub/dto/publishMessageForDriverTripProcessing.dto";
import { PublishMessageForMessageProcessingParams } from "../../../nestJs/modules/pubsub/dto/publishMessageForMessageProcessing.dto";
import { PublishMessageForTripProcessingParams } from "../../../nestJs/modules/pubsub/dto/publishMessageForTripProcessing.dto";
import { PublishMessageForVoidProcessingParams } from "../../../nestJs/modules/pubsub/dto/publishMessageForVoidProcessingParams.dto";
import { TxPayoutStatus } from "../../../nestJs/modules/transaction/dto/txPayoutStatus.dto";
import { TxTypes } from "../../../nestJs/modules/transaction/dto/txType.dto";
import { TripStatus } from "../../../nestJs/modules/transaction/transactionFactory/modules/trip/dto/tripStatus.dto";
import { LanguageOption } from "../../../nestJs/modules/validation/dto/language.dto";

export const expectedTxQueueEntry: PublishMessageForTripProcessingParams = {
  tx: {
    txApp: TxApp.fromJson({ id: "1234", name: TxAppsNames.TAPXI }),
    type: TxTypes.TRIP,
    id: "07aa2173-a3d9-45aa-be74-61e359761766",
    data: {
      ...fakeTripData,
      creation_time: fakeTripData.creation_time.toDate().toISOString(),
      last_update_time: fakeTripData.last_update_time.toDate().toISOString(),
      trip_end: fakeTripData.trip_end.toDate().toISOString(),
      trip_start: fakeTripData.trip_start.toDate().toISOString(),
      paymentType: PaymentType.DASH,
    },
  },
  merchant: { name: "Taxi D", phoneNumber: "+1231244" },
  paymentTx: [
    {
      id: "9081f05c-4ffd-49a8-980f-ad4b6f852989",
      amount: 23.5,
      gatewayTransactionId: "4b47b9d8820a0b4400440a46e49a3c4d7ad8e9c0",
      cardNumber: "436605******8868",
      paymentMethod: "VISA",
      status: PaymentInformationStatus.SUCCESS,
      type: PaymentInformationType.AUTH,
      gateway: PaymentGatewayTypes.SOEPAY,
      gatewayResponse: soepayAuthSuccess,
      createdAt: new Date("2023-05-22T22:49:12.000Z"),
    },
  ],
  txProcessed: true,
  doVoid: true,
  isDirectSale: false,
  isAfterTxEnd: false,
  isCaptureInApp: false,
};

export const expectedMessageQueueEntry: PublishMessageForMessageProcessingParams = {
  metadata: {
    schemeVersion: "1.3",
    createdAt: new Date("2023-01-01T00:00:01.000Z"),
  },
  recipient: {
    phone: "+85212345678",
  },
  tranId: "5a04256cb293686739a252e052e898aaf321decf",
  channel: ChannelTypes.WHATSAPP,
  language: LanguageOption.ZHHK,
  params: {
    receiptLink: "RECEIPT_URL/043f160c-792e-4f91-8031-4b786bbf2ce9",
    licensePlate: "TEST1234",
    tripTotal: 150,
    tripEndTime: "2023-01-01T00:01:00.000Z",
  },
  messageId: "1234",
  template: TemplateTypesText.RECEIPT,
};

export const expectedCaptureQueueEntry: PublishMessageForCaptureProcessingParams = {
  tx: {
    id: "07aa2173-a3d9-45aa-be74-61e359761766",
    txApp: TxApp.fromJson({ id: "1234", name: TxAppsNames.TAPXI }),
    type: TxTypes.TRIP,
    createdAt: new Date("2023-01-01T00:00:01.000Z"),
    updatedAt: new Date("2023-01-01T00:00:01.000Z"),
    metadata: {
      id: "07aa2173-a3d9-45aa-be74-61e359761766",

      dashFee: 12,
      dashFeeConstant: 1.5,
      dashFeeRate: 0,
      dashTipsEnable: false,
      dashTips: 0,
      extra: 0,
      fare: 22,

      paymentType: PaymentType.DASH,
      total: 23.5,
      tripTotal: 22,

      distance: 0,
      locationEnd: { _latitude: 22.46569995, _longitude: 114.00413758 },
      locationStart: { _latitude: 22.46571212, _longitude: 114.00406294 },
      locationStartAddress: "天水圍慧景軒3座",
      locationEndAddress: "天水圍慧景軒3座",
      tripStart: new Date("2023-05-22T22:48:45.000Z"),
      tripEnd: new Date("2023-05-22T22:49:23.000Z"),
      waitTime: 0,
      tripStatus: TripStatus.HIRED,
      licensePlate: "DF 698",
      driverId: "+1231244",
      meterId: "12897",

      language: "en",
      lastUpdateTime: new Date("2023-05-22T22:49:23.000Z"),
      sessionId: "1234",
      showToDriver: false,
      tripInfoAppVersion: "1.3.0",
      meterSoftwareVersion: "4.48",
    },
    payoutStatus: TxPayoutStatus.PRERELEASED,
    total: 23.5,
  } as Tx,
};

const paymentTx = PaymentTx.fromJson(
  {
    id: "9081f05c-4ffd-49a8-980f-ad4b6f852989",
    gatewayResponse: soepayAuthSuccess,
    createdAt: new Date("2023-05-22T22:49:12.000Z"),
    updatedAt: new Date("2023-05-22T22:49:12.000Z"),
    amount: 23.5,
    gatewayTransactionId: "4b47b9d8820a0b4400440a46e49a3c4d7ad8e9c0",
    cardNumber: "436605******8868",
    paymentMethod: "VISA",
    gateway: PaymentGatewayTypes.SOEPAY,
    status: PaymentInformationStatus.SUCCESS,
    type: PaymentInformationType.AUTH,
    tx: Tx.fromJson({
      txApp: TxApp.fromJson({ id: "1234", name: TxAppsNames.TAPXI }),
    }),
  },
  "07aa2173-a3d9-45aa-be74-61e359761766",
);
paymentTx.updatedAt = new Date("2023-05-22T22:49:12.000Z");
export const expectedVoidQueueEntry: PublishMessageForVoidProcessingParams = {
  tx: {
    id: "07aa2173-a3d9-45aa-be74-61e359761766",
    txApp: TxApp.fromJson({ id: "1234", name: TxAppsNames.TAPXI }),
    type: TxTypes.TRIP,
    createdAt: new Date("2023-01-01T00:00:01.000Z"),
    updatedAt: new Date("2023-01-01T00:00:01.000Z"),
    metadata: {
      id: "07aa2173-a3d9-45aa-be74-61e359761766",

      dashFee: 12,
      dashFeeConstant: 1.5,
      dashFeeRate: 0,
      dashTipsEnable: false,
      dashTips: 0,
      extra: 0,
      fare: 22,

      paymentType: PaymentType.DASH,
      total: 23.5,
      tripTotal: 22,

      distance: 0,
      locationEnd: { _latitude: 22.46569995, _longitude: 114.00413758 },
      locationStart: { _latitude: 22.46571212, _longitude: 114.00406294 },
      locationStartAddress: "天水圍慧景軒3座",
      locationEndAddress: "天水圍慧景軒3座",
      tripStart: new Date("2023-05-22T22:48:45.000Z"),
      tripEnd: new Date("2023-05-22T22:49:23.000Z"),
      waitTime: 0,
      tripStatus: TripStatus.HIRED,
      licensePlate: "DF 698",
      driverId: "+1231244",
      meterId: "12897",

      language: "en",
      lastUpdateTime: new Date("2023-05-22T22:49:23.000Z"),
      sessionId: "1234",
      showToDriver: false,
      tripInfoAppVersion: "1.3.0",
      meterSoftwareVersion: "4.48",
    },
    payoutStatus: TxPayoutStatus.PRERELEASED,
    total: 23.5,
  } as Tx,
  paymentTxs: [paymentTx],
};

export const expectedCopyTripToDriveQueueEntry: PublishMessageForCopyTripToDriverProcessingParams = {
  txId: "07aa2173-a3d9-45aa-be74-61e359761766",
  expiresAt: new Date("2023-01-01T00:00:01.000Z"),
};

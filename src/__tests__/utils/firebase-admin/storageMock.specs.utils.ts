export const mockBucket = jest.fn();
export const mockDownload = jest.fn();
export const mockMove = jest.fn();
export const mockFile = jest.fn();

export const resetStorageMocks = () => {
  mockDownload.mockImplementation(() => {
    return new Promise((resolve) => resolve([Buffer.from(JSON.stringify({ some: "content" }))]));
  });
  mockBucket.mockImplementation(() => {
    return {
      file: mockFile.mockImplementation(() => {
        return {
          download: mockDownload,
          moveFile: mockMove,
        };
      }),
    };
  });
};

resetStorageMocks();

const storageMock = jest.fn().mockImplementation(() => ({
  bucket: mockBucket,
}));

export default storageMock;

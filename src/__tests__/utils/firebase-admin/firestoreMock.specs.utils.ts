const date = new Date();

export const CollectionReferenceMock = jest.fn();
export const dataFirestoreMock = jest.fn();
export const docsDataFirestoreMock = jest.fn();
export const getFirestoreMock = jest.fn();
export const updateFirestoreMock = jest.fn();
export const setFirestoreMock = jest.fn();
export const deleteFirestoreMock = jest.fn();
export const createFirestoreMock = jest.fn();
export const whereFirestoreMock = jest.fn();
export const docFirestoreMock = jest.fn();
export const collectionFirestoreMock = jest.fn();

export class TimestampMock {
  static toDate = () => date;
  static fromDate = () => new TimestampMock();

  toDate = () => date;
  fromDate = () => new TimestampMock();
}

export const firestoreMock = {
  Timestamp: TimestampMock,
};

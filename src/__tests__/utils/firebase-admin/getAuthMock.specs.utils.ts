export const getAuthMock = () => {
  return {
    getUser: getUserMock,
    getUserByEmail: getUserByEmailMock,
    createCustomToken: createCustomTokenMock,
    verifyIdToken: verifiedIdTokenMock,
    updateUser: updateUserMock,
    setCustomUserClaims: setCustomUserClaimsMock,
  };
};

export const getUserMock = jest.fn();
export const getUserByEmailMock = jest.fn();
export const createCustomTokenMock = jest.fn();
export const verifiedIdTokenMock = jest.fn();
export const updateUserMock = jest.fn();
export const setCustomUserClaimsMock = jest.fn();

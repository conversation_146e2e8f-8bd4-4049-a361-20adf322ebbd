export const mockCreateTask = jest.fn();
export const mockQueuePath = jest.fn();

export const resetCloudTasksMocks = (params?: { taskName?: string }) => {
  mockCreateTask.mockImplementation(() => {
    return [{ name: params?.taskName ?? "task-123" }];
  });

  mockQueuePath.mockImplementation((projectId, location, queueName) => {
    return `projects/${projectId}/locations/${location}/queues/${queueName}`;
  });
};

resetCloudTasksMocks();

export class CloudTasksClientMock {
  createTask = mockCreateTask;
  queuePath = mockQueuePath;
}

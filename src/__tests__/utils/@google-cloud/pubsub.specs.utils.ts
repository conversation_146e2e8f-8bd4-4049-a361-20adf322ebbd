import { TopicNamesType } from "../../../nestJs/modules/pubsub/dto/topicName.dto";

export const mockPublishMessage = jest.fn();
export const mockCreateTopic = jest.fn();
export const mockGetTopics = jest.fn();

type ResetPubsubMocksParams = {
  messageId?: string;
  createdTopicName?: string;
  topicNamesType?: TopicNamesType;
};

export const resetPubsubMocks = (params?: ResetPubsubMocksParams) => {
  mockPublishMessage.mockImplementation(() => {
    return () => new Promise((resolve) => resolve(params?.messageId ?? "12345"));
  });

  mockCreateTopic.mockImplementation(() => {
    return new Promise((resolve) =>
      resolve([{ name: params?.createdTopicName ?? "topicName", publishMessage: mockPublishMessage() }]),
    );
  });

  mockGetTopics.mockImplementation(() => {
    return new Promise((resolve) => resolve([[{ name: params?.topicNamesType ?? TopicNamesType.TRIP_PROCESSING }]]));
  });
};

resetPubsubMocks();

class pubsubClassMock {
  createTopic = mockCreateTopic;
  getTopics = mockGetTopics;
  publishMessage = mockPublishMessage;
}

export const pubsubMock = pubsubClassMock;

export const pubsubTopicMock = jest.fn().mockImplementation(() => ({
  name: "topicName",
}));

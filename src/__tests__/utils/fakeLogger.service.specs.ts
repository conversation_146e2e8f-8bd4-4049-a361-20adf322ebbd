import { AsyncLocalStorage } from "async_hooks";

import { ClsService } from "nestjs-cls";

import ClsContextStorageService from "../../nestJs/modules/utils/context/clsContextStorage.service";
import LoggerServiceAdapter from "../../nestJs/modules/utils/logger/logger.service";

const FakeLoggerService = new LoggerServiceAdapter(
  new ClsContextStorageService(new ClsService(new AsyncLocalStorage())),
);

export default FakeLoggerService;

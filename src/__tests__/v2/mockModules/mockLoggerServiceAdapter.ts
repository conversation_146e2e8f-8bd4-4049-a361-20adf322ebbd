import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

export class MockLoggerServiceAdapter {
  public log = jest.fn();
  public error = jest.fn();
  public warn = jest.fn();
  public info = jest.fn();
  public debug = jest.fn();
  public verbose = jest.fn();
  public getContextId = jest.fn();
  public getContext = jest.fn();
}

export const MockLoggerServiceAdapterProvider = {
  provide: LoggerServiceAdapter,
  useClass: MockLoggerServiceAdapter,
};

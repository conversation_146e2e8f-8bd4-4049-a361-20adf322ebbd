import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";

const MockFirebaseRepository = {
  set: jest.fn(),
  create: jest.fn(),
  findOneById: jest.fn(),
  find: jest.fn(),
  collection: {
    add: jest.fn(),
  },
};

class MockAppDatabaseService {
  runTransaction = jest.fn();
  meterRepository = () => MockFirebaseRepository;
  driverRepository = () => MockFirebaseRepository;
  meterTripRepository = () => MockFirebaseRepository;
  driverTripRepository = () => MockFirebaseRepository;
  tripRepository = () => MockFirebaseRepository;
}

export const MockAppDatabaseServiceProvider = {
  provide: AppDatabaseService,
  useClass: MockAppDatabaseService,
};

export default MockAppDatabaseService;

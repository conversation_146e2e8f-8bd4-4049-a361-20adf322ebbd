import { CloudTaskClientService } from "@nest/modules/cloud-task-client/cloud-task-client.service";

export const mockCloudTaskClientService = {
  enqueueHttpTaskNow: jest.fn(),
  enqueueHttpTaskWithDelay: jest.fn(),
  enqueueHttpTask: jest.fn(),
  enqueueSendNotificationTask: jest.fn(),
  enqueueUpdateFleetTask: jest.fn(),
  enqueuePickupOrderNotificationTask: jest.fn(),
  removeTaskFromQueue: jest.fn(),
};

export const MockCloudTaskClientServiceProvider = {
  provide: CloudTaskClientService,
  useValue: mockCloudTaskClientService,
};

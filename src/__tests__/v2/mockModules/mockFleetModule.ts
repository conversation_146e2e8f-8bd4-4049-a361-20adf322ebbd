import { FleetOrderRepository } from "@nest/modules/database/repositories/fleetOrder.repository";
import { FleetOrderTimelineRepository } from "@nest/modules/database/repositories/fleetOrderTimeline.repository";
import { FleetPartnerRepository } from "@nest/modules/database/repositories/fleetPartner.repository";
import { FleetQuoteRepository } from "@nest/modules/database/repositories/fleetQuote.repository";

export const mockFleetOrderTimelineRepository = {
  create: jest.fn(),
  save: jest.fn(),
  find: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  findLastTimelineByFleetOrderId: jest.fn(),
};

export const MockFleetOrderTimelineRepository = {
  provide: FleetOrderTimelineRepository,
  useValue: mockFleetOrderTimelineRepository,
};

export const mockFleetOrderRepository = {
  create: jest.fn(),
  save: jest.fn(),
  find: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

export const MockFleetOrderRepository = {
  provide: FleetOrderRepository,
  useValue: mockFleetOrderRepository,
};

export const mockFleetQuoteRepository = {
  create: jest.fn(),
  save: jest.fn(),
  find: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

export const MockFleetQuoteRepository = {
  provide: FleetQuoteRepository,
  useValue: mockFleetQuoteRepository,
};

export const mockFleetPartnerRepository = {
  create: jest.fn(),
  save: jest.fn(),
  find: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  getUpdateInterval: jest.fn(),
};

export const MockFleetPartnerRepository = {
  provide: FleetPartnerRepository,
  useValue: mockFleetPartnerRepository,
};

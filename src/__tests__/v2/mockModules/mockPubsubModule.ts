import { PubSubService } from "@nest/modules/pubsub/pubsub.service";

export const mockPubSubService = {
  createTopic: jest.fn(),
  getTopic: jest.fn(),
  publishMessage: jest.fn(),
  publishMessageForTripProcessing: jest.fn(),
  publishMessageForMessageProcessingParams: jest.fn(),
  publishMessageForCaptureProcessing: jest.fn(),
  publishMessageForPostSaleProcessing: jest.fn(),
  publishMessageForVoidProcessing: jest.fn(),
  publishMessageForCopyTripToDriverProcessing: jest.fn(),
  publishMessageForDirectSaleProcessing: jest.fn(),
  publishMessageForReportJobProcessing: jest.fn(),
  publishMessageForWebhookMessageProcessing: jest.fn(),
  publishMessageForCampaignTriggerProcessing: jest.fn(),
  publishMessageForTripEndEvent: jest.fn(),
  publishMessageForHailingTxCreated: jest.fn(),
  publishMessageForHailingStatusChanged: jest.fn(),
  publishMessageForPickupReminder: jest.fn(),
};

export const MockPubsubServiceProvider = {
  provide: PubSubService,
  useValue: mockPubSubService,
};

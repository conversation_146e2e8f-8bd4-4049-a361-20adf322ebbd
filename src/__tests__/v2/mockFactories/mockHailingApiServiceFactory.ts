import { HailUpdateResponse } from "@nest/modules/cloudTaskFleetOrder/interface";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";

export const HailingApiServiceFactory = {
  mockUpdateFleetHailingRequest: (hailingApiService: HailingApiService) => {
    jest.spyOn(hailingApiService, "updateFleetHailingRequest").mockImplementation(async () => {
      return {
        id: "123",
        createdAt: "123",
        updatedAt: "123",
        userId: "123",
        userPhoneNumber: "123",
        language: "123",
        tripEstimation: {
          distanceMeters: 123,
          durationSeconds: 123,
        },
        itinerary: [],
        tripId: "123",
        time: "123",
        paymentDetails: {
          cardPrefix: "123",
          cardSuffix: "123",
          cardType: "123",
        },
        minMaxFareCalculations: [],
        status: "123",
        driverPhoneNumber: "123",
        driverLicensePlate: "123",
        driverName: "123",
        driverLocation: {
          lat: 123,
          lng: 123,
        },
      } as unknown as HailUpdateResponse;
    });
  },
  mockUpdateDriverHeartBeat: (hailingApiService: HailingApiService) => {
    jest.spyOn(hailingApiService, "updateDriverHeartBeat").mockImplementation(async () => {
      return;
    });
  },
};

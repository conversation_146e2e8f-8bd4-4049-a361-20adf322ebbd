import { DriverDocument } from "@nest/modules/appDatabase/documents/driver.document";
import { MeterDocument } from "@nest/modules/appDatabase/documents/meter.document";
import { TripDocument } from "@nest/modules/appDatabase/documents/trip.document";

export const AppDatabaseFactory = {
  mockMeterFindOne: (meterRepository: any) => {
    jest.spyOn(meterRepository, "findOneById").mockImplementation(async () => {
      return {
        id: "123",
        settings: {
          METER_ID: "123",
        },
      } as unknown as MeterDocument;
    });
  },
  mockDriverFindOne: (driverRepository: any) => {
    jest.spyOn(driverRepository, "findOneById").mockImplementation(async () => {
      return {
        id: "123",
        phoneNumber: "123",
      } as unknown as DriverDocument;
    });
  },
  mockMeterTripFindOne: (meterTripRepository: any) => {
    jest.spyOn(meterTripRepository, "findOneById").mockImplementation(async () => {
      return {
        id: "123",
        meterId: "123",
        driverId: "123",
        tripStart: new Date(),
        tripEnd: new Date(),
      } as unknown as TripDocument;
    });
  },
};

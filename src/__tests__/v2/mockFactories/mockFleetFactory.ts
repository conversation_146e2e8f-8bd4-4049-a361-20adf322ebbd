import FleetOrderEntity from "@nest/modules/database/entities/fleetOrder.entity";
import FleetOrderTimelineEntity from "@nest/modules/database/entities/fleetOrderTimeline.entity";
import FleetPartnerEntity from "@nest/modules/database/entities/fleetPartner.entity";
import { FleetOrderRepository } from "@nest/modules/database/repositories/fleetOrder.repository";
import { FleetOrderTimelineRepository } from "@nest/modules/database/repositories/fleetOrderTimeline.repository";
import { FleetPartnerRepository } from "@nest/modules/database/repositories/fleetPartner.repository";
import { PartnerKey } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.interface";

const mockFleetOrderFindOne = (
  fleetOrderRepository: FleetOrderRepository,
  fleetOrder: Partial<FleetOrderEntity> = {},
  isReturnNull = false,
) => {
  jest.spyOn(fleetOrderRepository, "findOne").mockImplementation(async () => {
    if (isReturnNull) {
      return null;
    }
    return {
      id: "123",
      partnerKey: PartnerKey.SYNCAB,
      thirdPartyOrderId: "123",
      status: "MATCHING",
      txId: "123",
      hailingRequestId: "123",
      user: {
        id: "123",
      },
      ...fleetOrder,
    } as unknown as FleetOrderEntity;
  });
};

const mockFleetPartnerFindOne = (
  fleetPartnerRepository: FleetPartnerRepository,
  fleetPartner: Partial<FleetPartnerEntity> = {},
  isReturnNull = false,
) => {
  jest.spyOn(fleetPartnerRepository, "findOne").mockImplementation(async () => {
    if (isReturnNull) {
      return null;
    }
    return {
      id: "123",
      partnerKey: PartnerKey.SYNCAB,
      config: {
        METER_ID: "123",
      },
      ...fleetPartner,
    } as unknown as FleetPartnerEntity;
  });
};

const mockFleetOrderTimelineFindOne = (
  fleetOrderTimelineRepository: FleetOrderTimelineRepository,
  fleetOrderTimeline: Partial<FleetOrderTimelineEntity> = {},
  isReturnNull = false,
) => {
  jest.spyOn(fleetOrderTimelineRepository, "findOne").mockImplementation(async () => {
    if (isReturnNull) {
      return null;
    }
    return {
      id: "123",
      fleetOrderId: "123",
      status: "MATCHING",
      thirdPartyStatus: "searching-for-driver",
      snapshot: {},
      ...fleetOrderTimeline,
    } as unknown as FleetOrderTimelineEntity;
  });
};

const mockFleetOrderTimelineFindLastTimelineByFleetOrderId = (
  fleetOrderTimelineRepository: FleetOrderTimelineRepository,
  fleetOrderTimeline: Partial<FleetOrderTimelineEntity> = {},
  isReturnNull = false,
) => {
  jest.spyOn(fleetOrderTimelineRepository, "findLastTimelineByFleetOrderId").mockImplementation(async () => {
    if (isReturnNull) {
      return null;
    }
    return {
      status: "MATCHING",
      thirdPartyStatus: "searching-for-driver",
      snapshot: {},
      ...fleetOrderTimeline,
    } as unknown as FleetOrderTimelineEntity;
  });
};

export const FleetOrderFactory = {
  mockFleetOrderFindOne,
  mockFleetPartnerFindOne,
  mockFleetOrderTimelineFindOne,
  mockFleetOrderTimelineFindLastTimelineByFleetOrderId,
};

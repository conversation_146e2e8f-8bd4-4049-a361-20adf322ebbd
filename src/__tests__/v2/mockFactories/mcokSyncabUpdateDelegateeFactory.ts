import { SyncabUpdateFleetOrderDelegatee } from "@nest/modules/cloudTaskFleetOrder/delegatees/SyncabUpdateFleetOrderDelegatee";
import { IUpdateFleetOrderResponse } from "@nest/modules/cloudTaskFleetOrder/interface";
import { FleetOrderStatus } from "@nest/modules/database/entities/fleetOrder.entity";
import { BookingReceiptSnapshot } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.interface";
import { SyncabFleetOrderResponse } from "@nest/modules/syncabApi/syncab.interface";

export const SyncabUpdateFleetOrderDelegateeFactory = {
  mockExecute: (
    syncabUpdateFleetOrderDelegatee: SyncabUpdateFleetOrderDelegatee,
    status: IUpdateFleetOrderResponse["status"] = FleetOrderStatus.ACCEPT,
    data?: Partial<IUpdateFleetOrderResponse>,
  ) => {
    jest.spyOn(syncabUpdateFleetOrderDelegatee, "execute").mockImplementation(async (_body, _fleetOrder) => {
      const mockSnapshot: SyncabFleetOrderResponse = {
        supplier_booking_id: "test-booking-id",
        status: "en-route",
        driver: {
          driver_id: "test-driver-id",
          first_name: "Test Driver",
          phone_number: "+852123456789",
          profile_picture: null,
          rating: "5.0",
          vehicle: {
            make: "Toyota",
            model: "Camry",
            colour: "White",
          },
          license_number: "ABC123",
        },
        location: {
          latitude: 22.3193,
          longitude: 114.1694,
          bearing: 90,
        },
      };

      const result = {
        status,
        thirdPartyStatus: "en-route",
        snapshot: mockSnapshot,
        driverPhoneNumber: "123",
        driverLicensePlate: "123",
        driverName: "123",
        driverLocation: {
          lat: 123,
          lng: 123,
        },
        ...data,
      };

      return result as unknown as IUpdateFleetOrderResponse;
    });
  },
  mockGetBookingReceiptSnapshot: (syncabUpdateFleetOrderDelegatee: SyncabUpdateFleetOrderDelegatee) => {
    jest.spyOn(syncabUpdateFleetOrderDelegatee, "getBookingReceiptSnapshot").mockImplementation(async () => {
      return {
        bookingFee: 100,
        cancellationFee: 0,
        snapshot: {},
      } as unknown as BookingReceiptSnapshot;
    });
  },
};

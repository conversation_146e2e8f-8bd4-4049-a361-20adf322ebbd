import Merchant from "@nest/modules/database/entities/merchant.entity";
import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";

const mockMerchantFindOne = (merchantRepository: MerchantRepository, merchant: Partial<Merchant> = {}) => {
  jest.spyOn(merchantRepository, "findOne").mockImplementation(async () => {
    return {
      id: "123",
      phoneNumber: "123",
      ...merchant,
    } as unknown as Merchant;
  });
};

export const MerchantFactory = {
  mockMerchantFindOne,
};

import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import { batches_collection, batches_data, batches_id } from "./data/batches";

describe(batches_collection, () => {
  /**
   * Read Fail
   */
  it(`tripInfoUser user can't read ${batches_collection}`, () =>
    testReadFail(tripInfoUser, batches_collection, batches_id));
  it(`insructiveUser user can't read ${batches_collection}`, () =>
    testReadFail(insructiveUser, batches_collection, batches_id));
  it(`meterUser user can't read ${batches_collection}`, () => testReadFail(meterUser, batches_collection, batches_id));
  it(`appUser user can't read ${batches_collection}`, () => testReadFail(appUser, batches_collection, batches_id));
  it(`publicUser user can't read ${batches_collection}`, () =>
    testReadFail(publicUser, batches_collection, batches_id));
  it(`driverUser user can't read ${batches_collection}`, () =>
    testReadFail(driverUser, batches_collection, batches_id));
  it(`driverAdminUser user can't read ${batches_collection}`, () =>
    testReadFail(driverAdminUser, batches_collection, batches_id));

  /**
   * Read Success
   */
  it(`adminUser user can read ${batches_collection}`, () =>
    testReadSuccess(adminUser, batches_collection, batches_id, batches_data));

  /**
   * Write Fail
   */
  it(`tripInfoUser user can't write ${batches_collection}`, () => testWriteFail(tripInfoUser, batches_collection));
  it(`insructiveUser user can't write ${batches_collection}`, () => testWriteFail(insructiveUser, batches_collection));
  it(`meterUser user can't write ${batches_collection}`, () => testWriteFail(meterUser, batches_collection));
  it(`appUser user can't write ${batches_collection}`, () => testWriteFail(appUser, batches_collection));
  it(`publicUser user can't write ${batches_collection}`, () => testWriteFail(publicUser, batches_collection));
  it(`driverUser user can't write ${batches_collection}`, () => testWriteFail(driverUser, batches_collection));
  it(`driverAdminUser user can't write ${batches_collection}`, () =>
    testWriteFail(driverAdminUser, batches_collection));

  /**
   * Write Success
   */
  it(`adminUser user can write ${batches_collection}`, () => testWriteSuccess(adminUser, batches_collection));
});

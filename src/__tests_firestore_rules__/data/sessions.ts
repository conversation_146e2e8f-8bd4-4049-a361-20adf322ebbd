import { testEnv } from "../_initTests";

export const sessions_data = {
  driver_id: "+85211111111",
  driver_ref: "/drivers/+85211111111",
  end_time: "2023-10-29T00:00:00Z",
  expected_end_time: "2023-10-29T00:00:00Z",
  id: "04Mb8BzIzeoHtuGRmeif",
  license_plate: "TC1234",
  meter_id: "TC1234",
  meter_ref: "/meters/TC1234",
  shift: "day",
  start_time: "2023-10-29T00:00:00Z",
};
export const sessions_id = "62VGj21i2dA8n0A3llI6";
export const sessions_collection = "sessions";
export const sessions_sub_collection = `${sessions_collection}/${sessions_id}/subCollection`;

beforeEach(async () => {
  await testEnv.withSecurityRulesDisabled((context) => {
    const firestore = context.firestore();

    // sub collection
    firestore.collection(sessions_sub_collection).doc(sessions_id).set(sessions_data);

    return firestore.collection(sessions_collection).doc(sessions_id).set(sessions_data);
  });
});

import { testEnv } from "../_initTests";

export const soepay_devices_data = {
  device_id: "SI00000763",
  licence_plate: "DASH20T1",
  type: "SECOND_ROW",
};
export const soepay_devices_id = "62VGj21i2dA8n0A3llI6";
export const soepay_devices_collection = "soepay_devices";

beforeEach(async () => {
  await testEnv.withSecurityRulesDisabled((context) => {
    const firestore = context.firestore();

    return firestore.collection(soepay_devices_collection).doc(soepay_devices_id).set(soepay_devices_data);
  });
});

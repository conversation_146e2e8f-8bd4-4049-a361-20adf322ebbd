import { testEnv } from "../_initTests";

export const users_data = {
  first_name: "<PERSON>kar<PERSON>",
  last_name: "<PERSON>",
};
export const users_id = "62VGj21i2dA8n0A3llI6";
export const other_users_id = "98VGj21i2dA8n0A3llI6";
export const users_collection = "users";
export const users_sub_collection = `${users_collection}/${users_id}/subCollection`;
export const users_other_sub_collection = `${users_collection}/${other_users_id}/subCollection`;

beforeEach(async () => {
  await testEnv.withSecurityRulesDisabled((context) => {
    const firestore = context.firestore();
    firestore.collection(users_collection).doc(users_id).set(users_data);

    // sub collection
    firestore.collection(users_sub_collection).doc(users_id).set(users_data);
    firestore.collection(users_other_sub_collection).doc(other_users_id).set(users_data);

    return firestore.collection(users_collection).doc(other_users_id).set(users_data);
  });
});

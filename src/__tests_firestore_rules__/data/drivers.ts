import { testEnv } from "../_initTests";

export const drivers_data = {
  activated_on: "2021-10-29T00:00:00Z",
  activation_ref: "/activations/qogkLMq8y49cDbYdGLya",
  bank_account: "************",
  bank_account_owner_name: "<PERSON>",
  bank_id: "004",
  created_on: "2021-10-29T00:00:00Z",
  driver_license: "111111",
  driver_license_photo: "https://firebasestorage.googleapis.com/driver_license_photo.jpg",
  gender: "M",
  hkid: "R111111Q",
  hkid_photo: "https://firebasestorage.googleapis.com/hkid_photo.jpg",
  id: "+***********",
  is_admin: false,
  last_expected_end_time: "2021-10-29T00:00:00Z",
  name: "DEV 111111",
  name_ch: "中文",
  phone_number: "+***********",
  profile_photo: "https://firebasestorage.googleapis.com/profile_photo.jpg",
  show_cash_trip: true,
};

export const drivers_sessions_data = {
  expected_end_time: null,
  license_plate: "DASH02T",
  meter_id: "DASH02T",
  session_id: "28v14iGJPgYeMJlmqEjP",
  shift: "day",
  start_time: "2021-10-29T00:00:00Z",
};

export const drivers_notifications_data = {
  created_on: "2021-10-29T00:00:00Z",
  locale: "zh-hk",
  message: "Please check DASH Driver app",
  status: "NEW",
  title: "Your payout is ready",
  type: "PAYOUT",
};

export const drivers_trips_data = {
  dash_fee: 0,
  total: 34.87,
};

export const drivers_collection = "drivers";
export const otherDriverPhoneNumber = "+85222222222";
export const driver_session_id = "28v14iGJPgYeMJlmqEjP";
export const driver_notification_id = "28v14iGJPgYeMJlmqEjP";
export const driver_trip_id = "28v14iGJPgYeMJlmqEjP";
export const driver_admin_id = "fo824gbgg4089g903g";
export const driver_admin_phone_number = "+85233333333";
export const drivers_sessions_collection = `${drivers_collection}/${drivers_data.phone_number}/sessions`;
export const drivers_other_sessions_collection = `${drivers_collection}/${otherDriverPhoneNumber}/sessions`;
export const drivers_notifications_collection = `${drivers_collection}/${drivers_data.phone_number}/notifications`;
export const drivers_other_notifications_collection = `${drivers_collection}/${otherDriverPhoneNumber}/notifications`;
export const drivers_trips_collection = `${drivers_collection}/${drivers_data.phone_number}/trips`;
export const drivers_other_trips_collection = `${drivers_collection}/${otherDriverPhoneNumber}/trips`;

beforeEach(async () => {
  await testEnv.withSecurityRulesDisabled((context) => {
    const firestore = context.firestore();
    firestore
      .collection(drivers_collection)
      .doc(otherDriverPhoneNumber)
      .set({ ...drivers_data, id: otherDriverPhoneNumber, phone_number: otherDriverPhoneNumber });

    // Driver admin
    firestore
      .collection(drivers_collection)
      .doc(driver_admin_phone_number)
      .set({ ...drivers_data, id: driver_admin_phone_number, phone_number: driver_admin_phone_number, is_admin: true });

    // Driver sessions
    firestore.collection(drivers_sessions_collection).doc(driver_session_id).set(drivers_sessions_data);
    firestore.collection(drivers_other_sessions_collection).doc(driver_session_id).set(drivers_sessions_data);

    // Driver notifications
    firestore.collection(drivers_notifications_collection).doc(driver_notification_id).set(drivers_notifications_data);
    firestore
      .collection(drivers_other_notifications_collection)
      .doc(driver_notification_id)
      .set(drivers_notifications_data);

    // Driver trips
    firestore.collection(drivers_trips_collection).doc(driver_trip_id).set(drivers_trips_data);
    firestore.collection(drivers_other_trips_collection).doc(driver_trip_id).set(drivers_trips_data);

    return firestore.collection(drivers_collection).doc(drivers_data.phone_number).set(drivers_data);
  });
});

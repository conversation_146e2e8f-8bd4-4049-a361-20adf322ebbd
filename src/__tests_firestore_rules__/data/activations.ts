import { testEnv } from "../_initTests";

export const activations_data = {
  created_on: "2021-10-29T00:00:00Z",
  driver_ref: "/drivers/+85292101712",
};
export const activations_collection = "activation";
export const activations_id = "62VGj21i2dA8n0A3llI6";

beforeEach(async () => {
  await testEnv.withSecurityRulesDisabled((context) => {
    const ref = context.firestore().collection(activations_collection);
    return ref.doc(activations_id).set(activations_data);
  });
});

import { testEnv } from "../_initTests";

export const happenings_data = {
  end_date: "2023-10-29T00:00:00Z",
  id: "FbkiARE8aeLt1F0B5b6M",
  image_url:
    "https://www.discoverhongkong.com/uk/what-s-new/events/dhk-highlighted-events/hong-kong-wine-and-dine-festival.thumb.800.480.png?ck=1705031069",
  link: "https://www.discoverhongkong.com/eng/what-s-new/events/dhk-highlighted-events/hong-kong-wine-and-dine-festival.html",
  location: "Central Harbourfront",
  location_zh: "中環海濱",
  name: "Wine & Dine '23",
  name_zh: "美酒佳餚巡禮",
  start_date: "2023-10-26T00:00:00Z",
};
export const happening_id = "FbkiARE8aeLt1F0B5b6M";
export const happenings_collection = "happenings";

beforeEach(async () => {
  // Setup initial configurations data
  await testEnv.withSecurityRulesDisabled((context) => {
    const configurationsRef = context.firestore().collection(happenings_collection);
    return configurationsRef.doc(happening_id).set(happenings_data);
  });
});

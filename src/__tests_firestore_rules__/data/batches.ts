import { testEnv } from "../_initTests";

export const batches_data = {
  created_by: "dummy",
  created_on: "2021-10-29T00:00:00Z",
  id: "62VGj21i2dA8n0A3llI6",
  original_input: [{ driver_id: "+85211111111", trip: ["bd5d3e7d-ef13-4eec-8454-3713bd00af56"] }],
  result: [{ driver_id: "+85211111111", trip: ["bd5d3e7d-ef13-4eec-8454-3713bd00af56"] }],
  status: "NEW",
  type: "PRERELEASE_REQUEST",
};
export const batches_collection = "batches";
export const batches_id = "62VGj21i2dA8n0A3llI6";

beforeEach(async () => {
  await testEnv.withSecurityRulesDisabled((context) => {
    const ref = context.firestore().collection(batches_collection);
    return ref.doc(batches_id).set(batches_data);
  });
});

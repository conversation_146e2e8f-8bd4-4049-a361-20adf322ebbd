import { testEnv } from "../_initTests";

export const meters_data = {
  settings: {
    dash_fee_constant: 0,
    dash_fee_rate: 0.03,
    heartbeat_interval: 5,
    instructive_app_version: "1.5.2(1920813)",
    instructive_device_serial_number: "SAH00001111",
    is_blank_screen_required: false,
  },
};
export const meters_id = "62VGj21i2dA8n0A3llI6";
export const meters_other_id = "98VGj21i2dA8n0A3llI6";
export const meters_collection = "meters";

beforeEach(async () => {
  await testEnv.withSecurityRulesDisabled((context) => {
    const firestore = context.firestore();

    firestore.collection(meters_collection).doc(meters_other_id).set(meters_data);

    return firestore.collection(meters_collection).doc(meters_id).set(meters_data);
  });
});

import { testEnv } from "../_initTests";

export const configurations_data = {
  app_versions: {
    recommended_android: "1.0.0",
    recommended_ios: "1.0.0",
    required_android: "1.0.0",
    required_ios: "1.0.0",
  },
  google_places_api_key: "AIzaSyAjF2btOGllWv43xjShvMOnCa3SQyJMuS8",
  max_tips_allowed_amount: 500,
  whitelisted_domains: {
    "www.disney.com": "app1",
    "www.google.com": "app2",
    "www.klook.com": "app2",
    "www.uber.com": "app1",
  },
};
export const configurations_collection = "configurations";
export const configurations_id = "28v14iGJPgYeMJlmqEjP";

beforeEach(async () => {
  await testEnv.withSecurityRulesDisabled((context) => {
    const ref = context.firestore().collection(configurations_collection);
    return ref.doc(configurations_id).set(configurations_data);
  });
});

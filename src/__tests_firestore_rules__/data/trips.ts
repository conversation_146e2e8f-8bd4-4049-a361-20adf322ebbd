import { testEnv } from "../_initTests";

export const trips_data = {
  dash_fee: 0,
  total: 34.87,
};
export const trips_collection = "trips";
export const trip_id = "28v14iGJPgYeMJlmqEjP";

beforeEach(async () => {
  // Setup initial configurations data
  await testEnv.withSecurityRulesDisabled((context) => {
    const configurationsRef = context.firestore().collection(trips_collection);
    return configurationsRef.doc(trip_id).set(trips_data);
  });
});

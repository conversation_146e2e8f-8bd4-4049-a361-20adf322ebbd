import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import { sessions_data, sessions_id, sessions_sub_collection } from "./data/sessions";

describe(sessions_sub_collection, () => {
  /**
   * Read Fail
   */
  it(`appUser user can't read ${sessions_sub_collection}`, () =>
    testReadFail(appUser, sessions_sub_collection, sessions_id));
  it(`publicUser user can't read ${sessions_sub_collection}`, () =>
    testReadFail(publicUser, sessions_sub_collection, sessions_id));
  it(`driverUser user can't read other ${sessions_sub_collection}`, () =>
    testReadFail(driverUser, sessions_sub_collection, sessions_id));
  it(`driverAdminUser user can't read other ${sessions_sub_collection}`, () =>
    testReadFail(driverAdminUser, sessions_sub_collection, sessions_id));

  /**
   * Read Success
   */
  it(`meterUser user can read ${sessions_sub_collection}`, () =>
    testReadSuccess(meterUser, sessions_sub_collection, sessions_id, sessions_data));
  it(`adminUser user can read ${sessions_sub_collection}`, () =>
    testReadSuccess(adminUser, sessions_sub_collection, sessions_id, sessions_data));

  /**
   * Write Fail
   */
  it(`appUser user can't write ${sessions_sub_collection}`, () => testWriteFail(appUser, sessions_sub_collection));
  it(`publicUser user can't write ${sessions_sub_collection}`, () =>
    testWriteFail(publicUser, sessions_sub_collection));
  it(`driverUser user can't write ${sessions_sub_collection}`, () =>
    testWriteFail(driverUser, sessions_sub_collection));
  it(`driverAdminUser user can't write ${sessions_sub_collection}`, () =>
    testWriteFail(driverAdminUser, sessions_sub_collection));

  /**
   * Write Success
   */
  it(`meterUser user can write ${sessions_sub_collection}`, () => testWriteSuccess(meterUser, sessions_sub_collection));
  it(`adminUser user can write ${sessions_sub_collection}`, () => testWriteSuccess(adminUser, sessions_sub_collection));

  /**
   * Change these tests when the token is specific for meter trip info and instructive
   */
  it(`tripInfoUser user can read ${sessions_sub_collection} BUT SHOULD FAIL`, () =>
    testReadSuccess(tripInfoUser, sessions_sub_collection, sessions_id, sessions_data));
  it(`insructiveUser user can read ${sessions_sub_collection} BUT SHOULD FAIL`, () =>
    testReadSuccess(insructiveUser, sessions_sub_collection, sessions_id, sessions_data));
  it(`tripInfoUser user can write ${sessions_sub_collection} BUT SHOULD FAIL`, () =>
    testWriteSuccess(tripInfoUser, sessions_sub_collection));
  it(`insructiveUser user can write ${sessions_sub_collection} BUT SHOULD FAIL`, () =>
    testWriteSuccess(insructiveUser, sessions_sub_collection));
});

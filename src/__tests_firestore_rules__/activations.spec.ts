import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import { activations_collection, activations_data, activations_id } from "./data/activations";

describe(activations_collection, () => {
  /**
   * Read Fail
   */
  it(`tripInfoUser user can't read ${activations_collection}`, () =>
    testReadFail(tripInfoUser, activations_collection, activations_id));
  it(`insructiveUser user can't read ${activations_collection}`, () =>
    testReadFail(insructiveUser, activations_collection, activations_id));
  it(`meterUser user can't read ${activations_collection}`, () =>
    testReadFail(meterUser, activations_collection, activations_id));
  it(`appUser user can't read ${activations_collection}`, () =>
    testReadFail(appUser, activations_collection, activations_id));
  it(`publicUser user can't read ${activations_collection}`, () =>
    testReadFail(publicUser, activations_collection, activations_id));
  it(`driverUser user can't read ${activations_collection}`, () =>
    testReadFail(driverUser, activations_collection, activations_id));
  it(`driverAdminUser user can't read ${activations_collection}`, () =>
    testReadFail(driverAdminUser, activations_collection, activations_id));

  /**
   * Read Success
   */
  it(`adminUser user can read ${activations_collection}`, () =>
    testReadSuccess(adminUser, activations_collection, activations_id, activations_data));

  /**
   * Write Fail
   */
  it(`tripInfoUser user can't write ${activations_collection}`, () =>
    testWriteFail(tripInfoUser, activations_collection));
  it(`insructiveUser user can't write ${activations_collection}`, () =>
    testWriteFail(insructiveUser, activations_collection));
  it(`meterUser user can't write ${activations_collection}`, () => testWriteFail(meterUser, activations_collection));
  it(`appUser user can't write ${activations_collection}`, () => testWriteFail(appUser, activations_collection));
  it(`publicUser user can't write ${activations_collection}`, () => testWriteFail(publicUser, activations_collection));
  it(`driverUser user can't write ${activations_collection}`, () => testWriteFail(driverUser, activations_collection));
  it(`driverAdminUser user can't write ${activations_collection}`, () =>
    testWriteFail(driverAdminUser, activations_collection));

  /**
   * Write Success
   */
  it(`adminUser user can write ${activations_collection}`, () => testWriteSuccess(adminUser, activations_collection));
});

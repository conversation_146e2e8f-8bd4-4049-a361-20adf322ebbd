import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import { happening_id, happenings_collection, happenings_data } from "./data/happenings";

describe(happenings_collection, () => {
  /**
   * Read Success
   */
  it(`tripInfoUser user can read ${happenings_collection}`, () =>
    testReadSuccess(tripInfoUser, happenings_collection, happening_id, happenings_data));
  it(`insructiveUser user can read ${happenings_collection}`, () =>
    testReadSuccess(insructiveUser, happenings_collection, happening_id, happenings_data));
  it(`meterUser user can read ${happenings_collection}`, () =>
    testReadSuccess(meterUser, happenings_collection, happening_id, happenings_data));
  it(`adminUser user can read ${happenings_collection}`, () =>
    testReadSuccess(adminUser, happenings_collection, happening_id, happenings_data));
  it(`appUser user can read ${happenings_collection}`, () =>
    testReadSuccess(appUser, happenings_collection, happening_id, happenings_data));
  it(`publicUser user can read ${happenings_collection}`, () =>
    testReadSuccess(publicUser, happenings_collection, happening_id, happenings_data));
  it(`driverUser user can read ${happenings_collection}`, () =>
    testReadSuccess(driverUser, happenings_collection, happening_id, happenings_data));
  it(`driverAdminUser user can read ${happenings_collection}`, () =>
    testReadSuccess(driverAdminUser, happenings_collection, happening_id, happenings_data));

  /**
   * Write Fail
   */
  it(`tripInfoUser user can't write ${happenings_collection}`, () =>
    testWriteFail(tripInfoUser, happenings_collection));
  it(`insructiveUser user can't write ${happenings_collection}`, () =>
    testWriteFail(insructiveUser, happenings_collection));
  it(`meterUser user can't write ${happenings_collection}`, () => testWriteFail(meterUser, happenings_collection));
  it(`appUser user can't write ${happenings_collection}`, () => testWriteFail(appUser, happenings_collection));
  it(`publicUser user can't write ${happenings_collection}`, () => testWriteFail(publicUser, happenings_collection));
  it(`driverUser user can't write ${happenings_collection}`, () => testWriteFail(driverUser, happenings_collection));
  it(`driverAdminUser user can't write ${happenings_collection}`, () =>
    testWriteFail(driverAdminUser, happenings_collection));

  /**
   * Write Success
   */
  it(`adminUser user can write ${happenings_collection}`, () => testWriteSuccess(adminUser, happenings_collection));
});

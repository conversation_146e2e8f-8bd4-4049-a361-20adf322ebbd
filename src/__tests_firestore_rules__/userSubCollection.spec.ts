import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testUpdateFail,
  testUpdateSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import { other_users_id, users_data, users_id, users_other_sub_collection, users_sub_collection } from "./data/users";

describe(users_sub_collection, () => {
  /**
   * Read Fail
   */
  it(`tripInfoUser user can't read ${users_sub_collection}`, () =>
    testReadFail(tripInfoUser, users_sub_collection, users_id));
  it(`insructiveUser user can't read ${users_sub_collection}`, () =>
    testReadFail(insructiveUser, users_sub_collection, users_id));
  it(`meterUser user can't read ${users_sub_collection}`, () =>
    testReadFail(meterUser, users_sub_collection, users_id));
  it(`publicUser user can't read ${users_sub_collection}`, () =>
    testReadFail(publicUser, users_sub_collection, users_id));
  it(`driverUser user can't read ${users_sub_collection}`, () =>
    testReadFail(driverUser, users_sub_collection, users_id));
  it(`appUser user can't read other ${users_sub_collection}`, () =>
    testReadFail(appUser, users_other_sub_collection, other_users_id));
  it(`driverAdminUser user can't read ${users_sub_collection}`, () =>
    testReadFail(driverAdminUser, users_sub_collection, other_users_id));

  /**
   * Read Success
   */
  it(`appUser user can read his own ${users_sub_collection}`, () =>
    testReadSuccess(appUser, users_sub_collection, users_id, users_data));
  it(`adminUser user can read ${users_sub_collection}`, () =>
    testReadSuccess(adminUser, users_sub_collection, users_id, users_data));

  /**
   * Write Fail
   */
  it(`tripInfoUser user can't write ${users_sub_collection}`, () => testWriteFail(tripInfoUser, users_sub_collection));
  it(`insructiveUser user can't write ${users_sub_collection}`, () =>
    testWriteFail(insructiveUser, users_sub_collection));
  it(`meterUser user can't write ${users_sub_collection}`, () => testWriteFail(meterUser, users_sub_collection));
  it(`publicUser user can't write ${users_sub_collection}`, () => testWriteFail(publicUser, users_sub_collection));
  it(`driverUser user can't write ${users_sub_collection}`, () => testWriteFail(driverUser, users_sub_collection));
  it(`driverAdminUser user can't write ${users_sub_collection}`, () =>
    testWriteFail(driverAdminUser, users_sub_collection));

  /**
   * Write Success
   */
  it(`adminUser user can write ${users_sub_collection}`, () => testWriteSuccess(adminUser, users_sub_collection));
  it(`appUser user can write ${users_sub_collection}`, () => testWriteSuccess(appUser, users_sub_collection));

  /**
   * Update Fail
   */
  it(`appUser user can't update other ${users_sub_collection}`, () =>
    testUpdateFail(appUser, users_other_sub_collection, other_users_id, users_data));
  it(`driverAdminUser user can't update ${users_sub_collection}`, () =>
    testUpdateFail(driverAdminUser, users_sub_collection, other_users_id, users_data));

  /**
   * Update Success
   */
  it(`appUser user can update his own ${users_sub_collection}`, () =>
    testUpdateSuccess(appUser, users_sub_collection, users_id, users_data));
  it(`adminUser user can update ${users_sub_collection}`, () =>
    testUpdateSuccess(adminUser, users_sub_collection, users_id, users_data));
});

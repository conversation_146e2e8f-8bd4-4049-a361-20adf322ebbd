import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import { meters_collection, meters_data, meters_id, meters_other_id } from "./data/meters";

describe(meters_collection, () => {
  /**
   * Read Fail
   */
  it(`appUser user can't read ${meters_collection}`, () => testReadFail(appUser, meters_collection, meters_id));
  it(`publicUser user can't read ${meters_collection}`, () => testReadFail(publicUser, meters_collection, meters_id));
  it(`driverUser user can't read other ${meters_collection}`, () =>
    testReadFail(driverUser, meters_collection, meters_id));
  it(`driverAdminUser user can't read other ${meters_collection}`, () =>
    testReadFail(driverAdminUser, meters_collection, meters_id));

  /**
   * Read Success
   */
  it(`meterUser user can read ${meters_collection}`, () =>
    testReadSuccess(meterUser, meters_collection, meters_id, meters_data));
  it(`tripInfoUser user can read ${meters_collection}`, () =>
    testReadSuccess(tripInfoUser, meters_collection, meters_id, meters_data));
  it(`insructiveUser user can read ${meters_collection}`, () =>
    testReadSuccess(insructiveUser, meters_collection, meters_id, meters_data));
  it(`adminUser user can read ${meters_collection}`, () =>
    testReadSuccess(adminUser, meters_collection, meters_id, meters_data));

  /**
   * Write Fail
   */
  it(`appUser user can't write ${meters_collection}`, () => testWriteFail(appUser, meters_collection));
  it(`publicUser user can't write ${meters_collection}`, () => testWriteFail(publicUser, meters_collection));
  it(`driverUser user can't write ${meters_collection}`, () => testWriteFail(driverUser, meters_collection));
  it(`driverAdminUser user can't write ${meters_collection}`, () => testWriteFail(driverAdminUser, meters_collection));

  /**
   * Write Success
   */
  it(`meterUser user can write ${meters_collection}`, () => testWriteSuccess(meterUser, meters_collection));
  it(`tripInfoUser user can write ${meters_collection}`, () => testWriteSuccess(tripInfoUser, meters_collection));
  it(`insructiveUser user can write ${meters_collection}`, () => testWriteSuccess(insructiveUser, meters_collection));
  it(`adminUser user can write ${meters_collection}`, () => testWriteSuccess(adminUser, meters_collection));

  /**
   * Change these tests when the token is specific for meter trip info and instructive
   */
  it(`meterUser user can read other ${meters_collection} BUT SHOULD FAIL`, () =>
    testReadSuccess(meterUser, meters_collection, meters_other_id, meters_data));
  it(`tripInfoUser user can read other ${meters_collection} BUT SHOULD FAIL`, () =>
    testReadSuccess(tripInfoUser, meters_collection, meters_other_id, meters_data));
  it(`insructiveUser user can read other ${meters_collection} BUT SHOULD FAIL`, () =>
    testReadSuccess(insructiveUser, meters_collection, meters_other_id, meters_data));
});

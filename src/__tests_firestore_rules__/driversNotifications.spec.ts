import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testUpdateFail,
  testUpdateSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import {
  driver_notification_id,
  drivers_notifications_collection,
  drivers_other_notifications_collection,
  drivers_notifications_data,
} from "./data/drivers";

describe(drivers_notifications_collection, () => {
  /**
   * Read Fail
   */
  it(`appUser user can't read ${drivers_notifications_collection}`, () =>
    testReadFail(appUser, drivers_notifications_collection, driver_notification_id));
  it(`meterUser user can't read ${drivers_notifications_collection}`, () =>
    testReadFail(meterUser, drivers_notifications_collection, driver_notification_id));
  it(`tripInfoUser user can't read ${drivers_notifications_collection}`, () =>
    testReadFail(tripInfoUser, drivers_notifications_collection, driver_notification_id));
  it(`insructiveUser user can't read ${drivers_notifications_collection}`, () =>
    testReadFail(insructiveUser, drivers_notifications_collection, driver_notification_id));
  it(`publicUser user can't read ${drivers_notifications_collection}`, () =>
    testReadFail(publicUser, drivers_notifications_collection, driver_notification_id));
  it(`driverUser user can't read other ${drivers_notifications_collection}`, () =>
    testReadFail(driverUser, drivers_other_notifications_collection, driver_notification_id));

  /**
   * Read Success
   */
  it(`driverUser user can read his own ${drivers_notifications_collection}`, () =>
    testReadSuccess(driverUser, drivers_notifications_collection, driver_notification_id, drivers_notifications_data));
  it(`adminUser user can read ${drivers_notifications_collection}`, () =>
    testReadSuccess(adminUser, drivers_notifications_collection, driver_notification_id, drivers_notifications_data));
  it(`driverAdminUser user can read ${drivers_notifications_collection}`, () =>
    testReadSuccess(
      driverAdminUser,
      drivers_notifications_collection,
      driver_notification_id,
      drivers_notifications_data,
    ));

  /**
   * Write Fail
   */
  it(`meterUser user can't write ${drivers_notifications_collection}`, () =>
    testWriteFail(meterUser, drivers_notifications_collection));
  it(`tripInfoUser user can't write ${drivers_notifications_collection}`, () =>
    testWriteFail(tripInfoUser, drivers_notifications_collection));
  it(`insructiveUser user can't write ${drivers_notifications_collection}`, () =>
    testWriteFail(insructiveUser, drivers_notifications_collection));
  it(`appUser user can't write ${drivers_notifications_collection}`, () =>
    testWriteFail(appUser, drivers_notifications_collection));
  it(`publicUser user can't write ${drivers_notifications_collection}`, () =>
    testWriteFail(publicUser, drivers_notifications_collection));
  it(`driverUser user can't write ${drivers_notifications_collection}`, () =>
    testWriteFail(driverUser, drivers_notifications_collection));

  /**
   * Write Success
   */
  it(`adminUser user can write ${drivers_notifications_collection}`, () =>
    testWriteSuccess(adminUser, drivers_notifications_collection));
  it(`driverAdminUser user can write ${drivers_notifications_collection}`, () =>
    testWriteSuccess(driverAdminUser, drivers_notifications_collection));

  /**
   * Update Fail
   */
  it(`driverUser user can't update other ${drivers_notifications_collection}`, () =>
    testUpdateFail(
      driverUser,
      drivers_other_notifications_collection,
      driver_notification_id,
      drivers_notifications_data,
    ));
  it(`driverUser user can't update his own ${drivers_notifications_collection}`, () =>
    testUpdateFail(driverUser, drivers_notifications_collection, driver_notification_id, drivers_notifications_data));

  /**
   * Update Success
   */
  it(`adminUser user can update ${drivers_notifications_collection}`, () =>
    testUpdateSuccess(adminUser, drivers_notifications_collection, driver_notification_id, drivers_notifications_data));
  it(`driverAdminUser user can update ${drivers_notifications_collection}`, () =>
    testUpdateSuccess(
      driverAdminUser,
      drivers_notifications_collection,
      driver_notification_id,
      drivers_notifications_data,
    ));
});

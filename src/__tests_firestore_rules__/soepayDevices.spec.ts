import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import { soepay_devices_collection, soepay_devices_data, soepay_devices_id } from "./data/soepayDevices";

describe(soepay_devices_collection, () => {
  /**
   * Read Fail
   */
  it(`appUser user can't read ${soepay_devices_collection}`, () =>
    testReadFail(appUser, soepay_devices_collection, soepay_devices_id));
  it(`publicUser user can't read ${soepay_devices_collection}`, () =>
    testReadFail(publicUser, soepay_devices_collection, soepay_devices_id));
  it(`driverUser user can't read other ${soepay_devices_collection}`, () =>
    testReadFail(driverUser, soepay_devices_collection, soepay_devices_id));
  it(`driverAdminUser user can't read other ${soepay_devices_collection}`, () =>
    testReadFail(driverAdminUser, soepay_devices_collection, soepay_devices_id));

  /**
   * Read Success
   */
  it(`meterUser user can read ${soepay_devices_collection}`, () =>
    testReadSuccess(meterUser, soepay_devices_collection, soepay_devices_id, soepay_devices_data));
  it(`tripInfoUser user can read ${soepay_devices_collection}`, () =>
    testReadSuccess(tripInfoUser, soepay_devices_collection, soepay_devices_id, soepay_devices_data));
  it(`insructiveUser user can read ${soepay_devices_collection}`, () =>
    testReadSuccess(insructiveUser, soepay_devices_collection, soepay_devices_id, soepay_devices_data));
  it(`adminUser user can read ${soepay_devices_collection}`, () =>
    testReadSuccess(adminUser, soepay_devices_collection, soepay_devices_id, soepay_devices_data));

  /**
   * Write Fail
   */
  it(`appUser user can't write ${soepay_devices_collection}`, () => testWriteFail(appUser, soepay_devices_collection));
  it(`publicUser user can't write ${soepay_devices_collection}`, () =>
    testWriteFail(publicUser, soepay_devices_collection));
  it(`driverUser user can't write ${soepay_devices_collection}`, () =>
    testWriteFail(driverUser, soepay_devices_collection));
  it(`driverAdminUser user can't write ${soepay_devices_collection}`, () =>
    testWriteFail(driverAdminUser, soepay_devices_collection));

  /**
   * Write Success
   */
  it(`meterUser user can write ${soepay_devices_collection}`, () =>
    testWriteSuccess(meterUser, soepay_devices_collection));
  it(`tripInfoUser user can write ${soepay_devices_collection}`, () =>
    testWriteSuccess(tripInfoUser, soepay_devices_collection));
  it(`insructiveUser user can write ${soepay_devices_collection}`, () =>
    testWriteSuccess(insructiveUser, soepay_devices_collection));
  it(`adminUser user can write ${soepay_devices_collection}`, () =>
    testWriteSuccess(adminUser, soepay_devices_collection));
});

import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testUpdateFail,
  testUpdateSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import { other_users_id, users_collection, users_data, users_id } from "./data/users";

describe(users_collection, () => {
  /**
   * Read Fail
   */
  it(`tripInfoUser user can't read ${users_collection}`, () => testReadFail(tripInfoUser, users_collection, users_id));
  it(`insructiveUser user can't read ${users_collection}`, () =>
    testReadFail(insructiveUser, users_collection, users_id));
  it(`meterUser user can't read ${users_collection}`, () => testReadFail(meterUser, users_collection, users_id));
  it(`publicUser user can't read ${users_collection}`, () => testReadFail(publicUser, users_collection, users_id));
  it(`driverUser user can't read ${users_collection}`, () => testReadFail(driverUser, users_collection, users_id));
  it(`appUser user can't read other ${users_collection}`, () =>
    testReadFail(appUser, users_collection, other_users_id));
  it(`driverAdminUser user can't read ${users_collection}`, () =>
    testReadFail(driverAdminUser, users_collection, other_users_id));

  /**
   * Read Success
   */
  it(`appUser user can read his own ${users_collection}`, () =>
    testReadSuccess(appUser, users_collection, users_id, users_data));
  it(`adminUser user can read ${users_collection}`, () =>
    testReadSuccess(adminUser, users_collection, users_id, users_data));

  /**
   * Write Fail
   */
  it(`tripInfoUser user can't write ${users_collection}`, () => testWriteFail(tripInfoUser, users_collection));
  it(`insructiveUser user can't write ${users_collection}`, () => testWriteFail(insructiveUser, users_collection));
  it(`meterUser user can't write ${users_collection}`, () => testWriteFail(meterUser, users_collection));
  it(`appUser user can't write ${users_collection}`, () => testWriteFail(appUser, users_collection));
  it(`publicUser user can't write ${users_collection}`, () => testWriteFail(publicUser, users_collection));
  it(`driverUser user can't write ${users_collection}`, () => testWriteFail(driverUser, users_collection));
  it(`driverAdminUser user can't write ${users_collection}`, () => testWriteFail(driverAdminUser, users_collection));

  /**
   * Write Success
   */
  it(`adminUser user can write ${users_collection}`, () => testWriteSuccess(adminUser, users_collection));

  /**
   * Update Fail
   */
  it(`appUser user can't update other ${users_collection}`, () =>
    testUpdateFail(appUser, users_collection, other_users_id, users_data));
  it(`driverAdminUser user can't update ${users_collection}`, () =>
    testUpdateFail(driverAdminUser, users_collection, other_users_id, users_data));

  /**
   * Update Success
   */
  it(`appUser user can update his own ${users_collection}`, () =>
    testUpdateSuccess(appUser, users_collection, users_id, users_data));
  it(`adminUser user can update ${users_collection}`, () =>
    testUpdateSuccess(adminUser, users_collection, users_id, users_data));
});

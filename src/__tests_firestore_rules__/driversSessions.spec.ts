import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testUpdateFail,
  testUpdateSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import {
  driver_session_id,
  drivers_other_sessions_collection,
  drivers_sessions_collection,
  drivers_sessions_data,
} from "./data/drivers";

describe(drivers_sessions_collection, () => {
  /**
   * Read Fail
   */
  it(`appUser user can't read ${drivers_sessions_collection}`, () =>
    testReadFail(appUser, drivers_sessions_collection, driver_session_id));
  it(`publicUser user can't read ${drivers_sessions_collection}`, () =>
    testReadFail(publicUser, drivers_sessions_collection, driver_session_id));
  it(`driverUser user can't read other ${drivers_sessions_collection}`, () =>
    testReadFail(driverUser, drivers_other_sessions_collection, driver_session_id));

  /**
   * Read Success
   */
  it(`meterUser user can read ${drivers_sessions_collection}`, () =>
    testReadSuccess(meterUser, drivers_sessions_collection, driver_session_id, drivers_sessions_data));
  it(`driverUser user can read his own ${drivers_sessions_collection}`, () =>
    testReadSuccess(driverUser, drivers_sessions_collection, driver_session_id, drivers_sessions_data));
  it(`adminUser user can read ${drivers_sessions_collection}`, () =>
    testReadSuccess(adminUser, drivers_sessions_collection, driver_session_id, drivers_sessions_data));
  it(`driverAdminUser user can read ${drivers_sessions_collection}`, () =>
    testReadSuccess(driverAdminUser, drivers_sessions_collection, driver_session_id, drivers_sessions_data));

  /**
   * Write Fail
   */
  it(`appUser user can't write ${drivers_sessions_collection}`, () =>
    testWriteFail(appUser, drivers_sessions_collection));
  it(`publicUser user can't write ${drivers_sessions_collection}`, () =>
    testWriteFail(publicUser, drivers_sessions_collection));
  it(`driverUser user can't write ${drivers_sessions_collection}`, () =>
    testWriteFail(driverUser, drivers_sessions_collection));

  /**
   * Write Success
   */
  it(`meterUser user can write ${drivers_sessions_collection}`, () =>
    testWriteSuccess(meterUser, drivers_sessions_collection));
  it(`adminUser user can write ${drivers_sessions_collection}`, () =>
    testWriteSuccess(adminUser, drivers_sessions_collection));
  it(`driverAdminUser user can write ${drivers_sessions_collection}`, () =>
    testWriteSuccess(driverAdminUser, drivers_sessions_collection));

  /**
   * Update Fail
   */
  it(`driverUser user can't update other ${drivers_sessions_collection}`, () =>
    testUpdateFail(driverUser, drivers_other_sessions_collection, driver_session_id, { shift: "night" }));
  it(`driverUser user can't update his own ${drivers_sessions_collection}`, () =>
    testUpdateFail(driverUser, drivers_sessions_collection, driver_session_id, { shift: "night" }));

  /**
   * Update Success
   */
  it(`meterUser user can update ${drivers_sessions_collection}`, () =>
    testUpdateSuccess(meterUser, drivers_sessions_collection, driver_session_id, { shift: "night" }));
  it(`adminUser user can update ${drivers_sessions_collection}`, () =>
    testUpdateSuccess(adminUser, drivers_sessions_collection, driver_session_id, { shift: "night" }));
  it(`driverAdminUser user can update ${drivers_sessions_collection}`, () =>
    testUpdateSuccess(driverAdminUser, drivers_sessions_collection, driver_session_id, { shift: "night" }));

  /**
   * Change these tests when the token is specific for meter trip info and instructive
   */
  it(`tripInfoUser user can read ${drivers_sessions_collection} BUT SHOULD FAIL`, () =>
    testReadSuccess(tripInfoUser, drivers_sessions_collection, driver_session_id, drivers_sessions_data));
  it(`insructiveUser user can read ${drivers_sessions_collection} BUT SHOULD FAIL`, () =>
    testReadSuccess(insructiveUser, drivers_sessions_collection, driver_session_id, drivers_sessions_data));
  it(`tripInfoUser user can write ${drivers_sessions_collection} BUT SHOULD FAIL`, () =>
    testWriteSuccess(tripInfoUser, drivers_sessions_collection));
  it(`insructiveUser user can write ${drivers_sessions_collection} BUT SHOULD FAIL`, () =>
    testWriteSuccess(insructiveUser, drivers_sessions_collection));
});

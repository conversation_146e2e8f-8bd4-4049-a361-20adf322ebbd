import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import { configurations_collection, configurations_data, configurations_id } from "./data/configurations";

describe(configurations_collection, () => {
  /**
   * Read Success
   */
  it(`tripInfoUser user can read ${configurations_collection}`, () =>
    testReadSuccess(tripInfoUser, configurations_collection, configurations_id, configurations_data));
  it(`insructiveUser user can read ${configurations_collection}`, () =>
    testReadSuccess(insructiveUser, configurations_collection, configurations_id, configurations_data));
  it(`meterUser user can read ${configurations_collection}`, () =>
    testReadSuccess(meterUser, configurations_collection, configurations_id, configurations_data));
  it(`adminUser user can read ${configurations_collection}`, () =>
    testReadSuccess(adminUser, configurations_collection, configurations_id, configurations_data));
  it(`appUser user can read ${configurations_collection}`, () =>
    testReadSuccess(appUser, configurations_collection, configurations_id, configurations_data));
  it(`publicUser user can read ${configurations_collection}`, () =>
    testReadSuccess(publicUser, configurations_collection, configurations_id, configurations_data));
  it(`driverUser user can read ${configurations_collection}`, () =>
    testReadSuccess(driverUser, configurations_collection, configurations_id, configurations_data));
  it(`driverAdminUser user can read ${configurations_collection}`, () =>
    testReadSuccess(driverAdminUser, configurations_collection, configurations_id, configurations_data));

  /**
   * Write Fail
   */
  it(`tripInfoUser user can't write ${configurations_collection}`, () =>
    testWriteFail(tripInfoUser, configurations_collection));
  it(`insructiveUser user can't write ${configurations_collection}`, () =>
    testWriteFail(insructiveUser, configurations_collection));
  it(`meterUser user can't write ${configurations_collection}`, () =>
    testWriteFail(meterUser, configurations_collection));
  it(`appUser user can't write ${configurations_collection}`, () => testWriteFail(appUser, configurations_collection));
  it(`publicUser user can't write ${configurations_collection}`, () =>
    testWriteFail(publicUser, configurations_collection));
  it(`driverUser user can't write ${configurations_collection}`, () =>
    testWriteFail(driverUser, configurations_collection));
  it(`driverAdminUser user can't write ${configurations_collection}`, () =>
    testWriteFail(driverAdminUser, configurations_collection));

  /**
   * Write Success
   */
  it(`adminUser user can write ${configurations_collection}`, () =>
    testWriteSuccess(adminUser, configurations_collection));
});

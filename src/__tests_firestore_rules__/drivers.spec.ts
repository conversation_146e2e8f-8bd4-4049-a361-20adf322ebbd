import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testUpdateFail,
  testUpdateSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import { drivers_collection, drivers_data, otherDriverPhoneNumber } from "./data/drivers";

describe(drivers_collection, () => {
  /**
   * Read Fail
   */
  it(`tripInfoUser user can't read ${drivers_collection}`, () =>
    testReadFail(tripInfoUser, drivers_collection, drivers_data.phone_number));
  it(`insructiveUser user can't read ${drivers_collection}`, () =>
    testReadFail(insructiveUser, drivers_collection, drivers_data.phone_number));
  it(`meterUser user can't read ${drivers_collection}`, () =>
    testReadFail(meterUser, drivers_collection, drivers_data.phone_number));
  it(`appUser user can't read ${drivers_collection}`, () =>
    testReadFail(appUser, drivers_collection, drivers_data.phone_number));
  it(`publicUser user can't read ${drivers_collection}`, () =>
    testReadFail(publicUser, drivers_collection, drivers_data.phone_number));
  it(`driverUser user can't read other ${drivers_collection}`, () =>
    testReadFail(driverUser, drivers_collection, otherDriverPhoneNumber));

  /**
   * Read Success
   */
  it(`driverUser user can read his own ${drivers_collection}`, () =>
    testReadSuccess(driverUser, drivers_collection, drivers_data.phone_number, drivers_data));
  it(`adminUser user can read ${drivers_collection}`, () =>
    testReadSuccess(adminUser, drivers_collection, drivers_data.phone_number, drivers_data));
  it(`driverAdminUser user can read ${drivers_collection}`, () =>
    testReadSuccess(driverAdminUser, drivers_collection, drivers_data.phone_number, drivers_data));

  /**
   * Write Fail
   */
  it(`tripInfoUser user can't write ${drivers_collection}`, () => testWriteFail(tripInfoUser, drivers_collection));
  it(`insructiveUser user can't write ${drivers_collection}`, () => testWriteFail(insructiveUser, drivers_collection));
  it(`meterUser user can't write ${drivers_collection}`, () => testWriteFail(meterUser, drivers_collection));
  it(`appUser user can't write ${drivers_collection}`, () => testWriteFail(appUser, drivers_collection));
  it(`publicUser user can't write ${drivers_collection}`, () => testWriteFail(publicUser, drivers_collection));
  it(`driverUser user can't write ${drivers_collection}`, () => testWriteFail(driverUser, drivers_collection));

  /**
   * Write Success
   */
  it(`adminUser user can write ${drivers_collection}`, () => testWriteSuccess(adminUser, drivers_collection));
  it(`driverAdminUser user can write ${drivers_collection}`, () =>
    testWriteSuccess(driverAdminUser, drivers_collection));

  /**
   * Update Fail
   */
  it(`driverUser user can't update other ${drivers_collection}`, () =>
    testUpdateFail(driverUser, drivers_collection, otherDriverPhoneNumber, drivers_data));

  /**
   * Update Success
   */
  it(`driverUser user can update his own ${drivers_collection}`, () =>
    testUpdateSuccess(driverUser, drivers_collection, drivers_data.phone_number, drivers_data));
  it(`adminUser user can update ${drivers_collection}`, () =>
    testUpdateSuccess(adminUser, drivers_collection, drivers_data.phone_number, drivers_data));
  it(`driverAdminUser user can update ${drivers_collection}`, () =>
    testUpdateSuccess(driverAdminUser, drivers_collection, drivers_data.phone_number, drivers_data));
});

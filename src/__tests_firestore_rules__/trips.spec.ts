import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import { trip_id, trips_collection, trips_data } from "./data/trips";

describe(trips_collection, () => {
  /**
   * Read Fail
   */
  it(`tripInfoUser user can't read ${trips_collection}`, () => testReadFail(tripInfoUser, trips_collection, trip_id));
  it(`insructiveUser user can't read ${trips_collection}`, () =>
    testReadFail(insructiveUser, trips_collection, trip_id));
  it(`meterUser user can't read ${trips_collection}`, () => testReadFail(meterUser, trips_collection, trip_id));
  it(`appUser user can't read ${trips_collection}`, () => testReadFail(appUser, trips_collection, trip_id));
  it(`publicUser user can't read ${trips_collection}`, () => testReadFail(publicUser, trips_collection, trip_id));
  it(`driverUser user can't read ${trips_collection}`, () => testReadFail(driverUser, trips_collection, trip_id));
  it(`driverAdminUser user can't read ${trips_collection}`, () =>
    testReadFail(driverAdminUser, trips_collection, trip_id));

  /**
   * Read Success
   */
  it(`adminUser user can read ${trips_collection}`, () =>
    testReadSuccess(adminUser, trips_collection, trip_id, trips_data));

  /**
   * Write Fail
   */
  it(`tripInfoUser user can't write ${trips_collection}`, () => testWriteFail(tripInfoUser, trips_collection));
  it(`insructiveUser user can't write ${trips_collection}`, () => testWriteFail(insructiveUser, trips_collection));
  it(`meterUser user can't write ${trips_collection}`, () => testWriteFail(meterUser, trips_collection));
  it(`appUser user can't write ${trips_collection}`, () => testWriteFail(appUser, trips_collection));
  it(`publicUser user can't write ${trips_collection}`, () => testWriteFail(publicUser, trips_collection));
  it(`driverUser user can't write ${trips_collection}`, () => testWriteFail(driverUser, trips_collection));
  it(`driverAdminUser user can't write ${trips_collection}`, () => testWriteFail(driverAdminUser, trips_collection));

  /**
   * Write Success
   */
  it(`adminUser user can write ${trips_collection}`, () => {
    testWriteSuccess(adminUser, trips_collection);
  });
});

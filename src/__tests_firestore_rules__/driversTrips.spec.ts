import {
  adminUser,
  appUser,
  driverAdminUser,
  driverUser,
  insructiveUser,
  meterUser,
  publicUser,
  testReadFail,
  testReadSuccess,
  testUpdateFail,
  testUpdateSuccess,
  testWriteFail,
  testWriteSuccess,
  tripInfoUser,
} from "./_initTests";
import {
  driver_trip_id,
  drivers_other_trips_collection,
  drivers_trips_data,
  drivers_trips_collection,
} from "./data/drivers";

describe(drivers_trips_collection, () => {
  /**
   * Read Fail
   */
  it(`appUser user can't read ${drivers_trips_collection}`, () =>
    testReadFail(appUser, drivers_trips_collection, driver_trip_id));
  it(`meterUser user can't read ${drivers_trips_collection}`, () =>
    testReadFail(meterUser, drivers_trips_collection, driver_trip_id));
  it(`tripInfoUser user can't read ${drivers_trips_collection}`, () =>
    testReadFail(tripInfoUser, drivers_trips_collection, driver_trip_id));
  it(`insructiveUser user can't read ${drivers_trips_collection}`, () =>
    testReadFail(insructiveUser, drivers_trips_collection, driver_trip_id));
  it(`publicUser user can't read ${drivers_trips_collection}`, () =>
    testReadFail(publicUser, drivers_trips_collection, driver_trip_id));
  it(`driverUser user can't read other ${drivers_trips_collection}`, () =>
    testReadFail(driverUser, drivers_other_trips_collection, driver_trip_id));

  /**
   * Read Success
   */
  it(`driverUser user can read his own ${drivers_trips_collection}`, () =>
    testReadSuccess(driverUser, drivers_trips_collection, driver_trip_id, drivers_trips_data));
  it(`adminUser user can read ${drivers_trips_collection}`, () =>
    testReadSuccess(adminUser, drivers_trips_collection, driver_trip_id, drivers_trips_data));
  it(`driverAdminUser user can read ${drivers_trips_collection}`, () =>
    testReadSuccess(driverAdminUser, drivers_trips_collection, driver_trip_id, drivers_trips_data));

  /**
   * Write Fail
   */
  it(`meterUser user can't write ${drivers_trips_collection}`, () =>
    testWriteFail(meterUser, drivers_trips_collection));
  it(`tripInfoUser user can't write ${drivers_trips_collection}`, () =>
    testWriteFail(tripInfoUser, drivers_trips_collection));
  it(`insructiveUser user can't write ${drivers_trips_collection}`, () =>
    testWriteFail(insructiveUser, drivers_trips_collection));
  it(`appUser user can't write ${drivers_trips_collection}`, () => testWriteFail(appUser, drivers_trips_collection));
  it(`publicUser user can't write ${drivers_trips_collection}`, () =>
    testWriteFail(publicUser, drivers_trips_collection));
  it(`driverUser user can't write ${drivers_trips_collection}`, () =>
    testWriteFail(driverUser, drivers_trips_collection));

  /**
   * Write Success
   */
  it(`adminUser user can write ${drivers_trips_collection}`, () =>
    testWriteSuccess(adminUser, drivers_trips_collection));
  it(`driverAdminUser user can write ${drivers_trips_collection}`, () =>
    testWriteSuccess(driverAdminUser, drivers_trips_collection));

  /**
   * Update Fail
   */
  it(`driverUser user can't update other ${drivers_trips_collection}`, () =>
    testUpdateFail(driverUser, drivers_other_trips_collection, driver_trip_id, { shift: "night" }));
  it(`driverUser user can't update his own ${drivers_trips_collection}`, () =>
    testUpdateFail(driverUser, drivers_trips_collection, driver_trip_id, { shift: "night" }));

  /**
   * Update Success
   */
  it(`adminUser user can update ${drivers_trips_collection}`, () =>
    testUpdateSuccess(adminUser, drivers_trips_collection, driver_trip_id, { shift: "night" }));
  it(`driverAdminUser user can update ${drivers_trips_collection}`, () =>
    testUpdateSuccess(driverAdminUser, drivers_trips_collection, driver_trip_id, { shift: "night" }));
});

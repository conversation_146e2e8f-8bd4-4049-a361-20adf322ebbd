import fs from "fs";

import {
  RulesTestContext,
  RulesTestEnvironment,
  assertFails,
  assertSucceeds,
  initializeTestEnvironment,
} from "@firebase/rules-unit-testing";
import { setLogLevel } from "firebase/firestore";

import { driver_admin_id, driver_admin_phone_number, drivers_data } from "./data/drivers";
import { users_id } from "./data/users";

const projectId = "test-project";
export const user_id = "Test-User";

export let testEnv: RulesTestEnvironment;
export let publicUser: RulesTestContext;
export let appUser: RulesTestContext;
export let tripInfoUser: RulesTestContext;
export let insructiveUser: RulesTestContext;
export let meterUser: RulesTestContext;
export let adminUser: RulesTestContext;
export let driverUser: RulesTestContext;
export let driverAdminUser: RulesTestContext;

beforeEach(async () => {
  // Create users for testing
  tripInfoUser = testEnv.authenticatedContext(user_id, { meterDevice: true });
  insructiveUser = testEnv.authenticatedContext(user_id, { meterDevice: true });
  meterUser = testEnv.authenticatedContext(user_id, { meterDevice: true });
  adminUser = testEnv.authenticatedContext(user_id, { admin: true });
  driverUser = testEnv.authenticatedContext(user_id, { phone_number: drivers_data.phone_number });
  driverAdminUser = testEnv.authenticatedContext(driver_admin_id, { phone_number: driver_admin_phone_number });
  appUser = testEnv.authenticatedContext(users_id, {});
  publicUser = testEnv.unauthenticatedContext();
});

beforeAll(async () => {
  setLogLevel("error");

  testEnv = await initializeTestEnvironment({
    projectId: projectId,
    firestore: {
      rules: fs.readFileSync("../../firestore.rules", "utf8"),
      host: "localhost",
      port: 8080,
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

/**
 * Test Fail
 */
export const testReadFail = async (user: RulesTestContext, collectionName: string, id: string) => {
  const read = user.firestore().collection(collectionName).doc(id).get();

  // Expect to Fail
  await assertFails(read);
};
export const testWriteFail = async (user: RulesTestContext, collectionName: string) => {
  const create = user.firestore().collection(collectionName).add({ some: "content" });

  // Expect to Fail
  await assertFails(create);
};
export const testUpdateFail = async (user: RulesTestContext, collectionName: string, id: string, data: any) => {
  const readPromise = user.firestore().collection(collectionName).doc(id).update(data);

  // Expect to Fail
  await assertFails(readPromise);
};

/**
 * Test Success
 */
export const testReadSuccess = async (user: RulesTestContext, collectionName: string, id: string, data: any) => {
  const readPromise = user.firestore().collection(collectionName).doc(id).get();

  // Expect to Succeed
  await assertSucceeds(readPromise);

  const read = (await readPromise).data();

  expect(read).toEqual(data);
};
export const testWriteSuccess = async (user: RulesTestContext, collectionName: string) => {
  const create = user.firestore().collection(collectionName).add({ some: "content" });

  // Expect to Succeed
  await assertSucceeds(create);
};
export const testUpdateSuccess = async (user: RulesTestContext, collectionName: string, id: string, data: any) => {
  const readPromise = user.firestore().collection(collectionName).doc(id).update(data);

  // Expect to Succeed
  await assertSucceeds(readPromise);
};

import { FundReleaseBatch } from "../model/batch/fund-release";

/**
 * BatchRequest - For batch api
 */
export interface BatchRequest {
  type: BatchType;
  input: FundPrereleaseRequestInput | FundReleaseRequestInput;
}

export interface BatchResponse {
  type: BatchType;
  batch?: FundReleaseBatch;
}

export enum BatchType {
  FUND_RELEASE = "FUND_RELEASE",
  UPDATE_DEVICE_CONFIG = "UPDATE_DEVICE_CONFIG",
}
/**
 * triggerPrereleaseRequest
 */
export interface TriggerPrereleaseRequest {
  dryrun: string;
}

/**
 * FundReleaseRequest - a type of batch request input
 */
export interface FundPrereleaseRequestInput {
  action: FundReleaseRequestType.REQUEST_PRERELEASE;
  payload: FundReleaseItem[];
}

/**
 * Only need to supply phone number to be released
 * backend will look up the ids from prerelease
 */
export interface FundReleaseRequestInput {
  action: FundReleaseRequestType.REQUEST_RELEASE;
  payload: {
    driverIds: string[];
    prereleaseBatchRef: string;
  };
}

export interface FundReleaseItem {
  driverId: string;
  trips: string[];
  status?: FundReleaseItemStatus;
}

export enum FundReleaseItemStatus {
  SUCCESS = "SUCCESS",
  FAILED = "FAILED",
}

export enum FundReleaseRequestType {
  REQUEST_PRERELEASE = "REQUEST_PRERELEASE", // For fund release
  REQUEST_RELEASE = "REQUEST_RELEASE", // For fund release
}

export interface FundReleaseResponse {
  action: FundReleaseRequestType;
  payload: FundReleaseItem;
}

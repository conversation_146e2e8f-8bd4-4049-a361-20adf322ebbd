import { JSONSchemaType } from "ajv";

import { AdjustmentReason } from "../model/trip";

interface CreateAdjustmentRequest {
  amount: number;
  reason: AdjustmentReason;
  note?: string;
}

const CreateAdjustmentRequestSchema: JSONSchemaType<CreateAdjustmentRequest> = {
  type: "object",
  properties: {
    amount: {
      type: "number",
    },
    reason: {
      type: "string",
      enum: Object.keys(AdjustmentReason) as readonly AdjustmentReason[],
    },
    note: {
      type: "string",
      nullable: true,
    },
  },
  required: ["amount", "reason"],
};

interface AddTagRequest {
  name: string;
  value: string;
}

export { CreateAdjustmentRequest, CreateAdjustmentRequestSchema, AddTagRequest };

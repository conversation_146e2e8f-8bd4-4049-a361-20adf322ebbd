import { diff } from "deep-diff";
import { Request, Response } from "express";
import * as functions from "firebase-functions";
import { Change, EventContext } from "firebase-functions";
import { DocumentSnapshot, QueryDocumentSnapshot } from "firebase-functions/v2/firestore";
import moment from "moment";

import {
  activateDriver,
  copySessionFromDriver,
  createdAndSendActivation,
  getActivationStatus,
  getSessionById,
  pair as pairService,
  unpair as unpairService,
  updateExpectedEndTimeService,
  validateQR,
} from "@legacy/service/driver";
import DriverService from "@legacy/service/driverService";
import NotificationService from "@legacy/service/notification";

export const pairOld = (req: Request, res: Response) => {
  functions.logger.info("Pair Handler 1.0 called");
  const driverId = req.params.driver_id;
  // const driverId = req.user?.phone_number;
  if (driverId == undefined) {
    res.status(403).send("Driver has no id");
    return;
  }
  const qr = req.body.qr;
  const licensePlate = qr.split("-")[0].substring(2);
  if (validateQR(qr.split("-")[1])) {
    pairService(driverId, licensePlate, null)
      .then((session) => {
        functions.logger.info("Handler: Paired session:" + session);
        getSessionById(session).then((result) => {
          res.status(200).json(result);
        });
      })
      .catch((error) => {
        if (error instanceof ReferenceError) {
          res.status(404).send("Driver " + driverId + " not found");
        } else {
          res.status(500).send("Error" + error);
        }
      });
  } else {
    functions.logger.debug("driver %s pair an expired QR", driverId);
    res.status(401).send({ error: "expired session" });
  }
};

export const unpair = (req: Request, res: Response) => {
  functions.logger.info("Unpair Handler 1.0 called");
  const sessionId = req.params.session_id;
  unpairService(sessionId).then((result) => {
    if (result == "success") {
      res.status(200).send();
    } else if (result == "duplicated") {
      res.status(400).send("session ended already");
    } else {
      res.status(500).send();
    }
  });
};

export const updateExpectedEndTime = (req: Request, res: Response) => {
  functions.logger.info("update expected end time called");
  const driverId = req.params.driver_id;
  functions.logger.info("update expected end time called" + req.body.expected_end_time);
  const endTime = moment(req.body.expected_end_time).toDate();
  updateExpectedEndTimeService(driverId, endTime).then((success) => {
    if (success) {
      res.status(200).json(success);
    } else {
      res.status(500).send();
    }
  });
};

export const getActivation = (req: Request, res: Response) => {
  functions.logger.info("getActivation called");
  const activationId = req.params.activation_id;
  getActivationStatus(activationId)
    .then((result) => {
      if (result) {
        res.status(200).json({ data: true });
      } else {
        res.status(200).json({ data: false });
      }
    })
    .catch((err) => {
      functions.logger.error("Not able to find activation:" + err);
      res.status(404).json({ msg: "Not able to find activation" });
    });
};

export const activationHandler = (req: Request, res: Response) => {
  functions.logger.info("activate called");
  const activationId = req.params.activation_id;
  activateDriver(activationId)
    .then((result) => {
      if (result) {
        res.status(200).json({ data: true });
      } else {
        res.status(200).json({ data: false });
      }
    })
    .catch((err) => {
      functions.logger.error("Not able to find activation:" + err);
      res.status(404).json({ msg: "Not able to find activation" });
    });
};

export const sendNotification = (req: Request, res: Response) => {
  functions.logger.info("send Notification Handler called");
  const driverId = req.params.driver_id;
  functions.logger.debug("body" + JSON.stringify(req.body));
  const title = req.body.notification.title;
  const message = req.body.notification.message;
  const type = req.body.notification.type;
  const locale = req.body.notification.locale;
  NotificationService.sendNotification(driverId, title, message, type, locale).then((success) => {
    if (success) {
      res.status(200).json(success);
    } else {
      res.status(500).send();
    }
  });
};

/**
 * driverChangeHandler
 * @param {Change<DocumentSnapshot>}change change from firestore
 * @param {EventContext} context of the change
 * @return {promise} promise of any
 */
export async function driverChangeHandler(
  change: Change<DocumentSnapshot>,
  context: EventContext<{
    driverId: string;
  }>,
): Promise<any> {
  const driverId = context.params.driverId;
  const difference = diff(change.before.data(), change.after.data());
  functions.logger.debug(
    "/driverId/%s changed; eventId:%s @ %s; change: %s",
    driverId,
    context.eventId,
    context.timestamp,
    JSON.stringify(difference),
  );
  // DASH-1504 - change since self-onboarding
  // created by admin then send activation to driver to confirm
  if (change.before.get("created_by") == undefined && change.after.get("created_by") != undefined) {
    functions.logger.debug("Driver: %s created by %s", driverId, change.after.get("created_by"));
    await createdAndSendActivation(driverId);
  }
  // session change, after v1.1 no need to copy, it is done in api
  // in an tx, 1.0 is updated by client
  // it is a change after create
  if (
    change.before.get("session.expected_end_time") == undefined &&
    change.before.get("session.expected_end_time") != change.after.get("session.expected_end_time") &&
    change.after.get("session") != undefined
  ) {
    const driverId = change.after.get("id");
    const sessionId = change.after.get("session.id");
    const sessionExpectedEndTime = change.after.get("session.expected_end_time");
    functions.logger.info("Driver session has been changed: %s for %s", sessionId, driverId);
    await copySessionFromDriver(sessionId, sessionExpectedEndTime);
  }
  return "Driver Change End";
}

/**
 * driverTripDeleteHandler
 * @param {QueryDocumentSnapshot} change change from firestore
 * @param {EventContext} context of the change
 * @return {promise} promise of any
 */
export async function driverTripDeleteHandler(
  change: QueryDocumentSnapshot,
  context: EventContext<{
    driverId: string;
    sessionId: string;
    tripId: string;
  }>,
): Promise<string> {
  functions.logger.info("driverTripDeleteHandler");
  const driverId = context.params.driverId;
  const sessionId = context.params.sessionId;
  const session = await DriverService.calculateSessionTotal(driverId, sessionId);
  // If there is no more trip in the session, delete the session
  if (session && session.count == 0) {
    functions.logger.info("Driver %s has no more trips", driverId);
    await DriverService.deleteDriverSessionById(driverId, sessionId);
  }
  return "Driver Delete End: " + JSON.stringify(session);
}

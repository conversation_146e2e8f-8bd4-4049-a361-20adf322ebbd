import diff from "deep-diff";
import * as functions from "firebase-functions";
import { Change, EventContext } from "firebase-functions";
import { DocumentSnapshot } from "firebase-functions/v2/firestore";

import { TripDocument } from "@nest/modules/appDatabase/documents/trip.document";

import repo from "../repository/repo";
import {
  convertLocation,
  moveTripFromMeterToTrips,
  // tripCalculation,
} from "../service/driver";
/**
 * meterTripChangeHandler
 * @param {Change<DocumentSnapshot>}change change from firestore
 * @param {EventContext} context of the change
 */
export async function meterTripChangeHandler(
  change: Change<DocumentSnapshot>,
  context: EventContext<{
    meterId: string;
    tripId: string;
  }>,
) {
  const meterId = context.params.meterId;
  const tripId = context.params.tripId;
  // Debug before/after
  // functions.logger.debug("before: %s", JSON.stringify(change.before));
  // functions.logger.debug("after: %s", JSON.stringify(change.after));
  const difference = diff(change.before.data(), change.after.data());
  functions.logger.debug(
    "meters/%s/trips/%s changed; eventId:%s @ %s; change: %s",
    meterId,
    tripId,
    context.eventId,
    context.timestamp,
    JSON.stringify(difference),
  );
  // this is start trip
  if (change.before.get("trip_start") == undefined) {
    functions.logger.info("meter trip start event for tripId " + tripId);
  }
  // this is after end trip
  // and only allow the following changes to be copied over
  // - first time i.e. before is undefiend
  // - end location address
  // - dummy field to trigger copy
  if (
    change.after.get("trip_end") != undefined &&
    (change.before.get("trip_end") == undefined ||
      change.after.get("location_end_address") ||
      change.after.get("dummy") != undefined)
  ) {
    functions.logger.info("meter trip end event for tripId " + tripId);
    const moveTripResult = await moveTripFromMeterToTrips(meterId, tripId);
    functions.logger.debug("moveTripFromMeterToTrips result: %s", moveTripResult);
  }
  if (change.before.get("location_start") == undefined && change.after.get("location_start") != undefined) {
    functions.logger.info("location_start changed:" + tripId);
    try {
      if (change.after.get("session.id") != null) {
        await convertLocation(meterId, tripId, "start");
      }
    } catch (err) {
      functions.logger.error("error converting start location: %s", err);
    }
  }
  if (change.before.get("location_end") == undefined && change.after.get("location_end") != undefined) {
    functions.logger.info("location_end changed:" + tripId);
    try {
      if (change.after.get("session.id") != null) {
        await convertLocation(meterId, tripId, "end");
      }
    } catch (err) {
      functions.logger.error("error converting end location: %s", err);
    }
    // await moveTripFromMeterToTrips(meterId, tripId);
  }
  return "Meter Trip Change Handler completed";
  // const calculateResult = await tripCalculation(meterId, tripId);
  // functions.logger.debug("tripCalculation result: %s", calculateResult);

  // copy over to trips
  /**
  const moveTripResult = await moveTripFromMeterToTrips(meterId, tripId);
  functions.logger.debug("moveTripFromMeterToTrips result: %s", moveTripResult);
   */
}
/**
 * meterChangeHandler - log the change for debug purpose
 * @param {Change<DocumentSnapshot>}change change from firestore
 * @param {EventContext} context of the change
 */
export async function meterChangeHandler(
  change: Change<DocumentSnapshot>,
  context: EventContext<{
    meterId: string;
  }>,
) {
  const meterId = context.params.meterId;
  // Debug before/after
  const difference = diff(change.before.data(), change.after.data());
  functions.logger.debug(
    "meters/%s changed; eventId:%s @ %s; change: %s",
    meterId,
    context.eventId,
    context.timestamp,
    JSON.stringify(difference),
  );
  return "Meter Change Handler completed";
}

export async function getMeterTripData(meterId: string, tripId: string) {
  const trip: TripDocument = (await repo.meterTrip(meterId).doc(tripId).get()).data() as TripDocument;

  if (!trip) {
    return undefined;
  }

  delete trip.session;
  delete trip.paymentInformation;
  delete trip.paymentStatus;
  delete trip.paymentType;

  return trip;
}

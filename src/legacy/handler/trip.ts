import diff from "deep-diff";
import * as functions from "firebase-functions";
import { Change, EventContext } from "firebase-functions";
import { DocumentSnapshot } from "firebase-functions/v2/firestore";

import { PaymentStatus, PaymentType } from "../model/trip";
import DriverService from "../service/driverService";
import TripService from "../service/trip";
// import {Client} from "../wati/client";

/**
 * tripChangeHandler
 * @param {Change<DocumentSnapshot>}change change from firestore
 * @param {EventContext} context of the change
 */
export async function tripChangeHandler(
  change: Change<DocumentSnapshot>,
  context: EventContext<{
    tripId: string;
  }>,
) {
  // Print diff
  const tripId = context.params.tripId;
  const difference = diff(change.before.data(), change.after.data());
  functions.logger.debug(
    "/trips/%s changed; eventId:%s @ %s; change: %s",
    tripId,
    context.eventId,
    context.timestamp,
    JSON.stringify(difference),
  );

  // this trip has ended
  if (change.after.get("trip_end") != undefined) {
    // this is the very first time and will called only once
    if (change.before.get("trip_end") == undefined) {
      await TripService.setVisibility(tripId);
    }
    // if it is DASH payment
    if (change.after.get("payment_type") == PaymentType.DASH) {
      // if payment status is undefiend then try capture
      if (change.after.get("payment_status") == undefined) {
        try {
          await TripService.captureTrip(context.params.tripId);
        } catch (err) {
          functions.logger.error("Issue with capture trip: %s", err);
        }
        // if payment status is captrued
      } else if (change.after.get("payment_status") == PaymentStatus.CAPTURED) {
        // check if we need to send receipt and if receipt is being sent or not
        if (change.after.get("passenger_information") != undefined && change.after.get("receipt_status") == undefined) {
          try {
            const updateResult = await TripService.sendReceipt(tripId);
            functions.logger.debug("Update %s", updateResult);
          } catch (err) {
            functions.logger.error("Error sending reciept: %s", err);
            // throw new Error(`Error sending reciept: ${err}`);
          }
        }
      } // [End if] payment status (if it is processing/failed, ignore)
      // Add payout info if it is a dash payment and the field not exist
      if (change.after.get("payout_status") == undefined) {
        try {
          await TripService.addMissingFields(tripId);
        } catch (err) {
          functions.logger.error("Error adding fields");
          throw err;
        }
      }
      // [End if] DASH payment, else it is CASH
    }

    // Copy to driver if any changes happened after trip end
    if (change.after.get("show_to_driver")) {
      functions.logger.info("show_to_driver %s:", change.after.get("show_to_driver"));
      await TripService.copyTripToDriver(tripId);
      const driverId = change.after.get("driver.id");
      const sessionId = change.after.get("session.id");
      await DriverService.calculateSessionTotal(driverId, sessionId);
    }
  }
  // adjustment changed on original trip.
  if (change.before.get("adjustment") != change.after.get("adjustment")) {
    functions.logger.info("tripChangeHandler: Adjustment amount changed %s", tripId);
    await TripService.copyTripToDriver(tripId);
    const driverId = change.after.get("driver.id");
    const sessionId = change.after.get("session.id");
    await DriverService.calculateSessionTotal(driverId, sessionId);
    // functions.logger.info("result: %s", result);
  }
  // payout information change
  if (change.before.get("payout_status") != change.after.get("payout_status")) {
    functions.logger.info("tripChangeHandler: Payout Information Chanaged %s: ", tripId);
    await TripService.copyTripToDriver(tripId);
    const driverId = change.after.get("driver.id");
    const sessionId = change.after.get("session.id");
    await DriverService.calculateHasPayout(driverId, sessionId);
  }
  return "tripChangeHandler Completed";
}

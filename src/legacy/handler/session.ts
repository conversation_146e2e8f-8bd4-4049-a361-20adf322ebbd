import { diff } from "deep-diff";
import { Request, Response } from "express";
import { Timestamp } from "firebase-admin/firestore";
import * as functions from "firebase-functions";
import { Change, EventContext } from "firebase-functions";
import { DocumentSnapshot } from "firebase-functions/v2/firestore";
import moment from "moment";

import { expireDriverSessionIfActive, expireMeterSessionIfActive } from "../service/driver";
import SessionService from "../service/session";

/**
 * meterTripChangeHandler
 * @param {Change<DocumentSnapshot>}change change from firestore
 * @param {EventContext} context of the change
 */
export async function sessionChangeHandler(
  change: Change<DocumentSnapshot>,
  context: EventContext<{
    sessionId: string;
  }>,
) {
  const sessionId = context.params.sessionId;
  const difference = diff(change.before.data(), change.after.data());
  functions.logger.debug(
    "/sessions/%s changed; eventId:%s @ %s; change: %s",
    sessionId,
    context.eventId,
    context.timestamp,
    JSON.stringify(difference),
  );
  // check if it is an unpair
  // and make sure both driver and meter
  if (change.before.get("end_time") == null && change.after.get("end_time") != null) {
    // session end event
    const driverRef = change.after.get("driver_ref");
    const meterRef = change.after.get("meter_ref");
    const sessionEndTime: Timestamp = change.after.get("end_time");
    functions.logger.info("Session has been ended %s", sessionId, sessionEndTime.toDate);
    try {
      // expire driver session if active
      functions.logger.info("expire driver: %s ; session: %s", driverRef.path, sessionId);
      await expireDriverSessionIfActive(driverRef, sessionId, sessionEndTime);
      // expire driver session if active
      functions.logger.info("expire meter: %s ; session: %s", meterRef.path, sessionId);
      await expireMeterSessionIfActive(meterRef, sessionId);
    } catch (err) {
      functions.logger.error(err);
    }
  }
  return 0;
}

export const expireAllSession = (req: Request, res: Response) => {
  functions.logger.info("expireAllSession called");
  const beforeDateTime = moment(req.body.datetime).toDate();
  SessionService.unpairExpiredSession(beforeDateTime).then((success) => {
    if (success) {
      res.status(200).json("{}");
    } else {
      res.status(500).json("{}");
    }
  });
};

export const unpairExpiredSessionHandler = async (context: EventContext<Record<string, string>>) => {
  functions.logger.info("expireAllSession called");
  const beforeDateTime = moment(context.timestamp).toDate();
  try {
    const result = await SessionService.unpairExpiredSession(beforeDateTime);
    functions.logger.info("expireAllSession completed: %s ", result);
  } catch (err) {
    functions.logger.error("unpairExpiredSessionHandler: %s", err);
    throw err;
  }
};

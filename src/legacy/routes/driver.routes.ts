import express from "express";

import driverController from "../controller/driverController";
import { pairOld, sendNotification, updateExpectedEndTime } from "../handler/driver";
import { authCheckMiddleware } from "../middleware/auth";

const app = express();

// Driver Pair - old
app.post("/:driver_id/pair", async (req: express.Request, res: express.Response) => {
  pairOld(req, res);
});

// Driver Pair - old
app.post("/v1.1/:driver_id/pair", [authCheckMiddleware], async (req: express.Request, res: express.Response) => {
  driverController.pair(req, res);
});

app.post("/:driver_id/notification", async (req, res) => {
  sendNotification(req, res);
});

app.patch("/:driver_id/session", async (req, res) => {
  updateExpectedEndTime(req, res);
});

app.get("/:driver_id/patch-expires-at", async (req, res) => {
  driverController.patchExpireAt(req, res);
});

export default app;

import express from "express";

import infoJson from "@static/info.json";

import activationRoutes from "./activation.routes";
import batchRoutes from "./batch.routes";
import driverRoutes from "./driver.routes";
import dummyRoutes from "./dummy.routes";
import messagingRoutes from "./messaging.routes";
import meterRoutes from "./meter.routes";
import sessionRoutes from "./session.routes";
import tripRoutes from "./trip.routes";
import userRoutes from "./user.routes";
import bankfileController from "../controller/bankfileController";
import mapController from "../controller/mapController";
import securityController from "../controller/securityController";
import { expireAllSession } from "../handler/session";

const app = express();

app.use("/activation", activationRoutes);
app.use("/batch", batchRoutes);
app.use("/driver", driverRoutes);
app.use("/dummy", dummyRoutes);
app.use("/messaging", messagingRoutes);
app.use("/session", sessionRoutes);
app.use("/trips", tripRoutes);
app.use("/user", userRoutes);
app.use("/meter", meterRoutes);

app.get("/info", (req: express.Request, res: express.Response) => {
  // const respondJson = {api: 1.0};
  res.status(200).json(infoJson);
});

app.post("/sessions/expire-all", async (req, res) => {
  expireAllSession(req, res);
});

// TODO: To implement
// app.post("/configuration/:app", async (req, res)=>{});

// TODO: To implement
// app.get("/meter/:meter_id/update-config", async (req, res)=>{});

app.post("/bankfile/response/process", async (req, res) => {
  bankfileController.processFile(req, res);
});

app.post("/map", async (req, res) => {
  mapController.getAddress(req, res);
});

// Security
app.post("/auth", async (req, res) => {
  securityController.getAccessToken(req, res);
});

app.post("/verify", async (req, res) => {
  securityController.verifyIdToken(req, res);
});

app.get("/test-auth", async (req, res) => {
  await securityController.testAuth(req, res);
});

export default app;

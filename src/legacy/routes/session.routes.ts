import express from "express";

import sessionController from "../controller/sessionController";
import { unpair } from "../handler/driver";
import { authCheckMiddleware } from "../middleware/auth";

const app = express();

// Driver Unpair
app.post("/:session_id/unpair", async (req: express.Request, res: express.Response) => {
  unpair(req, res);
});

// Driver Unpair
app.post("/v1.1/:session_id/unpair", [authCheckMiddleware], async (req: express.Request, res: express.Response) => {
  sessionController.unpair(req, res);
});

app.patch("/v1.1/:session_id", [authCheckMiddleware], async (req: express.Request, res: express.Response) => {
  sessionController.setExpectedEndTime(req, res);
});

export default app;

import express from "express";

import tripController from "../controller/tripController";

const app = express();

app.post("/:trip_id/resend-receipt", async (req, res) => {
  tripController.tripReceiptResend(req, res);
});

app.post("/:trip_id/adjustment", async (req, res) => {
  tripController.createAdjustment(req, res);
});

app.post("/:trip_id/capture", async (req, res) => {
  tripController.captureTransaction(req, res);
});

app.get("/uncaptured-trip", async (req, res) => {
  tripController.getUncapturedTrips(req, res);
});

app.get("/:trip_id", async (req, res) => {
  tripController.getTrimDownTripById(req, res);
});

app.post("/:trip_id/tag", async (req, res) => {
  tripController.tagTrip(req, res);
});

app.patch("/:trip_id/transId", async (req, res) => {
  await tripController.updateTransactionId(req, res);
});

export default app;

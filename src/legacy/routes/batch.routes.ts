import express from "express";

import batchController from "../controller/batchContoller";

const app = express();

app.post("/", async (req, res) => {
  batchController.batchRequestHandler(req, res);
});

// Process pre-release batch
app.get("/:batch_id/process", async (req, res) => {
  batchController.processPrereleaseBatch(req, res);
});

app.get("/trigger-prerelease", async (req, res) => {
  batchController.triggerPrerelease(req, res);
});

export default app;

import express from "express";

import { getMeterTripData } from "../handler/meter";

const app = express();

app.get("/:meterId/trip/:tripId", async (req: express.Request, res: express.Response) => {
  const { meterId, tripId } = req.params;
  const trip = await getMeterTripData(meterId, tripId);

  if (!trip) {
    return res.status(404).json({ error: "Resource not found" });
  }

  return res.status(200).json(trip);
});

export default app;

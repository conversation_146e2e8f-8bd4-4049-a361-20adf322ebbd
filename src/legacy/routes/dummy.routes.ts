import express from "express";
import moment from "moment";

import dummyController from "../controller/dummyController";
import { getShift, locationConversionService, validateQR } from "../service/driver";

const app = express();

// dummy
app.post("/", async (req, res) => {
  const lat = req.body.lat;
  const lng = req.body.lng;
  getShift(new Date());
  getShift(moment("2022-11-13T07:22:44.753Z").toDate());
  locationConversionService(lat, lng)
    .then((result) => {
      res.status(200).json(result);
    })
    .catch((err) => {
      res.status(500).json({ err: err });
    });
});

app.get("/i18n", async (req, res) => {
  dummyController.process(req, res);
});

// dummy2
app.post("/activation", async (req, res) => {
  validateQR("d55kpm9mLjVYHwGQpQ==");
  res.status(200).json({});

  /**
  const client = new Client();
  client.sendActivation("+85262049393", "bit.ly").then((result)=>{
    res.status(200).json(result.data);
  }).catch((err)=>{
    functions.logger.info("err:"+err);
    res.status(500).json({err: err});
  });
   */
});

export default app;

import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import moment from "moment";

import repo from "@legacy/repository/repo";
import { PaymentInformationStatus } from "@nest/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "@nest/modules/payment/dto/paymentInformationType.dto";

import ReceiptService, { SendReceiptError } from "./receipt";
import TransactionService, { CaptureError } from "./transaction";
import { CreateAdjustmentRequest } from "../api/trip";
import { ConfigurationService } from "../configuration/configuration.service";
import { ServerConfigrationKey } from "../model/serverConfiguration";
import {
  PaymentInformationAudit,
  PaymentType,
  Trip,
  PayoutStatus,
  TrimDownTrip,
  PaymentCardInformation,
  PaymentStatus,
  ReceiptStatus,
  TagMetadata,
} from "../model/trip";
import { db } from "../service/driver";
import { parseDuration } from "../utils";

/**
 * TripService
 */
class TripService {
  /**
   * updateTransactionId
   * @param {string} tripId
   */
  async updateTransactionId(tripId: string) {
    return await db.runTransaction(async (tx) => {
      const trip = (await tx.get(repo.trip.doc(tripId))).data();
      if (trip) {
        const paymentInformation = trip.paymentInformation;
        if (paymentInformation) {
          const lastCapture = findCapture(paymentInformation);
          const captureTranId = JSON.parse(lastCapture?.body || "").data.tranId;
          const authTranId = JSON.parse(lastCapture?.body || "").data.tranParentId;
          functions.logger.debug("captureTranId: %s", captureTranId);
          functions.logger.debug("authTranId: %s", authTranId);
          tx.set(repo.trip.doc(tripId), { authTranId: authTranId, captureTranId: captureTranId }, { merge: true });
        }
      }
      return true;
    });
  }

  private readonly configurationService = new ConfigurationService();

  /**
   * createAdjustmentTrip
   * @param {string} tripId
   * @param {CreateAdjustmentRequest} req
   */
  async createAdjustmentTrip(tripId: string, req: CreateAdjustmentRequest) {
    functions.logger.info("createAdjustmentTrip called tripId: %s", tripId);
    // get parent trip
    db.runTransaction(async (tx) => {
      const trip = (await tx.get(repo.trip.doc(tripId))).data();
      if (trip) {
        // trip or meter trip?
        const adjustmentTrip = repo.trip.doc();
        const payload = {
          id: adjustmentTrip.id,
          creationTime: admin.firestore.FieldValue.serverTimestamp(),
          paymentType: PaymentType.DASH_ADJUSTMENT,
          meterId: trip?.meterId || "",
          total: req.amount,
          adjustmentInformation: {
            reason: req.reason,
            note: req.note || "",
          },
          parent: repo.trip.doc(tripId),
          driver: trip.driver,
          licensePlate: trip.licensePlate,
          session: trip.session,
        } as Partial<Trip>;
        functions.logger.info("writting payload: %s", JSON.stringify(payload));
        tx.create(adjustmentTrip, payload);
        const currentAdjustment = trip.adjustment || 0;
        const newAdjustment = currentAdjustment + req.amount;
        // update adjustment
        tx.update(repo.trip.doc(tripId), { adjustment: newAdjustment });
        // add subcollection
        tx.create(repo.adjustmentTrip(tripId).doc(adjustmentTrip.id), payload);
      } else {
        throw new Error("Trip not found");
      }
    });
  }

  /**
   * copy trip to driver's session, during the copy
   * - add TTL
   * @param {string} tripId
   */
  async copyTripToDriver(tripId: string): Promise<string> {
    functions.logger.info("copyTripToDriver called, tripId:%s", tripId);
    // copy over from trips
    const result = await db.runTransaction(async (tx) => {
      // get trip by Id
      const tripRef = repo.trip.doc(tripId);
      const trip = (await tx.get(tripRef)).data();
      functions.logger.debug("debug-adjustment: %s", trip?.adjustment || 0);
      // check if there is a session associated to this trip
      functions.logger.debug("session id %s of trip %s ", trip?.session?.id, trip?.id);
      if (trip == undefined) {
        throw new ReferenceError(`Trip not found ${tripId}`);
      }

      // There is a session there is a driver
      // Show to driver
      if (trip && trip.showToDriver && trip.session?.id != undefined && trip.driver?.id != undefined) {
        functions.logger.info("copy required");
        // write to driver
        const driverId = trip.driver.id;
        const sessionId = trip.session.id;
        const driverTrip = { ...trip };
        delete driverTrip.driver;
        // copy transaction
        functions.logger.info("copy trip to driver trip - driver id: %s ; trip id: ", driverId, tripId);
        // Get ttl setting
        const configKey =
          trip.paymentType == PaymentType.DASH
            ? ServerConfigrationKey.DASH_TRIP_TTL
            : ServerConfigrationKey.CASH_TRIP_TTL;
        const ttl = await this.configurationService.getServerConfigurationByKey(configKey);
        const duration = parseDuration(ttl);

        const expiresAt = moment(trip.tripStart).add(duration).toDate();
        functions.logger.debug("start+dashTripTTL=expireAt %s, %s, %s", trip.tripStart, ttl, expiresAt);

        // add the ttl to payload
        driverTrip.expiresAt = expiresAt;

        // save to trips
        const driverTripRef = repo.driverTrip(driverId).doc(tripId);
        tx.set(driverTripRef, driverTrip, { merge: true });

        // save to session
        functions.logger.debug("debug-driver-trip: %s", JSON.stringify(driverTrip));
        functions.logger.debug("debug-driver-trip-adjustment: %s", driverTrip.adjustment);
        functions.logger.info(
          "copy trip to driver session - driver id: " + "%s ; session id: %s ; trip id: ",
          driverId,
          sessionId,
          tripId,
        );
        const sessionTripRef = repo.driverSessionTrip(driverId, sessionId).doc(tripId);
        tx.set(sessionTripRef, driverTrip, { merge: true });
      }
      return "completed";
    });
    functions.logger.info("result: %s", result);
    return result;
  }

  /**
   * addMissingFields
   * @param {string} tripId
   */
  async addMissingFields(tripId: string) {
    functions.logger.info("addMissingFields called");
    return await db.runTransaction(async (tx) => {
      const tripRef = repo.trip.doc(tripId);
      const trip = (await tx.get(tripRef)).data();
      const paymentType = trip?.paymentType;
      // add payout status to the trip if paid by DASH
      if (paymentType == "DASH") {
        functions.logger.debug("addMissingFields for trip: %s", tripId);
        tx.update(tripRef, {
          payout_status: PayoutStatus.NONE,
          dispute: null,
        });
      }
    });
  }

  /**
   * getTripById
   * @param {string} tripId
   */
  async getTrimDownTripById(tripId: string): Promise<TrimDownTrip | undefined> {
    const tripDoc = (await repo.trip.doc(tripId).get()).data();
    if (tripDoc == undefined) {
      return Promise.resolve(undefined);
    } else {
      return this.trimDownTrip(tripDoc);
    }
  }

  /**
   * Get trim down version of trip
   * @param {Trip} trip
   * @return {TrimDownTrip}
   */
  trimDownTrip(trip: Trip): TrimDownTrip {
    let paymentCardInformation: PaymentCardInformation | undefined = undefined;
    for (const paymentAudit of trip.paymentInformation || []) {
      if (
        paymentAudit.type == PaymentInformationType.CAPTURE &&
        paymentAudit.status == PaymentInformationStatus.SUCCESS
      ) {
        const body = paymentAudit.body;
        const paymentResultJson = JSON.parse(body);
        const cardType = paymentResultJson.data.payment.paymentMethod;
        const maskedPan =
          paymentResultJson.data.payment.payerFirst + "******" + paymentResultJson.data.payment.payerLast;
        paymentCardInformation = {
          cardType: cardType,
          maskedPan: maskedPan,
        };
        break;
      }
    }

    return {
      id: trip.id,
      language: trip.language,
      licensePlate: trip.licensePlate,
      // start
      tripStart: trip.tripStart,
      locationStart: trip.locationStart,
      locationStartAddress: trip.locationStartAddress,
      // end
      tripEnd: trip.tripEnd,
      locationEnd: trip.locationEnd,
      locationEndAddress: trip.locationEndAddress,
      // $ & distance
      fare: trip.fare,
      extra: trip.extra,
      dashFee: trip.dashFee,
      total: trip.total,
      dashTips: trip.dashTips,
      tripTotal: trip.tripTotal,
      distance: trip.distance,
      adjustment: trip.adjustment,
      // payment
      paymentType: trip.paymentType,
      paymentCardInformation: paymentCardInformation,
    };
  }

  /**
   * Capture Trip
   * @param {string} tripId
   * @return {Trip} capture result
   */
  async captureTrip(tripId: string): Promise<string> {
    functions.logger.info("TripController.captureTrip");
    // Get the trip to process
    const tripRef = repo.trip.doc(tripId);
    try {
      // lock trip by adding processing flag
      const {
        tranId,
        amount,
      }: {
        tranId: string;
        amount: number;
      } = await db.runTransaction(async (tx) => {
        const trip = (await tx.get(tripRef)).data();

        // check if trip exist
        if (!trip) {
          functions.logger.error("Trip Id does not exist: %s", tripId);
          throw new Error("Trip Id does not exist");
        }
        // check if it is captured already or in progress
        if (trip.paymentStatus) {
          functions.logger.error("Trip Id: %s ; status: %s", tripId, trip.paymentStatus);
          throw new Error(`Trip Id: ${tripId} ; status: ${trip.paymentStatus}`);
        }
        // check if it is a dash payment or if fare is 0
        if (trip.paymentType != PaymentType.DASH || trip.fare == 0) {
          functions.logger.error("Not a DASH payment or fare is 0");
          // return if not DASH or fare is 0
          throw new Error("Not a DASH payment or fare is 0");
        }
        // Find the success auth and try to capture
        const paymentAudit = trip.paymentInformation || [];
        // get last succesful auth
        const lastSuccessAuthAudit = findLastSuccessAuthWithoutCapture(paymentAudit);
        if (!lastSuccessAuthAudit) {
          functions.logger.error("No valid auth found to be captured for trip Id: %s", tripId);
          // return if not DASH or fare is 0
          throw new Error("Not a DASH payment or fare is 0");
        }
        // Get auth body to capture
        const txBody = lastSuccessAuthAudit.body;

        // Capture params
        const auth = JSON.parse(txBody);
        const tranId = auth.tranId;
        const amount = trip.total;

        functions.logger.info("Attempt to lock trip for processing: %s", tripId);
        const lockDoc: Partial<Trip> = {
          paymentStatus: PaymentStatus.PROCESSING, // mark as captured
        };
        await tx.set(tripRef, lockDoc, { merge: true });
        return { tranId, amount };
      });

      functions.logger.info("Trying to capture TripId: %s, TranId %s, amount: %s", tripId, tranId, amount);
      const captureResult = await TransactionService.doCapture(tranId, amount);
      functions.logger.debug("doCapture result %s:", JSON.stringify(captureResult));
      // Check if the result is success
      const resultCode = captureResult.result;
      let paymentInformationAudit;
      let updateDoc;
      // TODO: Current repo cannot handle arrayUnion
      // therefore cannot use proper type here, need to use snake case
      if (resultCode == 0) {
        functions.logger.info("tripId %s :Capture successfully", tripId);
        paymentInformationAudit = {
          type: PaymentInformationType.CAPTURE,
          created_on: new Date(),
          body: JSON.stringify(captureResult),
          status: PaymentInformationStatus.SUCCESS,
        };
        updateDoc = {
          payment_information: admin.firestore.FieldValue.arrayUnion(paymentInformationAudit),
          payment_status: PaymentStatus.CAPTURED, // mark as captured
        };
        await repo.trip.doc(tripId).update(updateDoc);
      } else {
        functions.logger.error("tripId %s :Capture failed", tripId);
        paymentInformationAudit = {
          type: PaymentInformationType.CAPTURE,
          created_on: new Date(),
          body: JSON.stringify(captureResult),
          status: PaymentInformationStatus.FAILURE,
        };
        updateDoc = {
          payment_information: admin.firestore.FieldValue.arrayUnion(paymentInformationAudit),
        };
        await repo.trip.doc(tripId).update(updateDoc);
        throw new CaptureError("Non zero result code");
      }
      return captureResult;
    } catch (err) {
      if (err instanceof CaptureError) {
        // set status to failed
        const releaseLock: Partial<Trip> = {
          paymentStatus: PaymentStatus.FAILED,
        };
        tripRef.set(releaseLock, { merge: true });
      }
      functions.logger.error("DASH-ERR-100: Error calling capture service %s:", err);
      throw err;
    }
  }

  /**
   * Get all tx that is not captured within
   * @param {date} from
   * @param {date} to
   */
  async getUncapturedTrips(from: Date, to: Date): Promise<object[]> {
    const query = repo.trip
      .where("payment_type", "==", "DASH")
      .where("trip_start", ">=", from)
      .where("trip_start", "<", to);

    const tripSnapshot = await query.get();

    // check if it is an empty snapshot, if so return empty array
    if (tripSnapshot.empty) {
      functions.logger.info("No trip found between: %s and %s", from, to);
      return [];
    }
    // get the docs
    const trips = tripSnapshot.docs;

    // return object
    const tripsHasNotBeenCaptured = [];
    // loop through the trips
    for (const tripDoc of trips) {
      const trip = tripDoc.data();
      // loop through payment information to check if it is required to captrue
      const paymentInformationAudits = trip.paymentInformation || [];
      try {
        const lastAuth = findLastSuccessAuthWithoutCapture(paymentInformationAudits);
        // if lastAuth is not undefined then
        if (lastAuth != undefined) {
          // add to tripsHasNotBeenCaptured as result
          tripsHasNotBeenCaptured.push({
            tripId: trip.id,
            payment_information: trip.paymentInformation,
          });
        }
      } catch (err) {
        functions.logger.error("trip id %s: %s", trip.id, err);
      }
    }
    return tripsHasNotBeenCaptured;
  }

  /**
   * sendReceipt
   * @param {string} tripId
   */
  async sendReceipt(tripId: string): Promise<string> {
    const tripRef = repo.trip.doc(tripId);
    try {
      const trip = await db.runTransaction(async (tx) => {
        const trip = (await tx.get(tripRef)).data();
        if (!trip) {
          functions.logger.error("Trip not found %s", tripId);
          throw new Error("Trip not found");
        }
        if (trip.passengerInformation?.phone == undefined) {
          functions.logger.info("nothing to be sent");
          throw new ReferenceError("Trip has no passenger phone number, noting to be sent");
        }
        if (trip.receiptStatus) {
          functions.logger.error("Trip Id: %s receipt status: %s ", tripId, trip.receiptStatus);
          throw new Error("Trip receipt sent/sending");
        }
        // add a lock to the trip
        const lockTrip: Partial<Trip> = {
          receiptStatus: ReceiptStatus.PROCESSING,
        };
        tx.set(tripRef, lockTrip, { merge: true });
        return trip;
      });
      const receiptId = await ReceiptService.sendReceipt(trip);
      const updateTrip: Partial<Trip> = {
        receiptId: receiptId,
        receiptStatus: ReceiptStatus.SENT,
      };
      await tripRef.set(updateTrip, { merge: true });
      return receiptId;
    } catch (err) {
      functions.logger.error("Error sending reciept for Id: %s , %s", tripId, err);
      // roll back if it is error sending receipt
      if (err instanceof SendReceiptError) {
        const unlockTrip: Partial<Trip> = {
          receiptStatus: ReceiptStatus.FAILED,
        };
        await tripRef.set(unlockTrip, { merge: true });
      }
      throw err;
    }
  }
  /**
   * tagTrip
   * @param {string} tripId
   * @param {string} tag
   * @param {TagMetadata} tagMetadata
   * @return {Trip} trip
   */
  async tagTrip(tripId: string, tag: string, tagMetadata: TagMetadata): Promise<Trip> {
    const tripRef = repo.trip.doc(tripId);
    try {
      const trip = await db.runTransaction(async (tx) => {
        const trip = (await tx.get(tripRef)).data();
        if (!trip) {
          functions.logger.error("Trip not found %s", tripId);
          throw new Error("Trip not found");
        }
        if (!tagMetadata.createdBy) {
          tagMetadata.createdBy = "SYSTEM";
        }
        if (!tagMetadata.createdOn) {
          tagMetadata.createdOn = admin.firestore.FieldValue.serverTimestamp();
        }
        const updateTrip: Partial<Trip> = {
          tags: {
            [`${tag}`]: tagMetadata,
          },
        };
        functions.logger.info("tags: %s", JSON.stringify(updateTrip));
        tx.set(tripRef, updateTrip, { merge: true });
        return trip;
      });
      return trip;
    } catch (err) {
      functions.logger.error("Error tagging trip: %s ; %s", tripId, err);
      throw err;
    }
  }

  /**
   * Tag to copy
   * @param {string} tripId
   */
  async setVisibility(tripId: string): Promise<Trip | undefined> {
    functions.logger.info("setVisibility trip Id: %s", tripId);
    try {
      const tripRef = repo.trip.doc(tripId);
      const result = await db.runTransaction(async (tx) => {
        functions.logger.debug("Transaction started: %s", tripRef);
        const tripDoc = await tx.get(tripRef);
        if (!tripDoc.exists) {
          throw new ReferenceError("Trip Does not exist");
        }
        const trip = tripDoc.data();
        if (trip == undefined) {
          throw new Error("Empty data");
        }
        // Check if there is a session
        const tripSession = trip.session;
        if (tripSession) {
          // if there is check if it is DASH
          if (trip.paymentType == PaymentType.DASH) {
            functions.logger.debug("Trip id: %s is a dash trip and needs to copy", tripId);
            const updateTrip: Partial<Trip> = {
              showToDriver: true,
            };
            return tx.set(tripRef, updateTrip, { merge: true });
            // Or Cash, if cash, check driver preference
          } else {
            if (!trip.driver) {
              throw new ReferenceError("Driver does not exist");
            }
            const driverRef = repo.driver.doc(trip.driver.id);
            const driverDoc = await tx.get(driverRef);
            if (!driverDoc.exists) {
              throw new ReferenceError("Driver does not exist");
            }
            const driver = driverDoc.data();
            if (driver == undefined) {
              throw new Error("Driver - Empty data");
            }
            if (driver.showCashTrip) {
              functions.logger.debug("Trip id: %s is a cash trip and needs to copy", tripId);
              const updateTrip: Partial<Trip> = {
                showToDriver: true,
              };
              return tx.set(tripRef, updateTrip, { merge: true });
            }
          }
        }
        return null;
      });
      if (!result) {
        functions.logger.error("empty write result");
        throw new Error("empty write result");
      }
      return this.getTripById(tripId);
    } catch (err) {
      if (err instanceof ReferenceError) {
        functions.logger.error("ReferenceError : ", err.message);
        throw err;
      } else {
        throw err;
      }
    }
  }

  /**
   * getTripById
   * @param {string} tripId
   * @return {Trip} trip
   */
  async getTripById(tripId: string): Promise<Trip> {
    try {
      const tripRef = repo.trip.doc(tripId);
      const tripDoc = await tripRef.get();
      if (!tripDoc.exists) {
        throw new ReferenceError("");
      }
      const trip = tripDoc.data();
      if (trip == undefined) {
        throw new Error("Empty data");
      }
      return trip;
    } catch (err) {
      if (err instanceof ReferenceError) {
        throw err;
      } else {
        throw err;
      }
    }
  }
}

/**
 * Loop through payment information audit and return the last successful
 * auth audit, note that, since we are looping from the back
 * if we found a capture, we can return right away
 * if we found a more recent auth void, we can return right away
 * if we found a more recent auth failure, we can return right away
 * @param {PaymentInformationAudit[]} paymentInformationAudits
 * @return {PaymentInformationAudit} last success auth or
 *                                   undefined if no success auth or captured
 */
function findLastSuccessAuthWithoutCapture(
  paymentInformationAudits: PaymentInformationAudit[],
): PaymentInformationAudit | undefined {
  // loop from last
  const auditsLength = paymentInformationAudits.length;
  let lastSuccessAuthAudit: PaymentInformationAudit | undefined;
  if (auditsLength) {
    for (let index: number = auditsLength - 1; index >= 0; index--) {
      const paymentInformationAudit = paymentInformationAudits[index];
      // Captured successfully already
      if (
        paymentInformationAudit.type == PaymentInformationType.CAPTURE &&
        paymentInformationAudit.status == PaymentInformationStatus.SUCCESS
      ) {
        functions.logger.debug("CAPTURED found");
        return undefined;
        // Void successfully already
      } else if (
        paymentInformationAudit.type == PaymentInformationType.VOID &&
        paymentInformationAudit.status == PaymentInformationStatus.SUCCESS
      ) {
        functions.logger.debug("AUTH VOID found");
        return undefined;
      } else if (
        paymentInformationAudit.type == PaymentInformationType.AUTH &&
        paymentInformationAudit.status == PaymentInformationStatus.FAILURE
      ) {
        functions.logger.error("AUTH FAILED found");
        return undefined;
      } else if (
        paymentInformationAudit.type == PaymentInformationType.AUTH &&
        paymentInformationAudit.status == PaymentInformationStatus.SUCCESS
      ) {
        lastSuccessAuthAudit = paymentInformationAudit;
        break;
      }
    }
  }
  return lastSuccessAuthAudit;
}

/**
 * Loop through payment information audit and return the capture success
 * @param {PaymentInformationAudit[]} paymentInformationAudits
 * @return {PaymentInformationAudit} last success auth or
 *                                   undefined if no success auth or captured
 */
function findCapture(paymentInformationAudits: PaymentInformationAudit[]): PaymentInformationAudit | undefined {
  // loop from last
  const auditsLength = paymentInformationAudits.length;
  let lastSuccessAuthAudit: PaymentInformationAudit | undefined;
  if (auditsLength) {
    for (let index: number = auditsLength - 1; index >= 0; index--) {
      const paymentInformationAudit = paymentInformationAudits[index];
      // Captured successfully already
      if (
        paymentInformationAudit.type == PaymentInformationType.CAPTURE &&
        paymentInformationAudit.status == PaymentInformationStatus.SUCCESS
      ) {
        functions.logger.debug("CAPTURED found");
        lastSuccessAuthAudit = paymentInformationAudit;
        break;
      }
    }
  }
  return lastSuccessAuthAudit;
}

export default new TripService();

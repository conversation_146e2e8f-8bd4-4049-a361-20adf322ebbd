import * as functions from "firebase-functions";
import i18next from "i18next";
import Backend from "i18next-fs-backend";

/**
 * i18n Service for message template
 */
class I18nService {
  /**
   * constructor
   */
  constructor() {
    i18next
      .use(Backend)
      .init({
        fallbackLng: "en",
        preload: ["en", "zh-HK"],
        load: "currentOnly",
        ns: ["notification"],
        backend: {
          loadPath: "./src/static/i18n/{{lng}}/{{ns}}.json",
        },
        debug: false,
      })
      .then(() => {
        /* functions.logger.info(
              "translation %s",
              JSON.stringify(transaction)); */
        i18next.on("loaded", (loaded) => {
          functions.logger.info("loaded: %s", JSON.stringify(loaded));
          functions.logger.info("loaded: %s", JSON.stringify(loaded.keys));
          functions.logger.info("loaded: %s", JSON.stringify(loaded.resources));
        });
        i18next.on("failedLoading", (lng, ns, msg) => {
          functions.logger.info("failed lng: %s", JSON.stringify(lng));
          functions.logger.info("failed ns: %s", JSON.stringify(ns));
          functions.logger.info("failed msg: %s", JSON.stringify(msg));
        });
      })
      .catch((err) => {
        functions.logger.error("err %s", err);
      });
  }
  /**
   * getText
   * @param {string} name
   * @param {object} params
   * @param {string} language i18nLangugage
   * @return {string} text
   */
  getText(name: string, params: object, language: string): string {
    functions.logger.info("name: %s", name);
    return i18next.t("notification:" + name, { ...params, lng: language });
  }
}

export enum i18nLangugage {
  ENGLISH = "en",
  TRADITIONAL_CHINESE = "zh-hk",
}

export default new I18nService();

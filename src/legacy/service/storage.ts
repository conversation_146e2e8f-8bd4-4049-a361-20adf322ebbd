import * as functions from "firebase-functions";

import { bucket } from "./index";
/**
import * as fs from "node:fs";
import * as os from "node:os";
import * as XLSX from "xlsx";
 */

/**
 * Storage  Service
 */
class StorageService {
  /**
   * @param {string} fileName
   * @param {string} content
   * @return {Promise}
   */
  async uploadToBucket(fileName: string, content: string): Promise<void> {
    functions.logger.info("uploading file: %s", fileName);
    return bucket.file(fileName).save(content);
  }

  /**
   * getFile
   * @param {string} filePath
   * @return {string} file in string
   */
  async getFile(filePath: string): Promise<Buffer> {
    const file = await bucket.file(filePath).download();
    return Promise.resolve(file[0]);
  }
}

export default new StorageService();

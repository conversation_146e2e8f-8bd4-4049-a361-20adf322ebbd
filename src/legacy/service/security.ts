// import * as admin from "firebase-admin";
// import {getAuth, signInAnonymously} from "firebase/auth";
import * as functions from "firebase-functions";

/** import {decode, verify} from "jws";
import axios from "axios";
*/
import repo from "@legacy/repository/repo";
/**
 * SecurityService
 */
class SecurityService {
  // do nothing
  /**
   * getTempTripTokenByPhoneNumber
   * @param {string} tripId tripId
   * @param {string} phoneNumber phone on that trip
   */
  async getTempTripTokenByPhoneNumber(tripId: string, phoneNumber: string): Promise<string> {
    functions.logger.info("getTempTripTokenByPhoneNumber");
    const trip = (await repo.trip.doc(tripId).get()).data();
    if (trip?.passengerInformation?.phone == phoneNumber) {
      // do nothing
    }
    return Promise.resolve("");
  }
}

export default new SecurityService();

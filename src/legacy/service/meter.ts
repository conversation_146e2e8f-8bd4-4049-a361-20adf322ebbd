import * as functions from "firebase-functions";

import repo from "@legacy/repository/repo";
/**
 * MeterService
 */
class MeterService {
  /**
   * copyMeterTripToTrips - copy trip in the meter level to meter
   * @param {string} meterId - meter
   * @param {string} tripId - trip
   */
  async copyMeterTripToTrips(meterId: string, tripId: string) {
    if (!meterId || !tripId) {
      functions.logger.error("Input Error");
      throw new Error("Invalid input");
    }
    const meterTripRef = repo.meterTrip(meterId).doc(tripId);
    const meterTripDoc = await meterTripRef.get();

    // Check if doucment exists
    if (!meterTripDoc.exists) {
      throw new Error(`Document ${meterTripRef.path} not found`);
    }

    // Check if destination exist
  }
}

export default new MeterService();

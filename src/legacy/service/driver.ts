/* eslint-disable new-cap */
// import {object, string, InferType, date, bool, number} from "yup";
import crypto from "crypto";

import { Client, Language } from "@googlemaps/google-maps-services-js";
import * as admin from "firebase-admin";
import { DocumentReference, FieldValue, Timestamp } from "firebase-admin/firestore";
import * as functions from "firebase-functions";
import { defineInt, defineString } from "firebase-functions/params";
import * as jsonpath from "jsonpath-plus";
import moment from "moment-timezone";

// import {PaymentType, PayoutStatus} from "../model/trip";
import WatiClient, * as wati from "../wati/client";

// Config
// AIzaSyAjF2btOGllWv43xjShvMOnCa3SQyJMuS8
const googleMapKey = defineString("GOOGLE_MAP_KEY");
const iv = defineString("CRYPTO_IV");
const salt = defineString("CRYPTO_SALT");
const algo = defineString("CRYPTO_ALGO");
const qrValidPeriod = defineInt("QR_VALID_PERIOD");
const activationLinkPrefix = defineString("ACTIVATION_URL_PREFIX");

// Firestore
admin.initializeApp();
export const db = admin.firestore();
export const storage = admin.storage();
export const messaging = admin.messaging();
export const auth = admin.auth();

/**
 * Pair will do
 * 1. Add session to meter
 * 2. Add session to driver
 * @param {string} driverId is the driver ID of the driver who initate
 * the pairing
 * @param {string} licensePlate is the QR code on the meter
 * @param {Date} expectedEndTime is the shift end time
 * @return {session}
 */
export function pair(driverId: string, licensePlate: string, expectedEndTime: Date | null): Promise<any | null> {
  functions.logger.info("Function: pair driverId: " + driverId);
  return db
    .runTransaction(async (tx) => {
      const driverRef = db.collection("drivers").doc(driverId);
      const driver = await tx.get(driverRef);
      if (!driver) {
        throw new ReferenceError();
      }
      // check if current driver has an existing session;
      const existingSession = driver.data()?.session;
      functions.logger.info("existing driver: %s", JSON.stringify(driver.data));
      functions.logger.info("existing driver session: %s", JSON.stringify(existingSession));
      if (existingSession != undefined) {
        throw new Error("There is an active session");
      }

      // check if meter has an existing session
      const meterRef = db.collection("meters").doc(licensePlate);
      const meter = await tx.get(meterRef);
      const existingSessionId = meter.data()?.session?.id;
      if (existingSessionId != undefined) {
        // there is an active session
        functions.logger.info(
          "There is an existing session on this meter %s with session id %s",
          licensePlate,
          existingSessionId,
        );
        const existingSessionRef = db.collection("sessions").doc(existingSessionId);
        const existingSession = await tx.get(existingSessionRef);
        const existingDriverId = existingSession.data()?.driver.id;
        functions.logger.info("existing session driver: %s", existingDriverId);
        const existingDriverRef = db.collection("drivers").doc(existingDriverId);
        // const existingDriver = await (await tx.get(existingDriverRef)).data();

        // add end time to existing session and remove existing driver session
        functions.logger.info("End existing session %s", existingSessionRef.path);
        tx.update(existingSessionRef, {
          end_time: admin.firestore.FieldValue.serverTimestamp(),
        });
        functions.logger.info("Delete existing driver's session: %s", existingDriverRef.path);
        tx.update(existingDriverRef, {
          session: admin.firestore.FieldValue.delete(),
        });
      } else {
        functions.logger.info("There is no existing session on meter");
      }

      // params
      const shift = getShift(new Date());
      const timestamp = admin.firestore.FieldValue.serverTimestamp();

      // create session
      functions.logger.info("create new session");
      functions.logger.debug("driver data: %s", driver.data());
      const sessionPayload = {
        shift: shift,
        meter_id: licensePlate,
        meter_ref: meterRef,
        license_plate: licensePlate,
        start_time: timestamp,
        end_time: null, // need this for query
        expected_end_time: expectedEndTime,
        driver: driver.data(),
        driver_id: driverId,
        driver_ref: driverRef,
      };
      const sessionRef = db.collection("sessions").doc();
      tx.set(sessionRef, sessionPayload);
      const sessionId = sessionRef.id;
      functions.logger.info("session created: " + sessionId);

      // add session to driver
      functions.logger.info("create driver session map");
      const driverSessionPayload = {
        session: {
          id: sessionId,
          session_id: sessionId,
          session_ref: sessionRef,
          shift: shift,
          meter_id: licensePlate,
          meter_ref: meterRef,
          license_plate: licensePlate,
          start_time: timestamp,
          end_time: null,
          expected_end_time: expectedEndTime,
        },
      };
      functions.logger.info("add session to driver %s", driverRef.path);
      tx.update(driverRef, driverSessionPayload);
      functions.logger.info("driver session map created");

      // add session to driver's session
      functions.logger.info("create driver session doc");
      const driverSessionRef = db.collection("drivers").doc(driverId).collection("sessions").doc(sessionId);
      const driverSessionCollectionPayload = {
        id: sessionId,
        session_id: sessionId,
        session_ref: sessionRef,
        shift: shift,
        meter_id: licensePlate,
        meter_ref: meterRef,
        license_plate: licensePlate,
        start_time: timestamp,
        end_time: null,
        expected_end_time: expectedEndTime,
      };
      functions.logger.info("add session to driver session %s", driverSessionRef.path);
      tx.set(driverSessionRef, driverSessionCollectionPayload);
      functions.logger.info("driver session doc created");

      // add session to meter
      const meterSessionPayload = {
        session: {
          id: sessionId,
          session_id: sessionId,
          session_ref: sessionRef,
          shift: shift,
          start_time: timestamp,
          end_time: null,
          expected_end_time: expectedEndTime,
          driver_ref: driverRef,
          driver: driver.data(),
        },
      };
      functions.logger.info("add session to meter %s", meterRef);
      tx.update(meterRef, meterSessionPayload);
      return sessionId;
    })
    .then((result) => {
      functions.logger.info("Transaction Completed: " + result);
      return Promise.resolve(result);
    })
    .catch((error) => {
      functions.logger.error("Transaction Error:" + error);
      throw error;
    });
}

/**
 * Get Session By Id
 * @param {string} sessionId
 * @return {Promise} promise with session
 */
export function getSessionById(sessionId: string): Promise<any | null> {
  return db
    .doc("sessions/" + sessionId)
    .get()
    .then((doc) => {
      // remove driver and fix datetime
      const startTime = doc.data()?.start_time.toDate();
      const resultJson = {
        session_id: sessionId,
        start_time: startTime,
        license_plate: doc.data()?.meter_id,
        meter_id: doc.data()?.meter_id,
        expected_end_time: null,
        shift: doc.data()?.shift,
      };
      return Promise.resolve(resultJson);
    });
}

/**
 * Unpair
 * @param {string} sessionId session to be unpaired
 * @return {boolean}
 */
export function unpair(sessionId: string): Promise<any | string> {
  functions.logger.info("unpair session: " + sessionId);
  return db.runTransaction((tx) => {
    const sessionRef = db.collection("sessions").doc(sessionId);
    return tx
      .get(sessionRef)
      .then((doc) => {
        functions.logger.info(JSON.stringify(doc.data()));
        if (doc.data()?.end_time != undefined) {
          return "duplicated";
        }
        const meterId = doc.data()?.meter_id;
        const timestamp = admin.firestore.FieldValue.serverTimestamp();
        // update session
        tx.update(sessionRef, { end_time: timestamp });

        // remove session from meter
        const meterRef = db.collection("meters").doc(meterId);
        tx.update(meterRef, { session: admin.firestore.FieldValue.delete() });

        // remove session from driver
        // since 1.1 there is no driver in session will use driver_id instead
        const driverId = doc.data()?.driver.id || doc.data()?.driver_id;
        const driverRef = db.collection("drivers").doc(driverId);
        tx.update(driverRef, { session: admin.firestore.FieldValue.delete() });
        functions.logger.info("unpair success: %s", sessionId);
        return Promise.resolve(`unpair success: ${sessionId}`);
      })
      .catch((error) => {
        functions.logger.error("session %s unpair error: %s", sessionId, error);
        return Promise.reject(error);
      });
  });
}

/**
 * update expected end time
 * @param {string} driverId
 * @param {Date} expectedEndtime
 * @return {Promise} promise to be returned
 */
export function updateExpectedEndTimeService(driverId: string, expectedEndtime: Date): Promise<any | null> {
  functions.logger.info("expected end time:" + expectedEndtime);
  const updatePayload = {
    session: {
      expected_end_time: admin.firestore.Timestamp.fromDate(expectedEndtime),
    },
  };
  return db
    .collection("drivers")
    .doc(driverId)
    .set(updatePayload, { merge: true })
    .then((result) => {
      functions.logger.info(result);
      return Promise.resolve();
    })
    .catch((err) => {
      functions.logger.error(err);
      return Promise.reject(err);
    });
}

/**
 * getShift is a util to calculate if it is day or night shift
 * @param {Date} date is the date
 * @return {string} the shift name
 */
export function getShift(date: Date): string {
  functions.logger.info("getShift %s", date);
  const hourInHKTz: number = +moment(date).tz("Asia/Hong_Kong").format("HH");
  functions.logger.debug("getShift get hour %s", hourInHKTz);
  if (hourInHKTz >= 3 && hourInHKTz < 15) {
    return "day";
  } else {
    return "night";
  }
}

/**
 * @param {string} meterId meter Id
 * @param {string} tripId trip Id
 * @param {string} sessionId session Id
 * @return {Promise} promise to be returned
 */
export async function moveTripFromMeterToTrips(meterId: string, tripId: string): Promise<any | null> {
  functions.logger.info("moveTripFromMeterToTrips called: %s %s", meterId, tripId);
  const toPath = "trips/" + tripId;
  const fromPath = "meters/" + meterId + "/trips/" + tripId;

  // attempt to copy over to trips
  return db
    .doc(fromPath)
    .get()
    .then(async (doc) => {
      if (!doc.exists) {
        throw new ReferenceError();
      }
      const trip = { ...doc.data() };
      functions.logger.debug("session: %s", doc.data()?.session);
      // check session
      if (doc.data()?.session != undefined) {
        const sessionId = doc.data()?.session.id;
        const sessionPath = "sessions/" + sessionId;
        await db
          .doc(sessionPath)
          .get()
          .then((sessionDoc) => {
            // trip not belongs to this session
            functions.logger.debug(
              "compare session %s end time and trip start %s",
              sessionDoc.data()?.end_time,
              doc.data()?.trip_start.toDate(),
            );
            if (sessionDoc.data()?.end_time != null && sessionDoc.data()?.end_time < doc.data()?.trip_start) {
              functions.logger.info("trip %s belongs to expired session %s", doc.data()?.id, sessionId);
              functions.logger.debug("time:" + sessionDoc.data()?.end_time + "<" + doc.data()?.trip_start);
              db.doc(fromPath).update({ session: FieldValue.delete() });
              // remove the session in the trip as well;
              delete trip["session"];
            } else {
              functions.logger.debug("debug trip %s", JSON.stringify(trip));
            }
          });
      } else {
        functions.logger.debug("%s trip without session", tripId);
      }
      return db.doc(toPath).set(trip, { merge: true });
    });
}

/**
 * tripCalculation
 * @param {string} meterId
 * @param {string} tripId
 * @return {Promise} promise to be returned
 */
export function tripCalculation(meterId: string, tripId: string): Promise<void> {
  functions.logger.info("tripCalculation called %s %s", meterId, tripId);
  return db.runTransaction((tx) => {
    const tripRef = db.collection("meters").doc(meterId).collection("trips").doc(tripId);
    return tx.get(tripRef).then((tripDoc) => {
      if (tripDoc.exists) {
        const fare = tripDoc.get("fare");
        const extra = tripDoc.get("extra") | 0;
        const tips = tripDoc.get("dash_tips") | 0;
        const tripTotal = fare + extra;
        let dashFee: number;
        if (tripDoc.get("payment_type") == "DASH") {
          dashFee = Math.round(((fare + extra + tips) * 0.035 + Number.EPSILON) * 100) / 100;
        } else {
          dashFee = 0;
        }
        const total = tripTotal + tips + dashFee;
        const updatePayload = {
          trip_total: tripTotal,
          dash_fee: dashFee,
          total: total,
        };
        tx.update(tripRef, updatePayload);
        return Promise.resolve();
      } else {
        return Promise.reject(new ReferenceError());
      }
    });
  });
}

/**
 * convertLocation
 * @param {string} meterId
 * @param {string} tripId
 * @param {string} fieldName
 * @return {promise} PRomise to be returned
 */
export function convertLocation(meterId: string, tripId: string, fieldName: string): Promise<any | null> {
  // functions.logger.info("convert location service");
  // const meterTripRef = db.collection("meters").doc(meterId)
  //    .collection("trips").doc(tripId);
  const meterTripRef = "meters/" + meterId + "/trips/" + tripId;
  functions.logger.info("convert location service:" + meterTripRef);
  return db
    .doc(meterTripRef)
    .get()
    .then((doc) => {
      if (!doc) {
        functions.logger.debug("no such doc meter/" + meterId + "/trip/" + tripId);
        throw ReferenceError;
      }
      let lat: number;
      let lng: number;
      let locationAddressFieldName: string;
      if (fieldName == "start") {
        lat = doc.data()?.location_start.latitude;
        lng = doc.data()?.location_start.longitude;
        locationAddressFieldName = "location_start_address";
      } else {
        lat = doc.data()?.location_end.latitude;
        lng = doc.data()?.location_end.longitude;
        locationAddressFieldName = "location_end_address";
      }
      functions.logger.debug(lat + ":" + lng + "=>?");
      return locationConversionService(lat, lng).then((locationName) => {
        functions.logger.debug(
          "converted trip id %s (%s): %s:%s => %s",
          tripId,
          locationAddressFieldName,
          lat,
          lng,
          locationName,
        );
        return db
          .doc(meterTripRef)
          .update({ [locationAddressFieldName]: locationName })
          .then(() => {
            functions.logger.info("updated %s location name", fieldName);
            return { [locationAddressFieldName]: locationName };
          })
          .catch((err) => {
            functions.logger.error("failed to update %s location name:%s", fieldName, err);
            throw err;
          });
      });
    });
}

export const googleMapAddressMapper = [
  { types: "premise", address_format: ["neighborhood", "premise"] },
  {
    types: "street_address",
    address_format: ["administrative_area_level_1", "neighborhood", "route", "street_number"],
  },
  {
    types: "neighborhood",
    address_format: ["administrative_area_level_1", "neighborhood"],
  },
  {
    types: "route",
    address_format: ["route"],
  },
];
/**
 * Convert long lan to location
 * @param {number} lat
 * @param {number} lng
 * @return {Promise} location name
 */
export async function locationConversionService(lat: number, lng: number): Promise<any | null> {
  functions.logger.info("locationConversionService %s %s", lat, lng);
  const client = new Client({});
  const params = {
    key: googleMapKey.value(),
    latlng: {
      lat: lat,
      lng: lng,
    },
    language: Language.zh_TW,
    // result_type: [AddressType.establishment, AddressType.neighborhood],
  };
  return client
    .reverseGeocode({ params: params })
    .then((result) => {
      functions.logger.debug("result: %s", JSON.stringify(result.data));
      let address = "";
      outerloop: for (let i = 0; i < googleMapAddressMapper.length; i++) {
        const addressType = googleMapAddressMapper[i];
        functions.logger.info("address Type: %s", addressType);
        const types = addressType.types;
        const addressComponents = addressType.address_format;
        const addressTypeResult = jsonpath.JSONPath({
          json: result.data,
          path: '$.results[?(@.types.indexOf("' + types + '") != -1)]',
          resultType: "value",
        });
        if (addressTypeResult.length > 0) {
          functions.logger.debug("types: %s", addressTypeResult);
          const firstType = addressTypeResult[0];
          const addressArray: string[] = [];
          for (let j = 0; j < addressComponents.length; j++) {
            const component = addressComponents[j];
            const addressJsonPath =
              "$.address_components" + '[?(@.types.indexOf("' + component + '") != -1)]' + ".short_name";
            addressArray.push(
              jsonpath.JSONPath({
                json: firstType,
                path: addressJsonPath,
                resultType: "value",
              }),
            );
          }
          address = addressArray.join("");
          functions.logger.debug("%s result %s", types, address);
          break outerloop;
        }
      }
      return Promise.resolve(address);
    })
    .catch((error) => {
      functions.logger.error(error);
    });
}

/**
 * @param {DocumentReference} driverRef driver ref
 * @param {string} sessionId session Id
 * @param {Timestamp} sessionEndTime
 * @return {Promise}
 */
export async function expireDriverSessionIfActive(
  driverRef: DocumentReference,
  sessionId: string,
  sessionEndTime: Timestamp,
): Promise<string> {
  functions.logger.info("expireDriverSessionIfActive called %s %s", driverRef.path, sessionId);
  return await db.runTransaction(async (tx) => {
    const driverDoc = await tx.get(driverRef);
    const driver = driverDoc.data();
    if (driver == undefined) {
      throw new ReferenceError("driver not found");
    }
    // expire session if it is the current session
    if (driver.session.id == sessionId) {
      functions.logger.log("expiring session %s for driver %s", sessionId, driverRef.id);

      const updateDriverPayload = {
        session: admin.firestore.FieldValue.delete(),
      };
      tx.set(driverRef, updateDriverPayload, { merge: true });
      const sessionPayload = {
        end_time: sessionEndTime,
      };
      const sessionRef = driverRef.collection("sessions").doc(sessionId);
      tx.set(sessionRef, sessionPayload, { merge: true });
      return "complete";
    } else {
      functions.logger.log(
        "expiring session %s different from %s for driver %s",
        sessionId,
        driver.session.id,
        driverRef.id,
      );
      return "expiring a different driver session";
    }
  });
}

/**
 * @param {DocumentReference} meterRef meter Id
 * @param {string} sessionId session Id
 * @return {Promise}
 */
export async function expireMeterSessionIfActive(meterRef: DocumentReference, sessionId: string): Promise<string> {
  functions.logger.info("expireMeterSessionIfActive called %s %s", meterRef, sessionId);
  return await db.runTransaction(async (tx) => {
    // Remove meter's session = sessionId
    const meterDoc = await tx.get(meterRef);
    const meter = meterDoc.data();
    if (meter == undefined) {
      throw new ReferenceError("meter not found");
    }
    if (meter.session.id == sessionId) {
      functions.logger.info("meter(%s) session.id (%s) == sessionId (%s)", meterRef.id, meter.session.id, sessionId);
      const updatePayload = { session: admin.firestore.FieldValue.delete() };
      tx.set(meterRef, updatePayload);
      return "completed";
    } else {
      functions.logger.info("meter(%s) session.id (%s) != sessionId (%s)", meterRef.id, meter.session.id, sessionId);
      return "expiring a different meter session";
    }
  });
}

/**
 * createdAndSendActivation
 * @param {string} driverId
 * @return {Promise} promise
 */
export function createdAndSendActivation(driverId: string): Promise<void> {
  functions.logger.info("createdAndSendActivation: " + driverId);
  return db
    .runTransaction(async (tx) => {
      const activationRef = db.collection("activations").doc();
      const driverRef = db.collection("drivers").doc(driverId);
      const driverActivationRef = (await tx.get(driverRef)).get("activation_ref");
      if (driverActivationRef != undefined) {
        throw Error("Activation Created");
      }
      const activationPayload = {
        driver_ref: driverRef,
        created_on: admin.firestore.FieldValue.serverTimestamp(),
      };
      tx.create(activationRef, activationPayload);
      functions.logger.info("activation created: " + activationRef.id);
      // update driver
      const driverPayload = {
        activation_ref: activationRef,
      };
      tx.update(driverRef, driverPayload);
      const activation = {
        activation_id: activationRef.id,
        phone_number: driverId,
      };
      return activation;
      // return Promise.resolve(activation);
    })
    .then((activation) => {
      functions.logger.info("Trying to send activation: %s", JSON.stringify(activation));
      const activationUrl = activationLinkPrefix.value() + activation.activation_id;
      const activationMessagePayload: wati.ActivationMessagePayload = {
        activation_link_en: activationUrl + "?lang=en-HK",
        activation_link_zh: activationUrl + "?lang=zh-HK",
      };
      return WatiClient.sendActivation(activation.phone_number, "", activationMessagePayload).then((result) => {
        functions.logger.info("activaiton send result %s", result);
        return Promise.resolve();
      });
    })
    .catch((err) => {
      return Promise.reject(err);
    });
}

/**
 * getDriverActivationService
 * @param {string} driverId
 * @return {Promise}

export function getDriverActivationService(
    driverId: string):Promise<string|void> {
  return db.runTransaction((tx)=>{

  });
}
 */

/**
 * getDriverActivationService
 * @param {string} activationId
 * @return {Promise} promise of true/false
 */
export function getActivationStatus(activationId: string): Promise<boolean> {
  return db.runTransaction((tx) => {
    functions.logger.info("looking up activation: " + activationId);
    const activationRef = db.collection("activations").doc(activationId);
    functions.logger.debug("activationRef path: " + activationRef.path);
    return tx.get(activationRef).then((doc) => {
      functions.logger.debug("get.tx");
      if (!doc.exists) {
        throw new ReferenceError("Activation ID not found");
      } else {
        const driverRef: DocumentReference = doc.data()?.driver_ref;
        return tx.get(driverRef).then((driverDoc) => {
          if (driverDoc.data()?.activated_on != undefined) {
            return Promise.resolve(false);
          } else {
            return Promise.resolve(true);
          }
        });
      }
    });
  });
}

/**
 * activateDriver
 * @param {string} activationId
 * @return {Promise} promise of true/false
 */
export function activateDriver(activationId: string): Promise<boolean> {
  return db.runTransaction((tx) => {
    functions.logger.info("looking up activation: " + activationId);
    const activationRef = db.collection("activations").doc(activationId);
    return tx.get(activationRef).then((doc) => {
      functions.logger.debug("activateDriver: tx.get");
      if (!doc.exists) {
        throw new ReferenceError("Activation ID not found");
      } else {
        const driverRef: DocumentReference = doc.data()?.driver_ref;
        return tx.get(driverRef).then((driverDoc) => {
          if (driverDoc.data()?.activated_on != undefined) {
            return Promise.resolve(false);
          } else {
            const activationPayload = {
              activated_on: admin.firestore.FieldValue.serverTimestamp(),
            };
            tx.update(driverRef, activationPayload);
            return Promise.resolve(true);
          }
        });
      }
    });
  });
}

/**
 * Unpair all expired session
 * @param {string} sessionId
 * @param {Timestamp} expectedEndTime
 * @return {Promise} promise true - success; false - failed
 */
export function copySessionFromDriver(sessionId: string, expectedEndTime: Timestamp): Promise<boolean> {
  functions.logger.info("copySessionFromDriver called %s %s", sessionId, expectedEndTime);
  const sessionRef = db.collection("sessions").doc(sessionId);

  functions.logger.debug(typeof expectedEndTime);

  const sessionUpdate = {
    expected_end_time: expectedEndTime,
  };
  return sessionRef.update(sessionUpdate).then((success) => {
    if (success) {
      return Promise.resolve(true);
    } else {
      return Promise.resolve(false);
    }
  });
}

/**
 *
 * @param {string} phoneNumber
 * @param {string} template
 * @param {string[]}params
 * @return {Promise} promise
 */
export function sendMessage(phoneNumber: string, template: string, params: string[]): Promise<void> {
  functions.logger.info("sendMessage %s %s %s", phoneNumber, template, params);
  return Promise.resolve();
}

/**
 * validate qr code
 * @param {string} qr
 * @return {boolean} true if valid, false otherwise
 */
export function validateQR(qr: string): boolean {
  const qrInHex = Buffer.from(qr, "base64").toString("hex");
  const decrypter = crypto.createDecipheriv(algo.value(), salt.value(), iv.value());
  const decryptedMsg = decrypter.update(qrInHex, "hex", "utf8");
  functions.logger.debug("date %s", decryptedMsg.slice(0, -1));
  const issueDate = moment.tz(decryptedMsg.slice(0, -1), "YYMMDDHHmmss", "Asia/Hong_Kong");
  const now = moment().tz("Asia/Hong_Kong");
  const difference = moment.duration(now.diff(issueDate)).asSeconds();
  functions.logger.info("difference %s", difference);
  if (difference > qrValidPeriod.value()) {
    return false;
  } else {
    return true;
  }
}

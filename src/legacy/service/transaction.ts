import axios, { AxiosError, isAxiosError } from "axios";
import axiosRetry, { exponentialDelay } from "axios-retry";
import CryptoJS from "crypto-js";
import * as functions from "firebase-functions";
import { defineString } from "firebase-functions/params";

const apiKey = defineString("SOEPAY_API_KEY");
const identity = defineString("SOEPAY_IDENTITY");
const appId = defineString("SOEPAY_APP_ID");
const soepayUrl = defineString("SOEPAY_URL");

/**
 * TransactionService
 */
class TransactionService {
  iv = CryptoJS.enc.Hex.parse("0000000000000000");

  /**
   * doCapture
   * @param {string} tranId
   * @param {number} amount
   * @return {Promise} response
   */
  async doCapture(tranId: string, amount: number): Promise<any> {
    // Get timestamp for encryption
    const timestamp = Date.now();
    functions.logger.debug("timestamp for encryption: %s", timestamp);
    const data = this.getEncryptedBody(timestamp, tranId, amount);
    functions.logger.debug("encrypted body:%s", data);
    const config = {
      method: "post",
      url: soepayUrl.value() + "/transaction/capture",
      headers: {
        "api-key": apiKey.value(),
        identity: identity.value(),
        "app-id": appId.value(),
        source: "SOFTPOS",
        "captcha-source": "SOFTPOS",
        "Content-Type": "text/plain",
        "Accept-Encoding": "gzip,deflate,compress",
      },
      data: null,
    };
    const fullConfig = {
      ...config,
      headers: {
        ...config.headers,
        timestamp: timestamp,
      },
      data: data,
    };
    functions.logger.debug("Soepay url: ", soepayUrl.value() + "/capture");
    functions.logger.debug("Soepay headers: ", JSON.stringify(fullConfig.headers));
    const retry = 3;
    axiosRetry(axios, {
      retries: retry,
      retryDelay: exponentialDelay,
      retryCondition: (error: AxiosError) => {
        // Only retry on network errors or 5xx server errors
        return !error.response || error.response.status >= 500;
      },
    });
    try {
      const response = await axios(fullConfig);
      const responseData = response.data;
      const status = response.status;
      functions.logger.debug(
        "capture response for tranId: %s ; status: %s ; response : %s ",
        tranId,
        status,
        responseData,
      );
      return responseData;
    } catch (err) {
      if (isAxiosError(err) && err.response?.status !== 200) {
        throw new CaptureError("API call failed with status code: " + `${err.response?.status} after 3 retry attempts`);
      } else {
        throw err;
      }
    }
    // return "";
  }

  /**
   * @param {number} timestamp
   * @param {string} tranId
   * @param {number} amount
   * @return {string} body
   */
  getEncryptedBody(timestamp: number, tranId: string, amount: number): string {
    const encrpytionKey = `${apiKey.value()}${timestamp}`;
    functions.logger.debug(encrpytionKey);
    const encrpytionKeySha = CryptoJS.enc.Utf8.parse(
      CryptoJS.SHA256(encrpytionKey.toLowerCase()).toString().substring(16, 48),
    );

    const body = {
      tranId: tranId,
      signatureOnPaper: false,
      amount: amount,
    };

    const encryptedBody = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(JSON.stringify(body)), encrpytionKeySha, {
      iv: this.iv,
      mode: CryptoJS.mode.CBC, // Block mode = CBC
      padding: CryptoJS.pad.Pkcs7, // Padding = PKCS7
      salt: "", // Salt is not required
    }).toString();
    return encryptedBody;
  }
}
/**
 * Capture Error
 */
export class CaptureError extends Error {
  /**
   * Constructor
   * @param {string} message
   */
  constructor(message: string) {
    super(message);

    Object.setPrototypeOf(this, CaptureError.prototype);
  }
}

export default new TransactionService();

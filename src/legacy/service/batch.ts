import Decimal from "decimal.js";
import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import { defineString } from "firebase-functions/params";
import moment from "moment-timezone";

import repo from "@legacy/repository/repo";

import { db } from "./driver";
import { BatchResponse, BatchType, FundReleaseItem, FundReleaseRequestType } from "../api/batch";
import { BankFile, BankFileEntry } from "../model/batch/bank-file";
import { FundReleaseBatch, FundReleaseBatchStatus } from "../model/batch/fund-release";
// import {Firestore} from "firebase-admin/firestore";
import { PayoutInformationAudit, PayoutInformationAuditType, PayoutStatus, Trip } from "../model/trip";
import StorageService from "../service/storage";

// import NotificationService from "../service/notification";
// import {Notification} from "../model/notification";

const folderName = defineString("BUCKET_FOLDER");

/**
 * Batch Service
 */
class BatchService {
  /**
   * constructor
   */
  constructor() {
    // empty
  }
  /**
   * Get all new items
   * @return {Promise<FundReleaseItem[]>} result
   */
  async preparePrerelease(): Promise<FundReleaseItem[]> {
    const result: FundReleaseItem[] = [];
    const yesterday6pm = moment()
      .tz("Asia/Hong_Kong")
      .subtract(1, "day")
      .set({ hour: 18, minute: 0, second: 0, millisecond: 0 })
      .toDate();
    const trips = await repo.trip
      .where("payout_status", "==", "NONE")
      .where("dispute", "==", null)
      .orderBy("trip_end", "desc")
      .where("trip_end", "<", yesterday6pm)
      .orderBy("driver.id", "asc")
      .get();
    functions.logger.info("trips where payout = NONE: size %s, empty %s", trips.size, trips.empty);
    const resultMap = new Map<string, string[]>();
    trips.forEach((trip) => {
      const driverId = trip.data()?.driver?.id;
      if (driverId == undefined || driverId == null) {
        return;
      }
      const tripId = trip.id;
      let tripsArray = resultMap.get(driverId);
      if (tripsArray == undefined) {
        tripsArray = [tripId];
      } else {
        tripsArray.push(tripId);
      }
      resultMap.set(driverId, tripsArray);
    });
    resultMap.forEach((value, key) => {
      result.push({
        driverId: key,
        trips: value,
      });
    });
    functions.logger.debug("==>result %s", JSON.stringify(result));
    return Promise.resolve(result);
  }
  /**
   * prerelease
   * @param {FundReleaseItem[]} fundReleaseItems
   * @return {Promise} batchId
   */
  async processPrereleaseRequest(fundReleaseItems: FundReleaseItem[]): Promise<BatchResponse> {
    functions.logger.info("start processPrereleaseRequest");
    // Create batch parent
    const batchRef = repo.batch.doc();
    const batchPayload: FundReleaseBatch = {
      createdOn: admin.firestore.FieldValue.serverTimestamp(),
      createdBy: "dummy",
      id: batchRef.id,
      originalInput: fundReleaseItems,
      result: fundReleaseItems,
      status: FundReleaseBatchStatus.NEW,
      type: FundReleaseRequestType.REQUEST_PRERELEASE,
    };
    await batchRef.create(batchPayload);

    // Mark trips PRERELEASE_REQUESTED
    for (const items of fundReleaseItems) {
      functions.logger.info("Processing driver %s, trip count %s", items.driverId, items.trips.length);
      // tx (assuming less than 500)
      db.runTransaction(async (tx) => {
        const refList = items.trips.map((id) => repo.trip.doc(id));
        const trips = tx.getAll(...refList);
        functions.logger.info((await trips).length);
        for (const trip of await trips) {
          const payoutInformation = trip.data()?.payoutInformation || [];
          functions.logger.info("payoutInformation: %s", JSON.stringify(payoutInformation));
          // to-do: need to check if current status is allow to perform
          // pre-release request, e.g. if it is already released, cannot release
          // again.
          const releaseAudit: PayoutInformationAudit = {
            type: PayoutInformationAuditType.PRERELEASE_REQUEST,
            batchRef: batchRef,
            createdOn: new Date(),
          };
          payoutInformation.push(releaseAudit);
          const update: Partial<Trip> = {
            payoutInformation: payoutInformation,
            payoutStatus: PayoutStatus.PRERELEASE_REQUESTED,
          };
          // cannot use update as converter won't work, so need to use set
          tx.set(repo.trip.doc(trip.id), update, { merge: true });
        }
      });
    }
    // response
    const batchResponse: BatchResponse = {
      type: BatchType.FUND_RELEASE,
      batch: batchPayload,
    };

    return Promise.resolve(batchResponse);
    /**
    for (const item of fundReleaseItems) {
        const driverId = item.driverId;
        db.runTransaction((tx)=>{
          tx.get
        });
    }
     */
  }

  /**
   * create subtask
   * @param {string} batchId
   * @return {Promise} boolean
   */
  async processPrereleaseBatch(batchId: string): Promise<boolean> {
    functions.logger.info("processBatch %s", batchId);
    const batchRef = repo.batch.doc(batchId);
    const batch = await batchRef.get();
    const input = batch.data()?.originalInput;
    const bankFile = new BankFile();
    functions.logger.debug(input);
    if (input) {
      for (const item of input) {
        functions.logger.info("processing item %s", JSON.stringify(item));
        const driverDoc = await (await db.collection("drivers").doc(item.driverId).get()).data();
        functions.logger.info("Acconunt info %s", JSON.stringify(driverDoc));
        const refList = item.trips.map((id) => repo.trip.doc(id));
        const payoutTotal = await db.runTransaction(async (tx) => {
          let payoutTotalLocal = new Decimal(0);
          const trips = await await tx.getAll(...refList);
          for (const tripSnapshot of trips) {
            const trip = tripSnapshot.data();
            if (trip) {
              functions.logger.info("adding trip %s fare %s:", trip.id, trip.tripTotal);
              // safe decimal operation
              payoutTotalLocal = payoutTotalLocal.plus(trip.tripTotal || 0);
              payoutTotalLocal = payoutTotalLocal.plus(trip.dashTips || 0);
              functions.logger.info("payout Total %s", payoutTotalLocal);
              const payoutInformation = trip.payoutInformation || [];
              functions.logger.info("original payoutInformation: %s", JSON.stringify(payoutInformation));
              const releaseAudit: PayoutInformationAudit = {
                type: PayoutInformationAuditType.PRERELEASE,
                batchRef: batchRef,
                createdOn: new Date(),
              };
              payoutInformation.push(releaseAudit);
              const updateDoc: Partial<Trip> = {
                payoutInformation: payoutInformation,
                payoutStatus: PayoutStatus.PRERLEASED,
              };
              tx.set(repo.trip.doc(trip.id), updateDoc, { merge: true });
            }
          }
          return payoutTotalLocal;
        });

        functions.logger.info("payout Total outside %s", payoutTotal);
        // first 3 digits = branch code
        const branchCode = driverDoc?.bank_account.substring(0, 3);
        // 4-end = bank account
        const bankAccount = driverDoc?.bank_account.substring(3).replace("=", "").replace(" ", "");
        const bankFileEntry = new BankFileEntry(
          driverDoc?.bank_account_owner_name,
          bankAccount,
          driverDoc?.bank_id,
          branchCode,
          payoutTotal.toNumber(),
          driverDoc?.phone_number,
        );
        bankFile.entries.push(bankFileEntry);
      }
    }
    functions.logger.info(bankFile.toString());
    const fileName = batchId + ".txt";
    StorageService.uploadToBucket(folderName.value() + "/" + fileName, bankFile.toString());
    // add file name to doc
    const batchUpdateDoc: Partial<FundReleaseBatch> = { file: fileName };
    await repo.batch.doc(batchId).set(batchUpdateDoc, { merge: true });

    const fileNameFPS = batchId + "-FPS.txt";
    StorageService.uploadToBucket(folderName.value() + "/" + fileNameFPS, bankFile.toStringFPS());

    return Promise.resolve(true);
  }
  /**
   * prerelease
   * @param {string[]} driverIds
   * @param {string} prereleaseBatchRef original prerelease batch
   * @return {Promise} batchId
   */
  async processReleaseRequest(driverIds: string[], prereleaseBatchRef: string): Promise<BatchResponse> {
    functions.logger.info("start processReleaseRequest %s, %s", JSON.stringify(driverIds), prereleaseBatchRef);

    // Create batch parent
    // const notification = {} as Notification;
    // create notification
    // NotificationService.sendPush(notification);
    const response: BatchResponse = {
      type: BatchType.FUND_RELEASE,
    };
    return Promise.resolve(response);
  }
}
export default new BatchService();

/**
 * Create a batch job
 * @param {Firestore} db
 * @param {BatchRequest} payload
 * @return {Promise} batch job
export async function createBatchJob(
    db:Firestore, payload:BatchRequest):Promise<any> {
  const batchRef = repo.batch.doc();
  const timestamp = admin.database.ServerValue.TIMESTAMP;
  const batchPayload =
    {
      created_on: admin.firestore.FieldValue.serverTimestamp(),
      created_by: "dummy",
      id: batchRef.id,
      status: "NEW",
      type: BatchType.FUND_RELEASE,
    };
  batchRef.create(batchPayload).then(()=>{
    batchRef.get().then((doc)=>{
      const batch = {...doc.data} as Batch;
      functions.logger.debug(JSON.stringify(batch));
      return Promise.resolve(batch);
    });
  });
}
*/

/**
 * prerelease trips
 * @param {[]} payload
 * @return {Promise}
 export async function requestPrereleaseTrips(
    payload:PrereleaseTask[]
):Promise<PrereleaseResponse> {
  functions.logger.info("requestPrereleaseTrips");
  functions.logger.info("processing %s objects", payload.length);
  const response:PrereleaseResponse = {data: []};
  // check if all docs are in NONE
  for (const task of payload) {
    const driver = task.driverId;
    const trips = task.trips;
    functions.logger.info("task for driver %s", driver);
    try {
      try{
        submitToQueue();
      }
      functions.logger.info(result);
    } catch (err) {
      functions.logger.error(err);
      task.result="FAILED";
      response.data.push(task);
    }
  }
  functions.logger.info("response %s", response);
  return Promise.resolve(response);
}
*/
/**
 * createBatchforDriver
 * @param {PrereleaseTask} task to be proceeses
export async function createBatchforDriver(task:PrereleaseTask){
  const trips = task.trips;
  const result = await db.runTransaction(async (tx)=>{
    for (const tripId of trips) {
      functions.logger.info("task tripId %s", tripId);
      const tripRef = db.collection("trips").doc(tripId);
      const tripDoc = await tx.get(tripRef);
      if (tripDoc.get("payout_status") != PayoutStatus.NONE) {
        functions.logger.error("Trip %s not in %s state, current state %s",
            tripId,
            PayoutStatus.NONE,
            tripDoc.get("payout_status"));
        throw new Error("Trip not in NONE state");
      }
    }
    // create new batch id and update trips
    const batchRef = db.collection("batches").doc();
    const createdOn = admin.firestore.FieldValue.serverTimestamp();
    const batchPayload =
  {
    created_by: null,
    created_on: createdOn,
    type: BatchType.FUND_RELEASE,
    action: FundReleaseAction.REQUEST_PRERELEASE,
    input: task,
  };
    tx.create(batchRef, batchPayload);
    // update trip to prerelease
    trips.forEach((tripId)=>{
      functions.logger.info("processing tripId %s", tripId);
      const tripRef = db.collection("trips").doc(tripId);
      const tripUpdatePayload =
    {
      payout_information: FieldValue.arrayUnion(
          {
            batch_ref: batchRef,
            action: FundReleaseAction.REQUEST_PRERELEASE,
            created_on: admin.firestore.Timestamp.now(),
          },
      ),
      payout_status: PayoutStatus.PRERELEASE_REQUESTED,
    };
      tx.update(tripRef, tripUpdatePayload);
    });
  });
  task.result="SUCCESS";
  response.data.push(task);
}
*/

/**
 * submit to queue
 * @param {PrereleaseTask[]} payload
 * @return {Promise}

export async function submitToQueue(
    payload:PrereleaseTask[]
):Promise<string> {
  functions.logger.info("submitToQueue");
  functions.logger.info("processing %s objects", payload.length);
  try {
    return batchTopic.publishMessage(
        {data: Buffer.from(JSON.stringify(payload))}
    );
  } catch (err) {
    functions.logger.error("Error submitting batch job");
    functions.logger.error("Error: " +err);
    return Promise.reject(err);
  }
}
*/

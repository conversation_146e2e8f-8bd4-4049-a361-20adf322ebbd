import * as functions from "firebase-functions";
import { defineString } from "firebase-functions/params";
import _ from "lodash";
import twilio from "twilio";

import { ReceiptMessagePayload } from "../wati/client";

const accountSid = defineString("TWILIO_ACCOUNT_SID");
const authToken = defineString("TWILIO_TOKEN");
const fromPhoneNumber = defineString("TWILIO_PHONE_NUMBER");

/**
 * SMSService
 */
class SMSService {
  /**
   * constructor
   */
  constructor() {
    functions.logger.info(accountSid + ":::" + authToken);
  }

  /**
   *
   * @param {string} phoneNumber
   * @param {string} body
   * @return {promises} message id
   */
  async sendSMSMessage(phoneNumber: string, body: string): Promise<string> {
    functions.logger.info("sendSMSMessage");
    functions.logger.info("body: %s", body);
    const client = twilio(accountSid.value(), authToken.value());
    const message = await client.messages.create({
      body: body,
      to: phoneNumber,
      from: fromPhoneNumber.value(),
    });
    functions.logger.info(message.sid);
    return "SMS:" + message.sid;
  }

  /**
   * @param {string} phoneNumber
   * @param {Record<string, string>} params
   * @return {promise} message id
   */
  async sendSMSReceipt(phoneNumber: string, params: Record<string, string>): Promise<string> {
    const body = this.computeMessage(receiptTemplateEn, params);
    return this.sendSMSMessage(phoneNumber, body);
  }

  /**
   * @param {string} phoneNumber
   * @param {string} language
   * @param {ReceiptMessagePayload} payload
   * @return {promise} message id
   */
  async sendSMSReceipt2(phoneNumber: string, language: string, payload: ReceiptMessagePayload): Promise<string> {
    let body;
    switch (language) {
      case "zh-hk": {
        body = this.computeMessage(receiptTemplateZh, payload);
        break;
      }
      default: {
        body = this.computeMessage(receiptTemplateEn, payload);
        break;
      }
    }
    return this.sendSMSMessage(phoneNumber, body);
  }

  /**
   * computeMessage
   * @param {string} template
   * @param {Record<string, string>} params
   * @return {string} final string
   */
  computeMessage(template: string, params: object): string {
    const complied = _.template(template);
    return complied(params);
  }
}

const receiptTemplateEn = "Receipt : ${ receiptLink }";
const receiptTemplateZh = "收據 : ${ receiptLink }";

export default new SMSService();

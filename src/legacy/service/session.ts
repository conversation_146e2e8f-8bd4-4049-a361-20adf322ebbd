import * as admin from "firebase-admin";
import * as functions from "firebase-functions";

import { Session } from "@legacy/model/session";
import repo from "@legacy/repository/repo";

import { db } from "./driver";
import { Driver } from "../model/driver";

/**
 * SessionService
 */
class SessionService {
  /**
   * setExpectedEndTime
   * @param {string} sessionId
   * @param {date} expectedEndTime
   */
  async setExpectedEndTime(sessionId: string, expectedEndTime: Date): Promise<string> {
    functions.logger.info("update session: %s with new expectedEndTime: %s", sessionId, expectedEndTime);
    return db.runTransaction(async (tx) => {
      const sessionRef = repo.session.doc(sessionId);
      const sessionDoc = await tx.get(sessionRef);
      const session = sessionDoc.data();

      if (session == undefined) {
        throw new ReferenceError(`Session does not exist: ${sessionId}`);
      }
      // set only if session not end
      if (session.endTime) {
        throw new Error("Session has ended");
      }

      // Update session
      const updateSessionDoc: Partial<Session> = {
        expectedEndTime: expectedEndTime,
      };

      tx.set(sessionRef, updateSessionDoc, { merge: true });

      // Update driver
      const driverRef = repo.driver.doc(session.driverId);
      const driverDoc = await driverRef.get();
      const driver = driverDoc.data();
      if (!driver) {
        throw new ReferenceError("Driver not found");
      }

      const updateSession =
        // eslint-disable-next-line max-len
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion, @typescript-eslint/no-non-null-asserted-optional-chain
        { ...driver.session!, expectedEndTime: expectedEndTime };
      const updateDriver: Partial<Driver> = {
        lastExpectedEndTime: expectedEndTime,
        session: updateSession,
      };
      tx.set(driverRef, updateDriver, { merge: true });

      return "Update session successfully";
    });
  }

  /**
   * getSessionById
   * @param {string} sessionId
   * @param {Session} session
   */
  async getSessionById(sessionId: string): Promise<Session | undefined> {
    functions.logger.info("get session by Id: %s", sessionId);
    const sessionRef = repo.session.doc(sessionId);
    const sessionDoc = await sessionRef.get();
    if (!sessionDoc.exists) {
      throw new ReferenceError(`Session doesnt exist: ${sessionId}`);
    }
    return sessionDoc.data();
  }

  /**
   * Unpair
   * @param {string} sessionId session to be unpaired
   * @return {boolean}
   */
  async unpair(sessionId: string): Promise<any | string> {
    functions.logger.info("unpair session: " + sessionId);
    return db
      .runTransaction(async (tx) => {
        const sessionRef = repo.session.doc(sessionId);
        const sessionDoc = await tx.get(sessionRef);
        const session = sessionDoc.data();
        if (!session) {
          functions.logger.error("session id: %s not found", sessionId);
          throw new ReferenceError(`session id: ${sessionId} not found`);
        }
        if (session.endTime != undefined) {
          functions.logger.error("session id: %s has ended already", sessionId);
          throw new Error(`session id: ${sessionId} has ended already`);
        }
        const updateSession: Partial<Session> = {
          endTime: admin.firestore.FieldValue.serverTimestamp(),
        };
        // update session
        tx.set(sessionRef, updateSession, { merge: true });

        // remove session from meter
        const meterRef = session.meterRef;
        tx.update(meterRef, { session: admin.firestore.FieldValue.delete() });

        // remove session from driver
        const driverRef = session.driverRef;
        tx.update(driverRef, { session: admin.firestore.FieldValue.delete() });
        return "session ended";
      })
      .catch((err) => {
        functions.logger.error("session id: %s unpair error", sessionId, err);
        throw err;
      });
  }

  /**
   * Unpair all expired session
   * @param {Date} beforeDateTime
   * @return {Promise} promise true - success; false - failed
   */
  async unpairExpiredSession(beforeDateTime: Date): Promise<boolean> {
    functions.logger.info("unpair expired session before: " + beforeDateTime);
    const firestoreTimestamp = admin.firestore.Timestamp.fromDate(beforeDateTime);
    // query all session before the date
    return repo.session
      .where("end_time", "==", null)
      .where("expected_end_time", "<=", firestoreTimestamp)
      .get()
      .then(async (list) => {
        const sessionDocList = list.docs;
        await Promise.all(
          sessionDocList.map(async (sessionDoc) => {
            const sessionId = sessionDoc.id;
            const session = sessionDoc.data();
            functions.logger.debug(
              "unpair expried session %s, " + "expected end time %s vs current time %s",
              session.id,
              session.expectedEndTime,
              firestoreTimestamp,
            );
            await this.unpair(sessionId);
          }),
        );
        return true;
      });
  }
}

export default new SessionService();

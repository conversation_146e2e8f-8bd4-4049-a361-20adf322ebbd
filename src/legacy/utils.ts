import _ from "lodash";
import moment from "moment";

export const camelizeKeys = (obj: any): object => {
  if (Array.isArray(obj)) {
    return obj.map((v) => camelizeKeys(v));
  } else if (obj != null && obj.constructor === Object) {
    return Object.keys(obj).reduce(
      (result, key) => ({
        ...result,
        [_.camelCase(key)]: camelize<PERSON>eys(obj[key]),
      }),
      {},
    );
  }
  return obj;
};

export const snakeKeys = (obj: any): object => {
  // functions.logger.info("before snake: %s", JSON.stringify(obj));
  if (Array.isArray(obj)) {
    return obj.map((v) => snakeKeys(v));
  } else if (obj != null && obj.constructor === Object) {
    return Object.keys(obj).reduce(
      (result, key) => ({
        ...result,
        [snakeCase(key)]: snakeKeys(obj[key]),
      }),
      {},
    );
  }
  // functions.logger.info("after snake: %s", JSON.stringify(obj));
  return obj;
};

/**
 * Cannot use lodash one as it converts dot to _
 * snakeCase
 * @param {string} input
 * @return {string} snake cased string
 */
const snakeCase = (input: string) => {
  // eslint-disable-next-line no-empty-character-class
  return input
    .replace(/\W+/g, " ")
    .split(/ |\B(?=[A-Z])/)
    .map((word) => word.toLowerCase())
    .join("_");
};

export type Subset<K> = {
  [attr in keyof K]?: K[attr] extends object
    ? Subset<K[attr]>
    : K[attr] extends object | null
    ? Subset<K[attr]> | null
    : K[attr] extends object | null | undefined
    ? Subset<K[attr]> | null | undefined
    : K[attr];
};
/**
 *
 * @param {string} durationString
 * @return {moment}
 */
export function parseDuration(durationString: string): moment.Duration {
  // eslint-disable-next-line max-len
  const durationRegex = /(-?\d+)(\s*)([a-z]+)\b/gi;
  let match = null;
  const duration = moment.duration();

  while ((match = durationRegex.exec(durationString)) !== null) {
    const value = parseInt(match[1]);
    const unit = match[3].toLowerCase();
    switch (unit) {
      case "years":
      case "year":
      case "yrs":
      case "yr":
        duration.add(value, "years");
        break;
      case "months":
      case "month":
      case "mos":
      case "mo":
        duration.add(value, "months");
        break;
      case "weeks":
      case "week":
      case "wks":
      case "wk":
        duration.add(value, "weeks");
        break;
      case "days":
      case "day":
      case "d":
        duration.add(value, "days");
        break;
      case "hours":
      case "hour":
      case "hrs":
      case "hr":
      case "h":
        duration.add(value, "hours");
        break;
      case "minutes":
      case "minute":
      case "mins":
      case "min":
      case "m":
        duration.add(value, "minutes");
        break;
      case "seconds":
      case "second":
      case "secs":
      case "sec":
      case "s":
        duration.add(value, "seconds");
        break;
      default:
        throw new Error(`Invalid duration unit: ${unit}`);
    }
  }

  return duration;
}

import { CollectionReference, DocumentData, Timestamp } from "firebase-admin/firestore";

import { FundReleaseBatch } from "@legacy/model/batch/fund-release";
import { Configuration } from "@legacy/model/configuration";
import { Driver } from "@legacy/model/driver";
import { Meter, MeterTrip } from "@legacy/model/meter";
import { Session } from "@legacy/model/session";
import { Trip } from "@legacy/model/trip";
import { db } from "@legacy/service/driver";

import { camelizeKeys, snakeKeys } from "../utils";
// import * as functions from "firebase-functions";

// Generic converter
export const genericConverter = <T>() => ({
  toFirestore(data: Partial<T>) {
    return dateToTimestamp<T>(snakeKeys(data));
  },
  fromFirestore(snapshot: FirebaseFirestore.QueryDocumentSnapshot): T {
    /**
    functions.logger.debug("snapshot.data(): %s",
        JSON.stringify(snapshot.data()));

    functions.logger.debug("camelcase: %s",
        camelizeKeys(snapshot.data()));
*/
    return timestampToDate<T>(camelizeKeys(snapshot.data()));
  },
});

const dataPoint = <T>(collectionPath: string): CollectionReference<T> =>
  db.collection(collectionPath).withConverter(genericConverter()) as CollectionReference<T>;

const repo = {
  batch: dataPoint<FundReleaseBatch>("batches"),
  // Trip
  adjustmentTrip: (tripId: string) => dataPoint<Trip>(`trips/${tripId}/adjustments`),
  trip: dataPoint<Trip>("trips"),
  // Meter
  meter: dataPoint<Meter>("meters"),
  configuration: dataPoint<Configuration>("configurations"),
  meterTrip: (meterId: string) => dataPoint<MeterTrip>(`meters/${meterId}/trips`),
  // Driver
  driver: dataPoint<Driver>("drivers"),
  driverSession: (driverId: string) => dataPoint<Session>(`drivers/${driverId}/sessions`),
  driverSessionTrip: (driverId: string, sessionId: string) =>
    dataPoint<Trip>(`drivers/${driverId}/sessions/${sessionId}/trips`),
  driverTrip: (driverId: string) => dataPoint<Trip>(`drivers/${driverId}/trips`),
  session: dataPoint<Session>("sessions"),
};

/**
 * timestampToDate
 * @param {DocumentData} obj
 * @return {T}
 */
function timestampToDate<T>(obj: DocumentData): T {
  for (const [key, value] of Object.entries(obj)) {
    if (value instanceof Timestamp) {
      obj[key] = value.toDate();
    }
  }
  return obj as T;
}

/**
 * dateToTimestamp
 * @param {DocumentData} obj
 * @return {T}
 */
function dateToTimestamp<T>(obj: Partial<T>): DocumentData {
  for (const [key, value] of Object.entries(obj)) {
    if (value instanceof Date) {
      obj[key as keyof T] = Timestamp.fromDate(value) as unknown as T[keyof T];
    }
  }
  return obj as DocumentData;
}

export default repo;

import { DocumentReference, FieldValue } from "firebase-admin/firestore";

import { PaymentInformationStatus } from "@nest/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "@nest/modules/payment/dto/paymentInformationType.dto";

import { Session } from "./session";

interface Trip {
  id: string;
  language?: string;
  // $$
  fare: number;
  extra?: number;
  tripTotal?: number;
  dashTips?: number;
  dashFee?: number;
  total: number;
  distance: number;

  // time
  tripStart?: Date;
  tripEnd?: Date;

  // locations
  locationStartAddress: string;
  locationStart: string;
  locationEndAddress: string;
  locationEnd: string;

  meterId: string;

  passengerInformation?: PassengerInformation;
  // Payment Information
  paymentInformation?: PaymentInformationAudit[];
  paymentType: PaymentType;
  paymentStatus?: PaymentStatus | FieldValue;
  // Payout information
  payoutStatus?: PayoutStatus;
  payoutInformation?: PayoutInformationAudit[];

  // Receipt
  receiptId?: string;
  receiptStatus?: ReceiptStatus | FieldValue;

  // Adjustment field
  parentTrip?: DocumentReference;

  // metadata
  driver?: Driver;
  licensePlate: string;
  session?: Session;
  adjustment?: number; // sum of all adjustment
  adjustmentInformation?: AdjustmentInformation;

  authTranId?: string;
  captureTranId?: string;

  // Visibility
  expiresAt?: Date;
  showToDriver?: boolean;

  // tag
  tags?: Tags;
}

interface Tags {
  [key: string]: TagMetadata;
}

interface TagMetadata {
  value?: any;
  createdOn?: Date | FieldValue;
  createdBy?: string;
}

interface Driver {
  id: string;
}

interface AdjustmentInformation {
  note?: string;
  reason?: AdjustmentReason;
}
class PassengerInformation {
  phone?: string;
}

interface PayoutInformationAudit {
  batchRef?: DocumentReference;
  type: PayoutInformationAuditType;
  createdOn: Date;
}

interface PaymentInformationAudit {
  type: PaymentInformationType;
  createdOn?: Date;
  creationTime?: Date;
  body: string;
  status: PaymentInformationStatus;
}

interface PayoutInfo {
  payoutDate: Date;
}

enum PaymentStatus {
  CAPTURED = "CAPTURED",
  PROCESSING = "PROCESSING",
  FAILED = "FAILED",
}

enum ReceiptStatus {
  SENT = "SENT",
  PROCESSING = "PROCESSING",
  FAILED = "FAILED",
}

enum PayoutStatus {
  PRERELEASE_REQUESTED = "PRERELEASE_REQUESTED",
  PRERLEASED = "PRERELEASED",
  RELEASE_REQUESTED = "RELEASE_REQUESTED",
  RELEASED = "RELEASED",
  NONE = "NONE",
}
enum PayoutInformationAuditType {
  PRERELEASE_REQUEST = "PRERELEASE_REQUEST",
  PRERELEASE = "PRERELEASE",
  RELEASE_REQUEST = "RELEASE_REQUEST",
  RELEASE = "RELEASE",
}

enum PaymentType {
  DASH = "DASH",
  DASH_ADJUSTMENT = "DASH_ADJUSTMENT",
}

enum AdjustmentReason {
  OVERPAID = "OVERPAID",
  UNDERPAID = "UNDERPAID",
  OTHER = "OTHER",
}

export {
  Trip,
  PaymentType,
  AdjustmentReason,
  PaymentInformationAudit,
  PaymentStatus,
  ReceiptStatus,
  PayoutStatus,
  PayoutInformationAudit,
  PayoutInformationAuditType,
  TagMetadata,
  PassengerInformation,
};

export interface TrimDownTrip {
  id: string;
  language?: string;
  // $$
  fare?: number;
  extra?: number;
  tripTotal?: number;
  dashTips?: number;
  dashFee?: number;
  total?: number;
  distance: number;

  // time
  tripStart?: Date;
  tripEnd?: Date;
  locationStartAddress: string;
  locationStart: string;
  locationEndAddress: string;
  locationEnd: string;

  paymentCardInformation: PaymentCardInformation | undefined;
  passengerInformation?: PassengerInformation;
  paymentType: PaymentType;
  payoutStatus?: PayoutStatus;
  payoutInformation?: PayoutInformationAudit[];
  /** hack */
  payoutInfo?: PayoutInfo;
  parentTrip?: DocumentReference;

  licensePlate: string;
  adjustment?: number; // sum of all adjustment
  adjustmentInformation?: AdjustmentInformation;
}

export interface PaymentCardInformation {
  cardType: CardType;
  maskedPan: string;
}

export enum CardType {
  VISA = "VISA",
  MASTER = "MASTER",
  UNIONPAY = "UNIONPAY",
}

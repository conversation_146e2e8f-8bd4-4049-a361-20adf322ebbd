import { DocumentReference, FieldValue } from "firebase-admin/firestore";

import { Driver } from "./driver";
import { MeterSettings } from "./meter";

export class Session {
  id: string;
  driverId: string;
  driverRef: DocumentReference;
  // only used to set in meter
  driver?: Driver;
  licensePlate: string;
  meterId?: string;
  meterRef: DocumentReference;
  meterSettings: MeterSettings | undefined;
  startTime: Date | FieldValue;
  endTime?: Date | FieldValue | null; // null as default for query
  expectedEndTime?: Date;
  shift: string;
  // summary
  cashCount?: number;
  dashCount?: number;
  count?: number;
  cashTotal?: number;
  dashTotal?: number;
  dashTipsTotal?: number;
  totalDistance?: number;
  total?: number;
}

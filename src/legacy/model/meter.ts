import { Session } from "./session";

export interface Meter {
  // License Plate
  licensePlate: string;
  // Meter Id from hardware
  meterId: string;
  session: Session;
  // setting map
  settings: MeterSettings;
}

export interface MeterTrip {
  id: string;
}

export interface MeterSettings {
  operatingArea: OperatingArea | undefined;
}

export enum OperatingArea {
  "URBAN" = "URBAN",
  "LANTAU" = "LANTAU",
  "NT" = "NT",
}

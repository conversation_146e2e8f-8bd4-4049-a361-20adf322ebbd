import Decimal from "decimal.js";
import moment from "moment";
/**
 * Bankfile class for generating Bankfile to upload to bank
 */
class BankFile {
  fileDate = moment(new Date()).format("DDMMYYYY");
  header = "HEADER," + this.fileDate + ",HKVISM01,VIS MOBILITY LIMITED";
  entries: BankFileEntry[] = [];
  total = 0;
  footerTemplate = "TRAILER,${count},${total}";

  /**
   * getContent
   * @return {string} file content
   */
  getContentAndFooter(): string {
    let content = "";
    let count = 0;
    let runningTotal = 0;
    for (const entry of this.entries) {
      content +=
        "PAYMENT,BPY,*********,HKD,,HKD,," +
        this.fileDate +
        ",,," +
        entry.accountName +
        ",,,,," +
        entry.accountNumber +
        ",," +
        entry.bankCode +
        "," +
        entry.branchCode +
        ",,,,,,,,," +
        entry.amount +
        ",,,,,20,," +
        entry.phoneNumber +
        ",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,," +
        "\n";
      runningTotal += entry.amount || 0;
      count++;
    }
    const footer = "TRAILER," + count + "," + runningTotal;
    return content + footer;
  }

  /**
   * FPS
   */
  /**
   * getContent
   * @return {string} file content
   */
  getContentAndFooterForFPS(): string {
    let content = "";
    let count = 0;
    let runningTotal = new Decimal(0);
    for (const entry of this.entries) {
      content +=
        "PAYMENT,GPP,*********,HKD," +
        entry.phoneNumber +
        ",HKD,," +
        this.fileDate +
        ",,," +
        entry.accountName +
        ",,,,," +
        entry.branchCode +
        entry.accountNumber +
        ",," +
        entry.bankCode +
        ",,,,,,,,,," +
        entry.amount +
        ",,,,,20,," +
        entry.phoneNumber +
        ",,,,B,,,,CXBSNS,,,,,,,,,,,,,,,,,,,,,,,,," +
        "\n";
      runningTotal = runningTotal.plus(entry.amount || 0);
      count++;
    }
    const footer = "TRAILER," + count + "," + runningTotal;
    return content + footer;
  }

  /**
   * @return {string} file string
   */
  toString(): string {
    return this.header + "\n" + this.getContentAndFooter();
  }

  /**
   * @return {string} file string
   */
  toStringFPS(): string {
    return this.header + "\n" + this.getContentAndFooterForFPS();
  }
}

/**
 * BankFileEntry
 */
class BankFileEntry {
  paymentDate: Date;
  accountName = "";
  accountNumber = 0;
  bankCode = 0;
  branchCode = 0;
  amount = 0;
  phoneNumber = "";

  /**
   * @param {string} accountName
   * @param {number} accountNumber
   * @param {number} bankCode
   * @param {number} branchCode
   * @param {number} amount
   * @param {string} phoneNumber
   * default constructor
   */
  constructor(
    accountName: string,
    accountNumber: number,
    bankCode: number,
    branchCode: number,
    amount: number,
    phoneNumber: string,
  ) {
    this.paymentDate = new Date();
    this.accountName = accountName;
    this.accountNumber = accountNumber;
    this.bankCode = bankCode;
    this.branchCode = branchCode;
    this.amount = amount;
    this.phoneNumber = phoneNumber;
  }
}

export { BankFile, BankFileEntry };

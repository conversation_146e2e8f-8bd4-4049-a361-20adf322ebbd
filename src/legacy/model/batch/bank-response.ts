/**
 * Bank Response mapping
 * An xlsx is given back to us and we need to process it
 */
export interface BankResponseLineItem {
  fileUploadDate: Date; // col 1
  paymentDate: Date; // col 2
  driverId: string; // col 6
  originalBatchFileName: string; // col 8
  amount: number; // col 10
  status: BankResponseLineItemStatus; // col 28 - "Bank Rejected", "Received"
}

export enum BankResponseLineItemStatus {
  RECEIVED = "Received",
  COMPLETED = "Completed",
  REJECTED = "Bank Rejected",
}

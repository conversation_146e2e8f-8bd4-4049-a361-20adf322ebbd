import * as admin from "firebase-admin";
import { DocumentReference } from "firebase-admin/firestore";

import { FundReleaseItem, FundReleaseRequestType } from "../../api/batch";

/**
 * batch related interface
 */
// Batch Job
export interface FundReleaseBatch {
  createdOn: admin.firestore.FieldValue;
  completedOn?: admin.firestore.FieldValue;
  createdBy: string;
  type: FundReleaseRequestType;
  id: string;
  status: string;
  file?: string;
  originalInput: FundReleaseItem[];
  result: FundReleaseItem[];
  child?: FundReleaseBatchChild[];
}

export enum FundReleaseBatchStatus {
  NEW = "NEW",
  COMPLETED = "COMPLETED",
}

export interface FundReleaseBatchChild {
  id: string;
  parent: DocumentReference;
}

export enum FundReleaseType {
  PRERELEASE = "PRERELEASE",
  RELEASE = "RELEASE",
}

export enum PayoutStatus {
  NONE = "NONE",
}

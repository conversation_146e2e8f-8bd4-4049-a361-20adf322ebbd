import { Injectable } from "@nestjs/common";
import { get } from "lodash";

import repo from "@legacy/repository/repo";

import { Configuration, ConfigurationSystem } from "../model/configuration";

@Injectable()
/**
 * ConfigurationService
 */
export class ConfigurationService {
  /**
   * getConfigurationById
   * @param {string} system
   * @return {Map} config map
   */
  async getConfigurationBySystem(system: string): Promise<Configuration | undefined> {
    const configRef = repo.configuration.doc(system);
    const configDoc = await configRef.get();
    if (!configDoc.exists) {
      throw new ReferenceError(`System does not exist ${system}`);
    }
    const config = configDoc.data();
    return config;
  }
  /**
   * getConfigurationByKey
   * @param {string} system
   * @param {string} key
   * @return {any} config value
   */
  async getConfigurationByKey(system: string, key: string): Promise<any> {
    const configRef = repo.configuration.doc(system);
    const configDoc = await configRef.get();
    if (!configDoc.exists) {
      throw new ReferenceError(`System does not exist ${system}`);
    }
    const config = configDoc.data();
    if (config) {
      return get(config, key);
    }
    return null;
  }

  /**
   * getServerConfigurationByKey
   * @param {string} key
   * @return {any} config value
   */
  async getServerConfigurationByKey(key: string): Promise<any> {
    const config = await this.getConfigurationByKey(ConfigurationSystem.SERVER, key);
    return config;
  }
}

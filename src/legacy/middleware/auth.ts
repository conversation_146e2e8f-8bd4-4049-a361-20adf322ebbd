import { Request, Response, NextFunction } from "express";
import admin from "firebase-admin";
import * as functions from "firebase-functions";

export const authCheckMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  // Get auth header
  const authHeader = req.headers.authorization;

  // Check if the auth header is presents
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    res.status(401).send("Unauthorized");
    return;
  }

  // Extract and check token
  const idToken = authHeader.split("Bearer ")[1];

  try {
    const decodedIdToken = await admin.auth().verifyIdToken(idToken);
    req.user = decodedIdToken;
    functions.logger.info("Auth User %s", JSON.stringify(decodedIdToken));
  } catch (err) {
    functions.logger.error("Error verifying ID token: %s", err);
    res.status(401).send("Unauthorized");
  }
  next();
};

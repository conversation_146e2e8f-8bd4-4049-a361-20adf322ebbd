import { Request, Response, NextFunction } from "express";
import * as functions from "firebase-functions";

import { camelizeKeys, snakeKeys } from "@legacy/utils";

export const camelizeMiddleware = (req: Request, res: Response, next: NextFunction) => {
  if (req.body) {
    functions.logger.debug("before camelize %s", JSON.stringify(req.body));
    req.body = camelizeKeys(req.body);
    functions.logger.debug("after camelize %s", JSON.stringify(req.body));
  }

  const originalJson = res.json;
  res.json = (body) => {
    if (req.originalUrl.startsWith("/trips")) {
      return originalJson.call(res, body);
    }
    const snakeCaseBody = snakeKeys(body);
    return originalJson.call(res, snakeCaseBody);
  };
  next();
};

import { Request, Response, NextFunction } from "express";
import * as functions from "firebase-functions";

export const loggerMiddleware = (req: Request, res: Response, next: NextFunction) => {
  functions.logger.debug("%s [STARTED]", req.originalUrl);
  res.on("finish", () => {
    functions.logger.debug("%s [FINISHED]", req.originalUrl);
  });
  res.on("close", () => {
    functions.logger.debug("%s [CLOSED]", req.originalUrl);
  });
  next();
};

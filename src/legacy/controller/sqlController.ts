import { sqladmin_v1 } from "@googleapis/sqladmin";
import { GoogleAuth } from "googleapis-common";

import loggerUtils from "@nest/modules/utils/utils/logger.utils";

import { SqlOnOffRequest } from "../api/sql";
/**
 * SqlController
 */
class SqlController {
  /**
   * restartOrStopSqlInstance
   * @param payload SqlOnOffRequest
   */
  async restartOrStopSqlInstance(payload: SqlOnOffRequest) {
    const auth: GoogleAuth = new GoogleAuth();
    const sqladmin: sqladmin_v1.Sqladmin = new sqladmin_v1.Sqladmin({ auth });
    try {
      const listParams: sqladmin_v1.Params$Resource$Instances$List = { project: payload.projectId };
      const res = await sqladmin.instances.list(listParams);
      const instanceList: sqladmin_v1.Schema$InstancesListResponse = res.data;
      if (instanceList.items && instanceList.items.length > 0) {
        const instance = instanceList.items.find((item) => item.name === payload.instanceId);
        if (instance) {
          const patchParams: sqladmin_v1.Params$Resource$Instances$Patch = {
            instance: payload.instanceId,
            project: payload.projectId,
            requestBody: {
              settings: {
                activationPolicy: payload.policy,
              },
            },
          };
          sqladmin.instances.patch(patchParams);
        }
      }
    } catch (e) {
      loggerUtils.error("restartOrStopSqlInstance error", { payload }, e as Error);
    }
  }
}

export default new SqlController();

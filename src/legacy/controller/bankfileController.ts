import { Request, Response } from "express";

import BankFileService from "../service/bankfile";
/**
 * BankFileController
 */
class BankFileController {
  /**
   * processFile
   * @param {Request} req
   * @param {Response} res
   */
  async processFile(req: Request, res: Response) {
    const filePath = req.body.filePath;
    BankFileService.processResponseFile(filePath);
    res.status(200).json({});
  }
}

export default new BankFileController();

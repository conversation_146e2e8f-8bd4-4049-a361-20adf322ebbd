import { Request, Response } from "express";
import * as functions from "firebase-functions";

import { SMSMessagingRequest, WhatsappMessagingRequest } from "../api/messagingRequest";
import SMSService from "../service/sms";
import WatiClient, { WhatsappTemplate } from "../wati/client";

/**
 * MessagingController
 */
class MessagingController {
  /**
   * sendSMS
   * @param {Request} req
   * @param {Response} res
   */
  async sendSMS(req: Request, res: Response) {
    const phoneNumber = req.params.phone;
    const body = req.body as SMSMessagingRequest;
    functions.logger.info("sendSMS body: %s", JSON.stringify(body));
    const messageId = await SMSService.sendSMSMessage(phoneNumber, body.body);
    const responseBody = { id: messageId };
    res.status(200).json(responseBody);
  }

  /**
   * sendSMS
   * @param {Request} req
   * @param {Response} res
   */
  async sendWhatsapp(req: Request, res: Response) {
    const phoneNumber = req.params.phone;
    const body = req.body as WhatsappMessagingRequest;
    functions.logger.info(body.templateName);
    const result = await WatiClient.sendWhatsappMessage(
      phoneNumber,
      // body.templateName,
      WhatsappTemplate[body.templateName as keyof typeof WhatsappTemplate],
      body.params,
    );
    // const data = result.data;
    // const statusCode = result.status;
    // functions.logger.info("result status %s", statusCode);
    // functions.logger.info("result data %s", data);
    const responseBody = { result: result };
    res.status(200).json(responseBody);
  }
}

export default new MessagingController();

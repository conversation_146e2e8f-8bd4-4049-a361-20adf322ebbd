import axios from "axios";
import { Request, Response } from "express";
import { getAuth } from "firebase-admin/auth";
import * as functions from "firebase-functions";
import { defineString } from "firebase-functions/params";
import { decode, verify } from "jws";

import { Role } from "@nest/modules/auth/types";

// import {auth} from "../service/driver";
// import * as admin from "firebase-admin";

import { ReceiptTokenRequest } from "../api/security";
// import {Claim} from "../model/user";

// import SecurityService from "../service/security";
// import {submitToQueue} from "../service/driver";
// import {camelizeKeys} from "../utils";

const webAPIKey = defineString("WEB_API_KEY");

/**
 * Reformats the base64 encoded key.
 * When manually copying the key, some whitespace is changed,
 * making the key non-compliant with the spec.
 * @param {string} pk
 * @return {string}
 */
function normalizePublicKey(pk: string): string {
  return (
    "-----BEGIN PUBLIC KEY-----\n" +
    pk.replace(/\\s|-----(BEGIN|END) PUBLIC KEY-----/g, "") +
    "\n-----END PUBLIC KEY-----\n"
  );
}

const publicKey =
  // eslint-disable-next-line no-multi-str
  "MIICIjANBgkqhkiG9w0BAQEFAAOC\
Ag8AMIICCgKCAgEAz0iqJpro3ojTifm3pXo1AYQ/3f3AV5fwmpZU0vJ\
zjxFkZ4jZfiKcxbOjUB5duDUxPCzifX81DWkHlX+90wwhQc40HBP37o\
dOwuMM782I55YmgF44d+svBzUWxJ9pWglYuqI2X9gtZg15E+Cr+deeN\
7Br/xku93vsv9u4BoANo8gT20p3Uzp6U0X4ixNWPnCBPvj+iaF69qMT\
4knPRl8OB9QgIYpzSxA4FblgONx121ijaVUzZL+wSl8M+vtHxJD6ZTw\
vGXlAuBsK7Y2fu9V8p1NOmxoZJCKLGLGtny9eAd0L++CrOZ6ViXfwoz\
29n878t4ZdFEm3qh3Zo60MvpuxiFtj4SVq0rx3FEjCZU8gGZH1VJUsW\
LWipq2L+khrf0MpspsNDjmE1bgHG5jES6AafrVa6mwVeE7gbGCLXyKH\
+BCPe4TnjBNlX4ryR5t58w13uQSS4wedNxPsBj+P70VahVKna3HC5NH\
ds60YJ1ME4CJJfJxLyPvJwPUWn08Ct2iNaqs56+cgVNR0MdanHUl5XB\
U6v6nHWZtYPau243MimQr6YU08wWVrXBnLkxBjLBqyAWg4UMnL++rqq\
cRIiPDigOSf5ASXXlaDvDdVASX73KVTX3ZHQUgPjoS0XG5Dm4x1qs9E\
y2ROmSxaojsx3EmMvDFkf7GzJ7U7PQ6nb9/3JJkCAwEAAQ==";

/**
 * SecurityController
 */
class SecurityController {
  /**
   * @param {Request} req
   * @param {Response} res
   */
  async getAccessToken(req: Request, res: Response) {
    functions.logger.info("getAccessToken");
    const deviceToken = req.body;
    const signature = decode(deviceToken);
    if (!signature) {
      res.status(401).send({
        errorCode: 3,
        message: "Invalid JWT!",
      });
      return;
    }
    const { header, payload } = signature;
    functions.logger.debug("jwt header: %s", JSON.stringify(header));
    functions.logger.debug("jwt payload: %s", JSON.stringify(payload));
    const deviceId = header.kid || ""; // kid = license plate now
    functions.logger.info("deviceId %s", deviceId);
    const alg = "RS256";
    // Verify the validity of device-generated JWT.
    const verifyResult = verify(deviceToken, alg, normalizePublicKey(publicKey));
    if (!verifyResult || header.alg !== alg) {
      res.status(401).send({
        errorCode: 4,
        message: "Invalid JWT!",
      });
      return;
    }

    const customToken = await getAuth().createCustomToken(deviceId, {
      meterDevice: true,
    });
    /**
    const user =
        await signInWithCustomToken(auth, customToken)
            .then((userCredential) => {
              const user = userCredential.user;
              functions.logger.info("user has signed in %s", user);
              return userCredential;
            });
    res.status(200).send(user);
    */
    functions.logger.debug("calling identity api");
    functions.logger.debug("custom token: %s", JSON.stringify(customToken));
    functions.logger.debug("key: %s", webAPIKey.value());
    const idTokenResponse = await axios.post<SignInWithCustomTokenResponse>(
      "https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key=" + webAPIKey.value(),
      { token: customToken, returnSecureToken: true },
    );
    const responseJson = {
      custom_token: customToken,
      id_token: idTokenResponse.data.idToken,
    };
    res.status(200).json(responseJson);
  }

  /**
   * testAuth
   * @param {Request} req
   * @param {Response} res
   */
  async testAuth(req: Request, res: Response) {
    if (
      (!req.headers.authorization || !req.headers.authorization.startsWith("Bearer ")) &&
      !(req.cookies && req.cookies.__session)
    ) {
      res.status(403).send("Unauthorized");
      return;
    }
    let idToken;
    if (req.headers.authorization && req.headers.authorization.startsWith("Bearer ")) {
      functions.logger.debug("Found token");
      // Read the ID Token from the Authorization header.
      idToken = req.headers.authorization.split("Bearer ")[1];
    } else if (req.cookies) {
      console.log('Found "__session" cookie');
      // Read the ID Token from cookie.
      idToken = req.cookies.__session;
    } else {
      // No cookie
      res.status(403).send("Unauthorized");
      return;
    }

    try {
      const decodedIdToken = await getAuth().verifyIdToken(idToken);
      console.log("ID Token correctly decoded", decodedIdToken);
      res.status(200).json(JSON.stringify(decodedIdToken));
      return;
    } catch (error) {
      console.error("Error while verifying Firebase ID token:", error);
      res.status(403).send("Unauthorized");
      return;
    }
  }

  /**
   * @param {Request} req
   * @param {Response} res
   */
  async verifyIdToken(req: Request, res: Response) {
    functions.logger.info("verifyIdToken called");
    const idToken = req.body;
    const decodedIdToken = await getAuth().verifyIdToken(idToken);
    functions.logger.info(decodedIdToken.uid);
    const responseJson = {
      uid: decodedIdToken.uid,
      auth_time: decodedIdToken.auth_time,
      email: decodedIdToken.email,
    };
    res.status(200).json(responseJson);
  }

  /**
   * @param {Request} req
   * @param {Response} res
   */
  async setCustomClaim(req: Request, res: Response) {
    const uid = req.params.uid;
    // const uid = userClaimRequest.uid;
    const claims = req.body.claims;
    functions.logger.debug("setting uid, claims: %s, %s", uid, JSON.stringify(claims));
    await getAuth().setCustomUserClaims(uid, { ...claims, role: Role.METER });
    res.status(200).json({});
  }

  /**
   * @param {Request} req
   * @param {Response} res
   */
  async getUser(req: Request, res: Response) {
    functions.logger.info("getUser");
    const uid = req.params.uid;
    functions.logger.debug("getting claim for uid %s, %s", uid);
    const user = await getAuth().getUser(uid);
    res.status(200).json(JSON.stringify(user));
  }

  /**
   * @param {Request} req
   * @param {Response} res
   */
  async genRecieptToken(req: Request, res: Response) {
    functions.logger.info("getUser");
    const tripId = req.params.tripId;
    const receiptTokenRequest = req.body as ReceiptTokenRequest;
    functions.logger.debug("getting claim for tripId %s, %s", tripId, receiptTokenRequest.phoneNumber);
    const customToken = getAuth().createCustomToken("receipt", {
      trip: tripId,
    });
    res.status(200).json(customToken);
  }
}

/** Response body of `signInWithCustomToken` REST API. */
interface SignInWithCustomTokenResponse {
  idToken: string;
  refreshToken: string;
  expiresIn: number;
}

export default new SecurityController();

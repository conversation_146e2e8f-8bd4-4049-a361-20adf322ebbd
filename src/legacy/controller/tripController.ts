import Ajv from "ajv";
import { Request, Response } from "express";
import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import moment from "moment";

import { AddTagRequest, CreateAdjustmentRequest, CreateAdjustmentRequestSchema } from "../api/trip";
import { TagMetadata } from "../model/trip";
import TripService from "../service/trip";
import { camelizeKeys } from "../utils";

const ajv = new Ajv();
const validate = ajv.compile<CreateAdjustmentRequest>(CreateAdjustmentRequestSchema);
/**
 * BatchController
 */
class TripController {
  /** hack */
  /**
   * tripReceiptResend
   * @param {Request} req
   * @param {Response} res
   */
  async updateTransactionId(req: Request, res: Response) {
    functions.logger.info("updateTransactionId called");
    const tripId = req.params.trip_id;
    const result = await TripService.updateTransactionId(tripId);
    functions.logger.info("updateTransactionId result, %s", result);
    res.status(200).json({ result: result });
  }
  /**
   * tripReceiptResend
   * @param {Request} req
   * @param {Response} res
   */
  async tripReceiptResend(req: Request, res: Response) {
    functions.logger.info("tripReceiptResend called");
    const tripId = req.params.trip_id;
    functions.logger.debug("trip id: %s", tripId);
    const body = req.body;
    functions.logger.debug(JSON.stringify(body));
    try {
      const result = await TripService.sendReceipt(tripId);
      res.status(200).json({ messageId: result });
    } catch (err) {
      res.status(400).json({ messageId: err });
    }
  }
  /**
   * tripReceiptResend
   * @param {Request} req
   * @param {Response} res
   */
  async createAdjustment(req: Request, res: Response) {
    const request = req.body as CreateAdjustmentRequest;
    const tripId = req.params.trip_id;
    functions.logger.debug(request);
    if (validate(request)) {
      await TripService.createAdjustmentTrip(tripId, request);
      res.status(200).json({});
    } else {
      res.status(400).json(validate.errors);
    }
  }

  /**
   * captureTransaction
   * @param {Request} req
   * @param {Response} res
   */
  async captureTransaction(req: Request, res: Response) {
    const tripId = req.params.trip_id;
    functions.logger.info("capture for tripId: %s", tripId);
    const result = await TripService.captureTrip(tripId);
    res.status(200).json({ result: result });
  }

  /**
   * getTripById
   * @param {Request} req
   * @param {Response} res
   */
  async getTrimDownTripById(req: Request, res: Response) {
    const tripId = req.params.trip_id;
    const result = await TripService.getTrimDownTripById(tripId);
    functions.logger.info(camelizeKeys({ result: result }));
    if (result == undefined) {
      res.status(404).json({ result: "NOT FOUND" });
    } else {
      res.status(200).json(camelizeKeys({ result: result }));
    }
  }

  /**
   * getUncapturedTrips
   * @param {Request} req
   * @param {Response} res
   */
  async getUncapturedTrips(req: Request, res: Response) {
    const from = req.query.from as string;
    const to = req.query.to as string;
    functions.logger.debug("getUncapturedTrips range: %s - %s", from, to);
    const fromDate = moment(from).toDate();
    const toDate = moment(to).toDate();
    let result = null;
    try {
      result = await TripService.getUncapturedTrips(fromDate, toDate);
    } catch (err) {
      functions.logger.error("getUncapturedTrips err: %s", err);
      res.status(500).json({ error: err });
    }
    res.status(200).json({ result: result });
  }
  /**
   * Add tag to a trip
   * @param {Request} req
   * @param {Response} res
   */
  async tagTrip(req: Request, res: Response) {
    const tripId = req.params.trip_id;
    const addTagRequest = req.body as AddTagRequest;
    const tagMetadata: TagMetadata = {
      value: addTagRequest.value,
      createdOn: admin.firestore.FieldValue.serverTimestamp(),
      createdBy: "SYSTEM",
    };
    try {
      const trip = TripService.tagTrip(tripId, addTagRequest.name, tagMetadata);
      res.status(200).json({ result: trip });
    } catch (err) {
      if (err instanceof ReferenceError) {
        res.status(404).json({ error: err });
      } else {
        res.status(500).json({ error: err });
      }
    }
  }
}

export default new TripController();

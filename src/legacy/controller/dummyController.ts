import { Request, Response } from "express";

import I18nService from "../service/i18n";

/**
 * dummyController
 */
class DummyController {
  /**
   * process
   * @param {Request} req
   * @param {Response} res
   */
  async process(req: Request, res: Response) {
    const translation = req.body.translation;
    const params = req.body.param;
    const text = I18nService.getText(translation, params, "ENGLISH");
    res.status(200).json({ text: text });
  }
}

export default new DummyController();

import { Request, Response } from "express";
import { DocumentSnapshot } from "firebase-admin/firestore";
import * as functions from "firebase-functions";
import { Change, EventContext } from "firebase-functions";

import { BatchRequest, BatchResponse, BatchType, FundReleaseRequestType } from "../api/batch";
import BatchService from "../service/batch";
// import {submitToQueue} from "../service/driver";
// import {camelizeKeys} from "../utils";

/**
 * BatchController
 */
class BatchController {
  /**
   * batchHandler
   * @param {Request} req
   * @param {Response} res
   */
  async batchRequestHandler(req: Request, res: Response) {
    functions.logger.info("batchHandler called");
    const fundRelesaeRequest = req.body as BatchRequest;
    functions.logger.debug(JSON.stringify(fundRelesaeRequest));
    let response: BatchResponse;
    switch (fundRelesaeRequest.input.action) {
      case FundReleaseRequestType.REQUEST_PRERELEASE: {
        functions.logger.debug("PRERELEASE_REQUEST detected");
        response = await BatchService.processPrereleaseRequest(fundRelesaeRequest.input.payload);
        break;
      }
      case FundReleaseRequestType.REQUEST_RELEASE: {
        /** response =
          await BatchService.processReleaseRequest(
              fundRelesaeRequest.input.payload.releaseItems,
              ""
          );
          */
        response = {
          type: BatchType.FUND_RELEASE,
        };
      }
    }
    res.status(200).json(response);
  }

  /**
   * triggerPrerelease
   * @param {Request} req
   * @param {Response} res
   */
  async triggerPrerelease(req: Request, res: Response) {
    functions.logger.debug("dry run %s", req.query.dryrun);
    const dryRun = (req.query.dryrun as string) === "true" || false;
    functions.logger.info("triggerPrerelease called with param: %s", dryRun);
    // get all tasks
    const prereleaseItems = await BatchService.preparePrerelease();
    functions.logger.debug("prereleaseItems: %s", JSON.stringify(prereleaseItems));
    if (!dryRun) {
      const result = await BatchService.processPrereleaseRequest(prereleaseItems);
      res.status(200).json({ result: result });
    } else {
      functions.logger.info("Dry Run Only");
      res.status(200).json({ result: JSON.stringify(prereleaseItems) });
    }
  }

  /**
   * processBatch
   * @param {Request} req
   * @param {Response} res
   */
  async processPrereleaseBatch(req: Request, res: Response) {
    const batchId = req.params.batch_id;
    const result = await BatchService.processPrereleaseBatch(batchId);
    res.status(200).json({ result: result });
  }

  /**
   * batchChangeHandler
   * @param {Change} change
   * @param {Context} context
   */
  async batchChangeHandler(
    change: Change<DocumentSnapshot>,
    context: EventContext<{
      batchId: string;
    }>,
  ) {
    const batchId = context.params.batchId;
    if (change.after.get("type") == FundReleaseRequestType.REQUEST_PRERELEASE) {
      BatchService.processPrereleaseBatch(batchId);
    }
  }
}
export default new BatchController();

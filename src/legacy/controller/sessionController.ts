import { Request, Response } from "express";
import * as functions from "firebase-functions";

import SessionService from "../service/session";
/**
 * SessionController
 */
class SessionController {
  /**
   * setExpectedEndTime
   * @param {Request} req
   * @param {Response} res
   */
  async setExpectedEndTime(req: Request, res: Response) {
    functions.logger.info("setExpectedEndTime");
    const sessionId = req.params.session_id;
    const expectedEndTime = new Date(req.body.expectedEndTime);
    try {
      await SessionService.setExpectedEndTime(sessionId, expectedEndTime);
      const session = await SessionService.getSessionById(sessionId);
      res.status(200).json({ session });
    } catch (err) {
      if (err instanceof ReferenceError) {
        res.status(404).json({ error: err.message });
      } else if (err instanceof Error) {
        res.status(440).json({ error: err.message });
      } else {
        res.status(500).json({ error: "Unknown Error" });
      }
    }
  }

  /**
   * unpair
   * @param {Request} req
   * @param {Response} res
   */
  async unpair(req: Request, res: Response) {
    const sessionId = req.params.session_id;
    functions.logger.info("SessionController unpair called session Id: %s", sessionId);
    try {
      const result = await SessionService.unpair(sessionId);
      res.status(200).json({ result: result });
    } catch (err) {
      if (err instanceof ReferenceError) {
        res.status(404).json({ error: "not found" });
      } else {
        res.status(409).json({ error: "duplicate" });
      }
    }
  }
}

export default new SessionController();

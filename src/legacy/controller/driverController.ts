import { Request, Response } from "express";
import * as functions from "firebase-functions";

import { validateQR } from "../service/driver";
import driverService from "../service/driverService";
import SessionService from "../service/session";

/**
 * DriverContoller
 */
class DriverContoller {
  /**
   * pair since v1.1
   * @param {Request} req
   * @param {Response} res
   */
  async pair(req: Request, res: Response) {
    functions.logger.info("DriverContoller pair 1.1 called");
    // get driver Id from user object (set by auth middleware)
    // const driverId = req.params.driver_id;
    const driverId = req.user?.phone_number;
    if (driverId == undefined) {
      res.status(403).send("Driver has no id");
      return;
    }
    const qr = req.body.qr;
    const licensePlate = qr.split("-")[0].substring(2);
    const tokenString = qr.split("-")[1];
    if (validateQR(tokenString)) {
      try {
        const sessionId = await driverService.pair(driverId, licensePlate);
        functions.logger.info("Handler: Paired session:" + sessionId);
        const session = await SessionService.getSessionById(sessionId);
        if (!session) {
          throw new ReferenceError("session not found");
        }
        res.status(200).json(session);
      } catch (err) {
        functions.logger.error("Handler: Pairing failed:  %s", err);
        if (err instanceof ReferenceError) {
          res.status(404).send("Driver " + driverId + " not found");
        } else {
          res.status(500).send("Error" + err);
        }
      }
    } else {
      functions.logger.debug("driver %s pair an expired QR", driverId);
      res.status(401).send({ error: "expired session" });
    }
  }

  /**
   * Patch ExpireAt used when 1.0 -> 1.1
   * @param {Request} req
   * @param {Response} res
   */
  async patchExpireAt(req: Request, res: Response) {
    try {
      const driverId = req.params.driver_id;
      functions.logger.info("patchExpireAt: %s", driverId);
      const sessionResult = await driverService.patchExpireAt(driverId);
      const tripResult = await driverService.patchTripExpireAt(driverId);
      res.status(200).json({ result: sessionResult, trip_result: tripResult });
    } catch (err) {
      res.status(500).json({ error: err });
    }
  }
}

export default new DriverContoller();

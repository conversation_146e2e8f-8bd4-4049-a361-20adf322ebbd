import { Request, Response } from "express";

import { locationConversionService } from "../service/driver";
/**
 * MapController
 */
class MapController {
  /**
   * getAddress
   * @param {request} req
   * @param {response} res
   */
  async getAddress(req: Request, res: Response) {
    const lnglan = req.body as Lnglan;
    const lng = Number.parseFloat(lnglan.location.split(",")[0]);
    const lan = Number.parseFloat(lnglan.location.split(",")[1]);
    const address = await locationConversionService(lng, lan);
    res.status(200).json({ address: address });
  }
}

interface Lnglan {
  location: string;
}
export default new MapController();

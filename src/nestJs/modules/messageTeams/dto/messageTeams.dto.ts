/**
 * Interface for sending a message to Teams
 */
export class TeamsAdaptiveCard {
  type: string = "message";
  attachments: TeamsAdaptiveCardAttachment[] = [];
}

export class TeamsAdaptiveCardAttachment {
  contentType: string = "application/vnd.microsoft.card.adaptive";
  content: TeamsAdaptiveCardContent = new TeamsAdaptiveCardContent();
}

export class TeamsAdaptiveCardContent {
  type: string = "AdaptiveCard";
  body: TeamsAdaptiveCardBody[] = [];
  actions: TeamsAdaptiveCardAction[] = [];
  $schema: string = "http://adaptivecards.io/schemas/adaptive-card.json";
  version: string = "1.2";
}

export class TeamsAdaptiveCardBody {
  type: string;
  size?: string;
  weight?: string;
  text?: string;
  wrap?: boolean;
  facts?: TeamsAdaptiveCardFact[];
}

export class TeamsAdaptiveCardFact {
  title: string;
  value: string;
}

export class TeamsAdaptiveCardAction {
  type: string;
  title: string;
  url: string;
}

// Keeping old DTOs for backward compatibility
export class TeamsMessageCard {
  "@type": string = "MessageCard";
  "@context": string = "http://schema.org/extensions";
  summary: string = "";
  title: string = "";
  sections: TeamsMessageCardSection[] = [];
  potentialAction: TeamsMessageCardAction[] = [];
}

export class TeamsMessageCardSection {
  facts: TeamsMessageCardFact[] = [];
  text: string = "";
}

export class TeamsMessageCardFact {
  name: string = "";
  value: string = "";
}

export class TeamsMessageCardAction {
  "@type": string = "OpenUri";
  name: string = "";
  targets: TeamsMessageCardActionTarget[] = [];
}

export class TeamsMessageCardActionTarget {
  os: string = "default";
  uri: string = "";
}

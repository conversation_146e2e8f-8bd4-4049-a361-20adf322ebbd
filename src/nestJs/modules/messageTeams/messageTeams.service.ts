import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import axios from "axios";

import {
  TeamsMessageCard,
  TeamsMessageCardAction,
  TeamsMessageCardActionTarget,
  TeamsMessageCardFact,
  TeamsMessageCardSection,
} from "./dto/messageTeams.dto";
import LoggerServiceAdapter from "../utils/logger/logger.service";

/**
 * Message Teams service
 */
@Injectable()
export class MessageTeamsService {
  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Send a message to Teams
   *
   */
  async sendMessageToTeams(card: any, webHook: string) {
    if (this.configService.get("JEST_WORKER_ID") !== undefined) {
      return;
    }
    try {
      await axios.post(webHook, card);
    } catch (error) {
      this.logger.error(`Error sending message to Teams: ${JSON.stringify(error)}`);
    }
  }

  /**
   * Process sending message
   * @param driverPhoneNumber string
   * @param message string
   * @returns Message
   */
  async sendDriverApplicationEventsToTeams(driverPhoneNumber: string, title: string, message: string): Promise<void> {
    const project = this.configService.getOrThrow("GCLOUD_PROJECT");
    const driverUrl = `https://console.firebase.google.com/u/0/project/${project}/firestore/databases/-default-/data/~2Fdrivers~2F${driverPhoneNumber}`;
    const webHook =
      "https://netorgft8809665.webhook.office.com/webhookb2/62d277bc-8fc0-4fda-87f0-52e1cac80376@42553b9c-b625-4c8d-9139-fa1af19f40fb/IncomingWebhook/c48e1b4ea4af45b1a9582c209461813f/9d02187c-eee5-4bfb-96ee-d499a874b10d";

    const messageCard = new TeamsMessageCard();
    messageCard["@type"] = "MessageCard";
    messageCard["@context"] = "http://schema.org/extensions";
    messageCard.summary = "Driver Application Update Detected";
    messageCard.title = title;
    const section = new TeamsMessageCardSection();
    const facts: TeamsMessageCardFact[] = [];
    facts.push({ name: "Environment:", value: project });
    section.facts = facts;
    section.text = message;
    messageCard.sections = [section];
    const action = new TeamsMessageCardAction();
    action["@type"] = "OpenUri";
    action.name = "View in Firestore";
    const target = new TeamsMessageCardActionTarget();
    target.os = "default";
    target.uri = driverUrl;
    action.targets = [target];
    messageCard.potentialAction = [action];

    return this.sendMessageToTeams(messageCard, webHook);
  }

  /**
   * Send payout notification to Teams
   * @param fileName string
   * @returns Message
   */
  async sendPayoutNotificationToTeams(fileName: string): Promise<void> {
    this.logger.info(`New Payout Generated: ${fileName}`);

    const project = this.configService.getOrThrow("GCLOUD_PROJECT");
    const createTime = new Date().toLocaleString(); // Convert to readable date-time format

    const fileUrl = `https://console.cloud.google.com/storage/browser/_details/${project}-payouts/${fileName};tab=live_object?project=${project}`;

    const webHook =
      "https://prod-84.southeastasia.logic.azure.com:443/workflows/7018dac825ab4f36a0d03a7e5df9332b/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=QxvJcKkuoLAJpb1WE25BXTEJTqlZEplp9zVJV5i0lMY";

    return this.sendMessageToTeams(
      {
        type: "message",
        attachments: [
          {
            contentType: "application/vnd.microsoft.card.adaptive",
            contentUrl: fileUrl,
            content: {
              $schema: "http://adaptivecards.io/schemas/adaptive-card.json",
              type: "AdaptiveCard",
              version: "1.0",
              body: [
                {
                  type: "TextBlock",
                  text: "💲 New Payout Generated 💲",
                  id: "Title",
                  spacing: "Medium",
                  horizontalAlignment: "Center",
                  size: "ExtraLarge",
                  weight: "Bolder",
                  color: "Accent",
                },
                {
                  type: "TextBlock",
                  text: "A new payout has been generated, please upload it on the bank plateform.",
                  id: "summary",
                  wrap: true,
                  separator: true,
                },
                {
                  type: "TextBlock",
                  text: `Environment: ${project}`,
                  id: "environment",
                },
                {
                  type: "TextBlock",
                  text: `Date: ${createTime}`,
                  id: "date",
                },
                {
                  type: "TextBlock",
                  text: `File: [${fileName}](${fileUrl})`,
                  id: "fileName",
                },
              ],
            },
          },
        ],
      },
      webHook,
    );
  }

  async sendSystemAlertNotificationToTeams(
    body: any[],
    webhookUrl: string,
    targetUrl: string,
    actionName: string,
  ): Promise<void> {
    return this.sendMessageToTeams(
      {
        type: "message",
        attachments: [
          {
            contentType: "application/vnd.microsoft.card.adaptive",
            contentUrl: null,
            content: {
              $schema: "http://adaptivecards.io/schemas/adaptive-card.json",
              type: "AdaptiveCard",
              version: "1.0",
              body: body,
              actions: [
                {
                  type: "Action.OpenUrl",
                  title: actionName,
                  url: targetUrl,
                  role: "button",
                },
              ],
            },
          },
        ],
      },
      webhookUrl,
    );
  }
}

import { <PERSON>, Get, HttpStatus, <PERSON>m, Req, Re<PERSON> } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Request, Response } from "express";

import { LinkService } from "./link.service";
import { errorBuilder } from "../utils/utils/error.utils";
import { apiTags } from "../utils/utils/swagger.utils";

/**
 * Link module for shortened urls, it creates, dissolves, logs and redirects shortened urls
 * backed by firestore as cache
 */
@Controller("links")
@ApiTags(...apiTags.me)
export class LinkController {
  constructor(private readonly linkService: LinkService) {}

  @ApiOperation({ summary: "Redirect to the destination URL" })
  @ApiResponse({ status: HttpStatus.FOUND, description: "Redirecting to the destination URL" })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: "Link not found" })
  @Get(":id")
  async get(@Param("id") id: string, @Req() req: Request, @Res() res: Response) {
    const linkDocument = await this.linkService.getLinkById(id);

    if (linkDocument) {
      if (linkDocument.expiresAt && linkDocument.expiresAt < new Date()) {
        throw errorBuilder.link.expired();
      }

      const destination = new URL(linkDocument.destination);
      if (linkDocument.includesParams) {
        const originalParams = new URLSearchParams(req.query as any);
        originalParams.forEach((value, key) => {
          destination.searchParams.append(key, value);
        });
      }

      return res.redirect(destination.toString());
    } else {
      throw errorBuilder.link.notFound();
    }
  }
}

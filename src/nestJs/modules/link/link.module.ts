// Link module for shortened urls
import { Modu<PERSON> } from "@nestjs/common";

import { LinkController } from "./link.controller";
import { LinkService } from "./link.service";
import { AppDatabaseModule } from "../appDatabase/appDatabase.module";

@Module({
  imports: [AppDatabaseModule],
  controllers: [LinkController],
  providers: [LinkService, AppDatabaseModule],
  exports: [LinkService],
})
export class LinkModule {}

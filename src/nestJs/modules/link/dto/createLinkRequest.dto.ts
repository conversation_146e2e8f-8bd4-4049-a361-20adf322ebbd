import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { LinkDocument } from "../../appDatabase/documents/link.document";

export class CreateLinkRequestDto {
  @ApiPropertyOptional({ example: "Dx9uF" })
  id?: string;

  @ApiProperty({ example: "https://www.d-ash.com/" })
  destination: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiPropertyOptional()
  campaignId?: string;

  @ApiPropertyOptional()
  expiresAt?: Date;

  @ApiPropertyOptional()
  includesParams?: boolean;
}

export const CreateLinkRequestSchema = Joi.object({
  id: Joi.string().alphanum().length(LinkDocument.idLength).optional(),
  destination: Joi.string().uri().required(),
  description: Joi.string().optional(),
  campaignId: Joi.string().optional(),
  includesParams: Joi.boolean().optional(),
  expiresAt: Joi.date().optional(),
});

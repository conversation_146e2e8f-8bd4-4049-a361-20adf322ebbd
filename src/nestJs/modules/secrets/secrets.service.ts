import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import { Secret } from "./types";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";

@Injectable()
export class SecretsService {
  private readonly secretManager: SecretManagerServiceClient;
  private readonly projectId: string;

  constructor(private readonly logger: LoggerServiceAdapter, configService: ConfigService) {
    this.secretManager = new SecretManagerServiceClient();
    this.projectId = configService.getOrThrow("GCP_PROJECT_ID");
  }

  async getSecret(secretName: Secret): Promise<string> {
    const [version] = await this.secretManager.accessSecretVersion({
      name: `projects/${this.projectId}/secrets/${secretName}/versions/latest`,
    });
    const value = version.payload?.data?.toString() ?? undefined;
    if (!value) {
      this.logger.error(`Secret ${secretName} not found`);
      throw errorBuilder.infrastructure.secretNotFound(secretName);
    }
    return value;
  }
}

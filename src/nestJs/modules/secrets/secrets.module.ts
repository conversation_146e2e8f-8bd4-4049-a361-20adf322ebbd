import { DynamicModule, Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { SecretsService } from "./secrets.service";

@Module({
  imports: [ConfigModule],
  providers: [SecretsService],
  exports: [SecretsService],
})
export class SecretsModule {
  static forRoot(): DynamicModule {
    return {
      global: true,
      module: SecretsModule,
      providers: [SecretsService],
      exports: [SecretsService],
    };
  }
}

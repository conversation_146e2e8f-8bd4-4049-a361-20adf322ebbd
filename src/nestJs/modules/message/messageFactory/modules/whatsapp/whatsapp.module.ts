import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { AppDatabaseModule } from "@nest/modules/appDatabase/appDatabase.module";

import { WhatsappServices } from "./whatsapp.service";
import { MessageRepository } from "../../../../database/repositories/message.repository";
import { UserRepository } from "../../../../database/repositories/user.repository";
import { PubSubService } from "../../../../pubsub/pubsub.service";

/**
 * Whatsapp module
 */
@Module({
  imports: [ConfigModule, HttpModule, AppDatabaseModule],
  providers: [WhatsappServices, MessageRepository, UserRepository, PubSubService],
  exports: [WhatsappServices],
})
export class WhatsappModule {}

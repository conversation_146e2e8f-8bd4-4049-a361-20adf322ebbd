import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import _ from "lodash";
import twilio from "twilio";

import { MessageRepository } from "../../../../database/repositories/message.repository";
import { PublishMessageForMessageProcessingParams } from "../../../../pubsub/dto/publishMessageForMessageProcessing.dto";
import { errorBuilder } from "../../../../utils/utils/error.utils";
import { LanguageOption } from "../../../../validation/dto/language.dto";
import { ChannelTypes } from "../../../dto/channelType.dto";
import { MessageParams } from "../../../dto/messageParams.dto";
import { isPhoneRecipient } from "../../../dto/recipientType.dto";
import { TemplateTypesText } from "../../../dto/templateType.dto";
import MessageFactoryInterface from "../../messageFactoryInterface";

const receiptTemplates: Record<LanguageOption, string> = {
  [LanguageOption.ZHHK]: "收據 : ${ receiptLink }",
  [LanguageOption.EN]: "Receipt : ${ receiptLink }",
};
/**
 * SMS service
 */
@Injectable()
export class SmsServices implements MessageFactoryInterface {
  constructor(private readonly configService: ConfigService, private readonly messageRepository: MessageRepository) {}

  /**
   * Implement the method defined in MessageFactoryServiceInterface.
   * Process Message in SMS Service.
   * @param message PublishMessageForMessageProcessingParams
   * @returns saved Message
   */
  readonly processMessage = async (message: PublishMessageForMessageProcessingParams) => {
    if (message.channel !== ChannelTypes.SMS) {
      throw errorBuilder.message.factoryWrongImplementation({
        location: "SmsServices/processMessage/channel",
        message,
      });
    }
    if (!isPhoneRecipient(message.recipient)) {
      throw errorBuilder.message.sms.recipientRequired(message.tranId);
    }
    let sid: string;
    switch (message.template) {
      case TemplateTypesText.RECEIPT:
        sid = await this.sendReceipt(message.recipient.phone, message.language, message.params);
        break;
      default:
        throw errorBuilder.message.sms.templateNotImplemented(message);
    }
    return this.messageRepository.saveMessageForPhoneRecipient(message, sid);
  };

  /**
   * Send Receipt Via SMS.
   * @param {string} phoneNumber
   * @param {LanguageOption} language
   * @param {MessageParams} params
   * @return {promises} message id
   */
  async sendReceipt(phoneNumber: string, language: LanguageOption, params: MessageParams): Promise<string> {
    if (!params.receiptLink) {
      throw errorBuilder.global.requiredParam("receiptLink");
    }
    const template = receiptTemplates[language];
    const complied = _.template(template);
    return await this.sendSMSMessage(phoneNumber, complied(params));
  }

  /**
   * Send SMS Message.
   * @param {string} phoneNumber
   * @param {string} body
   * @return {promises} message sid
   */
  async sendSMSMessage(phoneNumber: string, body: string): Promise<string> {
    const client = twilio(
      this.configService.getOrThrow("TWILIO_ACCOUNT_SID"),
      this.configService.getOrThrow("TWILIO_TOKEN"),
    );
    const from = this.configService.getOrThrow("TWILIO_PHONE_NUMBER");
    let message;

    try {
      message = await client.messages.create({
        body: body,
        to: phoneNumber,
        from,
      });
    } catch (error) {
      throw errorBuilder.message.sms.twilioCreate(error);
    }

    return message.sid;
  }
}

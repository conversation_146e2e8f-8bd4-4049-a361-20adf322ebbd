import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { SmsServices } from "./sms.service";
import { MessageRepository } from "../../../../database/repositories/message.repository";
import { UserRepository } from "../../../../database/repositories/user.repository";

/**
 * SMS module
 */
@Module({
  imports: [ConfigModule],
  providers: [SmsServices, MessageRepository, UserRepository],
  exports: [SmsServices],
})
export class SmsModule {}

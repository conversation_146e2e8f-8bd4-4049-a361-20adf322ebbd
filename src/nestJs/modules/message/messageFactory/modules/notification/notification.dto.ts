import Joi from "joi";

/**
 * Message Notification types enum
 */
export enum NotificationType {
  PAYOUT_PAID = "PAYOUT_PAID",
  PAYOUT_PAID_TO_FLEET = "PAYOUT_PAID_TO_FLEET",
}

export const notificationTemplate: Record<NotificationType, { title: string; body: string }> = {
  [NotificationType.PAYOUT_PAID]: {
    title: "DASH指示左銀行轉數俾你啦!",
    body: "我地已指示銀行向你戶口存入你昨天收取的電子支付車費，3小時內將完成轉賬，請到時查閱戶口。詳情亦可參閱【收入記錄】",
  },
  [NotificationType.PAYOUT_PAID_TO_FLEET]: {
    title: "DASH指示左銀行轉數俾車隊啦!",
    body: "我地已指示銀行向車隊存入你昨天收取的電子支付車費，3小時內將完成轉賬，到時亦將更新車隊資料，詳情亦可參閱【收入記錄】",
  },
};

/**
 * Message Notification types schema
 */
export const notificationTypeSchema = Joi.string<NotificationType>().valid(...Object.values(NotificationType));

/**
 * Message Notification recipient types enum
 */
export interface NotificationRecipientType {
  id: string;
}

/**
 * Message Notification recipient types schema
 */
export const notificationRecipientTypeSchema = Joi.object<NotificationRecipientType>({
  id: Joi.string().length(11).required(),
});

import { Injectable } from "@nestjs/common";
import { messaging } from "firebase-admin";

import { NotificationType, notificationTemplate } from "./notification.dto";
import Message from "../../../../database/entities/message.entity";
import { MessageRepository } from "../../../../database/repositories/message.repository";
import { PublishMessageForMessageProcessingParams } from "../../../../pubsub/dto/publishMessageForMessageProcessing.dto";
import { errorBuilder } from "../../../../utils/utils/error.utils";
import { ChannelTypes } from "../../../dto/channelType.dto";
import MessageFactoryInterface from "../../messageFactoryInterface";

/**
 * Notification service
 */
@Injectable()
export class NotificationService implements MessageFactoryInterface {
  constructor(private readonly messageRepository: MessageRepository) {}

  /**
   * Implement the method defined in MessageFactoryServiceInterface.
   * Process Message in Notification Service.
   * @param message PublishMessageForMessageProcessingParams
   * @returns saved Message
   */
  readonly processMessage = async (message: PublishMessageForMessageProcessingParams) => {
    if (message.channel !== ChannelTypes.NOTIFICATION) {
      throw errorBuilder.message.factoryWrongImplementation({
        location: "NotificationService/processMessage/channel",
        message,
      });
    }
    let messageProviderId: string;
    switch (message.template) {
      case NotificationType.PAYOUT_PAID:
      case NotificationType.PAYOUT_PAID_TO_FLEET:
        messageProviderId = await messaging().send({
          topic: message.recipient.id,
          notification: {
            title: notificationTemplate[NotificationType.PAYOUT_PAID].title,
            body: notificationTemplate[NotificationType.PAYOUT_PAID].body,
          },
        });
        break;
      default:
        throw errorBuilder.message.factoryWrongImplementation({
          location: "NotificationService/processMessage/template",
          message,
        });
    }

    const messageToSave = Message.fromJson(message);
    messageToSave.messageProviderId = messageProviderId;

    return this.messageRepository.save(messageToSave);
  };
}

import { Module } from "@nestjs/common";

import { NotificationService } from "./notification.service";
import { MessageRepository } from "../../../../database/repositories/message.repository";
import { UserRepository } from "../../../../database/repositories/user.repository";

/**
 * Notification module
 */
@Module({
  imports: [],
  providers: [NotificationService, MessageRepository, UserRepository],
  exports: [NotificationService],
})
export class NotificationModule {}

import { Injectable } from "@nestjs/common";

import { NotificationService } from "./modules/notification/notification.service";
import { SmsServices } from "./modules/sms/sms.service";
import { WhatsappServices } from "./modules/whatsapp/whatsapp.service";
import Message from "../../database/entities/message.entity";
import { PublishMessageForMessageProcessingParams } from "../../pubsub/dto/publishMessageForMessageProcessing.dto";
import { errorBuilder } from "../../utils/utils/error.utils";
import { ChannelTypes } from "../dto/channelType.dto";

type FactoryServiceMap = {
  service: WhatsappServices | SmsServices | NotificationService;
};

/**
 * MessageFactory Service
 */
@Injectable()
export class MessageFactoryService {
  /**
   * Map of the message factory types and their services
   */
  readonly messageFactoryServices: Record<ChannelTypes, FactoryServiceMap>;

  constructor(
    private readonly whatsappService: WhatsappServices,
    private readonly smsService: SmsServices,
    private readonly notificationService: NotificationService,
  ) {
    this.messageFactoryServices = {
      [ChannelTypes.WHATSAPP]: {
        service: this.whatsappService,
      },
      [ChannelTypes.SMS]: {
        service: this.smsService,
      },
      [ChannelTypes.NOTIFICATION]: {
        service: this.notificationService,
      },
    };
  }

  /**
   * Process message
   * @param message PublishMessageForMessageProcessingParams
   * @returns Message
   */
  async processMessage(message: PublishMessageForMessageProcessingParams): Promise<Message> {
    const messageFactory = this.messageFactoryServices[message.channel];
    if (!messageFactory || !messageFactory.service) {
      throw errorBuilder.message.factoryNotImplemented();
    }
    return messageFactory.service.processMessage(message);
  }
}

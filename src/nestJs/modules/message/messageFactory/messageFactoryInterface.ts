import Message from "../../database/entities/message.entity";
import { PublishMessageForMessageProcessingParams } from "../../pubsub/dto/publishMessageForMessageProcessing.dto";

/**
 * Service interface to be implemented by all message factory services
 */
export default interface MessageFactoryInterface {
  readonly processMessage: (message: PublishMessageForMessageProcessingParams) => Promise<Message>;
}

import { Modu<PERSON> } from "@nestjs/common";

import { MessageFactoryController } from "./messageFactory.controller";
import { MessageFactoryService } from "./messageFactory.service";
import { NotificationModule } from "./modules/notification/notification.module";
import { SmsModule } from "./modules/sms/sms.module";
import { WhatsappModule } from "./modules/whatsapp/whatsapp.module";

/**
 * MessageFactory module
 */
@Module({
  imports: [WhatsappModule, SmsModule, NotificationModule],
  providers: [MessageFactoryService],
  controllers: [MessageFactoryController],
  exports: [MessageFactoryService],
})
export class MessageFactoryModule {}

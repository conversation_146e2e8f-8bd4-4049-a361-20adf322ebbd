import { Modu<PERSON> } from "@nestjs/common";

import { MessageController } from "./message.controller";
import { MessageService } from "./message.service";
import { MessageFactoryModule } from "./messageFactory/messageFactory.module";

/**
 * Message module
 */
@Module({
  imports: [MessageFactoryModule],
  providers: [MessageService],
  controllers: [MessageController],
  exports: [MessageService],
})
export class MessageModule {}

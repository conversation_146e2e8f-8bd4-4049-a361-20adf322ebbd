import { Injectable } from "@nestjs/common";

import { MessageFactoryService } from "./messageFactory/messageFactory.service";
import Message from "../database/entities/message.entity";
import { PublishMessageForMessageProcessingParams } from "../pubsub/dto/publishMessageForMessageProcessing.dto";

/**
 * Message service
 */
@Injectable()
export class MessageService {
  constructor(private readonly messageFactoryService: MessageFactoryService) {}

  /**
   * Process a message
   * @param data PublishMessageForMessageProcessingParams
   * @returns the Saved Message
   */
  async processMessage(data: PublishMessageForMessageProcessingParams): Promise<Message> {
    return this.messageFactoryService.processMessage(data);
  }
}

import Joi from "joi";

/**
 * Message Template types enum for text messages
 */
export enum TemplateTypesText {
  RECEIPT = "RECEIPT",
  DISCOUNT = "DISCOUNT",
  DRIVER_APPLICATION_APPROVED = "DRIVER_APPLICATION_APPROVED",
  FIRST_HAIL_ORDER = "FIRST_HAIL_ORDER",
  FIRST_HAIL_TRIP_COMPLETED = "FIRST_HAIL_TRIP_COMPLETED",
}

/**
 * Message Template types schema
 */
export const templateTypesTextSchema = Joi.string<TemplateTypesText>().valid(...Object.values(TemplateTypesText));

import Joi from "joi";

/**
 * Interface for the phone recipient type
 */
export interface PhoneRecipientType {
  phone: string;
  email?: Date;
}
/**
 * Joi Phone recipient type schema
 */
export const phoneRecipientTypeSchema = Joi.object<PhoneRecipientType>({
  phone: Joi.string().required(),
  email: Joi.date().optional(),
});

/**
 * Interface for the email recipient type
 */
export interface EmailRecipientType {
  email: string;
  phone?: string;
}
/**
 * Joi Email recipient type schema
 */
const emailRecipientTypeSchema = Joi.object<EmailRecipientType>({
  phone: Joi.string().optional(),
  email: Joi.date().required(),
});

/**
 * Interface for the recipient type
 */
export type RecipientType = PhoneRecipientType | EmailRecipientType;
/**
 * Joi Recipient type schema
 */
export const recipientTypeSchema = Joi.alternatives([phoneRecipientTypeSchema, emailRecipientTypeSchema]);

export const isPhoneRecipient = (recipient: RecipientType): recipient is PhoneRecipientType => {
  return (recipient as PhoneRecipientType).phone !== undefined;
};

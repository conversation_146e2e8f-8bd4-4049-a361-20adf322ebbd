import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { AuthController } from "./auth.controller";
import { AuthService } from "./auth.service";
import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { EmailModule } from "../email/email.module";
import { EncryptionModule } from "../encryption/encryption.module";

@Module({
  imports: [ConfigModule, AppDatabaseModule, EmailModule, EncryptionModule],
  controllers: [AuthController],
  providers: [AuthService],
  exports: [AuthService],
})
export class AuthModule {}

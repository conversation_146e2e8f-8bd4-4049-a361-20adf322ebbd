import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export const resetPasswordFinalizeSchema = Joi.object({
  token: Joi.string().required(),
  newPassword: Joi.string().required(),
  newPasswordConfirmation: Joi.string().valid(Joi.ref("newPassword")).required(),
});

export class ResetPasswordFinalize {
  @ApiProperty()
  token: string;

  @ApiProperty()
  newPassword: string;

  @ApiProperty()
  newPasswordConfirmation: string;
}

import { Role, RoleMetadata } from "../types";

export enum AuthStatus {
  NEW = "NEW",
  ACTIVE = "ACTIVE",
}

export type DatedHash = {
  hash: string;
  date: Date;
};

export type PasswordReset = {
  token: string;
  expiration: Date;
};

export type AuthDto = {
  status: AuthStatus;
  userId: string;
  email: string;
  role: Role;
  roleMetadata?: RoleMetadata | null;
  lastAuth: Date;
  failedAuthAttempts: number;
  previousHashes: DatedHash[];
  passwordReset: PasswordReset | null;
};

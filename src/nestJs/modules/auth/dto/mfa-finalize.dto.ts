import Joi from "joi";

export const mfaFinalizeSchema = Joi.object({
  mfaPendingCredential: Joi.string().required(),
  mfaEnrollmentId: Joi.string().required(),
  phoneVerificationInfo: Joi.object({
    sessionInfo: Joi.string().required(),
    code: Joi.string().required(),
  }),
});

class PhoneVerificationInfoDto {
  sessionInfo: string;
  code: string;
}

export class MfaFinalizeDto {
  mfaPendingCredential: string;
  mfaEnrollmentId: string;
  phoneVerificationInfo: PhoneVerificationInfoDto;
}

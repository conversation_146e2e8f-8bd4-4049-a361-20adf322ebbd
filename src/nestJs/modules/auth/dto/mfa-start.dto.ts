import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export const mfaStartSchema = Joi.object({
  mfaPendingCredential: Joi.string().required(),
  mfaEnrollmentId: Joi.string().required(),
  phoneSignInInfo: Joi.object({
    phoneNumber: Joi.string().required(),
    recaptchaToken: Joi.string().required(),
  }).required(),
});

class PhoneSignInInfoDto {
  @ApiProperty()
  phoneNumber: string;

  @ApiProperty()
  recaptchaToken: string;
}

export class MfaStartDto {
  @ApiProperty()
  mfaPendingCredential: string;

  @ApiProperty()
  mfaEnrollmentId: string;

  @ApiProperty()
  phoneSignInInfo: PhoneSignInInfoDto;
}

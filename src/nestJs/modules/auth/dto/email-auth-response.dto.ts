import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import { FirebaseAuthResultsDto } from "./firebase-auth-results.dto";
import { Role } from "../types";

export enum AuthResultsType {
  SUCCESS = "success",
  MFA_REQUIRED = "mfaRequired",
  RESET_REQUIRED = "resetRequired",
  ERROR = "error",
}

class EmailAuthResetResponseDto {
  @ApiProperty()
  type: AuthResultsType.RESET_REQUIRED;
}

class EmailAuthSuccessResponseDto extends FirebaseAuthResultsDto {
  @ApiProperty()
  type: AuthResultsType;

  @ApiProperty()
  role: Role;

  @ApiPropertyOptional()
  phone?: string;

  @ApiPropertyOptional()
  customToken?: string;
}

export type EmailAuthResponseDto = EmailAuthResetResponseDto | EmailAuthSuccessResponseDto;

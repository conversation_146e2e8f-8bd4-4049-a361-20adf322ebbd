import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

class MfaInfo {
  @ApiProperty()
  phoneInfo: string;

  @ApiProperty()
  mfaEnrollmentId: string;

  @ApiProperty()
  displayName: string;

  @ApiProperty()
  enrolledAt: string;
}

export class FirebaseAuthResultsDto {
  @ApiProperty()
  kind: string;

  @ApiProperty()
  localId: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  displayName: string;

  @ApiPropertyOptional()
  idToken?: string;

  @ApiProperty()
  registered: boolean;

  @ApiPropertyOptional()
  refreshToken?: string;

  @ApiPropertyOptional()
  expiresIn?: string;

  @ApiPropertyOptional()
  mfaPendingCredential?: string;

  @ApiPropertyOptional()
  mfaInfo?: MfaInfo[];
}

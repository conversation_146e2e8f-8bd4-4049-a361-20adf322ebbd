import { DecodedIdToken } from "firebase-admin/auth";

export enum Role {
  DEFAULT = "DEFAULT",
  ADMIN = "ADMIN",
  DRIVER = "DRIVER",
  MECHANIC = "MECHANIC",
  METER = "METER",
  GARAGE = "GARAGE",
  CUSTOMER_SUPPORT = "CUSTOMER_SUPPORT",
  FLEET_MANAGER = "FLEET_MANAGER",
  MERCHANT = "MERCHANT",
}

export type RoleMetadata = Record<string, unknown>;

export enum PasswordAuthError {
  EMAIL_NOT_FOUND = "EMAIL_NOT_FOUND",
  INVALID_PASSWORD = "INVALID_PASSWORD",
  INVALID_LOGIN_CREDENTIALS = "INVALID_LOGIN_CREDENTIALS",
  USER_DISABLED = "USER_DISABLED",
}

export enum CustomTokenAuthError {
  INVALID_CUSTOM_TOKEN = "INVALID_CUSTOM_TOKEN",
  CREDENTIAL_MISMATCH = "CREDENTIAL_MISMATCH",
}

export type FirebaseAuthError = {
  error: {
    code: string;
    message: string;
  };
};

export enum CreateUserError { //from google api response
  EMAIL_EXISTS = "EMAIL_EXISTS",
}

export type AuthClaims = Record<string, unknown>;

export type DecodedToken = DecodedIdToken & {
  role?: Role;
};

export type VerifyToken = (decodedToken: DecodedToken) => boolean;

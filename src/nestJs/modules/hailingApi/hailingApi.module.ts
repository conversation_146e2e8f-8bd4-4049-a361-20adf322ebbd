import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";

import { HailingApiService } from "./hailingApi.service";

@Module({
  imports: [
    ConfigModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: 5000,
        headers: {
          "Content-Type": "application/json",
          "x-api-key": configService.getOrThrow<string>("CLOUD_TASKS_API_KEY"),
        },
        baseURL: configService.getOrThrow<string>("HAILING_BASE_URL"),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [HailingApiService],
  exports: [HailingApiService],
})
export class HailingApiModule {}

import { HttpService } from "@nestjs/axios";
import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { firstValueFrom } from "rxjs";

import { MeterDocument } from "../appDatabase/documents/meter.document";
import { HailUpdateResponse, IUpdateFleetOrderResponse } from "../cloudTaskFleetOrder/interface";
import FleetOrderEntity from "../database/entities/fleetOrder.entity";
import { HailStatus } from "../hailing/dto/updateHail.dto";
import { Heartbeat } from "../transaction/dto/txEventType.dto";
import LoggerServiceAdapter from "../utils/logger/logger.service";

@Injectable()
export class HailingApiService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  async updateDriverHeartBeat(phoneNumber: string, hailId: string, heartBeat: Heartbeat): Promise<void> {
    try {
      this.logger.info("HailingApiService/updateDriverHeartBeat-start", { phoneNumber, heartBeat, hailId });
      const response = await firstValueFrom(
        this.httpService.patch<void>("v1/drivers/update_heart_beat", {
          hailId,
          heartBeat,
          phoneNumber,
        }),
      );
      this.logger.info("HailingApiService/updateDriverHeartBeat-end", {
        phoneNumber,
        heartBeat,
        hailId,
        response: response.data,
      });
      return response.data;
    } catch (error) {
      this.logger.error(
        "HailingApiService/updateDriverHeartBeat-end",
        { error, phoneNumber, heartBeat, hailId },
        error as Error,
      );
      throw error;
    }
  }

  async updateFleetHailingRequest(
    fleetOrder: FleetOrderEntity,
    result: IUpdateFleetOrderResponse,
    fleetMeter: MeterDocument,
    hailStatus: HailStatus,
  ): Promise<HailUpdateResponse> {
    try {
      this.logger.info("HailingApiService/updateFleetHailingRequest-start", { fleetOrder, result, hailStatus });
      const response = await firstValueFrom(
        this.httpService.post<HailUpdateResponse>(
          `v1/hails/${fleetOrder.hailingRequestId}/fleet`,
          {
            status: hailStatus,
            driverPhoneNumber: result.driverPhoneNumber,
            driverLicensePlate: result.driverLicensePlate,
            driverName: result.driverName,
            driverLocation: result.driverLocation,
            meterId: fleetMeter.id,
          },
          {
            headers: {
              "X-API-KEY": this.configService.getOrThrow("CLOUD_TASKS_API_KEY"),
            },
          },
        ),
      );

      this.logger.info("HailingApiService/updateFleetHailingRequest-end", {
        fleetOrder,
        hailStatus,
        response: response.data,
      });
      return response.data;
    } catch (error) {
      this.logger.error("HailingApiService/updateFleetHailingRequest-end", { fleetOrder, hailStatus }, error as Error);
      throw error;
    }
  }

  async cancelFleetHailingRequest(hailId: string): Promise<void> {
    try {
      this.logger.info("HailingApiService/cancelFleetHailingRequest-start", { hailId });
      const response = await firstValueFrom(this.httpService.delete<void>(`v1/hails/${hailId}/fleet`));
      this.logger.info("HailingApiService/cancelFleetHailingRequest-end", { hailId, response: response.data });
      return response.data;
    } catch (error) {
      this.logger.error("HailingApiService/cancelFleetHailingRequest-end", { error, hailId }, error as Error);
      throw error;
    }
  }
}

/**
 * Moved these configs out to be shared without unnecessarily importing the FcmService.
 * These are readonly anyway and nothing sensitive, so it's fine exposing them as public.
 */

import { AndroidConfig, ApnsConfig } from "firebase-admin/messaging";

export class FcmConfig {
  public static readonly androidConfig: AndroidConfig = {
    priority: "high",
    notification: {
      sound: "default",
      visibility: "public",
    },
  };

  public static readonly apnsConfig: ApnsConfig = {
    payload: {
      aps: {
        sound: {
          critical: true,
          name: "default",
          volume: 1.0,
        },
      },
    },
  };
}

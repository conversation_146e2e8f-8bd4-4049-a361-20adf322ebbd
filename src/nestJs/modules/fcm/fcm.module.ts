import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { FcmService } from "./fcm.service";
import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { MerchantRepository } from "../database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "../database/repositories/merchantNotificationToken.repository";
import { UserRepository } from "../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../database/repositories/userNotificationToken.repository";

@Module({
  imports: [ConfigModule, AppDatabaseModule],
  providers: [
    FcmService,
    AppDatabaseModule,
    UserRepository,
    UserNotificationTokenRepository,
    MerchantRepository,
    MerchantNotificationTokenRepository,
  ],
  exports: [FcmService],
})
export class FcmModule {}

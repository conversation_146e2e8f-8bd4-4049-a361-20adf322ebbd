import Jo<PERSON> from "joi";

import { PreferredLanguageType } from "../identity/dto/user.dto";

export enum ClickNotificationEventType {
  GO_TO_TRANSACTIONS_HISTORY_PAGE_IN_USER_APP = "GO_TO_TRANSACTIONS_HISTORY_PAGE_IN_USER_APP",
}

export type CustomData = {
  clickEvent: ClickNotificationEventType;
  [key: string]: string;
};

export enum NotificationTriggerEventType {
  TRIP_END = "TRIP_END",
  RT2_ORDER_TIME_OUT = "RT2_ORDER_TIME_OUT",
  RT3_ORDER_ACCEPTED_BY_DRIVER = "RT3_ORDER_ACCEPTED_BY_DRIVER",
  RT4_ACCEPTED_ORDER_CANCELLED_BY_RIDER_QUICKLY = "RT4_ACCEPTED_ORDER_CANCELLED_BY_RIDER_QUICKLY",
  RT5_ACCEPTED_ORDER_CANCELLED_BY_RIDER_LATE = "RT5_ACCEPTED_ORDER_CANCELLED_BY_RIDER_LATE",
  RT6_ACCEPTED_ORDER_CANCELLED_BY_DRIVER = "RT6_ACCEPTED_ORDER_CANCELLED_BY_DRIVER",
  P4_60_MINUTES_BEFORE_PICK_UP_TIME = "P4_60_MINUTES_BEFORE_PICK_UP_TIME",
  P5_30_MINUTES_BEFORE_PICK_UP_TIME_WHILE_NOT_ACCEPTED = "P5_30_MINUTES_BEFORE_PICK_UP_TIME_WHILE_NOT_ACCEPTED",
  P6_ACCEPTED_ORDER_CANCELLED_BY_RIDER_GREATER_THAN_15_MINUTES_BEFORE_PICK_UP_TIME = "P6_ACCEPTED_ORDER_CANCELLED_BY_RIDER_GREATER_THAN_15_MINUTES_BEFORE_PICK_UP_TIME",
  P7_1_15_MINUTES_BEFORE_PICK_UP_TIME_FOR_ACCEPTED_ORDER = "P7_1_15_MINUTES_BEFORE_PICK_UP_TIME_FOR_ACCEPTED_ORDER",
  P8_ACCEPTED_ORDER_CANCELLED_BY_RIDER_LESS_THAN_15_MINUTES_BEFORE_PICK_UP_TIME = "P8_ACCEPTED_ORDER_CANCELLED_BY_RIDER_LESS_THAN_15_MINUTES_BEFORE_PICK_UP_TIME",
  P10_ACCEPTED_ORDER_CANCELLED_BY_DRIVER_LESS_THAN_15_MINUTES_BEFORE_PICK_UP_TIME = "P10_ACCEPTED_ORDER_CANCELLED_BY_DRIVER_LESS_THAN_15_MINUTES_BEFORE_PICK_UP_TIME",
  P11_ORDER_TIME_OUT = "P11_ORDER_TIME_OUT",
}
export enum NotificationRecipientType {
  RIDER = "RIDER",
  MERCHANT = "MERCHANT",
}
export type NotificationTemplate = {
  event: NotificationTriggerEventType;
  templates: Template[];
};

export type Template = {
  messages: TemplateMessage[];
  recipient: NotificationRecipientType;
};

export type TemplateMessage = {
  body: string;
  language: PreferredLanguageType;
  title: string;
};

export const notificationTriggerEventTypeSchema = Joi.string<NotificationTriggerEventType>().valid(
  ...Object.values(NotificationTriggerEventType),
);

export const notificationRecipientTypeSchema = Joi.string<NotificationRecipientType>().valid(
  ...Object.values(NotificationRecipientType),
);

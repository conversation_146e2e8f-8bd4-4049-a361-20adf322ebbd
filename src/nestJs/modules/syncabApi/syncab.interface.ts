export interface QuoteFeatures {
  max_number_of_passengers: number;
  max_suitcases: number;
  includes_meet_and_greet: boolean | null;
  wheelchair_accessible: boolean;
  wifi: boolean;
  baby_seat: boolean;
  covid_prepared: boolean;
}

export interface SyncabQuote {
  estimateId: string;
  pickup_eta: number | null;
  journey_eta: number;
  taxi_type: string;
  currency_code: string;
  estimate: number;
  cancellation_fee: number[];
  features: QuoteFeatures;
}

export interface SyncabQuotesResponse {
  quotes: SyncabQuote[];
}

export interface CreateSyncabOrderRequest {
  estimateId: string; // Required, get from create quote
  taxi_type: string; // Required, SPT | MPT | EMPT | PRIME
  booking_id: string; // Required, Sply<PERSON>'s booking id
  passenger: {
    name?: string;
    phone_number: string;
  };
}

export interface CreateSyncabOrderResponse {
  supplier_booking_id: string;
}

export interface Vehicle {
  make: string | null;
  model: string | null;
  colour: string | null;
}

export interface Driver {
  driver_id: string | null;
  first_name: string | null;
  phone_number: string | null;
  profile_picture: string | null;
  rating: string;
  vehicle: Vehicle;
  license_number: string | null;
}

export interface Location {
  latitude: number | null;
  longitude: number | null;
  bearing: number | null;
}

export interface SyncabFleetOrderResponse {
  supplier_booking_id: string;
  status: string;
  driver: Driver;
  location: Location;
}

export interface SyncabBookingBreakdown {
  type: "base-fare" | "toll-roads" | "cancellation-fee";
  amount: number | null;
}
export interface SyncabReceiptResponse {
  amount: number;
  currency_code: string;
  breakdown: SyncabBookingBreakdown[];
}

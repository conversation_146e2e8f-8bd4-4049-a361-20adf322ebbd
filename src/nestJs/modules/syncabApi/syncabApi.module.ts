import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";

import { SyncabApiService } from "./syncabApi.service";

@Module({
  imports: [
    ConfigModule,
    HttpModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        baseURL:
          configService.get<string>("MOCK_SYNCAB_API") === "true"
            ? configService.get<string>("SYNCAB_MOCK_API")
            : configService.get<string>("SYNCAB_API_URL"),
        timeout: 5000,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${configService.get<string>("SYNCAB_API_KEY")}`,
        },
      }),
    }),
  ],
  providers: [SyncabApiService],
  exports: [SyncabApiService],
})
export class SyncabApiModule {}

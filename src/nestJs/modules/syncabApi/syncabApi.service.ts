import { HttpService } from "@nestjs/axios";
import { Inject, Injectable } from "@nestjs/common";
import { firstValueFrom } from "rxjs";

import {
  CreateSyncabOrderRequest,
  CreateSyncabOrderResponse,
  SyncabFleetOrderResponse,
  SyncabQuotesResponse,
  SyncabReceiptResponse,
} from "./syncab.interface";
import { LocationPlaceDetailsResponse } from "../location/dto/location.dto";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";

@Injectable()
export class SyncabApiService {
  constructor(
    private readonly httpService: HttpService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  async createFleetQuote(
    departureDate: Date,
    locations: LocationPlaceDetailsResponse[],
  ): Promise<SyncabQuotesResponse> {
    try {
      this.logger.info("SyncabApiService/createFleetQuote-start", { departureDate, locations });
      const result = await firstValueFrom(
        this.httpService.post<SyncabQuotesResponse>("/external/dash/quote/create", {
          departure_date: departureDate.getTime(),
          stops: locations.map((location) => ({
            latitude: location.lat,
            longitude: location.lng,
          })),
        }),
      );
      this.logger.info("SyncabApiService/createFleetQuote-end", { departureDate, locations, result: result.data });
      return result.data;
    } catch (error) {
      this.logger.error("SyncabApiService/createFleetQuote-end", { error, departureDate, locations });
      throw errorBuilder.fleetTaxi.syncablQuoteApiError(error);
    }
  }

  async createFleetOrder(data: CreateSyncabOrderRequest): Promise<CreateSyncabOrderResponse> {
    try {
      this.logger.info("SyncabApiService/createFleetOrder-start", { data });
      const result = await firstValueFrom(
        this.httpService.post<CreateSyncabOrderResponse>("/external/dash/booking/create", {
          ...data,
        }),
      );
      this.logger.info("SyncabApiService/createFleetOrder-end", { data, result: result.data });
      return result.data;
    } catch (error) {
      this.logger.error("SyncabApiService/createFleetOrder-end", { data }, error as Error);
      throw errorBuilder.fleetTaxi.syncabCreateOrderApiError(error);
    }
  }

  async getFleetOrder(bookingId: string): Promise<SyncabFleetOrderResponse | null> {
    try {
      this.logger.info("SyncabApiService/getFleetOrder-start", { bookingId });
      const result = await firstValueFrom(
        this.httpService.post<SyncabFleetOrderResponse>("/external/dash/booking/query", {
          booking_id: bookingId,
        }),
      );
      const newResult: SyncabFleetOrderResponse = {
        ...result.data,
        driver: {
          ...result.data.driver,
          phone_number: result.data.driver.phone_number
            ? result.data.driver.phone_number?.startsWith("+852")
              ? result.data.driver.phone_number
              : `+852${result.data.driver.phone_number}`
            : null,
        },
      };
      this.logger.info("SyncabApiService/getFleetOrder-end", { bookingId, result: newResult });
      return newResult;
    } catch (error) {
      this.logger.error("SyncabApiService/getFleetOrder-end", { bookingId }, error as Error);
      return null;
    }
  }

  async cancelFleetOrder(bookingId: string): Promise<boolean> {
    try {
      this.logger.info("SyncabApiService/cancelFleetOrder-start", { bookingId });
      const result = await firstValueFrom(
        this.httpService.post<boolean>("/external/dash/booking/cancel", {
          booking_id: bookingId,
        }),
      );
      this.logger.info("SyncabApiService/cancelFleetOrder-end", { bookingId, result: result.data });
      return result.data;
    } catch (error) {
      this.logger.error("SyncabApiService/cancelFleetOrder-end", { bookingId }, error as Error);
      throw error;
    }
  }

  async getFleetReceipt(bookingId: string): Promise<SyncabReceiptResponse> {
    try {
      this.logger.info("SyncabApiService/getFleetReceipt-start", { bookingId });
      const result = await firstValueFrom(
        this.httpService.post<SyncabReceiptResponse>("/external/dash/booking/receipt", {
          booking_id: bookingId,
        }),
      );
      this.logger.info("SyncabApiService/getFleetReceipt-end", { bookingId, result: result.data });
      return result.data;
    } catch (error) {
      this.logger.error("SyncabApiService/getFleetReceipt-end", { bookingId }, error as Error);
      return { amount: 0, currency_code: "HKD", breakdown: [] };
    }
  }
}

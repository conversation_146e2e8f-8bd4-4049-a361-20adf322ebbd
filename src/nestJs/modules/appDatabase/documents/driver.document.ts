import { ApplicationStatus } from "@nest/modules/merchant/dto/merchant.dto";

import { SessionDocument } from "./session.document";
import { LanguageOption } from "../../validation/dto/language.dto";

export class DriverDocument {
  static collectionName = "drivers";

  collections: string[] = ["sessions", "trips"];

  id: string;
  phoneNumber: string;
  name: string;
  nameZh: string;
  lastExpectedEndTime?: Date;
  showCashTrip?: boolean;
  session?: SessionDocument;

  referralCode?: string;

  referrerCode?: string;

  referrer?: string;

  applicationStatus?: ApplicationStatus;

  approvedAt?: Date;
}

export enum NotificationStatus {
  NEW = "NEW",
  READ = "READ",
}

export enum NotificationDocumentType {
  SYSTEM = "SYSTEM",
  PAYOUT = "PAYOUT",

  MANUAL_MESSAGE = "MANUAL_MESSAGE",
}

export class NotificationDocument {
  static collectionName = "notifications";

  created_on: Date;
  locale: LanguageOption;
  message: string;
  status: NotificationStatus;
  title: string;
  type: NotificationDocumentType;
}

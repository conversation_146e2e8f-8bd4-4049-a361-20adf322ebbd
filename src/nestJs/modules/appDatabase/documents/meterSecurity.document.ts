// Purpose: Define the structure of the MeterSecret document in the database.
export class MeterSecurityDocument {
  type: SecurityType;
  value: string;
  createdAt: Date;

  // Constructor to initialize values
  constructor(type: SecurityType, value: string, createdAt: Date) {
    this.type = type;
    this.value = value;
    this.createdAt = createdAt;
  }
}

export enum SecurityType {
  TOTP = "TOTP",
}

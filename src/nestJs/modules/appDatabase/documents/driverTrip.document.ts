import { ApiProperty } from "@nestjs/swagger";

import { roundOneDecimal } from "@nest/modules/utils/utils/number.utils";

import { TripDocument } from "./trip.document";
import { PaymentStatus } from "../../payment/dto/paymentStatus.dto";
import { TxPayoutStatus } from "../../transaction/dto/txPayoutStatus.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";

export class DriverTripDocument extends TripDocument {
  static collectionName = "trips";

  @ApiProperty()
  expiresAt?: Date;
  @ApiProperty()
  adjustment?: number;
  @ApiProperty()
  payoutStatus?: TxPayoutStatus;
  @ApiProperty()
  paymentStatus?: PaymentStatus;

  static fromJson(json: Record<string, any>, logger: LoggerServiceAdapter): DriverTripDocument {
    const driverTripDocument = new DriverTripDocument();
    const tripDocument = super.fromJson(json);
    Object.assign(driverTripDocument, tripDocument);

    if (json.expiresAt) {
      driverTripDocument.expiresAt = new Date(json.expiresAt);
    }
    driverTripDocument.adjustment = roundOneDecimal(json.adjustment);
    if (json.payoutStatus) {
      driverTripDocument.payoutStatus = TxPayoutStatus[json.payoutStatus as TxPayoutStatus];
      if (!driverTripDocument.payoutStatus) {
        logger.warn(`DriverTripDocument.fromJson DriverTripDocument payoutStatus ${json.payoutStatus} is not valid`);
      }
    }
    return driverTripDocument;
  }

  static updateDateFormat(json: Record<string, any>) {
    const result = { ...json };
    if (json.expiresAt) {
      result.expiresAt = new Date(json.expiresAt);
    }
    if (json.tripStart) {
      result.tripStart = new Date(json.tripStart);
    }
    if (json.tripEnd) {
      result.tripEnd = new Date(json.tripEnd);
    }
    if (json.lastUpdateTime) {
      result.lastUpdateTime = new Date(json.lastUpdateTime);
    }
    if (json.creationTime) {
      result.creationTime = new Date(json.creationTime);
    }
    return result;
  }
}

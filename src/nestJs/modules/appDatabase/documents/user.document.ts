import { GenderType, PreferredLanguageType } from "../../identity/dto/user.dto";

export class UserDocument {
  static collectionName = "users";

  id: string;

  dashDbId?: string;

  dateOfBirth?: Date;

  firstName?: string;

  gender?: GenderType;

  isUserInfoSubmitted?: boolean;

  lastName?: string;

  marketingConsent?: boolean;

  marketingPreferenceEmail?: boolean;

  marketingPreferenceSms?: boolean;

  marketingPreferencePush?: boolean;

  publicKey?: string;

  unverifiedEmailAddress?: string;

  preferredLanguage?: PreferredLanguageType;

  phoneNumber?: string;

  referralCode?: string;

  referrerCode?: string;

  referrer?: string;
}

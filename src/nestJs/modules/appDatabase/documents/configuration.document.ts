import { NotificationTemplate } from "../../fcm/types";

export class ConfigurationDocument {
  static collectionName = "configuration";

  static readonly ENABLE_SQL_LOGGING = false;
  static readonly CASH_TRIP_TTL = "30D";
  static readonly DASH_TRIP_TTL = "90D";

  static readonly subCollectionUserAppName = "user_app";
  static readonly MAX_TIPS_ALLOWED_AMOUNT = 500;

  static readonly notificationTemplateCollectionName = "notification_templates";
  static readonly serverCollectionName = "server";

  static readonly hailConfigCollectionName = "hailing";

  enableSqlLogging: boolean;

  cashTripTtl: string;
  dashTripTtl: string;

  maxTipsAllowedAmount: number;

  notificationTemplates: NotificationTemplate[];

  enable3DSecure: boolean;

  migratingPaymentToKraken: boolean;

  isBulkPayoutEnabled: boolean;
}

export class ConfigurationHailDocument {
  static readonly documentName = "hailing";

  fleetCancellationFee: number;
  dashCancellationFee: number;
}

export class ConfigurationNotificationConfigDocument {
  static readonly documentName = "notification_config";

  beforeMinutesThresholdBeforeFirstOrderPickupNotification: number;
  beforeMinutesThresholdBeforeSecondOrderPickupNotification: number;
}

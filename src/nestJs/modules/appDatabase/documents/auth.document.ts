import { hash, compare } from "bcrypt";
import moment from "moment";

import { TokenUtils } from "@nest/modules/utils/utils/token.utils";

import { AuthDto, AuthStatus, DatedHash, PasswordReset } from "../../auth/dto/auth.dto";
import { AuthClaims, Role, RoleMetadata } from "../../auth/types";

/**
 * Firestore document for storing authentication information.
 */
export class AuthDocument {
  private static readonly MAX_ATTEMPTS_BEFORE_LOCK = 5;
  private static readonly MAX_HOURS_BEFORE_RE_AUTH = 2;

  /**
   * Password requirements:
   * - Alphanumeric
   * - At least 12 characters
   * - At least one special character.
   */
  private static readonly PASSWORD_REGEX = /^(?=.*[a-zA-Z0-9])(?=.*[^a-zA-Z0-9]).{12,}$/;

  static readonly COLLECTION_NAME = "auths";
  static readonly DEFAULT_ROLE = Role.DEFAULT;

  status: AuthStatus;
  userId: string;
  email: string;
  role: Role;
  roleMetadata?: RoleMetadata | null;
  lastAuth: Date;
  failedAuthAttempts: number;
  previousHashes: DatedHash[];
  passwordReset: PasswordReset | null;

  private constructor(authDto: AuthDto) {
    this.status = authDto.status;
    this.userId = authDto.userId;
    this.email = authDto.email;
    this.role = authDto.role;
    this.roleMetadata = authDto.roleMetadata ?? null;
    this.lastAuth = authDto.lastAuth;
    this.failedAuthAttempts = authDto.failedAuthAttempts;
    this.previousHashes = authDto.previousHashes;
    this.passwordReset = authDto.passwordReset ?? null;
  }

  static fromDto(authDto: AuthDto): AuthDocument {
    return new AuthDocument(authDto);
  }

  static isViablePassword(password: string): boolean {
    return AuthDocument.PASSWORD_REGEX.test(password);
  }

  static getClaimsForRole(role: Role): AuthClaims {
    let claims: AuthClaims;

    // NOTE: DO NOT ADD NEW CLAIMS HERE.
    // Necessary for legacy firestore rules.
    // Should be removed when roles are applied in rules.
    // Any additional claims should be added to the roleMetadata object.
    switch (role) {
      case Role.ADMIN:
        claims = { admin: true };
        break;
      case Role.METER:
        claims = {
          meterDevice: true,
        };
        break;
      default:
        claims = {};
    }
    return { ...claims, role };
  }

  get isReAuthRequired(): boolean {
    if (!this.lastAuth) return false;
    const hoursSinceLastAuth = moment().diff(this.lastAuth, "hours");
    return hoursSinceLastAuth >= AuthDocument.MAX_HOURS_BEFORE_RE_AUTH;
  }

  get isAuthAttemptsExceeded(): boolean {
    return this.failedAuthAttempts >= AuthDocument.MAX_ATTEMPTS_BEFORE_LOCK;
  }

  get claims(): AuthClaims {
    return { ...AuthDocument.getClaimsForRole(this.role), ...this.roleMetadata };
  }

  /**
   * Password reset is required for newly created accounts.
   */
  get isPasswordResetRequired(): boolean {
    return this.status === AuthStatus.NEW;
  }

  get isPasswordResetExpired(): boolean {
    if (!this.passwordReset) return true;
    return moment().isAfter(this.passwordReset.expiration);
  }

  private clearOutdatedHashes(): void {
    const oneYearAgo = moment().subtract(1, "year").toDate();
    this.previousHashes = this.previousHashes.filter((datedHash) => datedHash.date > oneYearAgo);
  }

  toDto(): AuthDto {
    return {
      status: this.status,
      userId: this.userId,
      email: this.email,
      role: this.role,
      roleMetadata: this.roleMetadata,
      lastAuth: this.lastAuth,
      failedAuthAttempts: this.failedAuthAttempts,
      previousHashes: this.previousHashes,
      passwordReset: this.passwordReset,
    };
  }

  updateLastAuth(): void {
    this.lastAuth = new Date();
  }

  incrementFailedAuthAttempts(): void {
    this.failedAuthAttempts++;
  }

  /**
   * Check if password has been used in the last year.
   */
  async isPasswordUsedInLastYear(password: string): Promise<boolean> {
    this.clearOutdatedHashes();

    const comparisons = await Promise.all(this.previousHashes.map((datedHash) => compare(password, datedHash.hash)));
    return comparisons.some((isMatch) => isMatch);
  }

  initiatePasswordReset(): PasswordReset {
    const token = TokenUtils.generateHexToken();
    const expiration = moment().add(1, "hour").toDate();
    this.passwordReset = { token, expiration };
    return this.passwordReset;
  }

  async finalizePasswordReset(password: string): Promise<void> {
    this.clearOutdatedHashes();

    const saltRounds = 10;
    const passwordHash = await hash(password, saltRounds);
    this.previousHashes.push({ hash: passwordHash, date: new Date() });
    this.passwordReset = null;
    this.status = AuthStatus.ACTIVE;
  }
}

import { OperatingArea } from "@legacy/model/meter";
import { SessionDocument } from "@nest/modules/appDatabase/documents/session.document";

import { TripDocument } from "./trip.document";

export class MeterDocument {
  static readonly collectionName = "meters";
  static readonly newFlowMeterSoftwareVersion = "4.47";

  collections?: string[] = [TripDocument.collectionName];

  settings: MeterSettings;
  carousel_assets?: Carouselassets;
  required_update?: boolean;

  licensePlate: string;
  // Meter Id from hardware
  meterId?: string;
  session?: SessionDocument;

  createdBy?: string;
  createdAt?: Date;

  id: string;
}

interface Carouselassets {
  marketing: Marketing[];
  post_payment: Marketing[];
}

interface Marketing {
  sequence: number;
  "zh-hk": LangData;
  en: LangData;
}

interface LangData {
  image: string;
}

export interface MeterSettingsVehicle {
  make: string;
  model: string;
  wheelchairRamp: boolean;
}

export interface MeterSettings {
  isDashMeter?: boolean;
  is_blank_screen_required?: boolean;
  meter_software_version?: string;
  dash_fee_constant?: number;
  sdk_version?: string;
  dash_fee_rate?: number;
  language?: string;
  heartbeat_interval?: number;
  vehicle?: MeterSettingsVehicle;
  vehicleId?: string;
  settleToFleet?: boolean;
  fleetId?: string;
  operatingArea?: OperatingArea;
  vehicleLicenseImage?: string;
}

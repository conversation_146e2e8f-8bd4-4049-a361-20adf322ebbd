import { ApiProperty } from "@nestjs/swagger";
import { FieldValue, Timestamp } from "firebase-admin/firestore";
import <PERSON><PERSON> from "joi";

import { TripType } from "@nest/modules/transaction/transactionFactory/modules/trip/types";

import { MeterTripDriver } from "./meterTripDriver.document";
import { SessionDocument } from "./session.document";
import { HailingItineraryStepResponse } from "../../hailing/dto/hailing.dto";
import { PaymentMethodSelected } from "../../payment/dto/paymentMethodSelected.dto";
import { PaymentStatus } from "../../payment/dto/paymentStatus.dto";
import { PaymentType } from "../../payment/dto/paymentType.dto";
import { PaymentInstrumentType } from "../../payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { TripStatus } from "../../transaction/transactionFactory/modules/trip/dto/tripStatus.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";

export class PaymentInformation {
  @ApiProperty()
  type: string;
  @ApiProperty()
  status: string;
  @ApiProperty({ type: Timestamp })
  creationTime: Timestamp;
  @ApiProperty()
  body: string;
}

export class Location {
  @ApiProperty()
  _latitude: number;
  @ApiProperty()
  _longitude: number;
}

export class TripDocumentUser {
  @ApiProperty()
  id: string;
  @ApiProperty()
  phone: string;
}

export class TripDocumentPaymentInstrument {
  @ApiProperty()
  id: string;
  @ApiProperty({ enum: PaymentInstrumentType })
  cardType: PaymentInstrumentType;
  @ApiProperty()
  cardSuffix: string;
}

export class PassengerInformation {
  phone?: string;
}

export class Rating {
  @ApiProperty()
  isUserEnjoy: boolean;
  @ApiProperty()
  reasons?: string[];
}

export const ratingSchema = Joi.object<Rating>({
  isUserEnjoy: Joi.boolean().required(),
  reasons: Joi.array().items(Joi.string()).optional(),
});

export class DashTransactionFeeSettings {
  @ApiProperty()
  dashFeeConstant: number;
  @ApiProperty()
  dashFeeRate: number;
}
export class DiscountSettings {
  @ApiProperty()
  discountIdThirdParty: string;
  @ApiProperty()
  discountRulesThirdParty: string;
  @ApiProperty()
  discountIdDash: string;
  @ApiProperty()
  discountRulesDash: string;
}
export class HailingFeeSettings {
  @ApiProperty()
  additionalBookingFeeRule?: string;
  @ApiProperty()
  fleetBookingFeeRule?: string;
}
export class Billing {
  @ApiProperty()
  additionalBookingFee?: number;
  @ApiProperty()
  fare: number;
  @ApiProperty()
  extra: number;
  @ApiProperty()
  dashBookingFee?: number;
  @ApiProperty()
  dashFeeTotal?: number;
  @ApiProperty()
  dashTransactionFee?: number;
  @ApiProperty()
  dashFeeSettings?: DashTransactionFeeSettings;
  @ApiProperty()
  dashTips?: number;
  @ApiProperty()
  discount?: number;
  @ApiProperty()
  discountDash?: number;
  @ApiProperty()
  discountSettings?: DiscountSettings;
  @ApiProperty()
  estimatedFare?: number;
  @ApiProperty()
  fleetBookingFee?: number;
  @ApiProperty()
  hailingFeeSettings?: HailingFeeSettings;
  @ApiProperty()
  tripTotal: number;
  @ApiProperty()
  total: number;
  @ApiProperty()
  boostAmount?: number;
  @ApiProperty()
  fleetPayoutFee?: number;
}

export class Vehicle {
  @ApiProperty()
  make: string;
  @ApiProperty()
  model: string;
  @ApiProperty()
  class: string;
  @ApiProperty()
  operatingArea?: string;
}
export class TripDocument {
  static collectionName = "trips";

  @ApiProperty()
  id: string;

  isDashMeter: boolean;

  // $$
  @ApiProperty()
  dashFee: number;
  @ApiProperty()
  dashFeeConstant: number;
  @ApiProperty()
  dashFeeRate: number;
  @ApiProperty()
  dashTipsEnable: boolean;
  @ApiProperty()
  dashTips?: number;
  @ApiProperty()
  extra: number;
  @ApiProperty()
  fare: number;
  @ApiProperty({ enum: PaymentType })
  paymentType?: PaymentType;
  @ApiProperty()
  total: number;
  @ApiProperty()
  tripTotal: number;
  @ApiProperty({ isArray: true, type: PaymentInformation })
  paymentInformation?: PaymentInformation[];
  @ApiProperty({ enum: PaymentStatus })
  paymentStatus?: PaymentStatus | FieldValue;
  @ApiProperty({ enum: PaymentMethodSelected })
  paymentMethodSelected?: PaymentMethodSelected | FieldValue;
  @ApiProperty()
  isTipsCalculated?: boolean;
  @ApiProperty()
  paymentInstrument?: TripDocumentPaymentInstrument;
  @ApiProperty()
  billing?: Billing;

  // trip
  @ApiProperty()
  distance: number;
  @ApiProperty({ type: Location })
  locationEnd: Location;
  @ApiProperty({ type: Location })
  locationStart: Location;
  @ApiProperty()
  locationEndAddress: string;
  @ApiProperty()
  locationStartAddress: string;
  @ApiProperty({ type: Date })
  tripStart: Date;
  @ApiProperty({ type: Date })
  tripEnd: Date;
  @ApiProperty({ enum: TripStatus })
  tripStatus: TripStatus;
  @ApiProperty()
  waitTime: number;
  @ApiProperty()
  adjustment?: number;

  //discount & bonus
  @ApiProperty()
  discountId?: string;
  @ApiProperty()
  discountRules?: string;
  @ApiProperty()
  bonusRules?: string;
  @ApiProperty()
  applicationRules?: string;
  @ApiProperty()
  discountAmount?: number;
  @ApiProperty()
  discountIdDash?: string;
  @ApiProperty()
  discountRulesDash?: string;

  @ApiProperty()
  userId?: string;
  @ApiProperty()
  user?: TripDocumentUser;
  @ApiProperty()
  appUserLanguage?: string;

  // Taxi
  @ApiProperty()
  driverId: string;
  @ApiProperty()
  licensePlate: string;
  @ApiProperty()
  meterId: string;

  @ApiProperty()
  language?: string;
  @ApiProperty({ type: Date })
  lastUpdateTime: Date;
  @ApiProperty()
  sessionId: string;
  @ApiProperty()
  showToDriver: boolean;
  @ApiProperty()
  isCreatedByFunctions?: boolean;

  @ApiProperty({ type: SessionDocument })
  session?: Pick<SessionDocument, "id">;

  @ApiProperty({ type: MeterTripDriver })
  driver?: MeterTripDriver;

  //passenger
  @ApiProperty({ type: PassengerInformation })
  passengerInformation?: PassengerInformation;

  tripInfoAppVersion?: string;
  meterSoftwareVersion: string;

  @ApiProperty()
  rating?: Rating;
  @ApiProperty()
  rateTime?: Date;

  @ApiProperty()
  tripItinerary?: HailingItineraryStepResponse[];

  @ApiProperty()
  vehicle?: Vehicle;

  //hailing
  @ApiProperty()
  type?: TripType;

  @ApiProperty()
  hailId?: string;

  @ApiProperty()
  settleToFleet?: boolean;

  @ApiProperty()
  fleetId?: string;

  static fromJson(json: Record<string, any>, logger?: LoggerServiceAdapter): TripDocument {
    const tripDocument = new TripDocument();
    tripDocument.id = json.id;

    tripDocument.dashFee = json.dashFee;
    tripDocument.dashFeeConstant = json.dashFeeConstant;
    tripDocument.dashFeeRate = json.dashFeeRate;
    tripDocument.dashTipsEnable = json.dashTipsEnable;
    tripDocument.dashTips = json.dashTips;
    tripDocument.extra = json.extra;
    tripDocument.fare = json.fare;
    if (json.paymentType) {
      tripDocument.paymentType = PaymentType[json.paymentType as PaymentType];
      if (!tripDocument.paymentType) {
        logger?.warn(`TripDocument.fromJson TripDocument paymentType ${json.paymentType} is not valid`, {
          tx: tripDocument.id,
        });
      }
    }
    tripDocument.total = json.total;
    tripDocument.tripTotal = json.tripTotal;
    if (json.paymentStatus) {
      tripDocument.paymentStatus = PaymentStatus[json.paymentStatus as PaymentStatus];
      if (!tripDocument.paymentStatus) {
        logger?.warn(`TripDocument.fromJson TripDocument paymentStatus ${json.paymentStatus} is not valid`, {
          tx: tripDocument.id,
        });
      }
    }
    if (json.paymentMethodSelected) {
      tripDocument.paymentMethodSelected = PaymentMethodSelected[json.paymentMethodSelected as PaymentMethodSelected];
      if (!tripDocument.paymentMethodSelected) {
        logger?.warn(
          `TripDocument.fromJson TripDocument paymentMethodSelected ${json.paymentMethodSelected} is not valid`,
          {
            tx: tripDocument.id,
          },
        );
      }
    }
    tripDocument.isTipsCalculated = json.isTipsCalculated;

    tripDocument.distance = json.distance;
    if (json.locationEnd) {
      const locationEnd = new Location();
      locationEnd._latitude = json.locationEnd._latitude;
      locationEnd._longitude = json.locationEnd._longitude;
      tripDocument.locationEnd = locationEnd;
    }
    if (json.locationStart) {
      const locationStart = new Location();
      locationStart._latitude = json.locationStart._latitude;
      locationStart._longitude = json.locationStart._longitude;
      tripDocument.locationStart = locationStart;
    }
    tripDocument.locationEndAddress = json.locationEndAddress;
    tripDocument.locationStartAddress = json.locationStartAddress;
    tripDocument.tripStart = new Date(json.tripStart);
    tripDocument.tripEnd = new Date(json.tripEnd);
    if (json.tripStatus) {
      tripDocument.tripStatus = TripStatus[json.tripStatus as TripStatus];
      if (!tripDocument.tripStatus) {
        logger?.warn(`TripDocument.fromJson TripDocument tripStatus ${json.tripStatus} is not valid`, {
          tx: tripDocument.id,
        });
      }
    }
    tripDocument.waitTime = json.waitTime;

    tripDocument.driverId = json.driverId;
    tripDocument.licensePlate = json.licensePlate;
    tripDocument.meterId = json.meterId;

    tripDocument.language = json.language;
    tripDocument.lastUpdateTime = new Date(json.lastUpdateTime);
    tripDocument.sessionId = json.sessionId;
    tripDocument.showToDriver = json.showToDriver;

    if (json.session) {
      tripDocument.session = {
        id: json.session.id,
      };
    }
    if (json.driver) {
      const driver = new MeterTripDriver();
      driver.id = json.driver.id;
      driver.name = json.driver.name;
      driver.nameCh = json.driver.nameCh;
      tripDocument.driver = driver;
    }
    if (json.passengerInformation) {
      const passengerInformation = new PassengerInformation();
      passengerInformation.phone = json.passengerInformation.phone;
      tripDocument.passengerInformation = passengerInformation;
    }
    tripDocument.tripInfoAppVersion = json.tripInfoAppVersion;
    tripDocument.meterSoftwareVersion = json.meterSoftwareVersion;

    if (json.adjustment) {
      tripDocument.adjustment = json.adjustment;
    }
    if (json.discountId) {
      tripDocument.discountId = json.discountId;
    }
    if (json.discountIdDash) {
      tripDocument.discountIdDash = json.discountIdDash;
    }
    if (json.user?.id && json.user?.phone) {
      tripDocument.user = json.user;
    }
    if (json.paymentInstrument) {
      const paymentInstrument = new TripDocumentPaymentInstrument();
      paymentInstrument.id = json.paymentInstrument.id;
      paymentInstrument.cardType = PaymentInstrumentType[json.paymentInstrument.cardType as PaymentInstrumentType];
      paymentInstrument.cardSuffix = json.paymentInstrument.cardSuffix;
      tripDocument.paymentInstrument = paymentInstrument;
    }
    return tripDocument;
  }
}

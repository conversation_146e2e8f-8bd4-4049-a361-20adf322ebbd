import { QrType } from "../../qrCode/dto/qrType";

export class QrCodeDocument {
  static collectionName = "qrcodes";
  id?: string; // Firestore document id, it will be created by firestore on the fly and save back to the document
  transactionId: string;
  type: QrType;
  metadata?: TripMetadata;
  baseUrl: string;
  createdAt: Date;
  createdBy: string;
  expireAt: Date;
}

export class TripMetadata {
  licensePlate: string;
}

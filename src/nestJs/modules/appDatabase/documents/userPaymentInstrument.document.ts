import {
  PaymentInstrumentState,
  PaymentInstrumentType,
} from "../../payment/modules/paymentInstrument/dto/paymentInstrument.dto";

export class UserPaymentInstrumentDocument {
  static collectionName = "paymentInstruments";

  id: string;
  token: string;
  instrumentIdentifier: string;
  cardHolderName: string;
  verifiedAt?: Date | null;
  expirationYear: string;
  expirationMonth: string;
  expirationDate: Date;
  state: PaymentInstrumentState;
  cardPrefix: string;
  cardSuffix: string;
  isPreferred: boolean;
  cardType: PaymentInstrumentType;
  verificationTransactionId?: string | null;
  isPayerAuthEnroled?: boolean | null;
  createdAt: Date;
  updatedAt: Date;
}

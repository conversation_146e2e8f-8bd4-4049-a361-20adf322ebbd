import { DocumentReference } from "firebase-admin/firestore";
import moment from "moment-timezone";

import { CreateSessionDto } from "@nest/modules/merchant/merchantDriver/dto/createSession.dto";
import { SessionDto } from "@nest/modules/merchant/merchantDriver/dto/session.dto";
import { Shift } from "@nest/modules/merchant/merchantDriver/types";

import { DriverDocument } from "./driver.document";
import { MeterSettings } from "./meter.document";

export class SessionDocument {
  static readonly collectionName = "sessions";
  static readonly collections: string[] = ["trips", "notifications"];

  id: string;
  driverId: string;
  driverRef: DocumentReference;
  driver?: DriverDocument | null = null;
  licensePlate: string;
  meterId?: string | null = null;
  meterRef: DocumentReference;
  meterSettings: MeterSettings | undefined;
  startTime: Date;
  endTime?: Date | null = null;
  expectedEndTime?: Date | null = null;
  shift: string;
  cashCount?: number | null = null;
  dashCount?: number | null = null;
  count?: number | null = null;
  cashTotal?: number | null = null;
  dashTotal?: number | null = null;
  dashTipsTotal?: number | null = null;
  totalDistance?: number | null = null;
  total?: number | null = null;

  private constructor(sessionDto: SessionDto) {
    Object.assign(this, sessionDto);
  }

  static fromDto(sessionDto: SessionDto): SessionDocument {
    return new SessionDocument(sessionDto);
  }

  static createNew(createSessionDto: CreateSessionDto, driverLastExpectedEndTime?: Date): SessionDocument {
    const startTime = new Date();
    const shift = this.getShift(startTime);
    return new SessionDocument({
      id: createSessionDto.id,
      shift: shift,
      meterRef: createSessionDto.meterRef,
      meterSettings: createSessionDto.meterSettings,
      licensePlate: createSessionDto.licensePlate,
      startTime: startTime,
      endTime: null,
      expectedEndTime: this.calculateExpectedEndtime(shift, driverLastExpectedEndTime),
      driverId: createSessionDto.driverId,
      driverRef: createSessionDto.driverRef,
    });
  }

  toDto(): SessionDto {
    return {
      id: this.id,
      driverId: this.driverId,
      driverRef: this.driverRef,
      licensePlate: this.licensePlate,
      meterId: this.meterId,
      meterRef: this.meterRef,
      meterSettings: this.meterSettings,
      startTime: this.startTime,
      endTime: this.endTime,
      expectedEndTime: this.expectedEndTime,
      shift: this.shift,
      cashCount: this.cashCount,
      dashCount: this.dashCount,
      count: this.count,
      cashTotal: this.cashTotal,
      dashTotal: this.dashTotal,
      dashTipsTotal: this.dashTipsTotal,
      totalDistance: this.totalDistance,
      total: this.total,
    };
  }

  private static getShift(date: Date): Shift {
    const hourInHKTz: number = +moment(date).tz("Asia/Hong_Kong").format("HH");
    return hourInHKTz >= 3 && hourInHKTz < 15 ? Shift.DAY : Shift.NIGHT;
  }

  private static calculateExpectedEndtime(shift: Shift, lastExpectedEndTime?: Date): Date {
    if (!lastExpectedEndTime) {
      lastExpectedEndTime = moment()
        .utcOffset(8)
        .set({
          hour: shift === Shift.DAY ? 17 : 7,
          minute: 0,
          second: 0,
          millisecond: 0,
        })
        .toDate();
    }

    const currentTime = moment();
    const inputTime = moment({
      hour: lastExpectedEndTime.getHours(),
      minute: lastExpectedEndTime.getMinutes(),
      second: lastExpectedEndTime.getSeconds(),
      millisecond: lastExpectedEndTime.getMilliseconds(),
    });
    let expectedEndTime = currentTime.clone().set({
      hour: inputTime.hour(),
      minute: inputTime.minute(),
      second: inputTime.second(),
      millisecond: inputTime.millisecond(),
    });
    if (expectedEndTime.isBefore(currentTime)) {
      expectedEndTime = expectedEndTime.add(1, "day");
    }
    return expectedEndTime.toDate();
  }
}

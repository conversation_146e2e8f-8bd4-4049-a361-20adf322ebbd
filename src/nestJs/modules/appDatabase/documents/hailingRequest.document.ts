import { TxHailingRequestStatus } from "../../transaction/dto/txHailingRequest.dto";
import { TxHailingMetadata } from "../../transaction/dto/txMetadata.dto";

export class HailingRequestDocument extends TxHailingMetadata {
  static collectionName = "trips";

  tripStart: Date;
  locationStartAddress: string;
  locationEndAddress: string;
  type: string;
  status: TxHailingRequestStatus;
  cancellationFee: number;
  paymentType: string;
}

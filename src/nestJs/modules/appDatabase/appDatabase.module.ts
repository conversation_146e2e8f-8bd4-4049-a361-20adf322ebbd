import { Module, DynamicModule } from "@nestjs/common";
import { Firestore } from "firebase-admin/firestore";

import { appDatabaseProvider, appDatabaseCollectionProviders } from "./appDatabase.provider";
import { AppDatabaseService } from "./appDatabase.service";
import firebaseUtils from "../utils/utils/firebase.utils";

/**
 * AppDatabase module
 */
@Module({
  providers: [AppDatabaseService],
  imports: [],
  controllers: [],
  exports: [AppDatabaseService],
})
export class AppDatabaseModule {
  static forRoot(): DynamicModule {
    /**
     * AppDatabase module provider
     */
    const dbProvider = {
      provide: appDatabaseProvider,
      useFactory: () => new Firestore(),
    };
    /**
     * AppDatabase module collection providers
     */
    const collectionProviders = appDatabaseCollectionProviders.map((providerName) => ({
      provide: providerName,
      useFactory: (db: Firestore) => db.collection(providerName).withConverter(firebaseUtils.withConverter()),
      inject: [appDatabaseProvider],
    }));
    return {
      global: true,
      module: AppDatabaseModule,
      providers: [dbProvider, ...collectionProviders],
      exports: [dbProvider, ...collectionProviders],
    };
  }
}

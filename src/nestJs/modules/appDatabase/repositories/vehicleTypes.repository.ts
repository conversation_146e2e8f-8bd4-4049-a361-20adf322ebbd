import { CollectionReference } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { VehicleType } from "../documents/vehicleType.document";

class VehicleTypesRepository extends BaseRepository<VehicleType> {
  constructor(collection: CollectionReference<VehicleType>, logger: LoggerServiceAdapter) {
    super(collection, logger);
  }
}

export default VehicleTypesRepository;

import { CollectionReference } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { FleetDocument } from "../documents/fleet.document";

class FleetRepository extends BaseRepository<FleetDocument> {
  constructor(collection: CollectionReference<FleetDocument>, logger: LoggerServiceAdapter) {
    super(collection, logger);
  }
}

export default FleetRepository;

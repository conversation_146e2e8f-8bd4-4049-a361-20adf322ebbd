import { CollectionReference } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { MeterDocument } from "../documents/meter.document";

class MeterRepository extends BaseRepository<MeterDocument> {
  constructor(meterCollection: CollectionReference<MeterDocument>, logger: LoggerServiceAdapter) {
    super(meterCollection, logger);
  }
}

export default MeterRepository;

import { CollectionReference } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import Tx from "../../database/entities/tx.entity";
import { PaymentStatus } from "../../payment/dto/paymentStatus.dto";
import { TxPayoutStatus } from "../../transaction/dto/txPayoutStatus.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { DriverTripDocument } from "../documents/driverTrip.document";

class DriverTripRepository extends BaseRepository<DriverTripDocument> {
  constructor(driverTripCollection: CollectionReference<DriverTripDocument>, logger: LoggerServiceAdapter) {
    super(driverTripCollection, logger);
  }

  async saveTrip(firestoreTx: FirebaseFirestore.Transaction, tripId: string, tx: Tx, expiresAt: Date) {
    let tripData = { ...tx.metadata, expiresAt: expiresAt } as Record<string, any>;
    if (tx.adjustment) {
      tripData = { ...tripData, adjustment: tx.adjustment };
    }
    if (tx.payoutStatus) {
      tripData = { ...tripData, payoutStatus: tx.payoutStatus };
    }
    try {
      return firestoreTx.set(this.collection.doc(tripId), DriverTripDocument.updateDateFormat(tripData), {
        merge: false,
      });
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async updateTrip(
    firestoreTx: FirebaseFirestore.Transaction,
    tripId: string,
    adjustment?: number,
    payoutStatus?: TxPayoutStatus,
    paymentStatus?: PaymentStatus,
  ) {
    try {
      return firestoreTx.set(
        this.collection.doc(tripId),
        {
          ...(adjustment ? { adjustment } : {}),
          ...(payoutStatus ? { payoutStatus } : {}),
          ...(paymentStatus ? { paymentStatus } : {}),
        },
        { merge: true },
      );
    } catch (err) {
      this.logger.error(err);
      throw err;
    }
  }

  async updateTripWithObject(tripId: string, data: Record<string, any>): Promise<DriverTripDocument> {
    return this.collection
      .doc(tripId)
      .set(data, { merge: true })
      .then(() => this.collection.doc(tripId).get())
      .then((doc) => {
        if (!doc.exists) {
          throw errorBuilder.meter.tripNotFound(this.collection.parent?.id || "", tripId);
        }
        return doc.data() as DriverTripDocument;
      })
      .catch((err) => {
        this.logger.error(err);
        throw err;
      });
  }
}

export default DriverTripRepository;

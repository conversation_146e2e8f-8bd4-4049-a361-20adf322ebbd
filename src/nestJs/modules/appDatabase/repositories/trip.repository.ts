import { CollectionReference } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import { PaymentInformationStatus } from "../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../payment/dto/paymentInformationType.dto";
import { PaymentCardInformation, TxReceipt, TxReceiptTrip } from "../../transaction/dto/txReceipt.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { TripDocument } from "../documents/trip.document";

class TripRepository extends BaseRepository<TripDocument> {
  constructor(tripCollection: CollectionReference<TripDocument>, logger: LoggerServiceAdapter) {
    super(tripCollection, logger);
  }

  async getReceiptFromTrip(tripId: string): Promise<TxReceipt> {
    const trip = await this.findOneById(tripId);
    if (!trip) {
      throw errorBuilder.trip.notFound(tripId);
    }

    const txReceiptTrip = new TxReceiptTrip();

    txReceiptTrip.id = tripId;
    txReceiptTrip.language = trip.language ?? "zh-hk";
    txReceiptTrip.licensePlate = trip.licensePlate;
    // start
    txReceiptTrip.tripStart = trip.tripStart;
    txReceiptTrip.locationStart = trip.locationStart;
    txReceiptTrip.locationStartAddress = trip.locationStartAddress;
    //end
    txReceiptTrip.tripEnd = trip.tripEnd;
    txReceiptTrip.locationEnd = trip.locationEnd;
    txReceiptTrip.locationEndAddress = trip.locationEndAddress;
    // $ & distance
    txReceiptTrip.fare = trip.fare;
    txReceiptTrip.extra = trip.extra;
    txReceiptTrip.dashFee = trip.dashFee ?? 0;
    txReceiptTrip.total = trip.total ?? 0;
    txReceiptTrip.dashTips = trip.dashTips ?? 0;
    txReceiptTrip.tripTotal = trip.tripTotal;
    txReceiptTrip.distance = trip.distance;
    txReceiptTrip.adjustment = trip.adjustment ?? 0;
    //payment
    txReceiptTrip.paymentType = trip.paymentType;
    const paymentCardInformation = new PaymentCardInformation();
    for (const paymentAudit of trip.paymentInformation || []) {
      if (
        paymentAudit.type == PaymentInformationType.CAPTURE &&
        paymentAudit.status == PaymentInformationStatus.SUCCESS
      ) {
        const body = paymentAudit.body;
        const paymentResultJson = JSON.parse(body);
        const cardType = paymentResultJson.data.payment.paymentMethod;
        const maskedPan =
          paymentResultJson.data.payment.payerFirst + "******" + paymentResultJson.data.payment.payerLast;
        paymentCardInformation.cardType = cardType;
        paymentCardInformation.maskedPan = maskedPan;
        break;
      }
    }
    txReceiptTrip.paymentCardInformation = paymentCardInformation;

    return txReceiptTrip;
  }
}

export default TripRepository;

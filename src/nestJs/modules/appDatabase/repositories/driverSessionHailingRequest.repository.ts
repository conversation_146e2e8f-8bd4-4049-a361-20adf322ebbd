import { CollectionReference } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { HailingRequestDocument } from "../documents/hailingRequest.document";

class DriverSessionHailingRequestRepository extends BaseRepository<HailingRequestDocument> {
  constructor(driverSessionCollection: CollectionReference<HailingRequestDocument>, logger: LoggerServiceAdapter) {
    super(driverSessionCollection, logger);
  }
}

export default DriverSessionHailingRequestRepository;

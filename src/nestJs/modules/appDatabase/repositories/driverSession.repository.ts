import { CollectionReference, DocumentData } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { SessionDocument } from "../documents/session.document";

class DriverSessionRepository extends BaseRepository<SessionDocument> {
  constructor(driverSessionCollection: CollectionReference<SessionDocument>, logger: LoggerServiceAdapter) {
    super(driverSessionCollection, logger);
  }

  async getSession(sessionId: string): Promise<SessionDocument | undefined> {
    return this.findOneById(sessionId);
  }

  async updateSession(sessionId: string, data: Record<string, any>) {
    await this.collection.doc(sessionId).set(data, { merge: true });
  }

  async getSessionAllTrips(sessionId: string): Promise<DocumentData[] | undefined> {
    return this.findSubCollectionById(sessionId, "trips");
  }
}

export default DriverSessionRepository;

import { CollectionReference, Filter } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import { AuthDto } from "../../auth/dto/auth.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { AuthDocument } from "../documents/auth.document";

class AuthRepository extends BaseRepository<AuthDto> {
  constructor(authCollection: CollectionReference<AuthDto>, logger: LoggerServiceAdapter) {
    super(authCollection, logger);
  }

  async getAuthByEmail(email: string): Promise<AuthDocument | undefined> {
    const auth = await this.findOneById(email);
    return auth ? AuthDocument.fromDto(auth) : undefined;
  }

  async getAuthByValidResetToken(resetToken: string): Promise<AuthDocument | undefined> {
    const auths = await this.find(
      Filter.where("password_reset.token", "==", resetToken),
      Filter.where("password_reset.expiration", ">=", new Date()),
    );
    const auth = auths[0];
    if (!auth) return undefined;
    return AuthDocument.fromDto(auth);
  }

  async saveAuth(email: string, authDocument: AuthDocument) {
    return this.collection
      .doc(email)
      .set(authDocument.toDto(), { merge: true })
      .catch((err) => {
        this.logger.error(err);
        throw err;
      });
  }
}

export default AuthRepository;

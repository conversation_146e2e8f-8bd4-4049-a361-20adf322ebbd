import { CollectionReference, DocumentData, Filter, Query, WithFieldValue } from "firebase-admin/firestore";

import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import firebaseUtils from "../../utils/utils/firebase.utils";

class BaseRepository<T extends DocumentData> {
  constructor(public readonly collection: CollectionReference<T>, public logger: LoggerServiceAdapter) {}

  public async findOneById(id: string): Promise<T | undefined> {
    const snapshot = await this.collection.doc(id).withConverter(firebaseUtils.withConverter(this.logger)).get();
    return snapshot.data() as T | undefined;
  }

  public async find(...filters: Filter[]): Promise<T[]> {
    let query: Query = this.collection;
    filters.forEach((filter) => {
      query = query.where(filter);
    });
    const snapshot = await query.get();
    return snapshot.docs.map((doc) => doc.data() as T);
  }

  public async findSubCollectionById(id: string, collectionName: string): Promise<T[] | undefined> {
    const collections = await this.collection.doc(id).collection(collectionName).get();
    return collections.docs.map((doc) => doc.data() as T);
  }

  public create(document: T & { id?: string }) {
    const docRef = document.id
      ? this.collection.doc(document.id).withConverter(firebaseUtils.withConverter(this.logger))
      : this.collection.doc().withConverter(firebaseUtils.withConverter(this.logger));
    return docRef.create(document);
  }

  /**
   * Create and return the document (without id)
   * @param document
   * @returns document
   */
  public async createAndGet(document: T): Promise<T> {
    const docRef = this.collection.doc().withConverter(firebaseUtils.withConverter(this.logger));
    // add id to the document
    const doc = { ...document, id: docRef.id } as T;
    await docRef.create(JSON.parse(JSON.stringify(doc)));
    return docRef.get().then((doc) => {
      if (!doc.exists) {
        throw errorBuilder.appDatabase.unableToCreateDocument(this.collection.path);
      }
      return doc.data() as T;
    });
  }

  public set(document: T & { id: string }) {
    return this.collection
      .doc(document.id)
      .withConverter(firebaseUtils.withConverter(this.logger))
      .set(document, { merge: true });
  }

  public async updateDocumentAndGetResult(documentId: string, data: Partial<T>): Promise<T> {
    return this.collection
      .doc(documentId)
      .set(data, { merge: true })
      .then(() => this.collection.doc(documentId).get())
      .then((doc) => {
        if (!doc.exists) {
          throw errorBuilder.appDatabase.notFoundInFirestore(this.collection.path, documentId);
        }
        return doc.data() as T;
      });
  }

  /**
   * Atomic create unique document and return the document, throw error if document already exist
   * @param documentId string
   * @param WithFieldValue<T> data
   */
  public async createUniqueDocument(documentId: string, data: WithFieldValue<T>): Promise<T> {
    const docRef = await this.collection.firestore.runTransaction(async (transaction) => {
      const docRef = this.collection.doc(documentId).withConverter(firebaseUtils.withConverter(this.logger));
      const doc = await transaction.get(docRef);
      if (doc.exists) {
        throw errorBuilder.appDatabase.documentAlreadyExists(this.collection.path, documentId);
      }
      transaction.set(docRef, data);
      return docRef;
    });

    return docRef.get().then((doc) => {
      if (!doc.exists) {
        throw errorBuilder.appDatabase.unableToCreateDocument(this.collection.path);
      }
      return doc.data() as T;
    });
  }
}

export default BaseRepository;

import { CollectionReference } from "firebase-admin/firestore";
import memoize from "memoizee";

import BaseRepository from "./baseRepository.repository";
import { NotificationTemplate } from "../../fcm/types";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import {
  ConfigurationDocument,
  ConfigurationHailDocument,
  ConfigurationNotificationConfigDocument,
} from "../documents/configuration.document";
import { ConfigurationWatiTemplateDocument } from "../documents/configurationWatiTemplates.document";

class ConfigurationRepository extends BaseRepository<ConfigurationDocument> {
  constructor(configurationCollection: CollectionReference<ConfigurationDocument>, logger: LoggerServiceAdapter) {
    super(configurationCollection, logger);
  }

  private async getEnableSqlLoggingMethod(): Promise<boolean> {
    const enableSqlLogging = ConfigurationDocument.ENABLE_SQL_LOGGING;

    const serverSnapshot = await this.collection.doc(ConfigurationDocument.serverCollectionName).get();
    if (!serverSnapshot.exists) {
      await this.collection.doc(ConfigurationDocument.serverCollectionName).set({ enableSqlLogging }, { merge: true });
      return enableSqlLogging;
    }

    const serverConfig = serverSnapshot.data();

    if (!serverConfig || serverConfig.enableSqlLogging === undefined) {
      await this.collection.doc(ConfigurationDocument.serverCollectionName).set({ enableSqlLogging }, { merge: true });
      return enableSqlLogging;
    }

    return serverConfig.enableSqlLogging;
  }

  private readonly memoizedGetEnableSqlLogging = memoize(this.getEnableSqlLoggingMethod, {
    promise: true,
    maxAge: 10000,
  });

  async getEnableSqlLogging(): Promise<boolean> {
    return this.memoizedGetEnableSqlLogging();
  }

  async getCashTripTTL(): Promise<string> {
    const cashTripTtl = ConfigurationDocument.CASH_TRIP_TTL;

    const serverSnapshot = await this.collection.doc(ConfigurationDocument.serverCollectionName).get();
    if (!serverSnapshot.exists) {
      await this.collection.doc(ConfigurationDocument.serverCollectionName).set({ cashTripTtl }, { merge: true });
      return cashTripTtl;
    }

    const serverConfig = serverSnapshot.data();

    if (!serverConfig || !serverConfig.cashTripTtl) {
      await this.collection.doc(ConfigurationDocument.serverCollectionName).set({ cashTripTtl }, { merge: true });
      return cashTripTtl;
    }

    return serverConfig.cashTripTtl;
  }

  async getDashTripTTL(): Promise<string> {
    const dashTripTtl = ConfigurationDocument.DASH_TRIP_TTL;

    const serverSnapshot = await this.collection.doc(ConfigurationDocument.serverCollectionName).get();

    if (!serverSnapshot.exists) {
      await this.collection.doc(ConfigurationDocument.serverCollectionName).set({ dashTripTtl }, { merge: true });
      return dashTripTtl;
    }

    const serverConfig = serverSnapshot.data();
    if (!serverConfig || !serverConfig.dashTripTtl) {
      await this.collection.doc(ConfigurationDocument.serverCollectionName).set({ dashTripTtl }, { merge: true });
      return dashTripTtl;
    }

    return serverConfig.dashTripTtl;
  }

  private async getMaxTipsAllowedAmountMethod(): Promise<number> {
    const maxTipsAllowedAmount = ConfigurationDocument.MAX_TIPS_ALLOWED_AMOUNT;

    const userAppSnapshot = await this.collection.doc(ConfigurationDocument.subCollectionUserAppName).get();
    if (!userAppSnapshot.exists) {
      await this.collection
        .doc(ConfigurationDocument.subCollectionUserAppName)
        .set({ maxTipsAllowedAmount }, { merge: true });
      return maxTipsAllowedAmount;
    }

    const userAppConfig = userAppSnapshot.data();
    if (!userAppConfig || userAppConfig.maxTipsAllowedAmount === undefined) {
      await this.collection
        .doc(ConfigurationDocument.subCollectionUserAppName)
        .set({ maxTipsAllowedAmount }, { merge: true });
      return maxTipsAllowedAmount;
    }

    return userAppConfig.maxTipsAllowedAmount;
  }

  private readonly memoizedGetMaxTipsAllowedAmount = memoize(this.getMaxTipsAllowedAmountMethod, {
    promise: true,
    maxAge: 10000,
  });

  async getMaxTipsAllowedAmount(): Promise<number> {
    return this.memoizedGetMaxTipsAllowedAmount();
  }

  private async getNotificationTemplatesMethod(): Promise<NotificationTemplate[]> {
    const templatesSnapshot = await this.collection.doc(ConfigurationDocument.notificationTemplateCollectionName).get();
    if (!templatesSnapshot.exists) {
      throw errorBuilder.pushNotification.templatesCollectionNotFoundInFirestore();
    }
    const templates = templatesSnapshot.data();
    if (!templates) {
      throw errorBuilder.pushNotification.templatesNoDataInFirestore();
    }
    return templates.notificationTemplates;
  }

  private readonly memoizedGetNotificationTemplatesMethod = memoize(this.getNotificationTemplatesMethod, {
    promise: true,
    maxAge: 10000,
  });

  async getNotificationTemplates(): Promise<NotificationTemplate[]> {
    return this.memoizedGetNotificationTemplatesMethod();
  }

  private async getWatiTemplatesMethod(): Promise<ConfigurationWatiTemplateDocument> {
    const watiTemplateSnapshot = await this.collection.doc(ConfigurationWatiTemplateDocument.documentName).get();
    if (!watiTemplateSnapshot.exists) {
      throw errorBuilder.config.watiTemplatesNotFound();
    }
    const watiTemplate = watiTemplateSnapshot.data() as unknown as ConfigurationWatiTemplateDocument;
    if (!watiTemplate) {
      throw errorBuilder.config.watiTemplatesNotFound();
    }
    return watiTemplate;
  }

  private readonly memoizedGetWatiTemplates = memoize(this.getWatiTemplatesMethod, {
    promise: true,
    maxAge: 10000,
  });

  async getWatiTemplates(): Promise<ConfigurationWatiTemplateDocument> {
    return this.memoizedGetWatiTemplates();
  }

  async getEnable3DSecure(): Promise<boolean> {
    const serverSnapshot = await this.collection.doc(ConfigurationDocument.serverCollectionName).get();
    if (!serverSnapshot.exists) {
      throw errorBuilder.appDatabase.notFoundInFirestore(ConfigurationDocument.serverCollectionName, "enable3DSecure");
    }
    const templates = serverSnapshot.data();
    if (!templates) {
      throw errorBuilder.appDatabase.notFoundInFirestore(ConfigurationDocument.serverCollectionName, "enable3DSecure");
    }
    return Boolean(templates.enable3DSecure);
  }

  private async getMigratingPaymentToKrakenMethod(): Promise<boolean> {
    const serverSnapshot = await this.collection.doc(ConfigurationDocument.serverCollectionName).get();
    if (!serverSnapshot.exists) {
      throw errorBuilder.appDatabase.notFoundInFirestore(
        ConfigurationDocument.serverCollectionName,
        "migratingPaymentToKraken",
      );
    }
    const templates = serverSnapshot.data();
    if (!templates) {
      throw errorBuilder.appDatabase.notFoundInFirestore(
        ConfigurationDocument.serverCollectionName,
        "migratingPaymentToKraken",
      );
    }
    return Boolean(templates.migratingPaymentToKraken);
  }

  private readonly memoizedGetMigratingPaymentToKrakenMethod = memoize(this.getMigratingPaymentToKrakenMethod, {
    promise: true,
    maxAge: 10000,
  });

  async getMigratingPaymentToKraken(): Promise<boolean> {
    return this.memoizedGetMigratingPaymentToKrakenMethod();
  }

  private async getIsBulkPayoutEnableMethod(): Promise<boolean> {
    const serverSnapshot = await this.collection.doc(ConfigurationDocument.serverCollectionName).get();
    if (!serverSnapshot.exists) {
      throw errorBuilder.appDatabase.notFoundInFirestore(
        ConfigurationDocument.serverCollectionName,
        "isBulkPayoutEnabled",
      );
    }
    const templates = serverSnapshot.data();
    if (!templates) {
      throw errorBuilder.appDatabase.notFoundInFirestore(
        ConfigurationDocument.serverCollectionName,
        "isBulkPayoutEnabled",
      );
    }
    return Boolean(templates.isBulkPayoutEnabled);
  }

  private readonly memoizedGetIsBulkPayoutEnableMethod = memoize(this.getIsBulkPayoutEnableMethod, {
    promise: true,
    maxAge: 10000,
  });

  async getIsBulkPayoutEnable(): Promise<boolean> {
    return this.memoizedGetIsBulkPayoutEnableMethod();
  }

  async getHailConfig(): Promise<ConfigurationHailDocument> {
    const hailConfigSnapshot = await this.collection.doc(ConfigurationHailDocument.documentName).get();

    if (!hailConfigSnapshot.exists) {
      throw errorBuilder.appDatabase.notFoundInFirestore(ConfigurationHailDocument.documentName, "hailConfig");
    }
    const hailConfig = hailConfigSnapshot.data() as unknown as ConfigurationHailDocument;

    if (!hailConfig) {
      return {
        fleetCancellationFee: 120,
        dashCancellationFee: 15,
      };
    }

    if (hailConfig.fleetCancellationFee == undefined || hailConfig.fleetCancellationFee == null) {
      hailConfig.fleetCancellationFee = 120;
    }
    if (hailConfig.dashCancellationFee == undefined || hailConfig.dashCancellationFee == null) {
      hailConfig.dashCancellationFee = 15;
    }

    return hailConfig;
  }

  async getNotificationConfig(): Promise<ConfigurationNotificationConfigDocument> {
    const notificationConfigSnapshot = await this.collection
      .doc(ConfigurationNotificationConfigDocument.documentName)
      .get();

    const defaultNotificationConfig: ConfigurationNotificationConfigDocument = {
      beforeMinutesThresholdBeforeFirstOrderPickupNotification: -90,
      beforeMinutesThresholdBeforeSecondOrderPickupNotification: -30,
    };

    const notificationConfig = notificationConfigSnapshot.data() as unknown as ConfigurationNotificationConfigDocument;

    if (!notificationConfig) {
      return defaultNotificationConfig;
    }

    if (
      notificationConfig.beforeMinutesThresholdBeforeFirstOrderPickupNotification == undefined ||
      notificationConfig.beforeMinutesThresholdBeforeFirstOrderPickupNotification == null
    ) {
      notificationConfig.beforeMinutesThresholdBeforeFirstOrderPickupNotification =
        defaultNotificationConfig.beforeMinutesThresholdBeforeFirstOrderPickupNotification;
    }
    if (
      notificationConfig.beforeMinutesThresholdBeforeSecondOrderPickupNotification == undefined ||
      notificationConfig.beforeMinutesThresholdBeforeSecondOrderPickupNotification == null
    ) {
      notificationConfig.beforeMinutesThresholdBeforeSecondOrderPickupNotification =
        defaultNotificationConfig.beforeMinutesThresholdBeforeSecondOrderPickupNotification;
    }

    return notificationConfig;
  }
}

export default ConfigurationRepository;

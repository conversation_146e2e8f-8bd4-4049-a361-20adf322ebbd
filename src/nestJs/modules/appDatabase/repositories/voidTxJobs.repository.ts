import { CollectionReference } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { VoidTxJobDocument } from "../documents/voidTxJob.document";

class VoidTxJobsRepository extends BaseRepository<VoidTxJobDocument> {
  constructor(collection: CollectionReference<VoidTxJobDocument>, logger: LoggerServiceAdapter) {
    super(collection, logger);
  }
}

export default VoidTxJobsRepository;

import { randomUUID } from "crypto";

import { CollectionReference } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { LockDocument } from "../documents/lock.document";

class MeterTripLockRepository extends BaseRepository<LockDocument> {
  constructor(collection: CollectionReference<LockDocument>, logger: LoggerServiceAdapter) {
    super(collection, logger);
  }

  getActiveLock = async (): Promise<LockDocument | undefined> => {
    // Locked when expired_at is in the future, unlocked_at is null and created_by is the user
    const snapshot = await this.collection.where("expired_at", ">", new Date()).where("unlocked_at", "==", null).get();

    if (snapshot.empty) {
      return undefined;
    }

    return snapshot.docs[0].data() as LockDocument;
  };

  createLock = async (userId: string, timeout: number): Promise<LockDocument> => {
    const lock: LockDocument = {
      id: randomUUID(),
      createdAt: new Date(),
      createdBy: userId,
      expiredAt: new Date(Date.now() + 1000 * timeout),
      unlockedAt: null,
    };

    await this.create(lock);

    return lock;
  };
}

export default MeterTripLockRepository;

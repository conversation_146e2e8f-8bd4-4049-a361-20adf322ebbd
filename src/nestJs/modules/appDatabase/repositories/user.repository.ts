import { CollectionReference } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { UserDocument } from "../documents/user.document";

class UsersRepository extends BaseRepository<UserDocument> {
  constructor(sessionCollection: CollectionReference<UserDocument>, logger: LoggerServiceAdapter) {
    super(sessionCollection, logger);
  }
}

export default UsersRepository;

import { CollectionReference } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { NotificationDocument } from "../documents/driver.document";

class DriverNotificationsRepository extends BaseRepository<NotificationDocument> {
  constructor(driverNotificationsCollection: CollectionReference<NotificationDocument>, logger: LoggerServiceAdapter) {
    super(driverNotificationsCollection, logger);
  }
}

export default DriverNotificationsRepository;

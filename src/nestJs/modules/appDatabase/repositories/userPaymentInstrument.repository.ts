import { CollectionReference } from "firebase-admin/firestore";

import BaseRepository from "./baseRepository.repository";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { UserPaymentInstrumentDocument } from "../documents/userPaymentInstrument.document";

class UserPaymentInstrumentRepository extends BaseRepository<UserPaymentInstrumentDocument> {
  constructor(
    userPaymentInstrumentCollection: CollectionReference<UserPaymentInstrumentDocument>,
    logger: LoggerServiceAdapter,
  ) {
    super(userPaymentInstrumentCollection, logger);
  }
}

export default UserPaymentInstrumentRepository;

import { AuthDocument } from "./documents/auth.document";
import { DriverDocument } from "./documents/driver.document";
import { FleetVehicleType } from "./documents/fleetVehicleType.document";
import { MeterDocument } from "./documents/meter.document";
import { QrCodeDocument } from "./documents/qrCode.document";
import { SessionDocument } from "./documents/session.document";
import { TripDocument } from "./documents/trip.document";
import { UserDocument } from "./documents/user.document";
import { VehicleType } from "./documents/vehicleType.document";
import { VoidTxJobDocument } from "./documents/voidTxJob.document";

/**
 * AppDatabase provider
 */
export const appDatabaseProvider = "firestoredb";

/**
 * AppDatabase collection providers
 */
export const appDatabaseCollectionProviders: string[] = [
  TripDocument.collectionName,
  MeterDocument.collectionName,
  DriverDocument.collectionName,
  SessionDocument.collectionName,
  UserDocument.collectionName,
  QrCodeDocument.collectionName,
  AuthDocument.COLLECTION_NAME,
  VehicleType.collectionName,
  VoidTxJobDocument.collectionName,
  FleetVehicleType.collectionName,
];

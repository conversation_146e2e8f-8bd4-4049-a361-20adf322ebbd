import Joi from "joi";

import { SessionDocument } from "@nest/modules/appDatabase/documents/session.document";

import { MerchantMetadata } from "./merchantMetadata.dto";
import Merchant from "../../database/entities/merchant.entity";
import { PayoutPeriod } from "../../transaction/dto/payout.dto";

export enum Gender {
  M = "M",
  F = "F",
}

export enum ApplicationStatus {
  UNDER_REVIEW = "UNDER_REVIEW",
  FOLLOW_UP_NEEDED = "FOLLOW_UP_NEEDED",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
}

/**
 * Gender schema
 */
export const genderSchema = Joi.string<Gender>().valid(...Object.values(Gender));

export const merchantSchema = Joi.object<Merchant>({
  id: Joi.string().required(),
  phoneNumber: Joi.string().required(),
  email: Joi.string().allow(null).optional(),
  name: Joi.string().optional(),
  createdAt: Joi.date().optional(),
  updatedAt: Joi.date().optional(),
  activatedAt: Joi.date().allow(null).optional(),
  bankAccount: Joi.string().allow(null, "").optional(),
  bankAccountOwnerName: Joi.string().allow(null, "").optional(),
  bankId: Joi.string().allow(null, "").optional(),
  gender: genderSchema.allow(null, "").optional(),
  idDocument: Joi.string().allow(null, "").optional(),
  idDocumentPhoto: Joi.string().allow(null, "").optional(),
  isAdmin: Joi.boolean().optional(),
  nameLocal: Joi.string().allow(null, "").optional(),
  profilePhoto: Joi.string().allow(null, "").optional(),
  session: Joi.object<SessionDocument>().allow(null).optional(),
  metadata: Joi.object<MerchantMetadata>().allow(null).optional(),
  lastExpectedEndTime: Joi.date().allow(null).optional(),
  showCashTrip: Joi.boolean().optional(),
  payoutPeriod: Joi.string<PayoutPeriod>()
    .valid(...Object.values(PayoutPeriod))
    .allow(null)
    .optional(),
  applicationStatus: Joi.string<ApplicationStatus>()
    .valid(...Object.values(ApplicationStatus))
    .allow(null)
    .optional(),
  bankCardPhoto: Joi.string().allow(null).optional(),
  dateOfBirth: Joi.date().allow(null).optional(),
  referralCode: Joi.string().allow(null).optional(),
  referrerId: Joi.string().allow(null).optional(),
}).unknown(true);

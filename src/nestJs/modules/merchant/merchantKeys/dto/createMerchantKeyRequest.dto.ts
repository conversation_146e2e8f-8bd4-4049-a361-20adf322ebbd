import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { KeyType } from "@nest/modules/database/entities/merchantKey.entity";

export class CreateMerchantKeyRequestDto {
  @ApiProperty({ type: "enum", enum: KeyType })
  type: KeyType;
}

export const createMerchantKeySchema = Joi.object<CreateMerchantKeyRequestDto>({
  type: Joi.string()
    .valid(...Object.values(KeyType))
    .required(),
});

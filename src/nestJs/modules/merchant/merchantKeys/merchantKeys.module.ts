import { MiddlewareConsumer, Module } from "@nestjs/common";

import { MerchantAuthMiddleware } from "@nest/infrastructure/middlewares/merchantAuth.middleware";
import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { MerchantKeyRepository } from "@nest/modules/database/repositories/merchantKey.repository";
import { EncryptionModule } from "@nest/modules/encryption/encryption.module";

import { MerchantKeysController } from "./merchantKeys.controller";
import { MerchantKeysService } from "./merchantKeys.service";

@Module({
  imports: [EncryptionModule],
  controllers: [MerchantKeysController],
  providers: [MerchantKeysService, MerchantKeyRepository, MerchantRepository],
  exports: [MerchantKeysService],
})
export class MerchantKeysModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(MerchantAuthMiddleware).forRoutes("/merchants/keys");
  }
}

import { Controller, Get, Post, Body, Delete, Param, Req, Res } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { AuthenticatedRequest, Response } from "express";

import { MerchantRoleData } from "@nest/modules/admin/adminAuthUser/dto/updateUserRole.dto";
import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { apiTags } from "@nest/modules/utils/utils/swagger.utils";
import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";

import { CreateMerchantKeyRequestDto, createMerchantKeySchema } from "./dto/createMerchantKeyRequest.dto";
import { MerchantKeyResponseDto } from "./dto/merchantKeyResponse.dto";
import { MerchantKeysService } from "./merchantKeys.service";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.merchant)
export class MerchantKeysController {
  constructor(
    private readonly merchantKeysService: MerchantKeysService,
    private readonly merchantRepository: MerchantRepository,
    private readonly logger: LoggerServiceAdapter,
  ) {}

  @Post()
  @ApiOperation({ summary: "Create merchant key" })
  @ApiResponse({ status: 201, description: "Create merchant key" })
  async create(
    @Req() req: AuthenticatedRequest<MerchantRoleData>,
    @Body(new JoiValidationPipe(createMerchantKeySchema)) createMerchantKeyRequestDto: CreateMerchantKeyRequestDto,
  ): Promise<MerchantKeyResponseDto> {
    this.logger.info(`Creating ${createMerchantKeyRequestDto.type} merchant key`);
    const merchantId = req.user.merchantId;
    const merchant = await this.merchantRepository.findOneBy({ id: merchantId });
    if (!merchant) {
      throw errorBuilder.merchant.notFound(merchantId);
    }
    this.logger.info(`Creating ${createMerchantKeyRequestDto.type} merchant key for merchant ${merchantId}`);
    return this.merchantKeysService.create(merchant, createMerchantKeyRequestDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all keys for merchant" })
  @ApiResponse({ status: 200, description: "Get all keys for merchant" })
  findAll(@Req() req: AuthenticatedRequest): Promise<MerchantKeyResponseDto[]> {
    return this.merchantKeysService.findAllForMerchantAndDecrypt(req.user.merchantId);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Expire a merchant key" })
  @ApiResponse({ status: 200, description: "Expire a merchant key" })
  async remove(@Req() req: AuthenticatedRequest, @Res() res: Response, @Param("id") id: string) {
    await this.merchantKeysService.remove(id, req.user.merchantId);
    res.sendStatus(200);
  }
}

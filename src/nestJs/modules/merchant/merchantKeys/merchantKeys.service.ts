import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { IsNull } from "typeorm";

import Merchant from "@nest/modules/database/entities/merchant.entity";
import MerchantKey from "@nest/modules/database/entities/merchantKey.entity";
import { MerchantKeyRepository } from "@nest/modules/database/repositories/merchantKey.repository";
import { CryptoKey, KeyRing, KmsEncryptionService } from "@nest/modules/encryption/kmsEncryption.service";
import { TokenUtils } from "@nest/modules/utils/utils/token.utils";

import { CreateMerchantKeyRequestDto } from "./dto/createMerchantKeyRequest.dto";
import { MerchantKeyResponseDto } from "./dto/merchantKeyResponse.dto";

@Injectable()
export class MerchantKeysService {
  private static readonly KEYRING = KeyRing.MERCHANT_KEYRING;
  private static readonly CRYPTO_KEY = CryptoKey.MERCHANT_KEYS;

  constructor(
    @InjectRepository(MerchantKeyRepository) private readonly merchantKeyRepository: MerchantKeyRepository,
    private readonly kmsEncryptionService: KmsEncryptionService,
  ) {}

  async create(
    merchant: Merchant,
    createMerchantKeyRequestDto: CreateMerchantKeyRequestDto,
  ): Promise<MerchantKeyResponseDto> {
    const randomToken = TokenUtils.generateTokenWithIdentifier(
      MerchantKey.getTokenIdentifier(createMerchantKeyRequestDto.type),
    );

    const encryptedKey = await this.encryptKey(randomToken);

    const merchantKey = await this.merchantKeyRepository.save({
      ...createMerchantKeyRequestDto,
      merchant: merchant,
      encryptedKey,
    });

    return {
      id: merchantKey.id,
      type: merchantKey.type,
      key: randomToken,
    };
  }

  async findAllForMerchantAndDecrypt(merchantId: string): Promise<MerchantKeyResponseDto[]> {
    const merchantKeys = await this.merchantKeyRepository.find({
      where: { merchant: { id: merchantId }, deletedAt: IsNull() },
    });
    const decryptedKeys = await Promise.all(
      merchantKeys.map((merchantKey) => this.decryptKey(merchantKey.encryptedKey)),
    );
    return merchantKeys.map((merchantKey, index) => ({
      id: merchantKey.id,
      type: merchantKey.type,
      key: decryptedKeys[index],
    }));
  }

  async findByIdAndDecrypt(id: string): Promise<MerchantKeyResponseDto | null> {
    const merchantKey = await this.merchantKeyRepository.findOneBy({ id: id, deletedAt: IsNull() });
    if (!merchantKey) {
      return null;
    }
    const decryptedKey = await this.decryptKey(merchantKey.encryptedKey);
    return {
      id: merchantKey.id,
      type: merchantKey.type,
      key: decryptedKey,
    };
  }

  remove(id: string, merchantId: string) {
    return this.merchantKeyRepository.softDelete({ id: id, merchant: { id: merchantId }, deletedAt: IsNull() });
  }

  private async encryptKey(key: string): Promise<Uint8Array> {
    return this.kmsEncryptionService.encrypt(key, MerchantKeysService.KEYRING, MerchantKeysService.CRYPTO_KEY);
  }

  private async decryptKey(encryptedKey: string | Uint8Array): Promise<string> {
    return this.kmsEncryptionService.decrypt(encryptedKey, MerchantKeysService.KEYRING, MerchantKeysService.CRYPTO_KEY);
  }
}

import { Module } from "@nestjs/common";
import { RouterModule } from "nest-router";

import { MerchantController } from "./merchant.controller";
import { merchantRoutes } from "./merchant.routes";
import { MerchantService } from "./merchant.service";
import { DriverModule } from "./merchantDriver/merchantDriver.module";
import { MerchantKeysModule } from "./merchantKeys/merchantKeys.module";

@Module({
  imports: [DriverModule, MerchantKeysModule, RouterModule.forRoutes(merchantRoutes)],
  providers: [MerchantService],
  controllers: [MerchantController],
  exports: [MerchantService],
})
export class MerchantModule {}

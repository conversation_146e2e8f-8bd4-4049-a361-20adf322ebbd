import { randomUUID } from "crypto";

import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Inject, Injectable } from "@nestjs/common";
import { Cache } from "cache-manager";
import { DecodedIdToken } from "firebase-admin/auth";
import { DocumentData, Timestamp } from "firebase-admin/firestore";
import { Notification } from "firebase-admin/messaging";
import Joi from "joi";
import { DeepPartial, In, InsertResult } from "typeorm";

import {
  DriverDocument,
  NotificationDocument,
  NotificationDocumentType,
  NotificationStatus,
} from "@nest/modules/appDatabase/documents/driver.document";
import { SessionDocument } from "@nest/modules/appDatabase/documents/session.document";
import { WebhookRepository } from "@nest/modules/database/repositories/webhook.repository";
import { FcmService } from "@nest/modules/fcm/fcm.service";
import { HailStatus } from "@nest/modules/hailing/dto/updateHail.dto";
import { HailingService } from "@nest/modules/hailing/hailing.service";
import { PaymentType } from "@nest/modules/payment/dto/paymentType.dto";
import {
  PublishMessageForWebhookProcessing,
  WebhookEventType,
  WebhookMessage,
} from "@nest/modules/pubsub/dto/PublishMessageForWebhookProcessing.dto";
import { TripService } from "@nest/modules/transaction/transactionFactory/modules/trip/trip.service";
import casesUtils from "@nest/modules/utils/utils/case/case.utils";
import firebaseUtils from "@nest/modules/utils/utils/firebase.utils";
import { roundOneDecimal } from "@nest/modules/utils/utils/number.utils";
import { LanguageOption } from "@nest/modules/validation/dto/language.dto";
import { WebhookService } from "@nest/modules/webhook/webhook.service";

import { UpsertDriverNotificationTokenBody } from "./dto/driverNotificationToken.dto";
import { IngestFromBucketFile } from "./dto/ingestFromBucketFile.dto";
import { CreateMeterBody, GetMeterDataResponse, GetMeterhasOngoingTripResponse } from "./dto/meterVehicle.dto";
import { NonDashMeterPairDto } from "./dto/nonDashMeterPair.dto";
import { PushNotificationDto, PushNotificationsResponse } from "./dto/pushNotification.dto";
import { SessionPairResponseDto } from "./dto/sessionPairResponse.dto";
import { UpdateTripEndDto } from "./dto/updateTripEnd.dto";
import { AppDatabaseService } from "../../appDatabase/appDatabase.service";
import { MeterDocument, MeterSettingsVehicle } from "../../appDatabase/documents/meter.document";
import { TripDocument } from "../../appDatabase/documents/trip.document";
import Merchant, { PlatformMerchantType } from "../../database/entities/merchant.entity";
import Tx from "../../database/entities/tx.entity";
import { MerchantRepository } from "../../database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "../../database/repositories/merchantNotificationToken.repository";
import { TxRepository } from "../../database/repositories/tx.repository";
import { PublishMessageForCopyTripToDriverProcessingParams } from "../../pubsub/dto/publishMessageForDriverTripProcessing.dto";
import { StorageService } from "../../storage/storage.service";
import { isTxHailing, isTxTrip, TxHailingRequest } from "../../transaction/dto/tx.dto";
import { TxHailingRequestStatus } from "../../transaction/dto/txHailingRequest.dto";
import { TxPayoutStatus } from "../../transaction/dto/txPayoutStatus.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { UtilsService } from "../../utils/utils.service";
import { ValidationService } from "../../validation/validation.service";

@Injectable()
export class DriverService {
  constructor(
    private readonly storageService: StorageService,
    private readonly merchantRepository: MerchantRepository,
    private readonly txRepository: TxRepository,
    private merchantNotificationTokenRepository: MerchantNotificationTokenRepository,
    private readonly appDatabaseService: AppDatabaseService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(UtilsService) private readonly utilsService: UtilsService,
    private readonly hailingService: HailingService,
    private readonly tripService: TripService,
    private fcmService: FcmService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly webhookService: WebhookService,
    @Inject(WebhookRepository) private webhookRepository: WebhookRepository,
  ) {}

  async ingestFromBucketFile({ bucketName, fileName }: IngestFromBucketFile): Promise<InsertResult> {
    const buffer = await this.storageService.readFileFromBucket(bucketName, fileName);
    const content = JSON.parse(buffer.toString());

    const [fileContent] = ValidationService.validate<[{ data: Record<string, Record<string, unknown>> }]>([
      {
        value: content,
        schema: Joi.object({
          meta: Joi.object(),
          data: Joi.object().required(),
        }).required(),
      },
    ]);

    const merchants = Object.entries(fileContent.data).map(([phone_number, merchantData]) =>
      Merchant.fromJson({ ...merchantData, phone_number }),
    );

    return this.upsertMerchants(merchants);
  }

  async upsertMerchants(merchants: Merchant[]) {
    return this.merchantRepository.upsert(merchants, ["phoneNumber", "platformMerchantType"]);
  }

  async copyToDriverCollectionInFireStore(data: PublishMessageForCopyTripToDriverProcessingParams) {
    const { txId, expiresAt, txIds } = data;
    return await this.appDatabaseService.runTransaction(async (firestoreTx) => {
      if (txId) {
        const foundTxWithTripEnd = await this.txRepository.findOne({
          relations: ["merchant"],
          where: { id: txId },
        });
        this.logger.info("foundTxWithTripEnd: ", { foundTxWithTripEnd });
        if (!foundTxWithTripEnd) {
          throw errorBuilder.transaction.notFound(txId);
        }
        await this.copyTrip(foundTxWithTripEnd, firestoreTx, expiresAt);
      } else if (txIds && txIds.length > 0) {
        const txs = await this.txRepository.find({ relations: ["merchant"], where: { id: In(txIds) } });
        await txs.reduce(async (prePromise, currentTx) => {
          await prePromise;
          try {
            console.log("COPYING TRIP");
            await this.copyTrip(currentTx, firestoreTx, expiresAt);
          } catch (e) {
            this.logger.error(e);
          }
        }, Promise.resolve());
      }
    });
  }

  private async copyHailing(tx: TxHailingRequest, firestoreTx: FirebaseFirestore.Transaction) {
    try {
      this.logger.info("merchantDriverService/copyHailing-start", { tx });

      const driverId =
        tx.merchant?.platformMerchantType === PlatformMerchantType.DASH || tx.merchant?.platformMerchantType == null
          ? tx.merchant?.phoneNumber
          : `${tx.merchant?.phoneNumber}-${tx.merchant?.platformMerchantType}`;
      if (!driverId) {
        throw errorBuilder.merchant.driver.notFound(driverId);
      }

      const driverFirestore = await firestoreTx
        .get(this.appDatabaseService.driverRepository().collection.doc(driverId))
        .then((doc) => doc.data());

      if (!driverFirestore) {
        throw errorBuilder.merchant.driver.notFound(driverId);
      }

      const billing = this.tripService.generateBillingObject(tx);

      billing.tripTotal = tx.payoutAmount ?? 0;

      const tripToCopy = {
        ...tx.metadata,
        billing,
        tripStart: new Date(tx.metadata.createdAt),
        locationStartAddress: tx.metadata.itinerary[0].i18n.zhHK?.formattedAddress ?? "",
        locationEndAddress: tx.metadata.itinerary[tx.metadata.itinerary.length - 1].i18n.zhHK?.formattedAddress ?? "",
        type: "HAIL",
        status: TxHailingRequestStatus.CANCELLED,
        cancellationFee: billing.tripTotal,
        paymentType: "DASH",
        total: billing.tripTotal,
        tripTotal: billing.tripTotal,
        meterSoftwareVersion: "4.48",
      };

      if (driverFirestore.session?.id) {
        firestoreTx.set(
          this.appDatabaseService
            .driverSessionHailingRequestRepository(driverFirestore.id, driverFirestore.session.id)
            .collection.doc(tx.id),
          tripToCopy,
          { merge: true },
        );
      }

      firestoreTx.set(
        this.appDatabaseService.driverHailingRequestRepository(driverFirestore.id).collection.doc(tx.id),
        tripToCopy,
        { merge: true },
      );

      this.logger.info("merchantDriverService/copyHailing-end", { tx, trip: tripToCopy });

      return tx;
    } catch (e) {
      this.logger.error("merchantDriverService/copyHailing-end", { tx }, e as Error);
      throw e;
    }
  }

  private async copyTrip(tx: Tx, firestoreTx: FirebaseFirestore.Transaction, expiresAt?: Date) {
    const driverId = tx.merchant?.phoneNumber;
    if (!driverId) {
      throw errorBuilder.merchant.driver.notFound(tx.id);
    }
    this.logger.info(`Copying trip for driverId:  ${driverId}`, { tx });
    if (isTxHailing(tx)) {
      return this.copyHailing(tx, firestoreTx);
    } else if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "DriverService/copyToDriverCollectionInFirStore");
    }
    const sessionId = tx.metadata?.session?.id;
    this.logger.info(`sessionId:  ${sessionId}`);
    if (expiresAt) {
      this.logger.info(`before saveTrip txId: ${tx.id}`);
      await this.appDatabaseService.driverTripRepository(driverId).saveTrip(firestoreTx, tx.id, tx, expiresAt);
      this.logger.info(`after saveTrip driverId: ${driverId}; txId: ${tx.id}`);
      if (sessionId) {
        await this.appDatabaseService
          .driverSessionTripRepository(driverId, sessionId)
          .saveSessionTrip(firestoreTx, tx.id, tx, expiresAt);
        this.logger.info(`after saveSessionTrip driverId: ${driverId}; sessionId: ${sessionId}; txId: ${tx.id}`);
      }
    } else {
      await this.appDatabaseService
        .driverTripRepository(driverId)
        .updateTrip(firestoreTx, tx.id, tx.adjustment, tx.payoutStatus);
      if (sessionId) {
        await this.appDatabaseService
          .driverSessionTripRepository(driverId, sessionId)
          .updateSessionTrip(firestoreTx, tx.id, tx.adjustment, tx.payoutStatus);
      }
    }
    return tx;
  }

  async driverTripChanged(
    driverId: string,
    sessionId: string,
    dataAfter: FirebaseFirestore.DocumentData,
    dataBefore?: FirebaseFirestore.DocumentData,
  ) {
    const session = await this.appDatabaseService.driverSessionRepository(driverId).getSession(sessionId);
    if (!session) {
      throw errorBuilder.merchant.driver.session.sessionNotFoundOrNoData(driverId, sessionId);
    }
    const trips = await this.appDatabaseService.driverSessionRepository(driverId).getSessionAllTrips(sessionId);
    if (!trips || trips.length === 0) {
      this.logger.error("Session should have trips data. if no, need delete this session", {
        driver: driverId,
        session: sessionId,
      });
      return;
    }
    if (!dataBefore) {
      await this.calculateSessionTotal(driverId, sessionId, trips);
      await this.calculateHasPayout(driverId, sessionId, trips);
    } else {
      if (dataAfter.adjustment !== dataBefore?.adjustment || dataAfter.payment_type !== dataBefore?.payment_type) {
        await this.calculateSessionTotal(driverId, sessionId, trips);
      }
      if (dataAfter.payout_status !== dataBefore.payout_status || dataAfter.payment_type !== dataBefore?.payment_type) {
        await this.calculateHasPayout(driverId, sessionId, trips);
      }
    }
  }

  private async calculateSessionTotal(driverId: string, sessionId: string, trips: DocumentData[]) {
    this.logger.info(`calculateSessionTotal called driverId: ${driverId}; sessionId: ${sessionId}`, {
      driverId: driverId,
      sessionId: sessionId,
    });
    const newTotalAndCount = trips.reduce(
      (previous, trip) => {
        if (trip.payment_type == PaymentType.DASH) {
          previous.dashTotal += trip.trip_total || 0;
          previous.dashTotal += trip.adjustment || 0;
          previous.dashTotal += trip.billing.additional_booking_fee || 0;
          previous.dashTotal += trip.billing.fleet_booking_fee || 0;
          previous.dashTotal += trip.billing.boost_amount || 0;
          previous.dashTipsTotal += trip.dash_tips || 0;

          if (trip.status !== "CANCELLED") {
            previous.dashCount++;
          }
        } else {
          previous.cashTotal += trip.trip_total || 0;

          previous.cashCount++;
        }

        previous.totalDistance += trip.distance || 0;

        previous.total = previous.cashTotal + previous.dashTotal + previous.dashTipsTotal;
        previous.count = previous.cashCount + previous.dashCount;

        return previous;
      },
      {
        cashTotal: 0,
        dashTotal: 0,
        dashTipsTotal: 0,
        total: 0,
        cashCount: 0,
        dashCount: 0,
        totalDistance: 0,
        count: 0,
      },
    );

    this.logger.debug("calculateSessionTotal newTotalAndCount", { newTotalAndCount });
    await this.appDatabaseService.driverSessionRepository(driverId).updateSession(sessionId, newTotalAndCount);
    const driver = await this.appDatabaseService.driverRepository().getDriver(driverId);
    if (driver && driver.session && driver.session.id === sessionId) {
      const sessionTotalAndCount = {
        ...driver.session,
        ...newTotalAndCount,
      };
      await this.appDatabaseService.driverRepository().updateSessionDataInDriver(driverId, sessionTotalAndCount);
    }
  }

  private async calculateHasPayout(driverId: string, sessionId: string, trips: DocumentData[]) {
    this.logger.info(`calculateHasPayout called driverId: ${driverId}; sessionId: ${sessionId}`, {
      driverId: driverId,
      sessionId: sessionId,
    });
    let hasUnreleasedTrip = false;
    for (const trip of trips) {
      if (
        trip.payment_type === PaymentType.DASH &&
        trip.payout_status !== TxPayoutStatus.RELEASED &&
        trip.payout_status !== TxPayoutStatus.RELEASED_TO_PAYOUT_MERCHANT
      ) {
        this.logger.info(`trip ${trip.id} has not been payout: ${trip.payout_status};`, {
          driverId: driverId,
          sessionId: sessionId,
          tripId: trip.id,
        });
        hasUnreleasedTrip = true;
        break;
      }
    }
    const payoutUpdate = {
      hasUnreleasedTrip: hasUnreleasedTrip,
    };
    return this.appDatabaseService.driverSessionRepository(driverId).updateSession(sessionId, payoutUpdate);
  }

  async getMeterSettingsData(meterId: string): Promise<GetMeterDataResponse> {
    const meterData = await this.appDatabaseService.meterRepository().findOneById(meterId);

    if (!meterData) {
      throw errorBuilder.meter.notFound(meterId);
    }

    return {
      isDashMeter: meterData.settings?.isDashMeter ?? false,
      vehicle: meterData.settings?.vehicle ?? {},
      operatingArea: meterData.settings?.operatingArea ?? null,
      vehicleId: meterData.settings?.vehicleId ?? "",
    };
  }

  async createMeter(
    meterId: string,
    user: DecodedIdToken,
    vehicleData: CreateMeterBody,
  ): Promise<GetMeterDataResponse> {
    const meterData = await this.appDatabaseService.meterRepository().findOneById(meterId);

    if (meterData) {
      throw errorBuilder.meter.alreadyExist(meterId);
    }

    const vehicle: MeterSettingsVehicle = {
      make: vehicleData.make,
      model: vehicleData.model,
      wheelchairRamp: vehicleData.wheelchairRamp,
    };

    const data: MeterDocument & { id: string } = {
      licensePlate: meterId,
      settings: {
        isDashMeter: false,
        vehicle: vehicle,
        operatingArea: vehicleData.operatingArea,
        vehicleId: vehicleData.vehicleId,
        vehicleLicenseImage: vehicleData.vehicleLicenseImage,
      },
      id: meterId,
      createdBy: user.uid,
      createdAt: new Date(),
    };

    await this.appDatabaseService.meterRepository().set(data);

    return data;
  }

  async meterHasOngoingTrip(meterId: string): Promise<GetMeterhasOngoingTripResponse> {
    const meter = await this.appDatabaseService.meterRepository().findOneById(meterId);

    if (!meter) {
      throw errorBuilder.meter.notFound(meterId);
    }

    const snapshot = await this.appDatabaseService
      .meterTripRepository(meterId)
      .collection.orderBy("trip_start", "desc")
      .limit(1)
      .get();

    if (snapshot.empty) {
      return {
        tripId: undefined,
        hasOngoingTrip: false,
      };
    }

    const lastTrip = snapshot.docs[0]?.data();

    const hasOngoingTrip = lastTrip && !lastTrip.tripEnd;

    return {
      tripId: hasOngoingTrip ? lastTrip.id : undefined,
      hasOngoingTrip,
    };
  }

  async upsertNotificationToken(
    content: UpsertDriverNotificationTokenBody,
    user: DecodedIdToken,
  ): Promise<InsertResult> {
    const merchant = await this.merchantRepository.findOne({ where: { phoneNumber: user.phone_number } });
    if (!merchant) {
      throw errorBuilder.merchant.driver.notFound(user.phone_number);
    }
    return this.merchantNotificationTokenRepository.upsert(
      { token: content.token, lastUpdateDate: new Date(), merchant: { id: merchant.id } },
      ["token"],
    );
  }

  async updateMeterTripEnd({
    driverId,
    meterId,
    tripId,
    updateTripEndDto,
    token,
  }: {
    driverId: string;
    meterId: string;
    tripId: string;
    updateTripEndDto: UpdateTripEndDto;
    token: string;
  }): Promise<TripDocument> {
    const [meter, tripToUpdate, driver] = await Promise.all([
      this.appDatabaseService.meterRepository().findOneById(meterId),
      this.appDatabaseService.meterTripRepository(meterId).findOneById(tripId),
      this.appDatabaseService.driverRepository().findOneById(driverId),
    ]);

    if (!meter) {
      throw errorBuilder.meter.notFound(meterId);
    }
    if (!tripToUpdate) {
      throw errorBuilder.meter.tripNotFound(meterId, tripId);
    }
    if (!driver) {
      throw errorBuilder.merchant.driver.notFound(driverId);
    }
    if (!driver.session) {
      throw errorBuilder.merchant.driver.session.sessionNotFoundOrNoData(driverId);
    }
    if (tripToUpdate.driver?.id !== driverId) {
      throw errorBuilder.meter.tripUpdateUnauthorized(meterId, tripId);
    }
    if (tripToUpdate.tripEnd) {
      throw errorBuilder.meter.tripAlreadyEnded(meterId, tripId);
    }

    this.logger.debug(`Meter trip to update: ${tripId}`, { tripToUpdate });

    const updatedTrip = await this.appDatabaseService
      .meterTripRepository(meterId)
      .updateTrip(tripId, this.getUpdateTripBody(tripToUpdate, updateTripEndDto, driver));

    if (tripToUpdate.hailId) {
      await this.hailingService.updateDriverHail(tripToUpdate.hailId, { status: HailStatus.COMPLETED }, token);
    }

    return updatedTrip;
  }

  async updateMeterTripEndByFleet({
    driverId,
    meterId,
    tripId,
    updateTripEndDto,
  }: {
    driverId: string;
    meterId: string;
    tripId: string;
    updateTripEndDto: UpdateTripEndDto;
  }): Promise<TripDocument> {
    try {
      this.logger.info("driverService/updateMeterTripEndByFleet-start", {
        driverId: driverId,
        meterId: meterId,
        tripId: tripId,
        updateTripEndDto: updateTripEndDto,
      });

      const [meter, tripToUpdate] = await Promise.all([
        this.appDatabaseService.meterRepository().findOneById(meterId),
        this.appDatabaseService.meterTripRepository(meterId).findOneById(tripId),
      ]);

      if (!meter) {
        throw errorBuilder.meter.notFound(meterId);
      }
      if (!tripToUpdate) {
        throw errorBuilder.meter.tripNotFound(meterId, tripId);
      }
      if (tripToUpdate.tripEnd) {
        throw errorBuilder.meter.tripAlreadyEnded(meterId, tripId);
      }

      const updatedTrip = await this.appDatabaseService
        .meterTripRepository(meterId)
        .updateTrip(tripId, this.getUpdateTripBody(tripToUpdate, updateTripEndDto));

      this.logger.info("driverService/updateMeterTripEndByFleet-end", {
        driverId: driverId,
        meterId: meterId,
        tripId: tripId,
        updateTripEndDto: updateTripEndDto,
      });

      return updatedTrip;
    } catch (error) {
      this.logger.error("driverService/updateMeterTripEndByFleet-end", { error });
      throw errorBuilder.meter.tripUpdateFailed(meterId, tripId);
    }
  }

  getUpdateTripBody(
    tripToUpdate: TripDocument,
    updateTripEndDto: UpdateTripEndDto,
    driver?: DriverDocument,
  ): DeepPartial<TripDocument> {
    const dashBookingFee = tripToUpdate.billing?.dashBookingFee ?? 0;
    const chargeableTotal =
      updateTripEndDto.tripTotal +
      (tripToUpdate.billing?.additionalBookingFee ?? 0) +
      (tripToUpdate.billing?.fleetBookingFee ?? 0) +
      (tripToUpdate.billing?.dashTips ?? 0) +
      (tripToUpdate.billing?.boostAmount ?? 0);

    const dashTransactionFee =
      (tripToUpdate.billing?.dashFeeSettings?.dashFeeConstant ?? 0) +
      (tripToUpdate.billing?.dashFeeSettings?.dashFeeRate ?? 0) * (chargeableTotal + dashBookingFee);

    const dashTotalFee = Math.ceil(dashBookingFee + dashTransactionFee);

    const result: DeepPartial<TripDocument> = {
      billing: {
        ...tripToUpdate.billing,
        fare: updateTripEndDto.tripTotal,
        extra: 0,
      },
      tripEnd: updateTripEndDto.tripEnd,
      tripTotal: roundOneDecimal(updateTripEndDto.tripTotal),
      total: roundOneDecimal(chargeableTotal + dashTotalFee),
      lastUpdateTime: new Date(),
    };

    if (updateTripEndDto.fleetPayoutFee && result.billing) {
      result.billing.fleetPayoutFee = updateTripEndDto.fleetPayoutFee;
    }

    if (driver?.session?.id) {
      result.session = {
        id: driver.session?.id,
      };
    }

    return result;
  }

  async pairToNonDashMeter(
    driverId: string,
    nonDashMeterPairDto: NonDashMeterPairDto,
  ): Promise<SessionPairResponseDto> {
    const { driverRef, driver } = await this.endCurrentSession(driverId);

    const meterRef = this.appDatabaseService
      .meterRepository()
      .collection.withConverter(firebaseUtils.withConverter())
      .doc(nonDashMeterPairDto.licensePlate);

    const newSessionRef = this.appDatabaseService
      .sessionRepository()
      .collection.withConverter(firebaseUtils.withConverter())
      .doc();

    const driverSessionRef = this.appDatabaseService
      .driverSessionRepository(driverId)
      .collection.withConverter(firebaseUtils.withConverter())
      .doc(newSessionRef.id);

    return this.appDatabaseService.runTransaction(async (tx) => {
      const meterDoc = await tx.get(meterRef);
      const meter = meterDoc.data();
      if (!meterDoc.exists || !meter) {
        throw errorBuilder.meter.notFound(nonDashMeterPairDto.licensePlate);
      }

      const newSession = SessionDocument.createNew(
        {
          id: newSessionRef.id,
          meterRef: meterRef,
          meterSettings: {
            operatingArea: meter.settings.operatingArea,
            isDashMeter: false,
          },
          licensePlate: nonDashMeterPairDto.licensePlate,
          driverId: driverId,
          driverRef: driverRef,
        },
        driver?.lastExpectedEndTime,
      );
      const newSessionDto = newSession.toDto();
      await tx.create(newSessionRef, newSessionDto);

      await tx.update(
        driverRef,
        casesUtils.snakeKeys({
          session: newSessionDto,
        }),
      );

      await tx.create(driverSessionRef, newSessionDto);

      return {
        sessionId: newSessionRef.id,
      };
    });
  }

  async unpairFromNonDashMeter(driverId: string): Promise<SessionPairResponseDto> {
    return this.appDatabaseService.runTransaction(async (tx) => {
      const { currentSession } = await this.endCurrentSession(driverId);
      return {
        sessionId: currentSession?.id,
      };
    });
  }

  private async endCurrentSession(driverId: string) {
    return this.appDatabaseService.runTransaction(async (tx) => {
      const driverRef = this.appDatabaseService
        .driverRepository()
        .collection.withConverter(firebaseUtils.withConverter())
        .doc(driverId);
      const driverDoc = await tx.get(driverRef);
      if (!driverDoc.exists) {
        throw errorBuilder.merchant.driver.notFound(driverId);
      }

      const driver = driverDoc.data();
      const currentSession = driver?.session;
      if (currentSession?.id) {
        this.logger.info(`Ending current session ${currentSession.id} for driver ${driverId}`);

        const sessionRef = this.appDatabaseService
          .sessionRepository()
          .collection.withConverter(firebaseUtils.withConverter())
          .doc(currentSession.id);
        const sessionDoc = await tx.get(sessionRef);
        if (sessionDoc.exists) {
          await tx.update(
            sessionRef,
            casesUtils.snakeKeys({
              endTime: new Date(),
            }),
          );
        }

        await tx.update(
          driverRef,
          casesUtils.snakeKeys({
            session: null,
          }),
        );
      }
      return { driverRef, driver, currentSession };
    });
  }

  async getOrCreateReferralCode(driverId: string): Promise<string> {
    const driver = await this.merchantRepository.findOne({
      where: { phoneNumber: driverId, platformMerchantType: PlatformMerchantType.DASH },
    });
    if (!driver) {
      throw errorBuilder.merchant.notFound(driverId);
    }

    if (driver.referralCode) {
      return driver.referralCode;
    }

    let attempts = 0;
    while (attempts < 10) {
      attempts++;
      const code = this.utilsService.string.generateReferralCode();

      try {
        driver.referralCode = code;
        await this.merchantRepository.save(driver);
      } catch (e: any) {
        if (e.code === "23505") {
          this.logger.debug(`[getReferralCode] Driver referral code ${code} already exists. Attempt ${attempts}`);
          continue;
        }
        throw errorBuilder.merchant.driver.referralCodeGenerationFailed(driverId);
      }
      await this.appDatabaseService.driverRepository().collection.doc(driverId).update({ referral_code: code });
      return code;
    }

    throw errorBuilder.merchant.driver.referralCodeGenerationLimitExceeded;
  }

  async linkReferralCode(driverId: string, referralCode: string): Promise<void> {
    this.logger.debug(`[linkReferralCode] Linking driver referral code ${referralCode} to driver ${driverId}`);
    const driver = await this.merchantRepository.findOne({ where: { phoneNumber: driverId } });
    if (!driver) {
      throw errorBuilder.merchant.notFound(driverId);
    }

    // Check if referral code exists in SQL
    const referrer = await this.merchantRepository.findOne({ where: { referralCode: referralCode } });
    if (!referrer) {
      throw errorBuilder.merchant.driver.referralCodeNotFound(referralCode);
    }

    if (referrer.id === driver.id) {
      throw errorBuilder.merchant.driver.referralCodeInvalid(referralCode);
    }

    driver.referrer = referrer;
    await this.merchantRepository.save(driver);

    // Save to Firestore
    await this.appDatabaseService
      .driverRepository()
      .collection.doc(driverId)
      .update({ referrer_code: referralCode, referrer: referrer.phoneNumber });
  }

  async pushNotification(message: PushNotificationDto): Promise<PushNotificationsResponse> {
    this.logger.info("Pushing notification to driver", {
      driverId: message.driverId,
      title: message.title,
      body: message.body,
      language: message.language,
      isSaveToFireStore: message.persistInInbox,
    });
    const response: PushNotificationsResponse = { driverId: message.driverId, isPushNotificationSuccess: false };

    try {
      const merchant = await this.merchantRepository.findOne({ where: { phoneNumber: message.driverId } });
      if (!merchant) {
        throw errorBuilder.transaction.merchantNotFound(message.driverId);
      }
      const merchantNotificationTokens = await this.merchantNotificationTokenRepository.find({
        where: { merchant: { id: merchant.id } },
      });
      this.logger.info("Sending push notification to driver", {
        driverId: message.driverId,
        title: message.title,
        body: message.body,
        merchantNotificationTokens: merchantNotificationTokens,
      });
      if (!merchantNotificationTokens || merchantNotificationTokens.length === 0) {
        this.logger.error("No notification tokens found for driver", { driverId: message.driverId });
        response.isPushNotificationSuccess = false;
      } else {
        const notification: Notification = {
          title: message.title,
          body: message.body,
        };
        await this.fcmService.pushNotificationToSpecificDevicesWithToken(
          merchantNotificationTokens.map((token) => token.token),
          notification,
        );
        response.isPushNotificationSuccess = true;
        this.logger.info("Push notification sent to driver successfully", {
          driverId: message.driverId,
          title: message.title,
          body: message.body,
        });
      }
    } catch (err: any) {
      this.logger.error("Error sending push notification", err);
      response.isPushNotificationSuccess = false;
    }

    if (message.persistInInbox) {
      response.willPersistInInbox = true;
      try {
        const driver = await this.appDatabaseService.driverRepository().findOneById(message.driverId);
        if (!driver) {
          throw errorBuilder.merchant.driver.notFound(message.driverId);
        }
        const notification: NotificationDocument = {
          created_on: new Date(),
          locale: LanguageOption.ZHHK,
          message: message.body,
          status: NotificationStatus.NEW,
          title: message.title,
          type: NotificationDocumentType.MANUAL_MESSAGE,
        };
        const addedNotification = await this.appDatabaseService
          .driverNotificationsRepository(message.driverId)
          .collection.add(notification);
        this.logger.info("Notification created", {
          notificationId: addedNotification.id,
          driverId: message.driverId,
          title: message.title,
          body: message.body,
        });
        response.isPersistInInboxSuccess = true;
      } catch (err: any) {
        this.logger.error("Error saving notification to Firestore", err);
        response.isPersistInInboxSuccess = false;
      }
    } else {
      response.willPersistInInbox = false;
    }

    return response;
  }

  async processMeterSessionStartEndEvent(
    data: FirebaseFirestore.DocumentData,
    eventType: Extract<WebhookEventType, WebhookEventType.METER_SESSION_START | WebhookEventType.METER_SESSION_END>,
  ): Promise<PublishMessageForWebhookProcessing[]> {
    const licensePlate = data.session.license_plate;
    const cacheTtlMs = 60000;

    const fleetMerchantId = await this.cacheManager.wrap<string | null>(
      `merchant:meter:${licensePlate}`,
      async () => {
        const meter = await this.appDatabaseService.meterRepository().findOneById(licensePlate);
        if (!meter || !meter.settings.fleetId) return null;

        const fleet = await this.appDatabaseService.fleetRepository().findOneById(meter.settings.fleetId);
        if (!fleet || !fleet.merchantId) return null;
        return fleet.merchantId;
      },
      cacheTtlMs,
    );

    if (!fleetMerchantId) return [];

    const webhookIds = await this.cacheManager.wrap<string[]>(
      this.webhookService.getCacheKeyForEvent(fleetMerchantId, eventType),
      async () => {
        this.logger.debug(`Fetching webhooks for merchant ${fleetMerchantId}`);

        const webhooks = await this.webhookRepository
          .createQueryBuilder("webhook")
          .innerJoinAndSelect("webhook.merchant", "merchant")
          .innerJoinAndSelect("webhook.events", "events")
          .where("merchant.id = :merchantId", { merchantId: fleetMerchantId })
          .andWhere("webhook.deletedAt IS NULL")
          .andWhere("events.type = :eventType", { eventType: eventType })
          .getMany();

        if (!webhooks || webhooks.length === 0) {
          return [];
        } else {
          return webhooks.map((webhook) => webhook.id);
        }
      },
      cacheTtlMs,
    );

    if (webhookIds.length === 0) {
      return [];
    }

    const sessionStart = data.session.start_time;
    const webhookMessage: WebhookMessage = {
      id: randomUUID(),
      event: eventType,
      data: {
        createdAt: new Timestamp(sessionStart._seconds, sessionStart._nanoseconds).toDate().toISOString(),
        sessionId: data.session.id,
        licensePlate: licensePlate,
        driverPhoneNumber: data.phone_number,
        driverId: data.id,
      },
    };

    return webhookIds.map<PublishMessageForWebhookProcessing>((webhookId) => ({
      webhookId: webhookId,
      message: webhookMessage,
    }));
  }

  async getDriverById(driverId: string): Promise<Merchant> {
    const driver = await this.merchantRepository.findOne({ where: { phoneNumber: driverId } });
    if (!driver) {
      throw errorBuilder.merchant.driver.notFound(driverId);
    }
    return driver;
  }
}

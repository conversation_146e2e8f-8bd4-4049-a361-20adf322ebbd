import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { OperatingArea } from "@legacy/model/meter";

import { MeterSettingsVehicle } from "../../../appDatabase/documents/meter.document";

export const meterIdSchema = Joi.string().required();

export type GetMeterDataResponse =
  | {
      vehicle: MeterSettingsVehicle;
      isDashMeter?: boolean;
      operatingArea?: OperatingArea;
      vehicleId?: string;
    }
  | object;

export class CreateMeterBody {
  @ApiProperty({ example: "Toyota", description: "Vehicle make" })
  make: string;

  @ApiProperty({ example: "Corolla", description: "Vehicle model" })
  model: string;

  @ApiProperty({ example: true, description: "Is accessible ramp available" })
  wheelchairRamp: boolean;

  @ApiProperty({ example: "URBAN", description: "Operating area" })
  operatingArea: OperatingArea;

  @ApiProperty({ example: "abc", description: "Vehicle ID" })
  vehicleId: string;

  @ApiProperty({ example: "image_url", description: "Vehicle license image URL" })
  vehicleLicenseImage?: string;
}

export const createMeterBodySchema = Joi.object<CreateMeterBody>({
  make: Joi.string().required(),
  model: Joi.string().required(),
  wheelchairRamp: Joi.boolean().required(),
  operatingArea: Joi.string()
    .valid(...Object.values(OperatingArea))
    .required(),
  vehicleId: Joi.string().required(),
  vehicleLicenseImage: Joi.string().required(),
});

export type GetMeterhasOngoingTripResponse = {
  tripId?: string;
  hasOngoingTrip: boolean;
};

import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import Jo<PERSON> from "joi";

import { LanguageOption } from "@nest/modules/validation/dto/language.dto";

export class PushNotificationDto {
  @ApiProperty({ example: "+85233333333", required: true })
  driverId: string;
  @ApiProperty({ example: "Title", required: true })
  title: string;
  @ApiProperty({ example: "Body", required: true })
  body: string;
  @ApiPropertyOptional({ default: LanguageOption.ZHHK, required: false })
  language?: LanguageOption;
  @ApiPropertyOptional({ default: true, required: false })
  persistInInbox?: boolean;
}

export const pushNotificationSchema = Joi.object<PushNotificationDto>({
  driverId: Joi.string().required(),
  title: Joi.string().required(),
  body: Joi.string().required(),
  language: Joi.string()
    .valid(...Object.values(LanguageOption))
    .default(LanguageOption.ZHHK),
  persistInInbox: Joi.boolean().default(true),
});

export class PushNotificationsDto {
  @ApiProperty({ type: [PushNotificationDto] })
  notifications: PushNotificationDto[];
}

export const pushNotificationsSchema = Joi.object<PushNotificationsDto>({
  notifications: Joi.array().items(pushNotificationSchema).required(),
});

export class PushNotificationsResponse {
  @ApiProperty()
  driverId: string;
  @ApiProperty()
  isPushNotificationSuccess: boolean;
  @ApiProperty()
  willPersistInInbox?: boolean;
  @ApiProperty()
  isPersistInInboxSuccess?: boolean;
}

import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export class UpdateTripEndDto {
  @ApiProperty()
  tripEnd: Date;
  @ApiProperty()
  tripTotal: number;
  @ApiProperty()
  fleetPayoutFee?: number;
}

export const updateTripEndSchema = Joi.object<UpdateTripEndDto>({
  tripEnd: Joi.date().required(),
  tripTotal: Joi.number().positive().precision(2).required(),
  fleetPayoutFee: Joi.number().positive().precision(2).optional(),
});

import { ApiProperty } from "@nestjs/swagger";
import Jo<PERSON> from "joi";

export type MerchantReferralRetrieveResponse = {
  code: string;
};

export class MerchantReferralLinkBody {
  @ApiProperty({ type: "string", example: "ABC123", description: "Referral code", required: true })
  referralCode: string;
}

export const merchantReferralLinkBodySchema = Joi.object<MerchantReferralLinkBody>({
  referralCode: Joi.string().required(),
});

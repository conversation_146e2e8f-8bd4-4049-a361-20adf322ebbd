import { MiddlewareConsumer, Module } from "@nestjs/common";

import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { UserNotificationTokenRepository } from "@nest/modules/database/repositories/userNotificationToken.repository";
import { WebhookRepository } from "@nest/modules/database/repositories/webhook.repository";
import { FcmModule } from "@nest/modules/fcm/fcm.module";
import { FcmService } from "@nest/modules/fcm/fcm.service";
import { HailingModule } from "@nest/modules/hailing/hailing.module";
import { TripModule } from "@nest/modules/transaction/transactionFactory/modules/trip/trip.module";
import { WebhookModule } from "@nest/modules/webhook/webhook.module";

import { MerchantDriverController } from "./merchantDriver.controller";
import { DriverService } from "./merchantDriver.service";
import { DriverAuthMiddleware } from "../../../infrastructure/middlewares/driverAuth.middleware";
import { AppDatabaseModule } from "../../appDatabase/appDatabase.module";
import { MerchantRepository } from "../../database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "../../database/repositories/merchantNotificationToken.repository";
import { PaymentTxRepository } from "../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../database/repositories/tx.repository";
import { MessageTeamsModule } from "../../messageTeams/messageTeams.module";
import { PubSubModule } from "../../pubsub/pubsub.module";
import { StorageModule } from "../../storage/storage.module";

@Module({
  imports: [
    StorageModule,
    AppDatabaseModule,
    MessageTeamsModule,
    PubSubModule,
    HailingModule,
    TripModule,
    FcmModule,
    WebhookModule,
  ],
  providers: [
    DriverService,
    MerchantRepository,
    TxRepository,
    PaymentTxRepository,
    MerchantNotificationTokenRepository,
    FcmService,
    UserRepository,
    UserNotificationTokenRepository,
    WebhookRepository,
  ],
  controllers: [MerchantDriverController],
  exports: [DriverService],
})
export class DriverModule {
  /**
   * Configure middleware
   * @param consumer MiddlewareConsumer
   */
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(DriverAuthMiddleware).forRoutes("/merchants/drivers");
  }
}

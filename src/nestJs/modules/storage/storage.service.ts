import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { storage } from "firebase-admin";
import readXlsxFile, { Row } from "read-excel-file/node";

import { CdcFileContent } from "./dto/cdcFileContent.model";
import { PayoutBankFileRow } from "../bank/dto/payoutBankFile.dto";
import { errorBuilder } from "../utils/utils/error.utils";

export enum Buckets {
  PAYOUT = "payouts",
  PAYOUT_RESPONSE = "payouts-responses",
}

/**
 * Storage service
 */
@Injectable()
export class StorageService {
  public bucketsNames: Record<Buckets, string>;

  constructor(private readonly configService: ConfigService) {
    this.bucketsNames = {
      [Buckets.PAYOUT]: `${this.configService.getOrThrow<string>("GCLOUD_PROJECT")}-${Buckets.PAYOUT}`,
      [Buckets.PAYOUT_RESPONSE]: `${this.configService.getOrThrow<string>("GCLOUD_PROJECT")}-${
        Buckets.PAYOUT_RESPONSE
      }`,
    };
  }

  async savePayoutFile(filePath: string, fileContent: string): Promise<void> {
    await this.writeFileToBucket(this.bucketsNames[Buckets.PAYOUT], filePath, Buffer.from(fileContent));
  }

  async moveFile(fileBucket: string, filePath: string, newFilePath: string): Promise<void> {
    const bucket = storage().bucket(fileBucket);
    await bucket.file(filePath).move(newFilePath);
  }

  async writeFileToBucket(fileBucket: string, filePath: string, fileContent: Buffer): Promise<void> {
    if (!filePath) {
      throw errorBuilder.storage.noFilePath();
    }

    const bucket = storage().bucket(fileBucket);
    await bucket.file(filePath).save(fileContent);
  }

  async readFileFromBucket(fileBucket: string, filePath: string): Promise<Buffer> {
    if (!filePath) {
      throw errorBuilder.storage.noFilePath();
    }

    const bucket = storage().bucket(fileBucket);
    const downloadResponse = await bucket.file(filePath).download();
    const fileBuffer = downloadResponse[0];

    return fileBuffer;
  }

  async getXlsxFileContentFromBucketFile(
    fileBucket: string,
    filePath: string,
    schema: any,
    transformData?: (rows: Row[]) => Row[],
  ): Promise<PayoutBankFileRow[]> {
    const buffer = await this.readFileFromBucket(fileBucket, filePath);

    if (!buffer) {
      throw errorBuilder.storage.fileNotFound(filePath);
    }

    const data = await readXlsxFile<PayoutBankFileRow>(buffer, {
      schema,
      transformData,
    });

    return data.rows;
  }

  async getJsonLFileContentFromBucketFile<ContentType>(
    fileBucket: string,
    filePath: string,
  ): Promise<CdcFileContent<ContentType>[]> {
    const fileBuffer = await this.readFileFromBucket(fileBucket, filePath);

    const json = fileBuffer.toString();

    let parsedJson;

    try {
      parsedJson = json.split(/\n/).map((line: string) => JSON.parse(line));
    } catch (error) {
      throw errorBuilder.storage.invalidContentType();
    }

    return parsedJson;
  }
}

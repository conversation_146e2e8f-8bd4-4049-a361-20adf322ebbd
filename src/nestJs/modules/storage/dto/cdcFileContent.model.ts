export interface CdcFileContent<Payload> {
  uuid: string;
  read_timestamp: string;
  source_timestamp: string;
  object: string;
  read_method: string;
  stream_name: string;
  schema_key: string;
  sort_keys: (number | string)[];
  source_metadata: Sourcemetadata;
  payload: Payload;
}

interface Sourcemetadata {
  table: string;
  database: string;
  primary_keys: string[];
  log_file: string;
  log_position: number;
  change_type: string;
  is_deleted: boolean;
}

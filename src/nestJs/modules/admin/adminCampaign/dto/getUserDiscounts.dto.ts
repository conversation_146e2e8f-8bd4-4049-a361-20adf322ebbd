import { ApiProperty } from "@nestjs/swagger";

import Campaign from "@nest/modules/database/entities/campaign.entity";

export class GetUserDiscountsResponse {
  @ApiProperty({
    type: "array",
    description: "List of campaigns",
  })
  campaigns: Campaign[];
  @ApiProperty({
    type: "number",
    description: "Total redeemed value",
  })
  totalRedeemedValue: number;
  @ApiProperty({
    type: "number",
    description: "Total available value",
  })
  totalAvailableValue: number;
}

import { Modu<PERSON> } from "@nestjs/common";

import { TransactionModule } from "@nest/modules/transaction/transaction.module";
import { UserModule } from "@nest/modules/user/user.module";

import { AdminCampaignController } from "./adminCampaign.controller";
import { CampaignModule } from "../../campaign/campaign.module";

@Module({
  providers: [AdminCampaignController],
  imports: [CampaignModule, UserModule, TransactionModule],
  controllers: [AdminCampaignController],
  exports: [AdminCampaignController],
})
export class AdminCampaignModule {}

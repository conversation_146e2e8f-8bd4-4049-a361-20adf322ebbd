import { randomUUID } from "crypto";

import { Body, Controller, Get, HttpCode, HttpStatus, Param, Patch, Post, Query } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiUnprocessableEntityResponse,
} from "@nestjs/swagger";

import { DiscountState } from "@nest/modules/discount/dto/discount.dto";
import * as issueCampaignDiscountDto from "@nest/modules/discount/dto/issue-campaign-discount.dto";
import * as redeemDiscountDto from "@nest/modules/discount/dto/redeem-discount.dto";
import { CampaignGetIssuedResponse } from "@nest/modules/me/modules/meCampaign/dto/meCampaign.dto";
import { TxTypes } from "@nest/modules/transaction/dto/txType.dto";
import { TransactionService } from "@nest/modules/transaction/transaction.service";
import { UserService } from "@nest/modules/user/user.service";
import genericSchemas from "@nest/modules/validation/dto/genericSchemas.dto";

import { CampaignService } from "../../campaign/campaign.service";
import { campaignIdSchema } from "../../campaign/dto/campaign.dto";
import { createCampaignBodySchema, CreateCampaignBodyDto } from "../../campaign/dto/createCampaignRequest.dto";
import {
  CampaignListingResponseDto,
  queryCampaignBodySchema,
  QueryCampaignRequestDto,
} from "../../campaign/dto/queryCampaignRequest.dto";
import { updateCampaignBodySchema, UpdateCampaignBodyDto } from "../../campaign/dto/updateCampaignRequest.dto";
import Campaign from "../../database/entities/campaign.entity";
import { errorBuilder } from "../../utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../validation/validationPipe.service";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.admin)
export class AdminCampaignController {
  constructor(
    private readonly campaignService: CampaignService,
    private readonly userService: UserService,
    private readonly transactionService: TransactionService,
  ) {}

  @Post()
  @ApiOperation({ summary: "Create Campaign" })
  async createAdminCampaigns(
    @Body(new JoiValidationPipe(createCampaignBodySchema)) createCampaignBodyDto: CreateCampaignBodyDto,
  ): Promise<Campaign> {
    return this.campaignService.createCampaign(createCampaignBodyDto);
  }

  @Patch(":campaignId")
  @ApiOperation({ summary: "Update Campaign" })
  @ApiNotFoundResponse({
    description: buildErrorHtml([errorBuilder.incentive.campaign.notFound("2c42c818-f447-4575-b2cd-784c2052de2c")]),
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.incentive.campaign.invalidDate("2c42c818-f447-4575-b2cd-784c2052de2c")]),
  })
  async updateAdminCampaigns(
    @Param("campaignId", new JoiValidationPipe(campaignIdSchema)) campaignId: string,
    @Body(new JoiValidationPipe(updateCampaignBodySchema)) updateCampaignBodyDto: UpdateCampaignBodyDto,
  ): Promise<Campaign> {
    return this.campaignService.updateCampaign(campaignId, updateCampaignBodyDto);
  }

  @Get()
  @ApiOperation({ summary: "Get Campaigns" })
  @ApiResponse({ status: 200, description: "Get Campaigns", type: CampaignListingResponseDto })
  async getCampaigns(
    @Query(new JoiValidationPipe(queryCampaignBodySchema)) queryDto: QueryCampaignRequestDto,
  ): Promise<CampaignListingResponseDto> {
    return this.campaignService.queryCampaigns(queryDto);
  }

  @Get("/user/:userId")
  @ApiOperation({ summary: "Get discounts for a user" })
  @ApiResponse({ status: 200, description: "Get discounts for a user", type: CampaignGetIssuedResponse })
  async getUserDiscounts(
    @Param("userId", new JoiValidationPipe(genericSchemas.uuid.required())) userId: string,
  ): Promise<CampaignGetIssuedResponse> {
    const user = await this.userService.getUserById(userId);
    if (!user) {
      throw errorBuilder.user.notFoundInSql(userId);
    }

    return this.campaignService.getCampaignsListWithAggregatedValuesForUser(user);
  }

  @Post("/discount/:discountId/redeem")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: "Redeem discount for a user trip",
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Discount redeemed successfully",
    example: redeemDiscountDto.redeemDiscountResponseMessage,
  })
  @ApiNotFoundResponse({
    description: "Resource not found",
    example: errorBuilder.transaction.notFound(randomUUID()).getResponse(),
  })
  @ApiBadRequestResponse({
    description: "Input validation error",
    example: errorBuilder.incentive.discount.invalidTxType(randomUUID(), TxTypes.TRIP).getResponse(),
  })
  @ApiUnprocessableEntityResponse({
    description: "Unprocessable entity. Change your request before re-sending.",
    example: errorBuilder.incentive.discount
      .userMismatch(randomUUID(), randomUUID(), randomUUID(), randomUUID())
      .getResponse(),
  })
  async redeemDiscount(
    @Param("discountId", new JoiValidationPipe(genericSchemas.uuid.required())) discountId: string,
    @Body(new JoiValidationPipe(redeemDiscountDto.redeemDiscountRequestDtoSchema))
    { redeemedValue, txId }: redeemDiscountDto.RedeemDiscountRequestDto,
  ): Promise<redeemDiscountDto.RedeemDiscountResponseDto> {
    const tx = await this.transactionService.getTransactionById(txId);
    if (!tx) {
      throw errorBuilder.transaction.notFound(txId);
    }

    return await this.campaignService.applyDiscountToTxWithCustomValue(discountId, redeemedValue, tx);
  }

  @Post("/issue")
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: "Issue a discount to a user" })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: "Campaign discount issued successfully",
    example: {
      status: "success",
      message: "Campaign discount issued successfully",
      discount: {
        id: randomUUID(),
        state: DiscountState.ISSUED,
        startAt: new Date(),
        endAt: new Date(),
        redeemedAt: null,
        redeemedValue: null,
        rewardCode: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    },
  })
  @ApiBadRequestResponse({
    description: "Input validation error",
  })
  @ApiNotFoundResponse({
    description: "Resource not found",
    example: errorBuilder.incentive.campaign.notFound(randomUUID()).getResponse(),
  })
  @ApiInternalServerErrorResponse({
    description: "Unexpected error",
    example: errorBuilder.global.unexpected(new Error("Unexpected error")).getResponse(),
  })
  async issueCampaignDiscount(
    @Body(new JoiValidationPipe(issueCampaignDiscountDto.issueCampaignDiscountRequestDtoSchema))
    { campaignId, userId }: issueCampaignDiscountDto.IssueCampaignDiscountRequestDto,
  ): Promise<issueCampaignDiscountDto.IssueCampaignDiscountResponseDto> {
    const discount = await this.campaignService.createIssuedDiscountById(campaignId, userId);

    return {
      status: "success",
      message: "Campaign discount issued successfully",
      discount: discount,
    };
  }
}

import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  Patch,
  Post,
  Query,
  Req,
} from "@nestjs/common";
import {
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnprocessableEntityResponse,
  getSchemaPath,
} from "@nestjs/swagger";
import { Request } from "express";

import {
  CreateManyNotificationRequestDto,
  createManyNotificationRequestSchema,
  UpdateManyNotificationRequestDto,
  updateManyNotificationRequestSchema,
} from "@nest/modules/cloud-task-notification-handler/dto/request.dto";
import NotificationTask from "@nest/modules/database/entities/notificationTask.entity";
import {
  NotificationTaskFilterDto,
  notificationTaskFilterSchema,
} from "@nest/modules/notification/dto/notification-filters.dto";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import { PaginatedResponseDto } from "@nest/modules/utils/paginated.dto";
import { ApiOkResponsePaginated } from "@nest/modules/utils/swagger.decorator";
import { apiTags } from "@nest/modules/utils/utils/swagger.utils";
import genericSchemas from "@nest/modules/validation/dto/genericSchemas.dto";
import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";

import { AdminNotificationService } from "./admin-notification.service";

@Controller()
@ApiTags(...apiTags.admin)
export class AdminNotificationController {
  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(AdminNotificationService) private readonly adminNotificationService: AdminNotificationService,
  ) {}

  @Post("/")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Send notifications to multiple users" })
  @ApiOkResponse({
    description: "Notifications sent successfully",
    content: {
      "application/json": {
        schema: {
          $ref: getSchemaPath(NotificationTask),
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({ description: "Error sending notifications" })
  createNotifications(
    @Body(new JoiValidationPipe(createManyNotificationRequestSchema))
    createManyNotificationRequest: CreateManyNotificationRequestDto,
    @Req() req: Request,
  ) {
    const adminEmail = req.user?.email;

    this.logger.info(
      `[AdminNotificationController] Received request from ${adminEmail} to send notifications to multiple users`,
      createManyNotificationRequest,
    );

    return this.adminNotificationService.createNotifications(createManyNotificationRequest, adminEmail!);
  }

  @Delete("/:taskId")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Delete notifications" })
  @ApiOkResponse({ description: "Notifications deleted successfully", type: () => NotificationTask })
  // @ApiOkResponse({
  //   description: "Notifications deleted successfully",
  //   example: {
  //     createdAt: "2025-06-23T06:40:33.826Z",
  //     updatedAt: "2025-06-23T06:41:46.619Z",
  //     status: "DELETED",
  //     failureReason: null,
  //     id: "49b74384-49ec-43dd-b489-72e7049ba458",
  //     name: "Test campaign",
  //     type: "PUSH",
  //     createdBy: "<EMAIL>",
  //     scheduledAt: "2025-06-27T04:00:33.000Z",
  //     cloudTaskReference:
  //       "projects/dash-dev2-edcb3/locations/asia-east2/queues/notifications-task-queue/tasks/8914289871901473779",
  //     payload: {
  //       deletedBy: "<EMAIL>",
  //       phoneNumbers: ["+85254000455", "+85251159365"],
  //       notificationRequest: {
  //         bodyEn: "This is a test notification",
  //         bodyHk: "這是一個測試通知;這是一個測試通知",
  //         titleEn: "Test Notification",
  //         titleHk: "測試通知",
  //       },
  //     },
  //   },
  // })
  @ApiInternalServerErrorResponse({ description: "Error deleting notifications" })
  deleteNotifications(
    @Param("taskId", new JoiValidationPipe(genericSchemas.uuid.required())) taskId: string,
    @Req() req: Request,
  ) {
    const adminEmail = req.user?.email;

    this.logger.info(
      `[AdminNotificationController] Received request from ${adminEmail} to delete notifications: ${taskId}`,
    );

    return this.adminNotificationService.deleteNotifications(adminEmail!, taskId);
  }

  @Get("/")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Get notifications" })
  // @ApiOkResponse({ description: "Notifications retrieved successfully", type: () => PaginatedResponseDto<NotificationTask> })
  // @ApiOkResponse({
  //   description: "Notifications retrieved successfully",
  //   // type: PaginatedResponseDto<NotificationTask>,
  //   example: {
  //     data: [
  //       {
  //         name: "Updated test campaign",
  //         createdAt: "2025-06-21T09:32:21.639Z",
  //         updatedAt: "2025-06-21T09:32:54.423Z",
  //         status: "DELETED",
  //         failureReason: null,
  //         id: "4301b235-254f-4241-a987-1d6641216901",
  //         type: "PUSH",
  //         createdBy: "<EMAIL>",
  //         scheduledAt: "2025-06-21T10:00:33.000Z",
  //         cloudTaskReference:
  //           "projects/dash-dev2-edcb3/locations/asia-east2/queues/notifications-task-queue/tasks/7962322007297877023",
  //         payload: {
  //           type: "PUSH",
  //           deletedBy: "<EMAIL>",
  //           phoneNumbers: ["+85254000455", "+85251159365"],
  //           notificationRequest: {
  //             bodyEn: "This is a test notification",
  //             bodyHk: "這是一個測試通知;這是一個測試通知",
  //             titleEn: "Test Notification",
  //             titleHk: "測試通知",
  //             clickAction: "OPEN_APP",
  //           },
  //         },
  //       },
  //     ],
  //     pagination: {
  //       count: 100,
  //       skip: 0,
  //       take: 10,
  //     },
  //   },
  // })
  @ApiOkResponsePaginated(NotificationTask)
  @ApiInternalServerErrorResponse({ description: "Error retrieving notifications" })
  async getNotifications(
    // FIXME: Query String params
    @Query(new JoiValidationPipe(notificationTaskFilterSchema))
    notificationTaskFilter: NotificationTaskFilterDto,
  ): Promise<PaginatedResponseDto<NotificationTask>> {
    return this.adminNotificationService.getNotifications(notificationTaskFilter);
  }

  @Patch("/:taskId")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Update notifications" })
  @ApiOkResponse({ description: "Notifications updated successfully", type: () => NotificationTask })
  // @ApiOkResponse({
  //   description: "Notifications updated successfully",
  //   example: {
  //     createdAt: "2025-06-23T06:40:24.728Z",
  //     updatedAt: "2025-06-23T06:40:52.597Z",
  //     status: "SCHEDULED",
  //     failureReason: null,
  //     id: "cae8b3fe-80b3-404f-bc4a-aa25adb9f4f8",
  //     name: "Updated test campaign",
  //     type: "PUSH",
  //     createdBy: "<EMAIL>",
  //     scheduledAt: "2025-06-30T04:00:59.000Z",
  //     cloudTaskReference:
  //       "projects/dash-dev2-edcb3/locations/asia-east2/queues/notifications-task-queue/tasks/7701729152384124535",
  //     payload: {
  //       updatedBy: "<EMAIL>",
  //       phoneNumbers: ["+85254000455", "+85251159365"],
  //       notificationRequest: {
  //         titleEn: "[NEW] Test Notification",
  //         titleHk: "NEW! @@@ 測試通知",
  //         bodyEn: "[NEW] This is a test notification",
  //         bodyHk: "NEW!! @@@ 這是一個測試通知;這是一個測試通知",
  //       },
  //     },
  //   },
  // })
  @ApiUnprocessableEntityResponse({
    description: "Task 8dce9a20-346b-4e0b-b30c-************ with status 'PROCESSING' can no longer be updated",
  })
  @ApiInternalServerErrorResponse({ description: "Error updating notifications" })
  async updateNotifications(
    @Param("taskId", new JoiValidationPipe(genericSchemas.uuid.required())) taskId: string,
    @Body(new JoiValidationPipe(updateManyNotificationRequestSchema))
    updateManyNotificationRequestDto: UpdateManyNotificationRequestDto,
    @Req() req: Request,
  ) {
    const adminEmail = req.user?.email;

    this.logger.info(
      `[AdminNotificationController] Received request from ${adminEmail} to update notifications: ${taskId}`,
    );

    return this.adminNotificationService.updateNotifications(adminEmail!, taskId, updateManyNotificationRequestDto);
  }
}

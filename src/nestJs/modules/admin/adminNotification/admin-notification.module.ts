import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { CloudTaskClientModule } from "@nest/modules/cloud-task-client/cloud-task-client.module";
import { NotificationManagerService } from "@nest/modules/notification/notification-manager.service";
import { NotificationModule } from "@nest/modules/notification/notification.module";
import { UserNotificationFactory } from "@nest/modules/notification/user-notification.factory";
import { LoggerModule } from "@nest/modules/utils/logger/logger.module";

import { AdminNotificationController } from "./admin-notification.controller";
import { AdminNotificationService } from "./admin-notification.service";

@Module({
  imports: [LoggerModule, NotificationModule, CloudTaskClientModule],
  providers: [AdminNotificationService, UserNotificationFactory, NotificationManagerService],
  controllers: [AdminNotificationController],
})
export class AdminNotificationModule {}

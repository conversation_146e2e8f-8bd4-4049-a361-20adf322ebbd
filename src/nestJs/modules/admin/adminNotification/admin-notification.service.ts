import { Inject, Injectable } from "@nestjs/common";

import { CloudTaskClientService } from "@nest/modules/cloud-task-client/cloud-task-client.service";
import {
  CreateManyNotificationRequestDto,
  UpdateManyNotificationRequestDto,
} from "@nest/modules/cloud-task-notification-handler/dto/request.dto";
import { NotificationTaskStatusError } from "@nest/modules/database/entities/notificationTask.entity";
import { NotificationTaskFilterDto } from "@nest/modules/notification/dto/notification-filters.dto";
import { NotificationManagerService } from "@nest/modules/notification/notification-manager.service";
import { UserNotificationFactory } from "@nest/modules/notification/user-notification.factory";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

@Injectable()
export class AdminNotificationService {
  // TODO: should be moved to env
  readonly batchSize = 100;

  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(CloudTaskClientService) private readonly cloudTaskClientService: CloudTaskClientService,
    private readonly notificationManagerService: NotificationManagerService,
    private readonly userNotificationFactory: UserNotificationFactory,
  ) {}

  async createNotifications(createManyNotificationRequestDto: CreateManyNotificationRequestDto, createdBy: string) {
    this.logger.log(
      `[AdminNotificationService] Admin ${createdBy} is creating notifications to ${createManyNotificationRequestDto.phoneNumbers.length} users`,
    );

    const userNotificationManager = this.userNotificationFactory.getValidator(
      createManyNotificationRequestDto.userType,
    );

    await userNotificationManager.validateUserPhoneNumbers(createManyNotificationRequestDto.phoneNumbers);

    const notification = await this.notificationManagerService.createNotificationTask(
      createManyNotificationRequestDto,
      createdBy,
      async (task) => {
        const taskId = await this.cloudTaskClientService.enqueueSendNotificationTask(task.id, task.scheduledAt);
        this.logger.log(
          `[AdminNotificationService] Admin ${createdBy} Created cloud task for notification task id ${task.id}`,
        );

        return taskId;
      },
    );
    this.logger.log(`[AdminNotificationService] Admin ${createdBy} Created notification task id '${notification.id}'`);

    return [notification];
  }

  async updateNotifications(adminEmail: string, taskId: string, notification: UpdateManyNotificationRequestDto) {
    this.logger.log(`[AdminNotificationService] Admin ${adminEmail} is updating notifications with task ${taskId}`);

    const userNotificationManager = await this.userNotificationFactory.getValidatorByTaskId(taskId);

    await userNotificationManager.validateUserPhoneNumbers(notification.phoneNumbers!);

    try {
      return await this.notificationManagerService.updateNotificationAfterCallback(
        taskId,
        adminEmail,
        notification,
        async (task) => {
          // remove and re-enqueue updated task. returns a new reference
          if (task.cloudTaskReference) {
            // when transitioning from DRAFT to SCHEDULED, the current task will not have cloudTaskReference yet
            await this.cloudTaskClientService.removeTaskFromQueue(task.cloudTaskReference);
          }
          return this.cloudTaskClientService.enqueueSendNotificationTask(taskId, task.scheduledAt);
        },
      );
    } catch (err) {
      if (err instanceof NotificationTaskStatusError) {
        throw errorBuilder.notification.taskInvalidState(err.taskId, err.currentState, err.newState);
      }

      throw err;
    }
  }

  async deleteNotifications(deletedBy: string, taskId: string) {
    this.logger.log(`[AdminNotificationService] Admin ${deletedBy} is deleting notifications with task ${taskId}`);

    try {
      return await this.notificationManagerService.deleteNotificationAfterCallback(taskId, deletedBy, async (task) => {
        await this.cloudTaskClientService.removeTaskFromQueue(task.cloudTaskReference);
        this.logger.log(
          `[AdminNotificationService] Admin ${deletedBy} deleted notification task ${task.cloudTaskReference}`,
        );

        return task.cloudTaskReference;
      });
    } catch (err) {
      if (err instanceof NotificationTaskStatusError) {
        throw errorBuilder.notification.taskInvalidState(err.taskId, err.currentState, err.newState);
      }

      throw err;
    }
  }

  async getNotifications(notificationTaskFilter: NotificationTaskFilterDto) {
    return this.notificationManagerService.getNotificationTasks(notificationTaskFilter);
  }
}

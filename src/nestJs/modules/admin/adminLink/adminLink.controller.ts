import { Body, Controller, HttpStatus, Post, Req } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Request } from "express";

import { CreateLinkResponseDto } from "./dto/createLinkResponse.dto";
import { LinkDocument } from "../../appDatabase/documents/link.document";
import { CreateLinkRequestDto, CreateLinkRequestSchema } from "../../link/dto/createLinkRequest.dto";
import { LinkService } from "../../link/link.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { apiTags } from "../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../validation/validationPipe.service";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.admin)
export class AdminLinkController {
  constructor(private readonly linkService: LinkService) {}

  @Post()
  @ApiOperation({ summary: "Create short link" })
  @ApiResponse({ status: HttpStatus.CREATED, description: "Create shortened link", type: LinkDocument })
  async create(
    @Body(new JoiValidationPipe(CreateLinkRequestSchema)) createLinkRequestDto: CreateLinkRequestDto,
    @Req() req: Request,
  ): Promise<CreateLinkResponseDto> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }

    return this.linkService.createUniqueLink(user.uid, createLinkRequestDto);
  }
}

import { MiddlewareConsumer, Module } from "@nestjs/common";
import { RouterModule } from "nest-router";

import { AdminAuthMiddleware } from "@nest/infrastructure/middlewares/adminAuth.middleware";

import { AdminAuthUserModule } from "./adminAuthUser/adminAuthUser.module";
import { AdminCampaignModule } from "./adminCampaign/adminCampaign.module";
import { AdminFleetOrderModule } from "./adminFleetOrder/adminFleetOrder.module";
import { AdminLinkModule } from "./adminLink/adminLink.module";
import { AdminMerchantModule } from "./adminMerchant/adminMerchant.module";
import { AdminNotificationModule } from "./adminNotification/admin-notification.module";
import { AdminPaymentModule } from "./adminPayment/adminPayment.module";
import { AdminPaymentInstrumentModule } from "./adminPaymentInstrument/adminPaymentInstrument.module";
import { AdminReportJobModule } from "./adminReportJob/adminReportJob.module";
import { AdminTransactionModule } from "./adminTransaction/adminTransaction.module";
import { AdminUserModule } from "./adminUser/adminUser.module";
import { routes } from "./routes";

/**
 * Admin module
 */
@Module({
  imports: [
    AdminPaymentModule,
    AdminTransactionModule,
    AdminMerchantModule,
    AdminCampaignModule,
    RouterModule.forRoutes(routes),
    AdminUserModule,
    AdminPaymentInstrumentModule,
    AdminAuthUserModule,
    AdminLinkModule,
    AdminReportJobModule,
    AdminNotificationModule,
    AdminFleetOrderModule,
  ],
})
export class AdminModule {
  /**
   * Configure middleware
   * @param consumer MiddlewareConsumer
   */
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminAuthMiddleware).forRoutes("admin");
  }
}

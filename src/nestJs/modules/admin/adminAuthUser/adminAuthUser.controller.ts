import { randomUUID } from "crypto";

import { Body, Controller, Get, Inject, Patch, Post, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiOperation, ApiTags, getSchemaPath } from "@nestjs/swagger";

import { Role } from "@nest/modules/auth/types";

import { CreateAuthUserDto, createAuthUserSchema } from "./dto/createUser.dto";
import { disableUserSchema, DisableUserRequest } from "./dto/disableUser.dto";
import { forceResetPasswordSchema, ForceResetPasswordRequest } from "./dto/forceResetPassword.dto";
import { AuthUserListingQueryDto, authUserListingSchema } from "./dto/listUser.dto";
import { resetFailedLoginAttemptsSchema, ResetFailedLoginAttemptsRequest } from "./dto/resetFailedAttempts.dto";
import { UpdateAuthUserRoleDto, updateAuthUserRoleSchema } from "./dto/updateUserRole.dto";
import { AuthDocument } from "../../appDatabase/documents/auth.document";
import { AuthService } from "../../auth/auth.service";
import { AuthDto } from "../../auth/dto/auth.dto";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { apiTags } from "../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../validation/validationPipe.service";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.admin)
export class AdminAuthUserController {
  constructor(
    private readonly authService: AuthService,
    @Inject(LoggerServiceAdapter) private readonly logger: LoggerServiceAdapter,
  ) {}

  @Post()
  @ApiOperation({ summary: "Create Firebase authentication User with Email" })
  async createAuthUser(
    @Body(new JoiValidationPipe(createAuthUserSchema)) createAuthUserDto: CreateAuthUserDto,
  ): Promise<AuthDocument> {
    this.logger.info("Create Auth User for email: ", { email: createAuthUserDto.email });
    return this.authService.signUpWithEmail(createAuthUserDto.email);
  }

  @Patch()
  @ApiOperation({ summary: "Update Firebase authentication User Role" })
  @ApiBody({
    schema: {
      oneOf: [{ $ref: getSchemaPath(UpdateAuthUserRoleDto) }],
    },
    examples: {
      [Role.ADMIN]: {
        description: "Admin role data",
        value: {
          email: "<EMAIL>",
          roleData: {
            role: Role.ADMIN,
          },
        },
      },
      [Role.FLEET_MANAGER]: {
        description: "Fleet manager role data",
        value: {
          email: "<EMAIL>",
          roleData: {
            role: Role.FLEET_MANAGER,
            fleetId: "09ru203vfr3h0323v",
          },
        },
      },
      [Role.MERCHANT]: {
        description: "Merchant role data",
        value: {
          email: "<EMAIL>",
          roleData: {
            role: Role.MERCHANT,
            merchantId: randomUUID(),
          },
        },
      },
    },
  })
  async updateAuthUserRole(
    @Body(new JoiValidationPipe(updateAuthUserRoleSchema)) updateAuthUserRoleDto: UpdateAuthUserRoleDto,
  ): Promise<AuthDocument> {
    this.logger.info("Update Auth User Role for email: ", { email: updateAuthUserRoleDto.email });
    return this.authService.updateUserRole(updateAuthUserRoleDto.email, updateAuthUserRoleDto.roleData);
  }

  @Get()
  @ApiOperation({ summary: "Get Users from Firestore auths collection" })
  async getUsers(
    @Query(new JoiValidationPipe(authUserListingSchema)) listingQueryDto: AuthUserListingQueryDto,
  ): Promise<AuthDto[]> {
    return this.authService.getUsers(listingQueryDto);
  }

  @Patch("/reset-failed-login-attempts")
  @ApiOperation({ summary: "Reset Failed Login Attempts if failed_attempts > 5" })
  async resetFailedLoginAttempts(
    @Body(new JoiValidationPipe(resetFailedLoginAttemptsSchema))
    resetFailedLoginAttemptsRequest: ResetFailedLoginAttemptsRequest,
  ): Promise<AuthDocument> {
    this.logger.info("Reset Failed Login Attempts for email: ", { email: resetFailedLoginAttemptsRequest.email });
    return this.authService.resetFailedLoginAttempts(resetFailedLoginAttemptsRequest.email);
  }

  @Patch("/force-reset-password")
  @ApiOperation({ summary: "Force Reset Password for email" })
  async forceResetPassword(
    @Body(new JoiValidationPipe(forceResetPasswordSchema))
    forceResetPasswordRequest: ForceResetPasswordRequest,
  ): Promise<AuthDocument> {
    this.logger.info("Force Reset Password for email: ", { email: forceResetPasswordRequest.email });
    return this.authService.forceResetPassword(forceResetPasswordRequest.email);
  }

  @Patch("disable")
  @ApiOperation({ summary: "Disable User, set failed_attempts to 5" })
  async disableUser(
    @Body(new JoiValidationPipe(disableUserSchema))
    disableUserRequest: DisableUserRequest,
  ): Promise<AuthDocument> {
    this.logger.info("Disable User for email: ", { email: disableUserRequest.email });
    return this.authService.disableUser(disableUserRequest.email);
  }
}

import { ApiPropertyOptional } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { Role } from "../../../auth/types";

export class AuthUserListingQueryDto {
  @ApiPropertyOptional()
  email?: string;
  @ApiPropertyOptional()
  role?: Role;
}

export const authUserListingSchema = Joi.object<AuthUserListingQueryDto>({
  email: Joi.string().email().optional(),
  role: Joi.string()
    .valid(...Object.values(Role))
    .optional(),
});

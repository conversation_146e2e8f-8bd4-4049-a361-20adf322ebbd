import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { Role } from "../../../auth/types";

type GenericRoles = Exclude<Role, Role.FLEET_MANAGER | Role.MERCHANT>;

export class GenericRoleData {
  @ApiProperty()
  role: GenericRoles;
}

export class DriverRoleData {
  @ApiProperty()
  role: Role.DRIVER;
  @ApiProperty()
  phone_number: string;
}

export class FleetManagerRoleData {
  @ApiProperty()
  role: Role.FLEET_MANAGER;
  @ApiProperty()
  fleetId: string;
}

export class MerchantRoleData {
  @ApiProperty()
  role: Role.MERCHANT;
  @ApiProperty()
  merchantId: string;
}

export type RoleData = GenericRoleData | FleetManagerRoleData | MerchantRoleData;

export class UpdateAuthUserRoleDto {
  @ApiProperty()
  email: string;

  @ApiProperty()
  roleData: RoleData;
}

export const updateAuthUserRoleSchema = Joi.object({
  email: Joi.string().email().required(),
  roleData: Joi.alternatives(
    Joi.object<GenericRoleData>({
      role: Joi.string()
        .valid(...Object.values(Role).filter((role) => role !== Role.FLEET_MANAGER && role !== Role.MERCHANT))
        .required(),
    }),
    Joi.object<FleetManagerRoleData>({
      role: Joi.string().valid(Role.FLEET_MANAGER).required(),
      fleetId: Joi.string().required(),
    }),
    Joi.object<MerchantRoleData>({
      role: Joi.string().valid(Role.MERCHANT).required(),
      merchantId: Joi.string().required(),
    }),
  ).required(),
});

import { AdminModule } from "./admin.module";
import { AdminAuthUserModule } from "./adminAuthUser/adminAuthUser.module";
import { AdminCampaignModule } from "./adminCampaign/adminCampaign.module";
import { AdminFleetOrderModule } from "./adminFleetOrder/adminFleetOrder.module";
import { AdminLinkModule } from "./adminLink/adminLink.module";
import { AdminMerchantModule } from "./adminMerchant/adminMerchant.module";
import { AdminDriverModule } from "./adminMerchant/modules/driver/adminDriver.module";
import { AdminNotificationModule } from "./adminNotification/admin-notification.module";
import { AdminPaymentModule } from "./adminPayment/adminPayment.module";
import { AdminPaymentInstrumentModule } from "./adminPaymentInstrument/adminPaymentInstrument.module";
import { AdminReportJobModule } from "./adminReportJob/adminReportJob.module";
import { AdminTransactionModule } from "./adminTransaction/adminTransaction.module";
import { AdminUserModule } from "./adminUser/adminUser.module";

export const routes = [
  {
    path: "/admin",
    module: AdminModule,
    children: [
      {
        path: "/payments",
        module: AdminPaymentModule,
      },
      {
        path: "/transactions",
        module: AdminTransactionModule,
      },
      {
        path: "/drivers",
        module: AdminDriverModule,
      },
      {
        path: "/campaigns",
        module: AdminCampaignModule,
      },
      {
        path: "/users",
        module: AdminUserModule,
      },
      {
        path: "/payment-instruments",
        module: AdminPaymentInstrumentModule,
      },
      {
        path: "/auth-users",
        module: AdminAuthUserModule,
      },
      {
        path: "/links",
        module: AdminLinkModule,
      },
      {
        path: "/report-jobs",
        module: AdminReportJobModule,
      },
      {
        path: "/notifications",
        module: AdminNotificationModule,
      },
      {
        path: "/merchants",
        module: AdminMerchantModule,
      },
      {
        path: "/fleet-orders",
        module: AdminFleetOrderModule,
      },
    ],
  },
];

import { Module } from "@nestjs/common";

import { FleetOrderRepository } from "@nest/modules/database/repositories/fleetOrder.repository";

import { AdminFleetOrderController } from "./adminFleetOrder.controller";
import { AdminFleetOrderService } from "./adminFleetOrder.service";

@Module({
  imports: [],
  providers: [AdminFleetOrderService, FleetOrderRepository],
  controllers: [AdminFleetOrderController],
  exports: [AdminFleetOrderService],
})
export class AdminFleetOrderModule {}

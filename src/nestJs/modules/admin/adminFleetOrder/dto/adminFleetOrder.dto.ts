import { ApiPropertyOptional } from "@nestjs/swagger";
import Jo<PERSON> from "joi";

import FleetOrderEntity, { FleetOrderStatus } from "@nest/modules/database/entities/fleetOrder.entity";

export class AdminFleetOrderQueryDto {
  @ApiPropertyOptional()
  page: number;

  @ApiPropertyOptional()
  limit: number;

  @ApiPropertyOptional()
  txId?: string;

  @ApiPropertyOptional()
  tripTxId?: string;

  @ApiPropertyOptional()
  statuses?: FleetOrderStatus[];

  @ApiPropertyOptional()
  merchantPhone?: string;

  @ApiPropertyOptional()
  licensePlate?: string;

  @ApiPropertyOptional()
  userPhone?: string;
}

export class AdminFleetOrderResponseDto {
  @ApiPropertyOptional()
  offset: number;

  @ApiPropertyOptional()
  limit: number;

  @ApiPropertyOptional()
  total: number;

  @ApiPropertyOptional()
  fleetOrders: FleetOrderEntity[];
}

export const adminFleetOrderQuerySchema = Joi.object<AdminFleetOrderQueryDto>({
  page: Joi.number().min(1).default(1).optional(),
  limit: Joi.number().min(1).default(100).optional(),
  txId: Joi.string().uuid().optional(),
  tripTxId: Joi.string().uuid().optional(),
  statuses: Joi.array()
    .items(Joi.string().valid(...Object.values(FleetOrderStatus)))
    .optional(),
  merchantPhone: Joi.string().allow(null, "").optional(),
  licensePlate: Joi.string().allow(null, "").optional(),
  userPhone: Joi.string().allow(null, "").optional(),
}).optional();

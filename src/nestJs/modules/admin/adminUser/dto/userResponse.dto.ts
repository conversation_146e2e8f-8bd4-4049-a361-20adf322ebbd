import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import User from "../../../database/entities/user.entity";
import { GenderType, PreferredLanguageType } from "../../../identity/dto/user.dto";

export class UserResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  phoneNumber: string;

  @ApiPropertyOptional()
  email?: string;

  @ApiPropertyOptional()
  appDatabaseId?: string;

  @ApiPropertyOptional()
  deletedAt?: Date;

  @ApiPropertyOptional()
  gender?: GenderType;

  @ApiPropertyOptional()
  dateOfBirth?: Date;

  @ApiPropertyOptional()
  firstName?: string;

  @ApiPropertyOptional()
  lastName?: string;

  @ApiPropertyOptional()
  preferredLanguage?: PreferredLanguageType;

  @ApiProperty()
  marketingPreferenceEmail: boolean;

  @ApiProperty()
  marketingPreferenceSMS: boolean;

  @ApiProperty()
  marketingConsent: boolean;

  @ApiProperty()
  pinErrorCount: number;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  static fromEntity(user: User): UserResponseDto {
    const dto: UserResponseDto = {
      id: user.id,
      phoneNumber: user.phoneNumber,
      email: user.email,
      appDatabaseId: user.appDatabaseId,
      deletedAt: user.deletedAt,
      gender: user.gender,
      dateOfBirth: user.dateOfBirth,
      firstName: user.firstName,
      lastName: user.lastName,
      preferredLanguage: user.preferredLanguage,
      marketingPreferenceEmail: user.marketingPreferenceEmail,
      marketingPreferenceSMS: user.marketingPreferenceSMS,
      marketingConsent: user.marketingConsent,
      pinErrorCount: user.pinErrorCount,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
    return dto;
  }
}

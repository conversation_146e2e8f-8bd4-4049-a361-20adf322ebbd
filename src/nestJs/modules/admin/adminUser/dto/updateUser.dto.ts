import { ApiPropertyOptional } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { GenderType, PreferredLanguageType } from "../../../identity/dto/user.dto";

export const updateUserSchema = Joi.object({
  gender: Joi.valid(...Object.values(GenderType)).optional(),
  dateOfBirth: Joi.date().optional(),
  firstName: Joi.string().optional(),
  lastName: Joi.string().optional(),
  preferredLanguage: Joi.valid(...Object.values(PreferredLanguageType)).optional(),
  marketingPreferenceEmail: Joi.boolean().optional(),
  marketingPreferenceSMS: Joi.boolean().optional(),
  marketingConsent: Joi.boolean().optional(),
  pinErrorCount: Joi.number().integer().min(0).optional(),
});

export class UpdateUserDto {
  @ApiPropertyOptional()
  appDatabaseId?: string;

  @ApiPropertyOptional()
  deletedAt?: Date;

  @ApiPropertyOptional()
  gender?: GenderType;

  @ApiPropertyOptional()
  dateOfBirth?: Date;

  @ApiPropertyOptional()
  firstName?: string;

  @ApiPropertyOptional()
  lastName?: string;

  @ApiPropertyOptional()
  preferredLanguage?: PreferredLanguageType;

  @ApiPropertyOptional()
  marketingPreferenceEmail?: boolean;

  @ApiPropertyOptional()
  marketingPreferenceSMS?: boolean;

  @ApiPropertyOptional()
  marketingConsent?: boolean;

  @ApiPropertyOptional()
  pinErrorCount?: number;
}

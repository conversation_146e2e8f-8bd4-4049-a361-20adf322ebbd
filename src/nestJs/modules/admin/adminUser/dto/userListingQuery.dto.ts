import { ApiPropertyOptional } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { BaseListingQueryDto, baseListingQuerySchema } from "../../../validation/dto/listingSchema.dto";

export enum UserSortableType {
  CREATED_AT = "createdAt",
  PHONE_NUMBER = "phoneNumber",
  FIRST_NAME = "firstName",
  LAST_NAME = "lastName",
}

export const userQueryListingSchema = baseListingQuerySchema<UserListingQueryDto>().keys({
  sort: Joi.string<UserSortableType>()
    .valid(...Object.values(UserSortableType))
    .optional(),
  id: Joi.string().uuid().optional(),
  phoneNumber: Joi.string().optional(),
  email: Joi.string().optional(),
  name: Joi.string().optional(),
  deleted: Joi.boolean().optional(),
  includeSecondRow: Joi.boolean().optional(),
  createdAtFrom: Joi.string().optional(),
  createdAtTo: Joi.string().optional(),
});

export class UserListingQueryDto extends BaseListingQueryDto {
  @ApiPropertyOptional()
  sort?: UserSortableType;

  @ApiPropertyOptional()
  id?: string;

  @ApiPropertyOptional()
  phoneNumber?: string;

  @ApiPropertyOptional()
  email?: string;

  @ApiPropertyOptional()
  name?: string;

  @ApiPropertyOptional()
  deleted?: boolean;

  @ApiPropertyOptional()
  includeSecondRow?: boolean;

  @ApiPropertyOptional()
  createdAtFrom?: string;

  @ApiPropertyOptional()
  createdAtTo?: string;
}

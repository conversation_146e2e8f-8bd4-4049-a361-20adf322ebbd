import { Inject, Injectable } from "@nestjs/common";
import { DecodedIdToken } from "firebase-admin/auth";
import { FieldValue } from "firebase-admin/firestore";
import merge from "lodash/merge";

import TxEvent from "@nest/modules/database/entities/txEvent.entity";
import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { AddEventBodyMe } from "@nest/modules/me/modules/meTransaction/dto/addEvent.dto";
import { DriverService } from "@nest/modules/merchant/merchantDriver/merchantDriver.service";
import { TxAdjustmentCreateDto } from "@nest/modules/transaction/dto/txAdjustment.dto";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import dateUtils from "@nest/modules/utils/utils/date.utils";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { UpdateTxMetadataDto } from "./dto/updateTxMetadata.dto";
import Tx from "../../database/entities/tx.entity";
import { PaymentInformationStatus } from "../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../payment/dto/paymentInformationType.dto";
import { PaymentType } from "../../payment/dto/paymentType.dto";
import { TransactionService } from "../../transaction/transaction.service";

@Injectable()
export class AdminTransactionService {
  constructor(
    private readonly transactionService: TransactionService,
    private readonly driverService: DriverService,
    private readonly merchantRepository: MerchantRepository,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  checkPaymentTxStatus(tx: Tx): { skipUpdate: boolean; paymentType: PaymentType | "" } {
    let isCapturedSucessfully = false;
    let isSaleSucessfully = false;
    let hasOpenAuth = true;
    if (tx.paymentTx && tx.paymentTx.length > 0) {
      isCapturedSucessfully = tx.paymentTx.some(
        (paymentTx) =>
          paymentTx.type === PaymentInformationType.CAPTURE && paymentTx.status === PaymentInformationStatus.SUCCESS,
      );
      const paymentTxExtendeds = this.transactionService.convertPaymentTxFromArrayToTree(tx.paymentTx);
      hasOpenAuth = paymentTxExtendeds.some(
        (paymentTxExtended) =>
          paymentTxExtended.canDoVoidOrCapture && paymentTxExtended.type === PaymentInformationType.AUTH,
      );
      isSaleSucessfully = paymentTxExtendeds.some(
        (paymentTx) =>
          paymentTx.type === PaymentInformationType.SALE &&
          paymentTx.status === PaymentInformationStatus.SUCCESS &&
          paymentTx.canDoVoidOrCapture,
      );
    }

    return {
      skipUpdate: hasOpenAuth,
      paymentType: isCapturedSucessfully || isSaleSucessfully ? PaymentType.DASH : "",
    };
  }

  async createTxAdjustment(txId: string, admin: DecodedIdToken, txAdjustmentRequest: TxAdjustmentCreateDto) {
    return this.mapCleanTx(await this.transactionService.createTxAdjustment(txId, txAdjustmentRequest, admin));
  }

  // removed relationships if they exist
  private mapCleanTx(tx: Tx): Tx {
    delete tx.user;
    delete tx.merchant;
    delete tx.parentTx;
    delete tx.payoutMerchant;

    return tx;
  }

  async updateTxMetadata(txId: string, body: UpdateTxMetadataDto): Promise<Tx> {
    const tx = await this.transactionService.getTransactionById(txId);
    if (!tx) {
      throw errorBuilder.transaction.notFound(txId);
    }

    const metadataUpdate: any = {};
    if (body.paymentType !== undefined) {
      metadataUpdate.paymentType = body.paymentType;
    }

    if (body.driver) {
      const driver = await this.driverService.getDriverById(body.driver);
      this.logger.debug(`[::Driver Data::] ${JSON.stringify(driver)}`);

      if (!driver) {
        throw errorBuilder.merchant.driver.notFound(body.driver);
      }
      metadataUpdate.driver = {
        id: driver.phoneNumber,
        name: driver.name,
        nameCh: driver.nameLocal,
        driverLicense: driver.metadata.driverLicense,
      };
    }

    if (body.session) {
      metadataUpdate.session = { id: body.session };
    }

    if (body.creationTime) {
      metadataUpdate.creationTime = new Date(body.creationTime);
    }

    if (body.checkMerchantId) {
      const driverId = body.driverPhoneNumber || (tx.metadata as any)?.driver?.id;
      const driver = await this.driverService.getDriverById(driverId);
      if (!driver) {
        throw errorBuilder.merchant.driver.notFound(driverId);
      }
      const merchant = await this.merchantRepository.findOneBy({ id: driver.id });
      if (!merchant) {
        throw errorBuilder.merchant.notFound(driver.id);
      }
      tx.merchant = merchant;
      tx.merchantId = driver.id;
      await this.transactionService.saveTransaction(tx);
    }

    if (body.addTripEnd) {
      const lastUpdateTime = (tx.metadata as any)?.lastUpdateTime;
      if (lastUpdateTime) {
        metadataUpdate.tripEnd = new Date(lastUpdateTime);
        metadataUpdate.lastUpdateTime = new Date(new Date(lastUpdateTime).getTime() + 1000);
        metadataUpdate.should_update_last_update_time = true;
      }
    }

    if (body.deleteTripEnd) {
      const tripEnd = (tx.metadata as any)?.tripEnd;
      if (tripEnd) {
        metadataUpdate.lastUpdateTime = new Date(tripEnd);
        metadataUpdate.tripEnd = FieldValue.delete();
      }
    }

    // // const mergedMetadata = { ...(tx.metadata || {}), ...metadataUpdate };

    const mergedMetadata = merge({}, tx.metadata, metadataUpdate);
    const metadataWithDates = dateUtils.convertIsoStringsToDates(mergedMetadata);
    const firestoreReadyMetadata = dateUtils.objectDatesToTimestamps(metadataWithDates);
    return this.transactionService.updateMetadata(tx, firestoreReadyMetadata);
  }

  async addEvent(transactionId: string, addEventBody: AddEventBodyMe): Promise<TxEvent> {
    return this.transactionService.addEvent(transactionId, "ADMIN", addEventBody);
  }
}

import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export enum UpdateTxMetadataPaymentType {
  DASH = "DASH",
}

export const updateTxMetadataSchema = Joi.object<UpdateTxMetadataDto>({
  paymentType: Joi.string()
    .valid(...Object.values(UpdateTxMetadataPaymentType))
    .optional(),
  driver: Joi.string().optional(),
  driverPhoneNumber: Joi.string().optional(),
  session: Joi.string().optional(),
  checkMerchantId: Joi.boolean().optional(),
  creationTime: Joi.date().optional(),
  addTripEnd: Joi.boolean().optional(),
  deleteTripEnd: Joi.boolean().optional(),
});

export class UpdateTxMetadataDto {
  @ApiProperty({
    description: "Payment type to set in metadata, if provided",
    enum: UpdateTxMetadataPaymentType,
    required: false,
    type: String,
    example: "DASH",
  })
  paymentType?: string;

  @ApiProperty({
    description: "Driver to set in metadata, if provided",
    required: false,
    type: String,
    example: "John Doe",
  })
  driver?: string;

  @ApiProperty({
    description: "Driver phone number to set in metadata, if provided",
    required: false,
    type: String,
    example: "+1234567890",
  })
  driverPhoneNumber?: string;

  @ApiProperty({
    description: "Session id to set in metadata, if provided",
    required: false,
    type: String,
    example: "session_123",
  })
  session?: string;

  @ApiProperty({
    description: "If true, update merchantId using driver if both driver and session exist in metadata",
    required: false,
    type: Boolean,
    example: true,
  })
  checkMerchantId?: boolean;

  @ApiProperty({
    description: "Creation Time to set in metadata, if provided",
    required: false,
    type: Date,
    example: "2023-01-01T00:00:00Z",
  })
  creationTime?: Date;

  @ApiProperty({
    description: "If true, copy last_update_time to trip_end and increment last_update_time by 1 second",
    required: false,
    type: Boolean,
    example: false,
  })
  addTripEnd?: boolean;

  @ApiProperty({
    description: "If true, delete trip_end from metadata",
    required: false,
    type: Boolean,
    example: false,
  })
  deleteTripEnd?: boolean;
}

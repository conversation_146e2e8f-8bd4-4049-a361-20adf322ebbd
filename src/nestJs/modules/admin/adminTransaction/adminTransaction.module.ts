import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";

import { AdminTransactionController } from "./adminTransaction.controller";
import { AdminTransactionService } from "./adminTransaction.service";
import { DriverModule } from "../../merchant/merchantDriver/merchantDriver.module";
import { TransactionModule } from "../../transaction/transaction.module";

@Module({
  providers: [AdminTransactionController, AdminTransactionService, MerchantRepository, UserRepository],
  imports: [ConfigModule, TransactionModule, DriverModule],
  controllers: [AdminTransactionController],
  exports: [AdminTransactionController, AdminTransactionService],
})
export class AdminTransactionModule {}

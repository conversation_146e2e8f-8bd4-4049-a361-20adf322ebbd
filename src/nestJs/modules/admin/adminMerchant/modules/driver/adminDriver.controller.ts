import { Post, Body, Controller, Inject } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { InsertResult } from "typeorm";

import {
  PushNotificationsDto,
  PushNotificationsResponse,
  pushNotificationsSchema,
} from "@nest/modules/merchant/merchantDriver/dto/pushNotification.dto";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

import {
  ingestFromBucketFileSchema,
  IngestFromBucketFile,
} from "../../../../merchant/merchantDriver/dto/ingestFromBucketFile.dto";
import { DriverService } from "../../../../merchant/merchantDriver/merchantDriver.service";
import { apiTags } from "../../../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../../../validation/validationPipe.service";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.admin)
export class AdminDriverController {
  constructor(
    private readonly driverService: DriverService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  @Post("ingest-from-bucket-file")
  @ApiOperation({ summary: "Ingest file from bucket to generate the merchants" })
  @ApiResponse({ status: 201, description: "Ingest file from bucket to generate the merchants" })
  async ingestFromBucketFile(
    @Body(new JoiValidationPipe(ingestFromBucketFileSchema)) ingestFromBucketFile: IngestFromBucketFile,
  ): Promise<InsertResult> {
    return this.driverService.ingestFromBucketFile(ingestFromBucketFile);
  }

  @Post("notification")
  @ApiOperation({ summary: "Push notifications to the drivers" })
  @ApiResponse({ status: 201, description: "Push notifications to the drivers" })
  async pushNotifications(
    @Body(new JoiValidationPipe(pushNotificationsSchema)) pushNotifications: PushNotificationsDto,
  ): Promise<PushNotificationsResponse[]> {
    const result = await Promise.allSettled(
      pushNotifications.notifications.map((notification) => {
        return this.driverService.pushNotification(notification);
      }),
    );
    const { fulfilled: results, rejected: errorReasons } = result.reduce(
      (acc, result) => {
        if (result.status === "fulfilled") {
          acc.fulfilled.push(result.value);
        } else if (result.status === "rejected") {
          acc.rejected.push(result.reason);
        }
        return acc;
      },
      { fulfilled: [] as PushNotificationsResponse[], rejected: [] as unknown[] },
    );
    if (errorReasons.length > 0) this.logger.error("Failed to push notification", { errorReasons });
    return results;
  }
}

import { Injectable } from "@nestjs/common";

import { PlatformMerchantType } from "@nest/modules/database/entities/merchant.entity";
import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";

import { AdminMerchantQueryDto, AdminMerchantResponseDto } from "./dto/adminMerchant.dto";

@Injectable()
export class AdminMerchantService {
  constructor(private readonly merchantRepository: MerchantRepository) {}

  async getMerchants(query: AdminMerchantQueryDto): Promise<AdminMerchantResponseDto> {
    const offset = (query.page - 1) * query.limit || 0;

    const phoneNumber = query.phoneNumber?.includes("-") ? query.phoneNumber.split("-")[0] : query.phoneNumber;
    const platformMerchantType = query.phoneNumber?.includes("-")
      ? (query.phoneNumber.split("-")[1] as PlatformMerchantType)
      : query.platformType || PlatformMerchantType.DASH;

    const [merchants, total] = await this.merchantRepository.findAndCount({
      where: { platformMerchantType, phoneNumber },
      skip: offset,
      take: query.limit,
    });

    return {
      offset,
      limit: query.limit,
      total,
      merchants,
    };
  }
}

import { Module } from "@nestjs/common";

import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";

import { AdminMerchantController } from "./adminMerchant.controller";
import { AdminMerchantService } from "./adminMerchant.service";
import { AdminDriverModule } from "./modules/driver/adminDriver.module";

@Module({
  imports: [AdminDriverModule],
  providers: [AdminMerchantService, MerchantRepository],
  controllers: [AdminMerchantController],
  exports: [AdminMerchantService],
})
export class AdminMerchantModule {}

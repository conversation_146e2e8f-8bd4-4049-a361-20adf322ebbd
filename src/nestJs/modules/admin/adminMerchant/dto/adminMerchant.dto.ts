import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import Jo<PERSON> from "joi";

import Merchant, { PlatformMerchantType } from "@nest/modules/database/entities/merchant.entity";

export class AdminMerchantResponseDto {
  offset: number;
  limit: number;
  total: number;
  merchants: Merchant[];
}

export class AdminMerchantQueryDto {
  @ApiProperty()
  page: number;
  @ApiProperty()
  limit: number;
  @ApiPropertyOptional({ type: "enum", enum: PlatformMerchantType })
  platformType?: PlatformMerchantType;

  @ApiPropertyOptional()
  phoneNumber?: string;
}

export const adminMerchantQuerySchema = Joi.object<AdminMerchantQueryDto>({
  page: Joi.number().optional().default(1),
  limit: Joi.number().optional().default(10),
  platformType: Joi.string<PlatformMerchantType>()
    .valid(...Object.values(PlatformMerchantType))
    .default(PlatformMerchantType.DASH)
    .optional(),
  phoneNumber: Joi.string().default(null).optional(),
});

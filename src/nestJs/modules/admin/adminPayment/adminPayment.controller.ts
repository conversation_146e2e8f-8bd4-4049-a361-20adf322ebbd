import { Body, Controller, Get, Param, Post, Query, Req } from "@nestjs/common";
import { ApiOperation, ApiTags, ApiBearerAuth, ApiResponse } from "@nestjs/swagger";
import { InjectRepository } from "@nestjs/typeorm";
import { Request } from "express";

import { PaymentInstrumentRepository } from "@nest/modules/database/repositories/paymentInstument.repository";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { PaymentAuthBodyDto, paymentAuthRequestSchema } from "@nest/modules/payment/dto/paymentAuthRequest.dto";
import { PaymentRefundBodyDto, paymentRefundRequestSchema } from "@nest/modules/payment/dto/paymentRefundRequest.dto";
import { PaymentSaleBodyDto, paymentSaleRequestSchema } from "@nest/modules/payment/dto/paymentSaleRequest.dto";
import { TxTypes } from "@nest/modules/transaction/dto/txType.dto";

import { GetAdminPaymentQuery, getAdminPaymentQuerySchema } from "./adminPayment.dto";
import PaymentTx from "../../database/entities/paymentTx.entity";
import { EnquiryResponse } from "../../payment/dto/paymentGatewayResponses.model";
import { PaymentInformationStatus } from "../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../payment/dto/paymentInformationType.dto";
import { PaymentBodyDto, paymentRequestSchema, paymentTxIdSchema } from "../../payment/dto/paymentRequest.dto";
import { PaymentService } from "../../payment/payment.service";
import { TransactionFactoryService } from "../../transaction/transactionFactory/transactionFactory.service";
import { apiTags } from "../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../validation/validationPipe.service";

/**
 * AdminPayment controller
 */
@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.admin)
export class AdminPaymentController {
  requestedByUnknown = "Unknown";

  constructor(
    private readonly paymentService: PaymentService,
    private readonly transactionFactoryService: TransactionFactoryService,
    @InjectRepository(TxRepository) private txRepository: TxRepository,
    @InjectRepository(PaymentInstrumentRepository) private paymentInstrumentRepository: PaymentInstrumentRepository,
  ) {}

  /**
   * Get the paymentTx information
   * if raw=true then return the raw paymentTx information from payment source
   */
  @Get(":paymentTxId")
  @ApiOperation({ summary: "Get payment transaction by id" })
  @ApiResponse({ status: 200, description: "Get payment transaction by id" })
  async getTransaction(
    @Param("paymentTxId", new JoiValidationPipe(paymentTxIdSchema.required())) paymentTxId: string,
    @Query(new JoiValidationPipe(getAdminPaymentQuerySchema)) query: GetAdminPaymentQuery,
    @Req() req: Request,
  ): Promise<EnquiryResponse | PaymentTx> {
    const requestedBy = req.user?.email || this.requestedByUnknown;
    const localPaymentTx = await this.paymentService.getPaymentTxById(paymentTxId);
    if (query.raw) {
      return this.paymentService.enquirePaymentTx(localPaymentTx, requestedBy);
    }
    return localPaymentTx;
  }

  /**
   * Void an authorised payment, Called in Portal
   * @param req Request
   * @param paymentTxId string
   * @return PaymentTx
   */
  @Post(":paymentTxId/void")
  @ApiOperation({ summary: "Void an authorised payment" })
  async voidPayment(
    @Param("paymentTxId", new JoiValidationPipe(paymentTxIdSchema)) paymentTxId: string,
    @Req() req: Request,
  ): Promise<PaymentTx> {
    const requestedBy = req.user?.email || this.requestedByUnknown;
    return this.paymentService.voidPayment(paymentTxId, requestedBy);
  }

  /**
   * refund a payment, Called in Portal
   * @param req Request
   * @param paymentTxId string
   * @return PaymentTx
   */
  @Post(":paymentTxId/refund")
  @ApiOperation({ summary: "Refund a payment" })
  async refundPayment(
    @Param("paymentTxId", new JoiValidationPipe(paymentTxIdSchema)) paymentTxId: string,
    @Body(new JoiValidationPipe(paymentRefundRequestSchema)) paymentRefundBodyDto: PaymentRefundBodyDto,
    @Req() req: Request,
  ): Promise<PaymentTx> {
    const requestedBy = req.user?.email || this.requestedByUnknown;
    return this.paymentService.refundPayment(paymentTxId, requestedBy, paymentRefundBodyDto.amount);
  }

  /**
   * Capture an authorised payment, Called in Portal
   * @param req Request
   * @param paymentTxId string
   * @param paymentBodyDto PaymentBodyDto
   * @return PaymentTx
   */
  @Post(":paymentTxId/capture")
  @ApiOperation({ summary: "Capture an authorised payment" })
  async capturePayment(
    @Param("paymentTxId", new JoiValidationPipe(paymentTxIdSchema)) paymentTxId: string,
    @Body(new JoiValidationPipe(paymentRequestSchema)) paymentBodyDto: PaymentBodyDto,
    @Req() req: Request,
  ): Promise<PaymentTx> {
    const requestedBy = req.user?.email || this.requestedByUnknown;
    const capturedPaymentTx = await this.paymentService.capturePayment(paymentTxId, paymentBodyDto.amount, requestedBy);
    await this.transactionFactoryService.postPaymentProcess(
      capturedPaymentTx.tx,
      capturedPaymentTx.type === PaymentInformationType.CAPTURE &&
        capturedPaymentTx.status === PaymentInformationStatus.SUCCESS,
    );

    return capturedPaymentTx;
  }

  /**
   * search for a payment
   * @param req Request
   * @param paymentTxId string
   * @return PaymentTx
   */
  @Post(":paymentTxId/search")
  @ApiOperation({ summary: "Search for a paymentTx" })
  async SearchPayment(
    @Param("paymentTxId", new JoiValidationPipe(paymentTxIdSchema)) paymentTxId: string,
    @Req() req: Request,
  ): Promise<EnquiryResponse> {
    const requestedBy = req.user?.email || this.requestedByUnknown;
    const paymentTx = await this.paymentService.getPaymentTxById(paymentTxId);
    return this.paymentService.searchPaymentTx(paymentTx, requestedBy);
  }

  /**
   * Enquire a payment
   * @param req Request
   * @param paymentTxId string
   * @return PaymentTx
   */
  @Post(":paymentTxId/enquire")
  @ApiOperation({ summary: "Enquire about a paymentTx" })
  async EnquirePayment(
    @Param("paymentTxId", new JoiValidationPipe(paymentTxIdSchema)) paymentTxId: string,
    @Req() req: Request,
  ): Promise<EnquiryResponse> {
    const requestedBy = req.user?.email || this.requestedByUnknown;
    const paymentTx = await this.paymentService.getPaymentTxById(paymentTxId);
    return this.paymentService.enquirePaymentTx(paymentTx, requestedBy);
  }

  /**
   * Create a Sale for a Transaction
   * @param req Request
   * @param paymentSaleBodyDto PaymentSaleBodyDto
   * @return PaymentTx
   */
  @Post("sale")
  @ApiOperation({ summary: "Create a Sale for a Transaction" })
  async salePayment(
    @Body(new JoiValidationPipe(paymentSaleRequestSchema)) paymentSaleBodyDto: PaymentSaleBodyDto,
    @Req() req: Request,
  ): Promise<PaymentTx> {
    const requestedBy = req.user?.email || this.requestedByUnknown;
    const tx = await this.txRepository.findOneOrFail({ where: { id: paymentSaleBodyDto.txId } });
    console.log("salePaymentController", { tx, paymentSaleBodyDto, requestedBy });
    const salePaymentTx = await this.paymentService.processSale(
      tx,
      paymentSaleBodyDto.paymentInstrumentId,
      paymentSaleBodyDto.amount,
      requestedBy,
    );
    if (tx.type === TxTypes.TRIP) {
      await this.transactionFactoryService.postPaymentProcess(
        salePaymentTx.tx,
        salePaymentTx.type === PaymentInformationType.SALE && salePaymentTx.status === PaymentInformationStatus.SUCCESS,
      );
    }

    return salePaymentTx;
  }

  /**
   * Create an Auth for a Transaction
   * @param req Request
   * @param paymentAuthBodyDto PaymentAuthBodyDto
   * @return PaymentTx
   */
  @Post("auth")
  @ApiOperation({ summary: "Create an Auth for a Transaction" })
  async authPayment(
    @Body(new JoiValidationPipe(paymentAuthRequestSchema)) paymentAuthBodyDto: PaymentAuthBodyDto,
    @Req() req: Request,
  ): Promise<PaymentTx> {
    const requestedBy = req.user?.email || this.requestedByUnknown;
    const tx = await this.txRepository.findOneOrFail({ where: { id: paymentAuthBodyDto.txId } });
    const paymentInstrument = await this.paymentInstrumentRepository.findOneOrFail({
      where: { id: paymentAuthBodyDto.paymentInstrumentId },
    });
    return this.paymentService.processAuth(tx, paymentInstrument, paymentAuthBodyDto.amount, requestedBy);
  }
}

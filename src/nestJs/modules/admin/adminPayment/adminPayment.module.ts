import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { PaymentInstrumentRepository } from "@nest/modules/database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "@nest/modules/database/repositories/paymentTx.repository";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";

import { AdminPaymentController } from "./adminPayment.controller";
import { PaymentModule } from "../../payment/payment.module";
import { TransactionFactoryModule } from "../../transaction/transactionFactory/transactionFactory.module";

/**
 * AdminPayment module
 */
@Module({
  providers: [AdminPaymentController, TxRepository, PaymentInstrumentRepository, PaymentTxRepository],
  imports: [ConfigModule, PaymentModule, TransactionFactoryModule],
  controllers: [AdminPaymentController],
  exports: [AdminPaymentController],
})
export class AdminPaymentModule {}

import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import PaymentInstrument from "../../../database/entities/paymentInstrument.entity";
import { PaymentGatewayTypes } from "../../../payment/dto/paymentGatewayTypes.dto";
import {
  PaymentInstrumentState,
  PaymentInstrumentType,
} from "../../../payment/modules/paymentInstrument/dto/paymentInstrument.dto";

export class PaymentInstrumentResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  instrumentIdentifier: string;

  @ApiProperty()
  cardHolderName: string;

  @ApiPropertyOptional({ type: Date })
  verifiedAt?: Date | null;

  @ApiProperty()
  expirationDate: Date;

  @ApiProperty()
  expirationYear: string;

  @ApiProperty()
  expirationMonth: string;

  @ApiProperty()
  state: PaymentInstrumentState;

  @ApiProperty()
  cardPrefix: string;

  @ApiProperty()
  cardSuffix: string;

  @ApiPropertyOptional({ type: String })
  verificationTransactionId?: string | null;

  @ApiPropertyOptional({ type: Boolean })
  isPayerAuthEnroled?: boolean | null;

  @ApiProperty()
  isPreferred: boolean;

  @ApiProperty()
  cardType: PaymentInstrumentType;

  @ApiProperty()
  deletedAt: Date;

  @ApiProperty()
  paymentGateway: PaymentGatewayTypes;

  static fromEntity(paymentInstrument: PaymentInstrument): PaymentInstrumentResponseDto {
    const dto: PaymentInstrumentResponseDto = {
      id: paymentInstrument.id,
      instrumentIdentifier: paymentInstrument.instrumentIdentifier,
      cardHolderName: paymentInstrument.cardHolderName,
      verifiedAt: paymentInstrument.verifiedAt,
      expirationDate: paymentInstrument.expirationDate,
      expirationYear: paymentInstrument.expirationYear,
      expirationMonth: paymentInstrument.expirationMonth,
      state: paymentInstrument.state,
      cardPrefix: paymentInstrument.cardPrefix,
      cardSuffix: paymentInstrument.cardSuffix,
      verificationTransactionId: paymentInstrument.verificationTransactionId,
      isPayerAuthEnroled: paymentInstrument.isPayerAuthEnroled,
      isPreferred: paymentInstrument.isPreferred,
      cardType: paymentInstrument.cardType,
      deletedAt: paymentInstrument.deletedAt,
      paymentGateway: paymentInstrument.paymentGateway,
    };

    return dto;
  }
}

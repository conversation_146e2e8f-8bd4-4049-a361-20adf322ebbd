import { Module } from "@nestjs/common";

import { AdminPaymentInstrumentController } from "./adminPaymentInstrument.controller";
import { PaymentInstrumentModule } from "../../payment/modules/paymentInstrument/paymentInstrument.module";

@Module({
  imports: [PaymentInstrumentModule],
  controllers: [AdminPaymentInstrumentController],
  providers: [AdminPaymentInstrumentController],
  exports: [AdminPaymentInstrumentController],
})
export class AdminPaymentInstrumentModule {}

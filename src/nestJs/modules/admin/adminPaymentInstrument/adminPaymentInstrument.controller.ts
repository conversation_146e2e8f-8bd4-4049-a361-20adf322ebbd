import { Controller, Get, HttpStatus, Param } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { PaymentInstrumentResponseDto } from "./dto/paymentInstrumentResponse.dto";
import { PaymentInstrumentService } from "../../payment/modules/paymentInstrument/paymentInstrument.service";
import { apiTags } from "../../utils/utils/swagger.utils";
import genericSchemas from "../../validation/dto/genericSchemas.dto";
import { JoiValidationPipe } from "../../validation/validationPipe.service";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.admin)
export class AdminPaymentInstrumentController {
  constructor(private readonly paymentInstrumentService: PaymentInstrumentService) {}

  @Get("/user/:userId")
  @ApiOperation({ summary: "Get payment instruments for user" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Get payment instruments for user",
    type: PaymentInstrumentResponseDto,
    isArray: true,
  })
  async getPaymentInstrumentsForUser(
    @Param("userId", new JoiValidationPipe(genericSchemas.uuid.required())) userId: string,
  ): Promise<PaymentInstrumentResponseDto[]> {
    const paymentInstruments = await this.paymentInstrumentService.getPaymentInstrumentsForUser(userId);
    return paymentInstruments.map((paymentInstrument) => PaymentInstrumentResponseDto.fromEntity(paymentInstrument));
  }
}

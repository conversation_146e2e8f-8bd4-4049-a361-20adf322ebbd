import { randomUUID } from "crypto";

import { <PERSON>, Post, Body, Req } from "@nestjs/common";
import { ApiBearerAuth, ApiTags, ApiOperation, ApiBody, getSchemaPath } from "@nestjs/swagger";
import { Request } from "express";

import ReportJob from "@nest/modules/database/entities/reportJob.entity";
import {
  CreateReportJobBodyDto,
  createReportJobBodySchema,
} from "@nest/modules/reportJob/dto/createReportJobRequest.dto";
import { ReportType, SystemAlertType } from "@nest/modules/reportJob/dto/reportJob.dto";
import { ReportJobService } from "@nest/modules/reportJob/reportJob.service";
import { apiTags } from "@nest/modules/utils/utils/swagger.utils";
import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";

import { errorBuilder } from "../../utils/utils/error.utils";
@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.admin)
export class AdminReportJobController {
  constructor(private readonly reportJobService: ReportJobService) {}

  @Post()
  @ApiOperation({ summary: "Create Report Job" })
  @ApiBody({
    schema: {
      oneOf: [{ $ref: getSchemaPath(CreateReportJobBodyDto) }],
    },
    examples: {
      [ReportType.TRIP]: {
        description: "Fleet Trip Report Job",
        value: {
          name: "Trip Report",
          cron: "* 0 * * *",
          reportType: ReportType.TRIP,
          destination: "<EMAIL>",
          queryString:
            "SELECT id, md5(meter_id), location, bearing, speed, device_time, server_time FROM `dash-prod-a345a`.`silver_dataset`.`heartbeat_table` WHERE device_time BETWEEN [START_TIMESTAMP] AND [END_TIMESTAMP];",
          merchantMetadata: {
            fleetId: "09ru203vfr3h0323v",
            merchantId: randomUUID(),
          },
        },
      },
      [ReportType.HEARTBEAT]: {
        description: "Send Heartbeat Data Report",
        value: {
          name: "Heartbeat Data",
          cron: "* 0 * * *",
          reportType: ReportType.HEARTBEAT,
          destination: "<EMAIL>",
          queryString:
            "SELECT id, md5(meter_id), location, bearing, speed, device_time, server_time FROM `dash-prod-a345a`.`silver_dataset`.`heartbeat_table` WHERE device_time BETWEEN [START_TIMESTAMP] AND [END_TIMESTAMP];",
          merchantMetadata: {},
        },
      },
      [ReportType.SYSTEM_ALERT]: {
        description: "Send System Alert Report",
        value: {
          name: "System Alert Report",
          cron: "* 0 * * *",
          reportType: ReportType.SYSTEM_ALERT,
          destination: "",
          queryString:
            "SELECT id, metadata__billing__estimated_fare, metadata__billing__fare, metadata__trip_end, metadata__license_plate FROM `dash-dev2-edcb3`.`silver_dataset`.`trip_table` where metadata__trip_end BETWEEN [START_TIMESTAMP] and [END_TIMESTAMP] and (metadata__billing__estimated_fare * 1.2 <=  metadata__billing__fare or metadata__billing__estimated_fare * 0.8 >=  metadata__billing__fare) order by metadata__trip_end desc",
          merchantMetadata: {
            alertType: SystemAlertType.FARE,
            summary: "Fare Alert",
          },
        },
      },
    },
  })
  async createReportJob(
    @Body(new JoiValidationPipe(createReportJobBodySchema)) createReportJobBody: CreateReportJobBodyDto,
    @Req() req: Request,
  ): Promise<ReportJob> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.reportJobService.createReportJob(createReportJobBody, user);
  }
}

import { randomUUID } from "crypto";

import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { DiscountEntity } from "../discount.entity";

export class IssueCampaignDiscountRequestDto {
  @ApiProperty({
    example: randomUUID(),
    description: "Campaign ID",
  })
  campaignId: string;

  @ApiProperty({
    example: randomUUID(),
    description: "User ID",
  })
  userId: string;
}

export const issueCampaignDiscountRequestDtoSchema = Joi.object({
  campaignId: Joi.string().uuid().required(),
  userId: Joi.string().uuid().required(),
});

export class IssueCampaignDiscountResponseDto {
  @ApiProperty({
    example: "success",
    description: "Status of the discount redemption.",
  })
  status: string;

  @ApiProperty({
    example: "Discount redeemed successfully",
    description: "Message indicating the result of the redemption.",
  })
  message: string;

  @ApiProperty({
    description: "The discount object created",
    type: DiscountEntity,
  })
  discount: DiscountEntity;
}

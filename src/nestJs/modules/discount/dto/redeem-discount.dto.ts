import { randomUUID } from "crypto";

import { ApiProperty } from "@nestjs/swagger";
import Jo<PERSON> from "joi";

export class RedeemDiscountRequestDto {
  @ApiProperty({
    example: randomUUID(),
    required: true,
    description: "The txId where this coupon is redeemed. Must be unique for the discount.",
  })
  txId: string;
  @ApiProperty({
    example: 20.0,
    required: true,
    description: "The redeemed value of the discount. Must not exceed the max coupon value.",
  })
  redeemedValue: number;
}

export const redeemDiscountRequestDtoSchema = Joi.object({
  txId: Joi.string().uuid().required(),
  redeemedValue: Joi.number().required().min(0),
});

export class RedeemDiscountResponseDto {
  @ApiProperty({
    example: "success",
    description: "Status of the discount redemption.",
  })
  status: string;

  @ApiProperty({
    example: "Discount redeemed successfully",
    description: "Message indicating the result of the redemption.",
  })
  message: string;
}

export const redeemDiscountResponseMessage: RedeemDiscountResponseDto = {
  status: "success",
  message: "Discount redeemed successfully",
};

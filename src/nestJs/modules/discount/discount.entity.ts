import { ApiProperty } from "@nestjs/swagger";

import Discount from "@nest/modules/database/entities/discount.entity";

import { DiscountState } from "./dto/discount.dto";

export class DiscountEntity {
  @ApiProperty()
  id: string;

  @ApiProperty()
  state: DiscountState;

  @ApiProperty()
  startAt?: Date;

  @ApiProperty()
  endAt?: Date;

  @ApiProperty()
  redeemedAt?: Date;

  @ApiProperty()
  redeemedValue?: number | null;

  @ApiProperty()
  rewardCode?: string;
  constructor(discount: Discount) {
    this.id = discount.id;
    this.state = discount.state;
    this.startAt = discount.startAt;
    this.endAt = discount.endAt;
    this.redeemedAt = discount.redeemedAt;
    this.redeemedValue = discount.redeemedValue;
    this.rewardCode = discount.rewardCode;
  }
}

import { MailDataRequired } from "@sendgrid/mail";

export type BaseEmailConfig = Omit<MailDataRequired, "from"> & {
  to: string;
  from?: string;
};

export type ActionEmailConfig = BaseEmailConfig & {
  dynamicTemplateData: {
    subject: string;
    heading: string;
    body: string;
    button: string;
    actionUrl: string;
  };
};

export type NotificationEmailConfig = BaseEmailConfig & {
  dynamicTemplateData: {
    subject: string;
    heading: string;
    body: string;
  };
};

import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import moment from "moment";
import { Row } from "read-excel-file/node";

import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";

import { TxRepository } from "../../../../database/repositories/tx.repository";
import { MerchantInfo } from "../../../../transaction/dto/createPayout.dto";
import { roundOneDecimal } from "../../../../utils/utils/number.utils";
import { PayoutFileResponse } from "../../../dto/payoutFileResponse.dto";
import bankFactoryInterface from "../../bankFactoryInterface";

@Injectable()
export class DbsService implements bankFactoryInterface {
  constructor(
    @InjectRepository(TxRepository) private txRepository: TxRepository,
    private readonly appDatabaseService: AppDatabaseService,
  ) {}

  async getIsBulkPayoutEnable(): Promise<boolean> {
    return await this.appDatabaseService.configurationRepository().getIsBulkPayoutEnable();
  }
  async generatePayoutFileContent(txIds: string[]): Promise<PayoutFileResponse> {
    const { merchants, processed, unprocessed, reasons } = await this.txRepository.updatePayoutAndGetMerchantsData(
      txIds,
    );
    const isBulkPayoutEnable = await this.getIsBulkPayoutEnable();
    return {
      content: this.getPayoutFileContentAndFooter(merchants, isBulkPayoutEnable),
      processed,
      unprocessed,
      reasons,
    };
  }

  /**
   * Generate the content of the payout file and the footer
   * @param merchants MerchantInfo[] list of merchants
   * @returns {string} file string
   */
  getPayoutFileContentAndFooter(merchants: MerchantInfo[], isBulkPayoutEnable: boolean = false): string {
    const fileDate = moment(new Date()).format("DDMMYYYY");
    const header = "HEADER," + fileDate + ",HKVISM01,VIS MOBILITY LIMITED\n";

    let count = 0;
    let runningTotal = 0;
    // isBulkPayoutEnable is true: bulk payout
    // isBulkPayoutEnable is false: FPS payout
    const content = isBulkPayoutEnable
      ? merchants
          .map((merchant) => {
            runningTotal = roundOneDecimal(runningTotal + merchant.total);
            count++;
            const bankAccount = merchant.bankAccount.substring(3);
            const branchCode = merchant.bankAccount.substring(0, 3);
            return (
              "PAYMENT,BPY,*********,HKD," +
              merchant.phoneNumber +
              ",HKD,," +
              fileDate +
              ",,," +
              merchant.bankAccountOwnerName +
              ",,,,," +
              bankAccount +
              ",," +
              merchant.bankId +
              "," +
              branchCode +
              ",,,,,,,,," +
              merchant.total +
              ",,,,,20,," +
              merchant.phoneNumber +
              ",,,,B,,,,CXBSNS,,,,,,,,,,,,,,,,,,,,,,,,,"
            );
          })
          .join("\n")
      : merchants
          .map((merchant) => {
            runningTotal = roundOneDecimal(runningTotal + merchant.total);
            count++;
            return (
              "PAYMENT,GPP,*********,HKD," +
              merchant.phoneNumber +
              ",HKD,," +
              fileDate +
              ",,," +
              merchant.bankAccountOwnerName +
              ",,,,," +
              merchant.bankAccount +
              ",," +
              merchant.bankId +
              ",,,,,,,,,," +
              merchant.total +
              ",,,,,20,," +
              merchant.phoneNumber +
              ",,,,B,,,,CXBSNS,,,,,,,,,,,,,,,,,,,,,,,,,"
            );
          })
          .join("\n");

    const footer = "\nTRAILER," + count + "," + runningTotal;

    return header + content + footer;
  }

  bankFileTransformer(content: Row[]): Row[] {
    return content.filter((row) => row[0] != null && row[1] != null);
  }
}

import { Row } from "read-excel-file/node";

import { MerchantInfo } from "../../transaction/dto/createPayout.dto";
import { PayoutFileResponse } from "../dto/payoutFileResponse.dto";

/**
 * Service interface to be implemented by all bank services
 */
export default interface bankFactoryInterface {
  generatePayoutFileContent: (txIds: string[]) => Promise<PayoutFileResponse>;
  getPayoutFileContentAndFooter: (merchants: MerchantInfo[]) => string;
  bankFileTransformer: (content: Row[]) => Row[];
}

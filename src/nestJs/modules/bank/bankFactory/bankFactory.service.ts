import { Injectable } from "@nestjs/common";
import { Row } from "read-excel-file/node";

import { DbsService } from "./modules/dbs/dbs.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { BankNames } from "../dto/bankName.dto";
import { PayoutFileResponse } from "../dto/payoutFileResponse.dto";

type FactoryServicesMap = {
  service: DbsService;
};

@Injectable()
export class BankFactoryService {
  readonly bankFactoryServices: Record<BankNames, FactoryServicesMap>;

  constructor(private readonly dbsService: DbsService) {
    this.bankFactoryServices = {
      [BankNames.DBS]: {
        service: this.dbsService,
      },
    };
  }

  /**
   * check factory
   * @param tx Tx
   * @returns FactoryServicesMap
   */
  private checkFactory(bankName: BankNames): FactoryServicesMap {
    if (!bankName) {
      throw errorBuilder.bank.factoryBankNameRequired();
    }

    const factory = this.bankFactoryServices[bankName];
    if (!factory || !factory.service) {
      throw errorBuilder.bank.factoryNotImplemented(bankName);
    }

    return factory;
  }

  /**
   * generatePayoutFileContent
   * @param bankName BankNames
   * @param txIds string[]
   * @returns Promise<PayoutFileResponse>
   */
  generatePayoutFileContent(bankName: BankNames, txIds: string[]): Promise<PayoutFileResponse> {
    const factory = this.checkFactory(bankName);

    return factory.service.generatePayoutFileContent(txIds);
  }

  /**
   * bankFileTransformer
   * @param bankName BankNames
   * @param content object[]
   * @returns Promise<PayoutBankFileRow[]>
   */
  bankFileTransformer(bankName: BankNames): (rows: Row[]) => Row[] {
    const factory = this.checkFactory(bankName);

    return factory.service.bankFileTransformer;
  }
}

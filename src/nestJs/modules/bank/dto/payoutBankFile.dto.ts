import { BankNames } from "./bankName.dto";

export interface PayoutBankFileRow {
  fileUploadDate: Date; // col 1
  paymentDate: Date; // col 2
  merchantId: string; // col 6
  originalBatchFileName: string; // col 8
  amount: number; // col 10
  status: BankResponseLineItemStatus; // col 28 - "Bank Rejected", "Received"
}

export enum BankResponseLineItemStatus {
  RECEIVED = "Received",
  COMPLETED = "Completed",
  REJECTED = "Rejected",
  BANKREJECTED = "Bank Rejected",
  CONFIRMED = "Confirmed",
}

export const bankFileSchemas = {
  [BankNames.DBS]: {
    "File Uploaded date": {
      prop: "fileUploadDate",
      type: Date,
    },
    "Payment Date": {
      prop: "paymentDate",
      type: Date,
    },
    "Customer Reference": {
      prop: "merchantId",
      type: String,
    },
    Filename: {
      prop: "originalBatchFileName",
      type: String,
    },
    Amount: {
      prop: "amount",
      type: Number,
    },
    Status: {
      prop: "status",
      type: String,
    },
  },
};

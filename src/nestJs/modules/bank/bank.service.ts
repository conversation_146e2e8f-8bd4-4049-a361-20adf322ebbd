import { Injectable } from "@nestjs/common";
import { Row } from "read-excel-file/node";

import { BankFactoryService } from "./bankFactory/bankFactory.service";
import { BankNames } from "./dto/bankName.dto";
import { PayoutFileResponse } from "./dto/payoutFileResponse.dto";

@Injectable()
export class BankService {
  constructor(private readonly bankFactoryService: BankFactoryService) {}

  generatePayoutFileContent(bankName: BankNames, txIds: string[]): Promise<PayoutFileResponse> {
    return this.bankFactoryService.generatePayoutFileContent(bankName, txIds);
  }

  bankFileTransformer(bankName: BankNames): (rows: Row[]) => Row[] {
    return this.bankFactoryService.bankFileTransformer(bankName);
  }
}

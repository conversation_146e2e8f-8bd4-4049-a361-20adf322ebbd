import { forwardRef, Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { UserController } from "./user.controller";
import { UserService } from "./user.service";
import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { CampaignModule } from "../campaign/campaign.module";
import { ProfileAuditRepository } from "../database/repositories/profileAudit.repository";
import { UserRepository } from "../database/repositories/user.repository";

/**
 * User module
 */
@Module({
  controllers: [UserController],
  imports: [ConfigModule, AppDatabaseModule, forwardRef(() => CampaignModule)],
  providers: [UserService, UserController, UserRepository, ProfileAuditRepository],
  exports: [UserService, UserController, UserRepository],
})
export class UserModule {}

import { <PERSON>, Inject } from "@nestjs/common";
import { Change } from "firebase-functions/v1";
import { DocumentSnapshot, FirestoreEvent } from "firebase-functions/v2/firestore";

import { UserService } from "./user.service";
import { UserDocument } from "../appDatabase/documents/user.document";
import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";
import { UtilsService } from "../utils/utils.service";

@Controller()
export class UserController {
  constructor(
    private userService: UserService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(UtilsService) private readonly utilsService: UtilsService,
  ) {}

  @CorrelationContext()
  async userProfileChangedInFirestore(
    event: FirestoreEvent<
      Change<DocumentSnapshot> | undefined,
      {
        userId: string;
      }
    >,
  ) {
    const { userId } = event.params;
    this.logger.info(`nest trigger: users/${userId} updated`, { userId });

    //camelize keys for user document after
    const dataAfter = event.data?.after.data();
    if (!dataAfter) {
      throw errorBuilder.user.updatedNoData(userId);
    }
    this.logger.info("current users/${userId} data", { dataAfter });
    dataAfter.date_of_birth = dataAfter.date_of_birth?.toDate();
    const userDocumentAfter = this.utilsService.case.camelizeKeys(dataAfter) as UserDocument;

    //camelize keys for user document before
    const dataBefore = event.data?.before.data();
    let userDocumentBefore: UserDocument | undefined;
    if (dataBefore) {
      dataBefore.date_of_birth = dataBefore.date_of_birth?.toDate();
      userDocumentBefore = this.utilsService.case.camelizeKeys(dataBefore) as UserDocument;
    }

    return this.userService.processUserProfileChanged(userId, userDocumentAfter, userDocumentBefore);
  }
}

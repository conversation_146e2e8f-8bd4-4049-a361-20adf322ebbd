import { MiddlewareConsumer, Module } from "@nestjs/common";
import { ScheduleModule } from "@nestjs/schedule";

import { MeterController } from "./meter.controller";
import { MeterService } from "./meter.service";
import { MeterDeviceAuthMiddleware } from "../../infrastructure/middlewares/meterDeviceAuth.middleware";
import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { PaymentTxRepository } from "../database/repositories/paymentTx.repository";
import { TxRepository } from "../database/repositories/tx.repository";
import { WebhookRepository } from "../database/repositories/webhook.repository";
import { PubSubModule } from "../pubsub/pubsub.module";
import { TransactionFactoryModule } from "../transaction/transactionFactory/transactionFactory.module";
import { WebhookModule } from "../webhook/webhook.module";

@Module({
  imports: [AppDatabaseModule, ScheduleModule.forRoot(), TransactionFactoryModule, PubSubModule, WebhookModule],
  providers: [MeterService, TxRepository, PaymentTxRepository, WebhookRepository],
  controllers: [MeterController],
  exports: [MeterService],
})
export class MeterModule {
  /**
   * Configure middleware
   * @param consumer MiddlewareConsumer
   */
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(MeterDeviceAuthMiddleware).forRoutes("/meters");
  }
}

import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { PaymentMethodSelected, paymentMethodSelectedSchema } from "../../payment/dto/paymentMethodSelected.dto";

export const meterTripRequestSchema = Joi.object({
  tip: Joi.number().positive().allow(0).required(),
  paymentMethodSelected: paymentMethodSelectedSchema,
});

export class MeterTripBodyDto {
  @ApiProperty()
  tip: number;
  @ApiProperty({ enum: PaymentMethodSelected })
  paymentMethodSelected: PaymentMethodSelected;
}

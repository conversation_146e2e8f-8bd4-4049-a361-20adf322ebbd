import { randomUUID } from "crypto";

import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Cache } from "cache-manager";
import { Timestamp } from "firebase-admin/firestore";
import { authenticator } from "otplib";

import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { MeterSecurityDocument, SecurityType } from "../appDatabase/documents/meterSecurity.document";
import { TripDocument } from "../appDatabase/documents/trip.document";
import { TxRepository } from "../database/repositories/tx.repository";
import { WebhookRepository } from "../database/repositories/webhook.repository";
import { PaymentMethodSelected } from "../payment/dto/paymentMethodSelected.dto";
import { PublishMessageForHeartbeatProcessing } from "../pubsub/dto/publishMessageForHeartbeatProcessing.dto";
import {
  PublishMessageForWebhookProcessing,
  WebhookEventType,
  WebhookMessage,
} from "../pubsub/dto/PublishMessageForWebhookProcessing.dto";
import { PubSubService } from "../pubsub/pubsub.service";
import { isTxTrip } from "../transaction/dto/tx.dto";
import { TransactionFactoryService } from "../transaction/transactionFactory/transactionFactory.service";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";
import { isGreaterOrEqualThan } from "../utils/utils/version.utils";
import { WebhookService } from "../webhook/webhook.service";

@Injectable()
export class MeterService {
  constructor(
    private readonly appDatabaseService: AppDatabaseService,
    @InjectRepository(TxRepository) private txRepository: TxRepository,
    private readonly transactionFactoryService: TransactionFactoryService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private pubsubService: PubSubService,
    private readonly webhookService: WebhookService,
    @Inject(WebhookRepository) private webhookRepository: WebhookRepository,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  findMeterTripById(meterId: string, tripId: string) {
    return this.appDatabaseService.meterTripRepository(meterId).findOneById(tripId);
  }

  async updateMeterTripById(
    meterId: string,
    tripId: string,
    tip: number,
    paymentMethodSelected: PaymentMethodSelected,
  ): Promise<TripDocument> {
    const trip = await this.findMeterTripById(meterId, tripId);
    if (!trip) throw errorBuilder.meter.tripNotFound(meterId, tripId);
    const updatedTrip = await this.appDatabaseService
      .meterTripRepository(meterId)
      .updateMeterTripTip(tripId, tip, paymentMethodSelected);
    if (!updatedTrip) throw errorBuilder.meter.tripUpdateFailed(meterId, tripId);

    return updatedTrip;
  }

  async lockTx(txId: string, userId: string) {
    const tx = await this.txRepository.findOneBy({ id: txId });

    if (!tx) {
      throw errorBuilder.transaction.notFound(txId);
    }

    if (!isTxTrip(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "MeterService/lockTx");
    }
    const tripDocument = tx.metadata;
    const tripInfoAppVersion = tripDocument.tripInfoAppVersion;
    if (tripInfoAppVersion && !isGreaterOrEqualThan(tripInfoAppVersion, "2.0")) {
      this.logger.info(`lockTx for txId: ${txId} skipped`, {
        tx: txId,
        data: { tripInfoAppVersion },
      });
      return;
    }
    return this.transactionFactoryService.lock(tx, userId, 150);
  }

  /**
   * Get secret by meterId
   * If the secret is not found, create one and query again to return
   * Note that, there is only one type of secret for now, which is TOTP
   * @param meterId
   * @param securityType
   * @returns secret
   */
  async getLatestSecurityByMeterIdAndType(meterId: string, securityType: SecurityType): Promise<MeterSecurityDocument> {
    this.logger.info(`getLatestSecurityByMeterIdAndType for meterId: ${meterId} with type: ${securityType}`);
    let meterSecurityDocument = await this.appDatabaseService
      .meterSecurityRepository(meterId)
      .findLatestSecurityByType(securityType);
    if (!meterSecurityDocument) {
      this.logger.info(
        `getLatestSecurityByMeterIdAndType for meterId: ${meterId} with type: ${securityType} not found`,
      );
      meterSecurityDocument = await this.appDatabaseService
        .meterSecurityRepository(meterId)
        .createAndGet(this.generateMeterSecurityTOTPDocument());
    }
    return meterSecurityDocument;
  }

  /**
   * Create a new secret for the meter
   */
  async createSecurityByMeterId(meterId: string, securityType: SecurityType): Promise<MeterSecurityDocument> {
    this.logger.info(`createSecurityByMeterId for meterId: ${meterId} with type: ${securityType}`);
    return this.appDatabaseService
      .meterSecurityRepository(meterId)
      .createAndGet(this.generateMeterSecurityTOTPDocument());
  }

  /**
   * Helper function to generate a meter security document with type TOTP
   */
  generateMeterSecurityTOTPDocument(): MeterSecurityDocument {
    const secret = authenticator.generateSecret();
    return new MeterSecurityDocument(SecurityType.TOTP, secret, new Date());
  }

  async processMeterHeartbeat(heartbeat: PublishMessageForHeartbeatProcessing) {
    const documentPathSegments = heartbeat.document.split("/");
    const meterId = documentPathSegments[1];
    const heartbeatId = documentPathSegments[3];
    const heartbeatData = heartbeat.after;
    const cacheTtlMs = 60000;

    if (heartbeatData.location._latitude === 0 && heartbeatData.location._longitude === 0) {
      this.logger.info(`Skipping meter heartbeat with zero location for meter ${meterId}`);
      return;
    }

    const fleetMerchantId = await this.cacheManager.wrap<string | null>(
      `merchant:meter:${meterId}`,
      async () => {
        const meter = await this.appDatabaseService.meterRepository().findOneById(meterId);
        if (!meter || !meter.settings.fleetId) return null;

        const fleet = await this.appDatabaseService.fleetRepository().findOneById(meter.settings.fleetId);
        if (!fleet || !fleet.merchantId) return null;
        return fleet.merchantId;
      },
      cacheTtlMs,
    );

    if (!fleetMerchantId) return;

    const webhookIds = await this.cacheManager.wrap<string[]>(
      this.webhookService.getCacheKeyForEvent(fleetMerchantId, WebhookEventType.METER_HEARTBEAT),
      async () => {
        this.logger.debug(`Fetching webhooks for merchant ${fleetMerchantId}`);

        const webhooks = await this.webhookRepository
          .createQueryBuilder("webhook")
          .innerJoinAndSelect("webhook.merchant", "merchant")
          .innerJoinAndSelect("webhook.events", "events")
          .where("merchant.id = :merchantId", { merchantId: fleetMerchantId })
          .andWhere("webhook.deletedAt IS NULL")
          .andWhere("events.type = :eventType", { eventType: WebhookEventType.METER_HEARTBEAT })
          .getMany();

        if (!webhooks || webhooks.length === 0) {
          return [];
        } else {
          return webhooks.map((webhook) => webhook.id);
        }
      },
      cacheTtlMs,
    );

    if (webhookIds.length === 0) {
      return;
    }

    const webhookMessage: WebhookMessage = {
      id: randomUUID(),
      event: WebhookEventType.METER_HEARTBEAT,
      data: {
        createdAt: new Timestamp(heartbeatData.server_time._seconds, heartbeatData.server_time._nanoseconds)
          .toDate()
          .toISOString(),
        heartbeatId: heartbeatId,
        licensePlate: meterId,
        location: {
          lat: heartbeatData.location._latitude,
          lng: heartbeatData.location._longitude,
        },
        bearing: heartbeatData.bearing,
        speed: heartbeatData.speed,
      },
    };

    const webhookPubsubMessages = webhookIds.map<PublishMessageForWebhookProcessing>((webhookId) => ({
      webhookId: webhookId,
      message: webhookMessage,
    }));

    await Promise.all(
      webhookPubsubMessages.map((webhookPubsubMessage) =>
        this.pubsubService.publishMessageForWebhookMessageProcessing(webhookPubsubMessage),
      ),
    );
  }
}

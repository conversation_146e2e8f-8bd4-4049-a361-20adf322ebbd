import { CallHand<PERSON>, ExecutionContext, Injectable, NestInterceptor } from "@nestjs/common";
import { SchedulerRegistry } from "@nestjs/schedule";
import { Observable, TimeoutError, catchError, timeout } from "rxjs";

import { errorBuilder } from "../../utils/utils/error.utils";

@Injectable()
export class UpdateTimeoutInterceptor implements NestInterceptor {
  constructor(private readonly schedulerRegistry: SchedulerRegistry) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      timeout(10000),
      catchError((err) => {
        if (err instanceof TimeoutError) {
          this.schedulerRegistry.deleteInterval(
            `checkIsTipsCalculatedInterval_${context.getArgs()[0].params.meterId}_${
              context.getArgs()[0].params.tripId
            }`,
          );
          throw errorBuilder.meter.tripCheckTimeOut(
            context.getArgs()[0].params.meterId,
            context.getArgs()[0].params.tripId,
          );
        }
        throw err;
      }),
    );
  }
}

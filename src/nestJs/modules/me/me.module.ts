import { MiddlewareConsumer, Module } from "@nestjs/common";
import { RouterModule } from "nest-router";

import { MeCampaignModule } from "./modules/meCampaign/meCampaign.module";
import { MeFleetTaxiModule } from "./modules/meFleetTaxi/meFleetTaxi.module";
import { MeHailingModule } from "./modules/meHailing/meHailing.module";
import { MeInitializeModule } from "./modules/meInitialize/meInitialize.module";
import { MeLocationModule } from "./modules/meLocation/meLocation.module";
import { MeLogoutModule } from "./modules/meLogout/meLogout.module";
import { MeNotificationTokenModule } from "./modules/meNotificationToken/meNotificationToken.module";
import { MePaymentInstrumentModule } from "./modules/mePaymentInstrument/mePaymentInstrument.module";
import { MePinModule } from "./modules/mePin/mePin.module";
import { MeQrCodeModule } from "./modules/meQrCode/meQrCode.module";
import { MeReferralModule } from "./modules/meReferral/meReferral.module";
import { MeTransactionModule } from "./modules/meTransaction/meTransaction.module";
import { routes } from "./routes";
import { UserAppAuthMiddleware } from "../../infrastructure/middlewares/userAppAuth.middleware";

/**
 * Me module
 */
@Module({
  imports: [
    MePaymentInstrumentModule,
    MePinModule,
    MeTransactionModule,
    MeQrCodeModule,
    MeNotificationTokenModule,
    MeInitializeModule,
    MeLocationModule,
    MeHailingModule,
    MeReferralModule,
    MeLogoutModule,
    MeCampaignModule,
    RouterModule.forRoutes(routes),
    MeFleetTaxiModule,
  ],
})
export class MeModule {
  /**
   * Configure middleware
   * @param consumer MiddlewareConsumer
   */
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(UserAppAuthMiddleware).forRoutes("me");
  }
}

import { constants, privateDecrypt } from "crypto";

import { Injectable, NestMiddleware } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { hash } from "bcrypt";
import { Request, Response, NextFunction } from "express";

import { ProfileAuditActionType, ProfileAuditSourceType } from "../../audit/profileAudit/dto/profileAudit.dto";
import { VerifyPinApiMetadata } from "../../audit/profileAudit/dto/profileAuditMetadata.dto";
import { ProfileAuditRepository } from "../../database/repositories/profileAudit.repository";
import { UserRepository } from "../../database/repositories/user.repository";
import { errorBuilder } from "../../utils/utils/error.utils";

@Injectable()
export class VerifyPinMiddleware implements NestMiddleware {
  constructor(
    @InjectRepository(UserRepository) private userRepository: UserRepository,
    @InjectRepository(ProfileAuditRepository) private profileAuditRepository: ProfileAuditRepository,
  ) {}
  async use(req: Request, res: Response, next: NextFunction) {
    const pin = (req.query.pin ?? req.body.pin) as string;
    if (!pin) {
      throw errorBuilder.user.pin.verifyFailedWithPinNotFoundInRequest();
    }

    const userInReq = req.user;
    if (!userInReq) {
      throw errorBuilder.user.missing();
    }
    const appDatabaseId = userInReq.user_id;
    const user = await this.userRepository.findAppUserById(appDatabaseId);
    if (!user.hashedPin) {
      throw errorBuilder.user.pin.verifyPinFailedWithoutExistingPin(appDatabaseId);
    }
    if (user.pinErrorCount >= 5) {
      throw errorBuilder.user.pin.userBlockedForExceedingPinErrorCount(appDatabaseId);
    }
    const encryptedHashedPinBuffer = Buffer.from(pin, "base64");
    const decryptedHashedPin = privateDecrypt(
      {
        key: user.privateKey,
        padding: constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: "sha1",
      },
      encryptedHashedPinBuffer,
    );
    const hashedPinWithSalt = await hash(decryptedHashedPin, user.salt);
    const verified = hashedPinWithSalt === user.hashedPin;

    const metadate = new VerifyPinApiMetadata();
    metadate.pinToVerify = hashedPinWithSalt;
    metadate.pinInSql = user.hashedPin;
    metadate.verified = verified;

    await this.userRepository.manager.transaction(async () => {
      if (verified) {
        user.pinErrorCount = 0;
      } else {
        user.pinErrorCount++;
      }
      await this.userRepository.save(user);
      await this.profileAuditRepository.createProfileAudit(
        ProfileAuditActionType.ME_API_VERIFY_PIN,
        user,
        metadate,
        ProfileAuditSourceType.ME_API,
      );
    });
    req.verifyPinResult = { verified: verified, user: user };
    next();
  }
}

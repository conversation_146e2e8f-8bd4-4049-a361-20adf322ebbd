import { MeModule } from "./me.module";
import { MeCampaignModule } from "./modules/meCampaign/meCampaign.module";
import { MeFleetTaxiModule } from "./modules/meFleetTaxi/meFleetTaxi.module";
import { MeHailingModule } from "./modules/meHailing/meHailing.module";
import { MeInitializeModule } from "./modules/meInitialize/meInitialize.module";
import { MeLocationModule } from "./modules/meLocation/meLocation.module";
import { MeLogoutModule } from "./modules/meLogout/meLogout.module";
import { MePaymentInstrumentModule } from "./modules/mePaymentInstrument/mePaymentInstrument.module";
import { MePinModule } from "./modules/mePin/mePin.module";
import { MeQrCodeModule } from "./modules/meQrCode/meQrCode.module";
import { MeReferralModule } from "./modules/meReferral/meReferral.module";
import { MeTransactionModule } from "./modules/meTransaction/meTransaction.module";

export const routes = [
  {
    path: "/me",
    module: MeModule,
    children: [
      {
        path: "/payment-instruments",
        module: MePaymentInstrumentModule,
      },
      { path: "/hailing", module: MeHailingModule },
      { path: "/pin", module: MePinModule },
      { path: "/transactions", module: MeTransactionModule },
      { path: "/qrcode", module: MeQrCodeModule },
      { path: "/initialize", module: MeInitializeModule },
      { path: "/locations", module: MeLocationModule },
      { path: "/referral", module: MeReferralModule },
      { path: "/campaigns", module: MeCampaignModule },
      { path: "/logout", module: MeLogoutModule },
      { path: "/fleet-taxi", module: MeFleetTaxiModule },
    ],
  },
];

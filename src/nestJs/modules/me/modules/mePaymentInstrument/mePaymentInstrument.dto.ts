import { ApiProperty } from "@nestjs/swagger";
import Jo<PERSON> from "joi";

import PaymentInstrument from "@nest/modules/database/entities/paymentInstrument.entity";

import {
  PaymentInstrumentType,
  paymentInstrumentTypeSchema,
} from "../../../payment/modules/paymentInstrument/dto/paymentInstrument.dto";

export class CreatePaymentInstrumentBody {
  @ApiProperty({ example: "****************" })
  cardNumber: string;

  @ApiProperty({ example: "2031" })
  expirationYear: string;

  @ApiProperty({ example: "01" })
  expirationMonth: string;

  @ApiProperty({ example: "John Doe" })
  cardHolderName: string;

  @ApiProperty({ example: "777" })
  securityCode: string;

  @ApiProperty({ example: PaymentInstrumentType.VISA })
  cardType: PaymentInstrumentType;
}

export const createPaymentInstrumentBodySchema = Joi.object({
  cardNumber: Joi.string().required(),
  expirationYear: Joi.string().required(),
  expirationMonth: Joi.string().required(),
  cardHolderName: Joi.string().required(),
  securityCode: Joi.string().required(),
  cardType: paymentInstrumentTypeSchema.required(),
});

export class InitiatePaymentInstrumentVerifyParams {
  @ApiProperty({ example: "5a21d2ba-25ad-44ed-82b6-e941ba628356" })
  paymentInstrumentId: string;
}

export const initiatePaymentInstrumentVerifyParamsSchema = Joi.object({
  paymentInstrumentId: Joi.string().required(),
});

export class ProcessPaymentInstrumentVerifyParams {
  @ApiProperty({ example: "5a21d2ba-25ad-44ed-82b6-e941ba628356" })
  paymentInstrumentId: string;

  @ApiProperty({ example: "2cb85048-a586-44b3-b3de-31f5b47ac584" })
  sessionId: string;
}

export const processPaymentInstrumentVerifyParamsSchema = Joi.object({
  paymentInstrumentId: Joi.string().required(),
  sessionId: Joi.string().required(),
});

export class ProcessPaymentInstrumentVerifyBody {
  @ApiProperty({ example: "12.345.678.90" })
  ipAddress: string;

  @ApiProperty({ example: "850" })
  httpBrowserScreenHeight: string;

  @ApiProperty({ example: "500" })
  httpBrowserScreenWidth: string;
}

export const processPaymentInstrumentVerifyBodySchema = Joi.object({
  ipAddress: Joi.string().required(),
  httpBrowserScreenHeight: Joi.string().required(),
  httpBrowserScreenWidth: Joi.string().required(),
});

export class GetPaymentInstrumentParams {
  @ApiProperty({ example: "5a21d2ba-25ad-44ed-82b6-e941ba628356" })
  paymentInstrumentId: string;
}

export const getPaymentInstrumentParamsSchema = Joi.object({
  paymentInstrumentId: Joi.string().required(),
});

export class CheckPayerEnrolmentParams {
  @ApiProperty({ example: "5a21d2ba-25ad-44ed-82b6-e941ba628356" })
  paymentInstrumentId: string;
}

export const checkPayerEnrolmentParamsSchema = Joi.object({
  paymentInstrumentId: Joi.string().required(),
});

export class SetPreferredPaymentInstrumentParams {
  @ApiProperty({ example: "5a21d2ba-25ad-44ed-82b6-e941ba628356" })
  paymentInstrumentId: string;
}

export const setPreferredPaymentInstrumentParamsSchema = Joi.object({
  paymentInstrumentId: Joi.string().required(),
});

export class DeletePaymentInstrumentParams {
  @ApiProperty({ example: "5a21d2ba-25ad-44ed-82b6-e941ba628356" })
  paymentInstrumentId: string;
}

export const deletePaymentInstrumentParamsSchema = Joi.object({
  paymentInstrumentId: Joi.string().required(),
});

export class UpdatePaymentInstrumentParams {
  @ApiProperty({ example: "5a21d2ba-25ad-44ed-82b6-e941ba628356" })
  paymentInstrumentId: string;
}

export const updatePaymentInstrumentParamsSchema = Joi.object({
  paymentInstrumentId: Joi.string().required(),
});

export class UpdatePaymentInstrumentBody {
  @ApiProperty({ example: "2031" })
  expirationYear: string;

  @ApiProperty({ example: "01" })
  expirationMonth: string;

  @ApiProperty({ example: "John Doe" })
  cardHolderName: string;

  @ApiProperty({ example: "777" })
  securityCode: string;
}

export const updatePaymentInstrumentBodySchema = Joi.object({
  expirationYear: Joi.string().required(),
  expirationMonth: Joi.string().required(),
  cardHolderName: Joi.string().required(),
  securityCode: Joi.string().required(),
});

export type CreatePaymentInstrumentResponse = {
  paymentInstrument: Partial<PaymentInstrument>;
  webviewVerificationUrl?: string;
};

import { Body, Controller, Delete, Get, Param, Patch, Post, Req } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { Request } from "express";

import {
  CheckPayerEnrolmentParams,
  CreatePaymentInstrumentBody,
  CreatePaymentInstrumentResponse,
  DeletePaymentInstrumentParams,
  GetPaymentInstrumentParams,
  InitiatePaymentInstrumentVerifyParams,
  ProcessPaymentInstrumentVerifyBody,
  ProcessPaymentInstrumentVerifyParams,
  SetPreferredPaymentInstrumentParams,
  UpdatePaymentInstrumentBody,
  UpdatePaymentInstrumentParams,
  checkPayerEnrolmentParamsSchema,
  createPaymentInstrumentBodySchema,
  deletePaymentInstrumentParamsSchema,
  getPaymentInstrumentParamsSchema,
  initiatePaymentInstrumentVerifyParamsSchema,
  processPaymentInstrumentVerifyBodySchema,
  processPaymentInstrumentVerifyParamsSchema,
  setPreferredPaymentInstrumentParamsSchema,
  updatePaymentInstrumentBodySchema,
  updatePaymentInstrumentParamsSchema,
} from "./mePaymentInstrument.dto";
import { MePaymentInstrumentService } from "./mePaymentInstrument.service";
import PaymentInstrument from "../../../database/entities/paymentInstrument.entity";
import {
  Initiate3DSecureResponse,
  CreatePaymentResponse,
} from "../../../payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { errorBuilder } from "../../../utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "../../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../../validation/validationPipe.service";

/**
 * MePaymentInstrument controller
 */
@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.me)
export class MePaymentInstrumentController {
  constructor(private readonly mePaymentInstrumentService: MePaymentInstrumentService) {}

  /**
   * Create a payment instrument
   * @param body createPaymentInstrumentBody
   * @param req Request
   */
  @Post("")
  @ApiOperation({
    summary: "create a payment instrument",
  })
  @ApiResponse({
    status: 201,
    description: "created payment instrument (Challenge 4456530000001096. Frictionless 4456530000001005)",
  })
  async createPaymentInstrument(
    @Body(new JoiValidationPipe(createPaymentInstrumentBodySchema)) body: CreatePaymentInstrumentBody,
    @Req() req: Request,
  ): Promise<CreatePaymentInstrumentResponse> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.mePaymentInstrumentService.createPaymentInstrument(body, user.user_id);
  }

  /**
   * get Payment Instrument by id
   * @param params GetPaymentInstrumentParams
   * @param req Request
   */
  @Get("/:paymentInstrumentId")
  @ApiOperation({
    summary: "Get payment instrument by id",
  })
  @ApiResponse({ status: 201, description: "Get payment instrument by id" })
  async getPaymentInstrument(
    @Param(new JoiValidationPipe(getPaymentInstrumentParamsSchema))
    params: GetPaymentInstrumentParams,
    @Req() req: Request,
  ): Promise<Partial<PaymentInstrument>> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.mePaymentInstrumentService.getPaymentInstrument(params, user.user_id);
  }

  /**
   * Initiate payment instrument verify through 3D Secure
   * @param params InitiatePaymentBody
   * @param req Request
   */
  @Post("/:paymentInstrumentId/3d-secure")
  @ApiOperation({
    summary: "Initiate payment instrument verify through 3D Secure",
  })
  @ApiResponse({ status: 201, description: "Initiate payment instrument verify through 3D Secure" })
  async initiate3DSecure(
    @Param(new JoiValidationPipe(initiatePaymentInstrumentVerifyParamsSchema))
    params: InitiatePaymentInstrumentVerifyParams,
    @Req() req: Request,
  ): Promise<Initiate3DSecureResponse> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.mePaymentInstrumentService.initiate3DSecure(params, user.user_id);
  }

  /**
   * Process payment instrument verify through 3D Secure
   * @param params InitiatePaymentBody
   * @param req Request
   */
  @Post("/:paymentInstrumentId/3d-secure/session/:sessionId")
  @ApiOperation({
    summary: "Process payment instrument verify through 3D Secure",
  })
  @ApiResponse({ status: 201, description: "Process payment instrument verify through 3D Secure" })
  async process3DSecure(
    @Param(new JoiValidationPipe(processPaymentInstrumentVerifyParamsSchema))
    params: ProcessPaymentInstrumentVerifyParams,
    @Body(new JoiValidationPipe(processPaymentInstrumentVerifyBodySchema))
    body: ProcessPaymentInstrumentVerifyBody,
    @Req() req: Request,
  ): Promise<CreatePaymentResponse> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.mePaymentInstrumentService.createPayment(params, body, user.user_id);
  }

  /**
   * Check payer enrolment to 3D Secure
   * @param params CheckPayerEnrolmentParams
   * @param req Request
   */
  @Get("/:paymentInstrumentId/3d-secure/check-enrolment")
  @ApiOperation({
    summary: "Check payer enrolment to 3D Secure",
  })
  @ApiResponse({ status: 201, description: "Check payer enrolment to 3D Secure" })
  async checkPayerAuthEnrollment(
    @Param(new JoiValidationPipe(checkPayerEnrolmentParamsSchema))
    params: CheckPayerEnrolmentParams,
    @Req() req: Request,
  ): Promise<PaymentInstrument> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.mePaymentInstrumentService.checkPayerAuthEnrollment(params, user.user_id);
  }

  /**
   * Set Preferred Payment Instrument
   * @param params CheckPayerEnrolmentParams
   * @param req Request
   * @returns PaymentInstrument
   */
  @Patch("/:paymentInstrumentId/preferred")
  @ApiOperation({
    summary: "Set Preferred Payment Instrument",
  })
  @ApiResponse({ status: 201, description: "Set Preferred Payment Instrument" })
  async setPreferredPaymentInstrument(
    @Param(new JoiValidationPipe(setPreferredPaymentInstrumentParamsSchema))
    params: SetPreferredPaymentInstrumentParams,
    @Req() req: Request,
  ): Promise<Partial<PaymentInstrument>> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }

    return this.mePaymentInstrumentService.setPreferredPaymentInstrument(params, user.user_id);
  }

  /**
   * Delete payment instrument
   * @param params DeletePaymentInstrumentParams
   * @param req Request
   */
  @Delete("/:paymentInstrumentId")
  @ApiOperation({
    summary: "Delete payment instrument",
  })
  @ApiResponse({ status: 200, description: "Delete payment instrument" })
  @ApiBadRequestResponse({ description: buildErrorHtml([errorBuilder.user.missing()]) })
  @ApiNotFoundResponse({
    description: buildErrorHtml([
      errorBuilder.user.notFoundInSql("IkCO7YHOJ3c073P8RihpRZxX3yC2"),
      errorBuilder.payment.instrument.notFound("5a21d2ba-25ad-44ed-82b6-e941ba628356"),
    ]),
  })
  @ApiInternalServerErrorResponse({
    description: buildErrorHtml([
      errorBuilder.payment.instrument.couldNotDelete("5a21d2ba-25ad-44ed-82b6-e941ba628356", new Error("Some Error")),
    ]),
  })
  async delete(
    @Param(new JoiValidationPipe(deletePaymentInstrumentParamsSchema))
    params: DeletePaymentInstrumentParams,
    @Req() req: Request,
  ): Promise<Partial<PaymentInstrument>> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.mePaymentInstrumentService.delete(params, user.user_id);
  }

  /**
   * update payment instrument
   * @param params CheckPayerEnrolmentParams
   * @param req Request
   */
  @Patch("/:paymentInstrumentId")
  @ApiOperation({
    summary: "Update payment instrument",
  })
  @ApiResponse({ status: 200, description: "Update payment instrument" })
  @ApiBadRequestResponse({ description: buildErrorHtml([errorBuilder.user.missing()]) })
  @ApiNotFoundResponse({
    description: buildErrorHtml([
      errorBuilder.user.notFoundInSql("IkCO7YHOJ3c073P8RihpRZxX3yC2"),
      errorBuilder.payment.instrument.notFound("5a21d2ba-25ad-44ed-82b6-e941ba628356"),
    ]),
  })
  @ApiInternalServerErrorResponse({
    description: buildErrorHtml([
      errorBuilder.payment.instrument.couldNotSave("5a21d2ba-25ad-44ed-82b6-e941ba628356", new Error("Some Error")),
    ]),
  })
  async update(
    @Param(new JoiValidationPipe(updatePaymentInstrumentParamsSchema)) params: UpdatePaymentInstrumentParams,
    @Body(new JoiValidationPipe(updatePaymentInstrumentBodySchema)) body: UpdatePaymentInstrumentBody,
    @Req() req: Request,
  ): Promise<Partial<PaymentInstrument>> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.mePaymentInstrumentService.update(params, body, user.user_id);
  }
}

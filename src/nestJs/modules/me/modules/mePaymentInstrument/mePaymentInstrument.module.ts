import { Module } from "@nestjs/common";

import { MePaymentInstrumentController } from "./mePaymentInstrument.controller";
import { MePaymentInstrumentService } from "./mePaymentInstrument.service";
import { PaymentInstrumentModule } from "../../../payment/modules/paymentInstrument/paymentInstrument.module";

/**
 * MePaymentInstrument module
 */
@Module({
  providers: [MePaymentInstrumentService, MePaymentInstrumentController],
  imports: [PaymentInstrumentModule],
  controllers: [MePaymentInstrumentController],
  exports: [MePaymentInstrumentService, MePaymentInstrumentController],
})
export class MePaymentInstrumentModule {}

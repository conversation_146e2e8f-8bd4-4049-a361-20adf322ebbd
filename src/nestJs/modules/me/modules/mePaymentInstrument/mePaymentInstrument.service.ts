import { Injectable } from "@nestjs/common";

import {
  CreatePaymentInstrumentBody,
  InitiatePaymentInstrumentVerifyParams,
  GetPaymentInstrumentParams,
  ProcessPaymentInstrumentVerifyParams,
  ProcessPaymentInstrumentVerifyBody,
  CheckPayerEnrolmentParams,
  SetPreferredPaymentInstrumentParams,
  DeletePaymentInstrumentParams,
  UpdatePaymentInstrumentParams,
  UpdatePaymentInstrumentBody,
  CreatePaymentInstrumentResponse,
} from "./mePaymentInstrument.dto";
import PaymentInstrument from "../../../database/entities/paymentInstrument.entity";
import { PaymentGatewayTypes } from "../../../payment/dto/paymentGatewayTypes.dto";
import {
  Initiate3DSecureResponse,
  CreatePaymentResponse,
} from "../../../payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { PaymentInstrumentService } from "../../../payment/modules/paymentInstrument/paymentInstrument.service";

/**
 * MePaymentInstrument service
 */
@Injectable()
export class MePaymentInstrumentService {
  constructor(private readonly paymentInstrumentService: PaymentInstrumentService) {}

  /**
   * Create payment instrument
   * @param body CreatePaymentInstrumentBody
   * @param userId string
   */
  async createPaymentInstrument(
    body: CreatePaymentInstrumentBody,
    userId: string,
  ): Promise<CreatePaymentInstrumentResponse> {
    return this.paymentInstrumentService.createPaymentInstrument(body, userId, PaymentGatewayTypes.KRAKEN);
  }

  /**
   * Initiate payment instument verify through 3D Secure
   * @param params InitiatePaymentInstrumentVerifyParams
   * @param userId string
   */
  async initiate3DSecure(
    params: InitiatePaymentInstrumentVerifyParams,
    userId: string,
  ): Promise<Initiate3DSecureResponse> {
    return this.paymentInstrumentService.initiate3DSecure(params, userId);
  }

  /**
   * Get Payment Instrument by id
   * @param params GetPaymentInstrumentParams
   * @param userId string
   */
  async getPaymentInstrument(params: GetPaymentInstrumentParams, userId: string): Promise<Partial<PaymentInstrument>> {
    return this.paymentInstrumentService.getPaymentInstrument(params, userId);
  }

  /**
   * Process payment instument verify through 3D Secure
   * @param params ProcessPaymentInstrumentVerifyParams
   * @param userId string
   */
  async createPayment(
    params: ProcessPaymentInstrumentVerifyParams,
    body: ProcessPaymentInstrumentVerifyBody,
    userId: string,
  ): Promise<CreatePaymentResponse> {
    return this.paymentInstrumentService.createPayment(params, body, userId);
  }

  /**
   * Check payer enrolment to 3D Secure
   * @param params CheckPayerEnrolmentParams
   * @param userId string
   */
  async checkPayerAuthEnrollment(params: CheckPayerEnrolmentParams, userId: string): Promise<PaymentInstrument> {
    return this.paymentInstrumentService.checkPayerAuthEnrollment(params, userId);
  }

  /**
   * Set Preferred Payment Instrument
   * @param params SetPreferredPaymentInstrumentParams
   * @param userId string
   * @returns PaymentInstrument
   */
  async setPreferredPaymentInstrument(
    params: SetPreferredPaymentInstrumentParams,
    userId: string,
  ): Promise<Partial<PaymentInstrument>> {
    return this.paymentInstrumentService.setPreferredPaymentInstrument(params, userId);
  }

  /**
   * Delete payment instrument
   * @param params DeletePaymentInstrumentParams
   * @param userId string
   */
  async delete(params: DeletePaymentInstrumentParams, userId: string): Promise<Partial<PaymentInstrument>> {
    return this.paymentInstrumentService.delete(params, userId);
  }

  /**
   * Update payment instrument
   * @param params CheckPayerEnrolmentParams
   * @param userId string
   */
  async update(
    params: UpdatePaymentInstrumentParams,
    paymentInstrumentData: UpdatePaymentInstrumentBody,
    userId: string,
  ): Promise<Partial<PaymentInstrument>> {
    return this.paymentInstrumentService.update(params, paymentInstrumentData, userId);
  }
}

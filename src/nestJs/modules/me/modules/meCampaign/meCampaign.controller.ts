import { Controller, Req, Post, Body, Get } from "@nestjs/common";
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse, ApiBadRequestResponse } from "@nestjs/swagger";
import { Request } from "express";

import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";

import {
  CampaignClaimBody,
  campaignClaimBodySchema,
  CampaignClaimResponse,
  CampaignGetIssuedResponse,
  CampaignQueryApplicableBody,
  campaignQueryApplicableBodySchema,
  CampaignQueryApplicableResponse,
} from "./dto/meCampaign.dto";
import { MeCampaignService } from "./meCampaign.service";
import { apiTags, buildErrorHtml } from "../../../utils/utils/swagger.utils";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.me)
export class MeCampaignController {
  constructor(private readonly meCampaignService: MeCampaignService) {}

  /**
   * Query for applicable campaign
   */
  @Post("query-applicable")
  @ApiOperation({
    summary: "Query for applicable campaigns",
  })
  @ApiResponse({ status: 200, description: "Query for applicable campaigns" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async queryApplicable(
    @Body(new JoiValidationPipe(campaignQueryApplicableBodySchema)) body: CampaignQueryApplicableBody,
    @Req() req: Request,
  ): Promise<CampaignQueryApplicableResponse> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }

    return this.meCampaignService.queryApplicable(body, user);
  }

  @Get("list")
  @ApiOperation({
    summary: "Get list of campaigns with discount issued",
  })
  @ApiResponse({ status: 200, description: "Get list of campaigns with discount issued" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async getCampaignsList(@Req() req: Request): Promise<CampaignGetIssuedResponse> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }

    return this.meCampaignService.getCampaignsList(user);
  }

  @Post("claim")
  @ApiOperation({
    summary: "Claim discount using reward code",
  })
  @ApiResponse({ status: 201, description: "Claim discount using reward code", type: CampaignClaimResponse })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.incentive.discount.rewardCodeNotFound("abc123"),
      errorBuilder.incentive.discount.rewardCodeAlreadyClaimed("abc123"),
      errorBuilder.incentive.discount.rewardCodeExpired("abc123"),
      errorBuilder.incentive.discount.rewardCodeLimitExceeded("abc123"),
    ]),
  })
  async claimDiscount(
    @Body(new JoiValidationPipe(campaignClaimBodySchema)) body: CampaignClaimBody,
    @Req() req: Request,
  ): Promise<CampaignClaimResponse> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }

    return await this.meCampaignService.claim(body.rewardCode, user);
  }
}

import { Modu<PERSON> } from "@nestjs/common";

import { CampaignModule } from "@nest/modules/campaign/campaign.module";
import { UserModule } from "@nest/modules/user/user.module";

import { MeCampaignController } from "./meCampaign.controller";
import { MeCampaignService } from "./meCampaign.service";

@Module({
  providers: [MeCampaignController, MeCampaignService],
  imports: [CampaignModule, UserModule],
  controllers: [MeCampaignController],
  exports: [MeCampaignController, MeCampaignService],
})
export class MeCampaignModule {}

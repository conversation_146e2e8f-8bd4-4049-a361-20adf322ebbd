import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { MeInitializeController } from "./meInitialize.controller";
import { MeInitializeService } from "./meInitialize.service";
import { UserRepository } from "../../../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../../../database/repositories/userNotificationToken.repository";
import { UserModule } from "../../../user/user.module";
import { MeNotificationTokenModule } from "../meNotificationToken/meNotificationToken.module";

@Module({
  providers: [MeInitializeController, MeInitializeService, UserRepository, UserNotificationTokenRepository],
  imports: [MeNotificationTokenModule, UserModule],
  controllers: [MeInitializeController],
  exports: [MeInitializeController, MeInitializeService],
})
export class MeInitializeModule {}

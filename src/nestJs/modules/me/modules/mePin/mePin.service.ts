import { constants, privateDecrypt } from "crypto";

import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { hash } from "bcrypt";

import { CampaignEventTriggers } from "@nest/modules/campaign/dto/campaign.dto";
import { PubSubService } from "@nest/modules/pubsub/pubsub.service";

import { ProfileAuditActionType, ProfileAuditSourceType } from "../../../audit/profileAudit/dto/profileAudit.dto";
import { SetPinApiMetadata, UpdatePinApiMetadata } from "../../../audit/profileAudit/dto/profileAuditMetadata.dto";
import { ProfileAuditRepository } from "../../../database/repositories/profileAudit.repository";
import { UserRepository } from "../../../database/repositories/user.repository";
import { AppUser } from "../../../user/dto/user.dto";
import { errorBuilder } from "../../../utils/utils/error.utils";

@Injectable()
export class MePinService {
  constructor(
    @InjectRepository(UserRepository) private userRepository: UserRepository,
    @InjectRepository(ProfileAuditRepository) private profileAuditRepository: ProfileAuditRepository,
    private pubSubService: PubSubService,
  ) {}

  /**
   * Create user pin
   * @param txId string
   * @param appDatabaseId string
   */
  async createUserPin(pin: string, appDatabaseId: string): Promise<void> {
    const user = await this.userRepository.findAppUserById(appDatabaseId);
    if (user.hashedPin) {
      throw errorBuilder.user.pin.createPinFailedWithExistingPin(appDatabaseId);
    }
    const encryptedHashedPinHashedPin = Buffer.from(pin, "base64");
    const decryptedHashedPin = privateDecrypt(
      {
        key: user.privateKey,
        padding: constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: "sha1",
      },
      encryptedHashedPinHashedPin,
    );
    const hashedPinWithSalt = await hash(decryptedHashedPin, user.salt);
    user.hashedPin = hashedPinWithSalt;
    user.pinErrorCount = 0;
    const savedUser = await this.userRepository.save(user);
    if (savedUser) {
      const metadate = new SetPinApiMetadata();
      metadate.newPin = hashedPinWithSalt;
      await this.profileAuditRepository.createProfileAudit(
        ProfileAuditActionType.ME_API_SET_PIN,
        savedUser,
        metadate,
        ProfileAuditSourceType.ME_API,
      );

      // Discount handling
      this.pubSubService.publishMessageForCampaignTriggerProcessing({
        eventTrigger: CampaignEventTriggers.USER_SIGNED_UP,
        userId: savedUser.id,
        generateToLimit: true,
      });

      if (savedUser.referrerId) {
        this.pubSubService.publishMessageForCampaignTriggerProcessing({
          eventTrigger: CampaignEventTriggers.USER_SIGNED_UP_REFEREE,
          userId: savedUser.id,
        });
      }
    }
    return undefined;
  }

  /**
   * Update user pin
   * @param user User
   * @param newPin string
   */
  async updateUserPin(user: AppUser, newPin: string): Promise<void> {
    const encryptedHashedPinBuffer = Buffer.from(newPin, "base64");
    const decryptedHashedPin = privateDecrypt(
      {
        key: user.privateKey,
        padding: constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: "sha1",
      },
      encryptedHashedPinBuffer,
    );
    const newHashedPinWithSalt = await hash(decryptedHashedPin, user.salt);

    const metadate = new UpdatePinApiMetadata();
    metadate.oldPin = user.hashedPin as string;
    metadate.newPin = newHashedPinWithSalt;

    user.hashedPin = newHashedPinWithSalt;
    user.pinErrorCount = 0;
    await this.userRepository.manager.transaction(async () => {
      await this.userRepository.save(user);
      await this.profileAuditRepository.createProfileAudit(
        ProfileAuditActionType.ME_API_UPDATE_PIN,
        user,
        metadate,
        ProfileAuditSourceType.ME_API,
      );
    });
    return undefined;
  }
}

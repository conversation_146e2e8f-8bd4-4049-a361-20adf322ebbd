import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { AppUser } from "../../../../user/dto/user.dto";

export class CreatePinBody {
  @ApiProperty()
  pin: string;
}

export class VerifyPinQuery {
  @ApiProperty()
  pin: string;
}

export class VerifyPinResult {
  @ApiProperty()
  verified: boolean;
  user: AppUser;
}

export class UpdateUserPinBody {
  @ApiProperty()
  pin: string;
  @ApiProperty()
  newPin: string;
}

export const pinSchema = Joi.object({
  pin: Joi.string().required(),
});

export const updateUserPinSchema = Joi.object({
  pin: Joi.string().required(),
  newPin: Joi.string().required(),
});

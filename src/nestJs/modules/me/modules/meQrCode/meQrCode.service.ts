import { Injectable } from "@nestjs/common";

import { LocalizedLanguage } from "../../../location/dto/location.dto";
import { QrCodeDecodedData, QrCodePairResponse } from "../../../qrCode/dto/qrCode.dto";
import { QrCodeFactoryService } from "../../../qrCode/qrCodeFactory.service";

/**
 * Me Qr code service
 */
@Injectable()
export class MeQrCodeService {
  constructor(private qrCodeServiceFactory: QrCodeFactoryService) {}

  /**
   * pair a qrcode
   * @param qrCodeData QrCodeDecodedData
   * @param userId string
   * @returns Promise<QrCodePairResponse>
   */
  async pair(
    qrCodeData: QrCodeDecodedData,
    userId: string,
    paymentInstrumentId: string,
    language: LocalizedLanguage,
  ): Promise<QrCodePairResponse> {
    return this.qrCodeServiceFactory.pair(qrCodeData, userId, paymentInstrumentId, language);
  }
}

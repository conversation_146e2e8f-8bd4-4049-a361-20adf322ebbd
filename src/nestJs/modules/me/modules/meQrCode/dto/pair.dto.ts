import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { LocalizedLanguage, localizedLanguageSchema } from "../../../../location/dto/location.dto";

export class QrCodePairBody {
  @ApiProperty({ example: "https://dash.com/1T20SClCNCCDJTqGzxIrLddDASH02T" })
  link: string;

  @ApiProperty({ example: "5a21d2ba-25ad-44ed-82b6-e941ba628356" })
  paymentInstrumentId: string;

  @ApiProperty({ enum: LocalizedLanguage, example: LocalizedLanguage.EN })
  language: LocalizedLanguage;
}

export const qrCodePairBodySchema = Joi.object<QrCodePairBody>({
  link: Joi.string().required(),
  paymentInstrumentId: Joi.string().required(),
  language: localizedLanguageSchema.required(),
});

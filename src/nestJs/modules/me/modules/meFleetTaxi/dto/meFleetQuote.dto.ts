import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { LocationLanguage } from "@nest/modules/location/dto/location.dto";

export class RideHailingMatrixDto {
  @ApiProperty({ example: ["ChIJwXMVrLriAzQRcMQVFWJ-Hsg", "ChIJPZXTUy_8AzQRBVRH5GyvszU"] })
  placeIds: string[];

  @ApiProperty({ example: "2025-06-06T08:05:17.068476+00:00" })
  time: Date;

  @ApiProperty({ example: "en" })
  language: LocationLanguage;

  @ApiProperty({ example: "12345678" })
  sessionToken: string;
}

export const rideHailingMatrixDtoSchema = Joi.object<RideHailingMatrixDto>({
  placeIds: Joi.array().items(Joi.string()).required(),
  time: Joi.date().required(),
  language: Joi.string().required(),
  sessionToken: Joi.string().required(),
});

import { Inject, Injectable } from "@nestjs/common";

import FleetOrderEntity, { FleetOrderStatus } from "@nest/modules/database/entities/fleetOrder.entity";
import { FleetOrderRepository } from "@nest/modules/database/repositories/fleetOrder.repository";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { SyncabCancelFleetOrderDelegatee } from "./SyncabCancelFleetOrderDelegatee";
import { BookingReceiptSnapshot, PartnerKey } from "../meFleetTaxi.interface";

@Injectable()
export class CancelFleetOrderDelegatee {
  constructor(
    private readonly fleetOrderRepository: FleetOrderRepository,
    private readonly syncabCancelFleetOrderDelegatee: SyncabCancelFleetOrderDelegatee,
    private readonly hailingApiService: HailingApiService,
    @Inject(LoggerServiceAdapter) private readonly logger: LoggerServiceAdapter,
  ) {}

  getDelegatee(fleetOrder: FleetOrderEntity) {
    if (fleetOrder.partnerKey === PartnerKey.SYNCAB) {
      return this.syncabCancelFleetOrderDelegatee;
    }
    return null;
  }

  async execute(txId: string, isAdminCancel: boolean = false): Promise<BookingReceiptSnapshot | null> {
    try {
      this.logger.info("CancelFleetOrderDelegatee/execute-start", { txId });

      const fleetOrder = await this.fleetOrderRepository.findOne({
        where: { txId },
      });

      if (!fleetOrder) {
        this.logger.warn("CancelFleetOrderDelegatee/execute-end", { txId, message: "No fleet order found" });
        return null;
      }

      const delegatee = this.getDelegatee(fleetOrder);

      if (!delegatee) {
        throw errorBuilder.fleetTaxi.noCancelFleetOrderDelegatee();
      }

      await delegatee.execute(fleetOrder);

      const snapshot = await delegatee.getBookingReceiptSnapshot(fleetOrder);

      await this.fleetOrderRepository.update(fleetOrder.id, {
        status: FleetOrderStatus.CANCELLED,
        bookingReceiptSnapshot: snapshot as unknown as JSON,
      });

      if (isAdminCancel) {
        await this.hailingApiService.cancelFleetHailingRequest(fleetOrder.hailingRequestId);
      }

      this.logger.info("CancelFleetOrderDelegatee/execute-end", { txId, snapshot });

      return snapshot;
    } catch (error) {
      this.logger.error("CancelFleetOrderDelegatee/execute-end", { txId }, error as Error);
      return null;
    }
  }
}

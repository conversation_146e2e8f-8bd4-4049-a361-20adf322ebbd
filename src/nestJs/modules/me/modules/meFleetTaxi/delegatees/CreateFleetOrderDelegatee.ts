import { Inject } from "@nestjs/common";
import retry from "async-retry";
import { DecodedIdToken } from "firebase-admin/auth";

import { CloudTaskClientService } from "@nest/modules/cloud-task-client/cloud-task-client.service";
import { FleetOrderStatus } from "@nest/modules/database/entities/fleetOrder.entity";
import Tx from "@nest/modules/database/entities/tx.entity";
import { FleetOrderRepository } from "@nest/modules/database/repositories/fleetOrder.repository";
import { FleetOrderTimelineRepository } from "@nest/modules/database/repositories/fleetOrderTimeline.repository";
import { FleetPartnerRepository } from "@nest/modules/database/repositories/fleetPartner.repository";
import { FleetQuoteRepository } from "@nest/modules/database/repositories/fleetQuote.repository";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { HailingApiService } from "@nest/modules/hailingApi/hailingApi.service";
import { HailingCreateOrderBody } from "@nest/modules/me/modules/meHailing/dto/meHailing.dto";
import { PaymentService } from "@nest/modules/payment/payment.service";
import { TxHailingMetadata } from "@nest/modules/transaction/dto/txMetadata.dto";
import { TxTypes } from "@nest/modules/transaction/dto/txType.dto";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { SyncabCreateFleetOrderDelegatee } from "./SyncabCreateFleetOrderDelegatee";

export class CreateFleetOrderDelegatee {
  constructor(
    private readonly syncabCreateFleetOrderDelegatee: SyncabCreateFleetOrderDelegatee,
    private readonly userRepository: UserRepository,
    private readonly fleetQuoteRepository: FleetQuoteRepository,
    private readonly fleetOrderRepository: FleetOrderRepository,
    private readonly fleetOrderTimelineRepository: FleetOrderTimelineRepository,
    @Inject(LoggerServiceAdapter) private readonly logger: LoggerServiceAdapter,
    private readonly cloudTaskClientService: CloudTaskClientService,
    private readonly fleetPartnerRepository: FleetPartnerRepository,
    private readonly txRepository: TxRepository,
    private readonly paymentService: PaymentService,
    private readonly hailingApiService: HailingApiService,
  ) {}

  getDelegatee(_data: HailingCreateOrderBody) {
    return this.syncabCreateFleetOrderDelegatee;
  }

  async execute(data: HailingCreateOrderBody, tx: Tx, user: DecodedIdToken) {
    try {
      if (tx.type !== TxTypes.HAILING_REQUEST) {
        throw errorBuilder.fleetTaxi.invalidFleetOrderRequest();
      }

      this.logger.info("CreateFleetOrderDelegatee/execute-start", { tx: tx.id, data });

      const appUser = await this.userRepository.findAppUserById(user.uid, ["paymentInstruments"]);

      const delegatee = this.getDelegatee(data);

      const fleetQuote = await this.fleetQuoteRepository.findOne({
        where: { userId: appUser.id, partnerKey: delegatee.partnerKey },
        order: { createdAt: "DESC" },
      });

      if (!fleetQuote) {
        throw errorBuilder.fleetTaxi.noFleetQuoteFound();
      }

      const fleetPartner = await this.fleetPartnerRepository.findOne({
        where: { partnerKey: delegatee.partnerKey },
      });

      if (!fleetPartner) {
        throw errorBuilder.fleetTaxi.noFleetPartnerFound();
      }

      const { thirdPartyOrderId, snapshot, thirdPartyStatus } = await delegatee.execute(data, tx, fleetQuote, appUser);

      const fleetOrder = await this.fleetOrderRepository.findOne({
        where: { txId: tx.id },
      });

      if (fleetOrder) {
        return fleetOrder;
      }

      const newFleetOrder = await this.fleetOrderRepository.save({
        txId: tx.id,
        hailingRequestId: (tx.metadata as TxHailingMetadata).id,
        thirdPartyOrderId,
        partnerKey: delegatee.partnerKey,
        status: FleetOrderStatus.MATCHING,
        user: appUser,
      });

      await this.fleetOrderTimelineRepository.save({
        fleetOrderId: newFleetOrder.id,
        status: FleetOrderStatus.MATCHING,
        thirdPartyStatus,
        snapshot,
      });

      await this.cloudTaskClientService.enqueueUpdateFleetTask(
        newFleetOrder.id,
        this.fleetPartnerRepository.getUpdateInterval(fleetPartner, newFleetOrder.status),
      );

      this.logger.info("CreateFleetOrderDelegatee/execute-end", { tx: tx.id, data, fleetOrder });

      return fleetOrder;
    } catch (error) {
      this.logger.error("CreateFleetOrderDelegatee/execute-end", { tx: tx.id, data }, error as Error);
      throw error;
    }
  }

  async executeWithRetry(data: HailingCreateOrderBody, tx: Tx, user: DecodedIdToken) {
    try {
      return await retry(
        async () => {
          return await this.execute(data, tx, user);
        },
        {
          retries: 3,
          factor: 2,
          minTimeout: 1000,
          maxTimeout: 5000,
        },
      );
    } catch (error) {
      this.handleCreateFleetOrderFailure(tx.id);
      throw error;
    }
  }

  async handleCreateFleetOrderFailure(txId: string): Promise<void> {
    try {
      this.logger.info("CreateFleetOrderDelegatee/handleCreateFleetOrderFailure-start", { txId });
      await this.txRepository.updateHailingTxToCancelled(txId);
      await this.paymentService.voidPaymentWithTxId(txId);
      await this.hailingApiService.cancelFleetHailingRequest(txId);
    } catch (error) {
      this.logger.error("CreateFleetOrderDelegatee/handleCreateFleetOrderFailure-end", { txId }, error as Error);
      throw errorBuilder.fleetTaxi.createFleetOrderFailure(error);
    }
  }
}

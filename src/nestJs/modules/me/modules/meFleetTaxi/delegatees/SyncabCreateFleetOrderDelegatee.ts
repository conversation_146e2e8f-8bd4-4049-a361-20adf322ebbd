import { Injectable } from "@nestjs/common";

import FleetQuoteEntity from "@nest/modules/database/entities/fleetQuote.entity";
import Tx from "@nest/modules/database/entities/tx.entity";
import { HailingCreateOrderBody } from "@nest/modules/me/modules/meHailing/dto/meHailing.dto";
import { SyncabApiService } from "@nest/modules/syncabApi/syncabApi.service";
import { TxTypes } from "@nest/modules/transaction/dto/txType.dto";
import { AppUser } from "@nest/modules/user/dto/user.dto";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { ICreateFleetOrderDelegatee, PartnerKey } from "../meFleetTaxi.interface";

@Injectable()
export class SyncabCreateFleetOrderDelegatee implements ICreateFleetOrderDelegatee {
  constructor(private readonly syncabApiService: SyncabApiService) {}

  partnerKey = PartnerKey.SYNCAB;

  async execute(data: HailingCreateOrderBody, tx: Tx, fleetQuote: FleetQuoteEntity, appUser: AppUser) {
    if (tx.type !== TxTypes.HAILING_REQUEST) {
      throw errorBuilder.fleetTaxi.invalidFleetOrderRequest();
    }

    if (!data.fleetVehicleType || !data.fleetQuoteVehicleId) {
      throw errorBuilder.fleetTaxi.invalidFleetOrderRequest();
    }

    const order = await this.syncabApiService.getFleetOrder(tx.id);

    if (order) {
      return {
        thirdPartyOrderId: order.supplier_booking_id,
        snapshot: order,
        thirdPartyStatus: order.status,
      };
    }

    const result = await this.syncabApiService.createFleetOrder({
      estimateId: fleetQuote.thirdPartyQuoteId,
      taxi_type: data.fleetVehicleType,
      booking_id: tx.id,
      passenger: {
        name: appUser.firstName,
        phone_number: appUser.phoneNumber,
      },
    });

    return {
      thirdPartyOrderId: result.supplier_booking_id,
      snapshot: result,
      thirdPartyStatus: "searching-for-driver",
    };
  }
}

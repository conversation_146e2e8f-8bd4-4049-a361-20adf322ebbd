import { Injectable } from "@nestjs/common";

import FleetOrderEntity from "@nest/modules/database/entities/fleetOrder.entity";
import { SyncabApiService } from "@nest/modules/syncabApi/syncabApi.service";

import { ICancelFleetOrderDelegatee } from "../meFleetTaxi.interface";

@Injectable()
export class SyncabCancelFleetOrderDelegatee implements ICancelFleetOrderDelegatee {
  constructor(private readonly syncabApiService: SyncabApiService) {}

  async execute(fleetOrder: FleetOrderEntity) {
    await this.syncabApiService.cancelFleetOrder(fleetOrder.txId);
  }

  async getBookingReceiptSnapshot(fleetOrder: FleetOrderEntity) {
    const data = await this.syncabApiService.getFleetReceipt(fleetOrder.txId);
    const cancellationFee = data.breakdown.find((item) => item.type === "cancellation-fee")?.amount ?? 0;
    return { bookingFee: data.amount, cancellationFee, snapshot: data };
  }
}

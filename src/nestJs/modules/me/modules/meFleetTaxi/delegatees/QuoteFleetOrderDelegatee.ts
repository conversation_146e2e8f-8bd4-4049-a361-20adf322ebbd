import { Inject, Injectable } from "@nestjs/common";
import { DecodedIdToken } from "firebase-admin/auth";

import { FleetQuoteRepository } from "@nest/modules/database/repositories/fleetQuote.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { LocationService } from "@nest/modules/location/location.service";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

import { SyncabQuoteFleetOrderDelegatee } from "./SyncabQuoteFleetOrderDelegatee";
import { RideHailingMatrixDto } from "../dto/meFleetQuote.dto";
import { QuoteFleetOrderResponse } from "../meFleetTaxi.interface";

@Injectable()
export class QuoteFleetOrderDelegatee {
  constructor(
    private readonly locationService: LocationService,
    private readonly fleetQuoteRepository: FleetQuoteRepository,
    private readonly syncabQuoteFleetOrderDelegatee: SyncabQuoteFleetOrderDelegatee,
    private readonly userRepository: UserRepository,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  getDelegatee(_data: RideHailingMatrixDto) {
    return this.syncabQuoteFleetOrderDelegatee;
  }

  async execute(data: RideHailingMatrixDto, user: DecodedIdToken): Promise<QuoteFleetOrderResponse> {
    try {
      const appUser = await this.userRepository.findAppUserById(user.uid, ["paymentInstruments"]);

      const delegatee = this.getDelegatee(data);

      const locations = await Promise.all(
        data.placeIds.map((placeId) =>
          this.locationService.getPlaceDetails(
            {
              placeId,
              language: data.language,
            },
            data.sessionToken,
          ),
        ),
      );

      const { vehicleClassOptions, thirdPartyQuoteId, snapshot } = await delegatee.getQuote(data, locations);

      const fleetQuote = await this.fleetQuoteRepository.findOne({
        where: { thirdPartyQuoteId, partnerKey: delegatee.partnerKey, userId: appUser.id },
        order: { createdAt: "DESC" },
      });

      if (fleetQuote) {
        return {
          vehicleClassOptions,
          fleetQuote,
        };
      }

      const newFleetQuote = await this.fleetQuoteRepository.save({
        userId: appUser.id,
        thirdPartyQuoteId,
        snapshot,
        partnerKey: delegatee.partnerKey,
      });

      return {
        vehicleClassOptions,
        fleetQuote: newFleetQuote,
      };
    } catch (error) {
      this.logger.error("QuoteFleetOrderDelegatee/execute-error", { error });

      return {
        vehicleClassOptions: [],
        fleetQuote: null,
      };
    }
  }
}

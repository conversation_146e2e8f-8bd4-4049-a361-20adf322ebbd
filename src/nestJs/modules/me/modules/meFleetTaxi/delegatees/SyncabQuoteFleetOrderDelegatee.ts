import { BadRequestException, Injectable } from "@nestjs/common";
import dayjs from "dayjs";
import { Filter } from "firebase-admin/firestore";

import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";
import { LocationPlaceDetailsResponse } from "@nest/modules/location/dto/location.dto";
import { SyncabQuote } from "@nest/modules/syncabApi/syncab.interface";
import { SyncabApiService } from "@nest/modules/syncabApi/syncabApi.service";

import { RideHailingMatrixDto } from "../dto/meFleetQuote.dto";
import { IQuoteFleetOrderDelegatee, PartnerKey, VehicleClassOption } from "../meFleetTaxi.interface";

@Injectable()
export class SyncabQuoteFleetOrderDelegatee implements IQuoteFleetOrderDelegatee {
  constructor(
    private readonly syncabApiService: SyncabApiService,
    private readonly appDatabaseService: AppDatabaseService,
  ) {}

  partnerKey = PartnerKey.SYNCAB;

  async getQuote(data: RideHailingMatrixDto, locations: LocationPlaceDetailsResponse[]) {
    const result = await this.syncabApiService.createFleetQuote(dayjs(data.time).toDate(), locations);

    if (!result.quotes) {
      throw new BadRequestException("No quotes available from Syncab API.");
    }

    const vehicleClassOptions = await this.quotesToVehicleClassOptions(result.quotes);

    return {
      thirdPartyQuoteId: result.quotes[0].estimateId,
      vehicleClassOptions,
      snapshot: result,
    };
  }

  async quotesToVehicleClassOptions(quotes: SyncabQuote[]): Promise<VehicleClassOption[]> {
    const fleetVehicleKeys = quotes.map((quote) => quote.taxi_type);

    const vehicles = await this.appDatabaseService
      .fleetVehicleTypesRepository()
      .find(...[Filter.where("fleet_vehicle_key", "in", fleetVehicleKeys)]);

    const vehicleMap = new Map(vehicles.map((vehicle) => [vehicle.fleetVehicleKey, vehicle]));

    return quotes.reduce<VehicleClassOption[]>((acc, quote) => {
      const vehicle = vehicleMap.get(quote.taxi_type)!;
      if (!vehicle) {
        return acc;
      }

      acc.push({
        fleetVehicleId: quote.estimateId,
        fleetVehicleType: quote.taxi_type,
        vehicleClass: vehicle.fleetClass as string,
        vehicleClassName: vehicle.name as string,
        vehicleClassNameTc: vehicle.nameTc as string,
        vehicleClassDescription: vehicle.description as string,
        vehicleClassDescriptionTc: vehicle.descriptionTc as string,
        seatsCount: vehicle.numberOfSeat as number,
        luggageCount: vehicle.numberOfLuggage as number,
        isIncludeWheelChair: vehicle.isIncludeWheelChair as boolean,
        baseFare: quote.estimate,
        vehicleIconUrl: vehicle.vehicleIconUrl || "",
        fleetIconUrl: vehicle.fleetIconUrl || "",
        fleetPartnerKey: this.partnerKey,
      });

      return acc;
    }, [] as VehicleClassOption[]);
  }
}

import { HttpModule } from "@nestjs/axios";
import { forwardRef, Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { AppDatabaseModule } from "@nest/modules/appDatabase/appDatabase.module";
import { CloudTaskClientModule } from "@nest/modules/cloud-task-client/cloud-task-client.module";
import { FleetOrderRepository } from "@nest/modules/database/repositories/fleetOrder.repository";
import { FleetOrderTimelineRepository } from "@nest/modules/database/repositories/fleetOrderTimeline.repository";
import { FleetPartnerRepository } from "@nest/modules/database/repositories/fleetPartner.repository";
import { FleetQuoteRepository } from "@nest/modules/database/repositories/fleetQuote.repository";
import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { PaymentTxRepository } from "@nest/modules/database/repositories/paymentTx.repository";
import { TxRepository } from "@nest/modules/database/repositories/tx.repository";
import { UserRepository } from "@nest/modules/database/repositories/user.repository";
import { HailingApiModule } from "@nest/modules/hailingApi/hailingApi.module";
import { LocationModule } from "@nest/modules/location/location.module";
import { DriverModule } from "@nest/modules/merchant/merchantDriver/merchantDriver.module";
import { PaymentModule } from "@nest/modules/payment/payment.module";
import { SyncabApiModule } from "@nest/modules/syncabApi/syncabApi.module";
import { TransactionEventModule } from "@nest/modules/transaction/modules/transactionEvent.module";

import { CancelFleetOrderDelegatee } from "./delegatees/CancelFleetOrderDelegatee";
import { CreateFleetOrderDelegatee } from "./delegatees/CreateFleetOrderDelegatee";
import { QuoteFleetOrderDelegatee } from "./delegatees/QuoteFleetOrderDelegatee";
import { SyncabCancelFleetOrderDelegatee } from "./delegatees/SyncabCancelFleetOrderDelegatee";
import { SyncabCreateFleetOrderDelegatee } from "./delegatees/SyncabCreateFleetOrderDelegatee";
import { SyncabQuoteFleetOrderDelegatee } from "./delegatees/SyncabQuoteFleetOrderDelegatee";
import { MeFleetQuoteController } from "./meFleetQuote.controller";

@Module({
  imports: [
    SyncabApiModule,
    LocationModule,
    AppDatabaseModule,
    CloudTaskClientModule,
    HttpModule,
    ConfigModule,
    forwardRef(() => TransactionEventModule),
    DriverModule,
    HailingApiModule,
    PaymentModule,
    HailingApiModule,
  ],
  providers: [
    FleetQuoteRepository,
    FleetOrderRepository,
    FleetPartnerRepository,
    MerchantRepository,
    FleetOrderTimelineRepository,
    UserRepository,
    TxRepository,
    PaymentTxRepository,
    SyncabQuoteFleetOrderDelegatee,
    SyncabCreateFleetOrderDelegatee,
    SyncabCancelFleetOrderDelegatee,
    QuoteFleetOrderDelegatee,
    CreateFleetOrderDelegatee,
    CancelFleetOrderDelegatee,
  ],
  exports: [
    SyncabQuoteFleetOrderDelegatee,
    SyncabCreateFleetOrderDelegatee,
    SyncabCancelFleetOrderDelegatee,
    QuoteFleetOrderDelegatee,
    CreateFleetOrderDelegatee,
    CancelFleetOrderDelegatee,
  ],
  controllers: [MeFleetQuoteController],
})
export class MeFleetTaxiModule {}

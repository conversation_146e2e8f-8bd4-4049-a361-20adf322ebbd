import { <PERSON>, <PERSON>q, Post, Body } from "@nestjs/common";
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse, ApiBadRequestResponse } from "@nestjs/swagger";
import { Request } from "express";

import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "@nest/modules/utils/utils/swagger.utils";
import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";

import { QuoteFleetOrderDelegatee } from "./delegatees/QuoteFleetOrderDelegatee";
import { RideHailingMatrixDto, rideHailingMatrixDtoSchema } from "./dto/meFleetQuote.dto";
import { QuoteFleetOrderResponse } from "./meFleetTaxi.interface";

@ApiBearerAuth()
@Controller("quotes")
@ApiTags(...apiTags.me)
export class MeFleetQuoteController {
  constructor(private readonly quoteFleetOrderDelegatee: QuoteFleetOrderDelegatee) {}

  @Post("/")
  @ApiOperation({
    summary: "Get fleet quote",
  })
  @ApiResponse({ status: 200, description: "Get fleet quote" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async getFleetQuote(
    @Body(new JoiValidationPipe(rideHailingMatrixDtoSchema)) body: RideHailingMatrixDto,
    @Req() req: Request,
  ): Promise<QuoteFleetOrderResponse> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }

    return this.quoteFleetOrderDelegatee.execute(body, user);
  }
}

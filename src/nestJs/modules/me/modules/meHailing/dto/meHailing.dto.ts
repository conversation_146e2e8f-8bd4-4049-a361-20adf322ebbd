import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { FleetVehicleClass, fleetVehicleClassSchema } from "../../../../fleet/dto/fleet.dto";
import { HailingItineraryStepRequest } from "../../../../hailing/dto/hailing.dto";
import { LocalizedLanguage, localizedLanguageSchema } from "../../../../location/dto/location.dto";
import { TxHailingMetadata } from "../../../../transaction/dto/txMetadata.dto";
import { PartnerKey } from "../../meFleetTaxi/meFleetTaxi.interface";

export class HailingCreateOrderOptionsBody {
  @ApiProperty({ example: true, description: "Is assistant required" })
  isAssistant: boolean;

  @ApiProperty({ example: true, description: "Is pet friendly" })
  isPetFriendly: boolean;
}

export enum PlatformType {
  FLEET = "FLEET",
  DASH = "DASH",
}

/**
 * Hailing create order
 */
export class HailingCreateOrderBody {
  @ApiProperty({ required: false, example: "sessionToken", description: "Session token" })
  sessionToken?: string;

  @ApiProperty({ example: "en", description: "Languages accepted" })
  language: LocalizedLanguage;

  @ApiProperty({
    example: [
      {
        index: 0,
        placeId: "ChIJE-FWMvMDBDQRBCxQzoVA-Ao",
        lng: 114.26161142551531,
        lat: 22.306779062030994,
        displayName: "TKO Plaza",
        formattedAddress: "Tseung Kwan O Plaza Club House, 1 Tong Tak St, Tseung Kwan O",
      },
      {
        index: 1,
        placeId: "ChIJuwh9w1YABDQRaRbZ1DgateI",
        lng: 114.18396337853324,
        lat: 22.279593203640275,
        displayName: "Hysan Place",
        formattedAddress: "Hysan Place, 500 Hennessy Rd, Causeway Bay",
      },
    ],
    description: "Index of the location (0: origin - 1: destination)",
    type: Array<HailingItineraryStepRequest>,
  })
  itinerary: HailingItineraryStepRequest[];

  @ApiProperty({
    required: true,
    example: PlatformType.DASH,
    description: "Type of hailing",
  })
  platformType: PlatformType;

  @ApiProperty({
    required: false,
    example: new Date(),
    description:
      "Add time for hailing booking. If not specified, it'll create an order for the current time. It has to be at least 50 minutes in the future.",
    type: Date,
  })
  time?: Date;

  @ApiProperty({
    required: false,
    example: {
      STANDARD: [],
      COMFORT: ["URadOsz9sqDON7Me9DmC", "tDHMLh9kPED51bIjpsVH"],
      LUXURY: ["tDHMLh9kPED51bIjpsVH"],
    },
    description:
      "Fleet vehicle class Record<VehicleClass, FleetId[]>. Empty array means all fleets. No key won't match the class type.",
  })
  fleetVehicleClass?: FleetVehicleClass;

  @ApiProperty({
    required: false,
    example: "URadOsz9sqDON7Me9DmC",
    description: "Fleet vehicle type",
  })
  fleetVehicleType?: string;

  @ApiProperty({
    required: false,
    example: "b584cd9a-93b6-474e-9ba3-2318ed40154c",
    description: "Fleet Quote Vehicle id",
  })
  fleetQuoteVehicleId?: string;

  @ApiProperty({
    required: false,
    example: PartnerKey.SYNCAB,
    description: "Partner key",
  })
  fleetPartnerKey: PartnerKey;

  @ApiProperty({
    required: false,
    example: "b584cd9a-93b6-474e-9ba3-2318ed40154c",
    description: "Payment instrument id. If no specified, default payment instrument will be used",
  })
  paymentInstrumentId?: string;

  @ApiProperty({
    example: { isAssistant: true, isPetFriendly: true },
    description: "Options for the order",
  })
  options: HailingCreateOrderOptionsBody;

  @ApiProperty({
    required: false,
    example: true,
    description: "Prioritize favorite drivers",
  })
  prioritizeFavoriteDrivers?: boolean;

  @ApiProperty({
    required: false,
    example: "6cf2d323-8138-4a38-8230-c43a1925d40f",
    description: "Id of third party campaign to use",
  })
  campaignIdThirdParty?: string;

  @ApiProperty({
    required: false,
    example: "e222f64c-75e6-4c04-9e62-8b13f45d8719",
    description: "Id of dash campaign to use",
  })
  campaignIdDash?: string;

  @ApiProperty({
    required: false,
    example: "some jsonLogic rule",
    description: "Rules of third party campaign to use",
  })
  campaignRulesThirdParty?: string;

  @ApiProperty({
    required: false,
    example: "some jsonLogic rule",
    description: "Rules of dash campaign to use",
  })
  campaignRulesDash?: string;

  @ApiProperty({
    required: false,
    example: ["LANTAU", "URBAN"],
    description: "Operating areas",
  })
  operatingAreas?: string[];

  @ApiProperty({
    required: false,
    example: true,
    description: "Whether to use double tunnel fee",
  })
  doubleTunnelFee?: boolean;
}

export const hailingCreateOrderOptionsBodySchema = Joi.object<HailingCreateOrderOptionsBody>({
  isAssistant: Joi.boolean().required(),
  isPetFriendly: Joi.boolean().required(),
});

export const hailingCreateOrderBodySchema = Joi.object<HailingCreateOrderBody>({
  sessionToken: Joi.string(),
  language: localizedLanguageSchema.required(),
  itinerary: Joi.array<HailingItineraryStepRequest>()
    .items(
      Joi.object<HailingItineraryStepRequest>({
        displayName: Joi.string().required(),
        formattedAddress: Joi.string().required(),
        index: Joi.number().required(),
        placeId: Joi.string().required(),
        lng: Joi.number().required(),
        lat: Joi.number().required(),
      }),
    )
    .required()
    .min(2),
  time: Joi.date().greater(Date.now() + 1 * 20 * 60 * 1000), // Has to be at least 50 minutes in the future
  fleetVehicleClass: fleetVehicleClassSchema.optional(),
  platformType: Joi.string()
    .default(PlatformType.DASH)
    .valid(...Object.values(PlatformType))
    .optional(),
  fleetVehicleType: Joi.string().optional(),
  fleetQuoteVehicleId: Joi.string().optional(),
  fleetPartnerKey: Joi.string().valid(...Object.values(PartnerKey)),
  paymentInstrumentId: Joi.string(),
  options: hailingCreateOrderOptionsBodySchema.required(),
  prioritizeFavoriteDrivers: Joi.boolean(),
  campaignIdThirdParty: Joi.string(),
  campaignIdDash: Joi.string(),
  campaignRulesThirdParty: Joi.string(),
  campaignRulesDash: Joi.string(),
  operatingAreas: Joi.array().items(Joi.string()),
  doubleTunnelFee: Joi.boolean(),
});

export type MeHailingCreateOrderResponse = TxHailingMetadata;

import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export class BoostOrderBody {
  @ApiProperty({
    example: 10.0,
    description:
      "Amount to add to the current boost (cumulative). Must be greater than 0. Will be capped at the maximum boost limit configured firestore hail configuration.",
    required: false,
    minimum: 0,
  })
  boostAmount?: number;
}

export class BoostOrderResponse {
  @ApiProperty({
    example: true,
    description: "Whether the boost update was successful",
    default: true,
  })
  success: boolean;

  @ApiProperty({
    example: 25.0,
    description: "Total boost amount after this update",
  })
  totalBoostAmount: number;

  @ApiProperty({
    example: 10.0,
    description: "Amount of boost added in this request",
  })
  addedBoostAmount: number;

  @ApiProperty({
    example: "Added $10.00 boost. Total boost: $25.00",
    description: "Human-readable message about the boost update",
    default: "Boost added successfully",
  })
  message: string;

  @ApiProperty({
    example: "2025-01-28T12:00:00Z",
    description: "Timestamp when the hail was last updated",
  })
  updatedAt: string;
}

export const boostOrderBodySchema = Joi.object<BoostOrderBody>({
  boostAmount: Joi.number().min(0).precision(2).optional(),
});

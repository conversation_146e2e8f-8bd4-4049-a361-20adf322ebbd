import { Modu<PERSON> } from "@nestjs/common";

import { CloudTaskClientModule } from "@nest/modules/cloud-task-client/cloud-task-client.module";
import { PubSubModule } from "@nest/modules/pubsub/pubsub.module";

import { MeHailingController } from "./meHailing.controller";
import { MeHailingService } from "./meHailing.service";
import { FleetModule } from "../../../fleet/fleet.module";
import { HailingModule } from "../../../hailing/hailing.module";
import { LocationModule } from "../../../location/location.module";
import { MeFleetTaxiModule } from "../meFleetTaxi/meFleetTaxi.module";

@Module({
  imports: [LocationModule, HailingModule, FleetModule, MeFleetTaxiModule, CloudTaskClientModule, PubSubModule],
  controllers: [MeHailingController],
  providers: [MeHailingController, MeHailingService],
  exports: [MeHailingController, MeHailingService],
})
export class MeHailingModule {}

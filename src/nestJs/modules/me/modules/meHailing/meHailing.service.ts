import { Injectable } from "@nestjs/common";
import { DecodedIdToken } from "firebase-admin/auth";

import { LogAll } from "@nest/decorators/log-all.decorator";
import { PubSubService } from "@nest/modules/pubsub/pubsub.service";

import { BoostOrderBody, BoostOrderResponse } from "./dto/boostOrder.dto";
import { HailingCreateOrderBody, PlatformType, MeHailingCreateOrderResponse } from "./dto/meHailing.dto";
import { FleetService } from "../../../fleet/fleet.service";
import { HailingItineraryStepResponse } from "../../../hailing/dto/hailing.dto";
import { HailingService } from "../../../hailing/hailing.service";
import { LocalizedLanguage, LocationLanguage } from "../../../location/dto/location.dto";
import { LocationService } from "../../../location/location.service";
import { CreateFleetOrderDelegatee } from "../meFleetTaxi/delegatees/CreateFleetOrderDelegatee";

@LogAll()
@Injectable()
export class MeHailingService {
  constructor(
    private readonly hailingService: HailingService,
    private readonly locationService: LocationService,
    private readonly fleetService: FleetService,
    private readonly createFleetOrderDelegatee: CreateFleetOrderDelegatee,
    private readonly pubsubService: PubSubService,
  ) {}

  async createOrder(
    body: HailingCreateOrderBody,
    user: DecodedIdToken,
    token: string,
  ): Promise<MeHailingCreateOrderResponse> {
    if (body.fleetVehicleClass) {
      await this.fleetService.verifyFleetVehicleClass(body.fleetVehicleClass);
    } else {
      body.fleetVehicleClass = { FOUR_SEATER: [] };
    }

    let itinerary: HailingItineraryStepResponse[];

    if (body.language === LocalizedLanguage.ZHHK) {
      itinerary = body.itinerary.map((itineraryStep) => ({
        index: itineraryStep.index,
        placeId: itineraryStep.placeId,
        lat: itineraryStep.lat,
        lng: itineraryStep.lng,
        i18n: {
          [LocalizedLanguage.ZHHK]: {
            displayName: itineraryStep.displayName,
            formattedAddress: itineraryStep.formattedAddress,
          },
        },
      }));
    } else {
      const promises = await Promise.all(
        body.itinerary.map((itineraryStep) =>
          this.locationService.getPlaceDetails(
            {
              placeId: itineraryStep.placeId,
              language: LocationLanguage.ZH_HK,
            },
            body.sessionToken,
          ),
        ),
      );

      itinerary = body.itinerary.map((itineraryStep, index) => ({
        index: itineraryStep.index,
        placeId: itineraryStep.placeId,
        lat: itineraryStep.lat,
        lng: itineraryStep.lng,
        i18n: {
          [body.language]: {
            displayName: itineraryStep.displayName,
            formattedAddress: itineraryStep.formattedAddress,
          },
          [LocalizedLanguage.ZHHK]: {
            displayName: promises[index].displayName,
            formattedAddress: promises[index].formattedAddress,
          },
        },
      }));
    }

    const { tx, authedAmount } = await this.hailingService.createOrder(body, itinerary, user, token);

    const metadata = tx.metadata;
    metadata.authedAmount = authedAmount;

    if (body.platformType === PlatformType.FLEET) {
      // Create fleet order
      await this.createFleetOrderDelegatee.execute(body, tx, user);
    }

    if (metadata.type === "SCHEDULED" && body.time) {
      await this.pubsubService.publishMessageForHailingTxCreated({ txId: tx.id });
    }

    return metadata;
  }

  async boostOrder(
    orderId: string,
    body: BoostOrderBody,
    user: DecodedIdToken,
    token: string,
  ): Promise<BoostOrderResponse> {
    const updatedHail = await this.hailingService.updateHailBoost(orderId, body.boostAmount ?? 0, token);

    // Map the response from hailing service to our DTO format
    return {
      success: updatedHail.success,
      totalBoostAmount: updatedHail.totalBoostAmount,
      addedBoostAmount: updatedHail.addedBoostAmount,
      message: updatedHail.message,
      updatedAt: updatedHail.updatedAt,
    };
  }
}

import { Module } from "@nestjs/common";

import { MeLocationController } from "./meLocation.controller";
import { MeLocationService } from "./meLocation.service";
import { LocationModule } from "../../../location/location.module";

@Module({
  providers: [MeLocationController, MeLocationService],
  imports: [LocationModule],
  controllers: [MeLocationController],
  exports: [MeLocationController, MeLocationService],
})
export class MeLocationModule {}

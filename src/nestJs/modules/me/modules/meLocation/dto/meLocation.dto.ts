import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { LocationLanguage, locationLanguageSchema } from "../../../../location/dto/location.dto";

/**
 * Location text autocomplete query.
 */
export class LocationPlaceAutocompleteQuery {
  @ApiProperty({ required: false, example: "sessionToken", description: "Session token" })
  sessionToken?: string;

  @ApiProperty({ example: "Mall", description: "Query text" })
  query: string;

  @ApiProperty({ example: "en", description: "Languages accepted", enum: LocationLanguage })
  language: LocationLanguage;

  @ApiProperty({ example: 0, description: "Index of the location (0: origin - 1: destination)" })
  index: number;

  @ApiProperty({ required: false, example: 114.26161142551531, description: "Longitude" })
  lng?: number;

  @ApiProperty({ required: false, example: 22.306779062030994, description: "Latitude" })
  lat?: number;

  @ApiProperty({ required: false, example: 1000, description: "Radius in meters" })
  radius?: number;
}

export const locationPlaceAutocompleteQuerySchema = Joi.object<LocationPlaceAutocompleteQuery>({
  sessionToken: Joi.string(),
  query: Joi.string().required(),
  language: locationLanguageSchema.required(),
  index: Joi.number().required(),
  lng: Joi.number(),
  lat: Joi.number(),
  radius: Joi.number(),
}).and("lng", "lat");

/**
 * Location text details query.
 */
export class LocationPlaceDetailsQuery {
  @ApiProperty({ required: false, example: "sessionToken", description: "Session token" })
  sessionToken?: string;

  @ApiProperty({ example: "ChIJE-FWMvMDBDQRBCxQzoVA-Ao", description: "place Id" })
  placeId: string;

  @ApiProperty({ example: "en", description: "Languages accepted", enum: LocationLanguage })
  language: LocationLanguage;

  @ApiProperty({ example: 0, description: "Index of the location (0: origin - 1: destination)" })
  index: number;
}

export const locationPlaceDetailsQuerySchema = Joi.object<LocationPlaceDetailsQuery>({
  sessionToken: Joi.string(),
  placeId: Joi.string().required(),
  language: locationLanguageSchema.required(),
  index: Joi.number().required(),
});

/**
 * V2 of the place details query
 */
export class LocationV2PlaceDetailsDto {
  @ApiProperty({ required: false, example: "sessionToken", description: "Session token" })
  sessionToken?: string;

  @ApiProperty({ example: ["ChIJE-FWMvMDBDQRBCxQzoVA-Ao", "ChIJj6ZHpA4DBDQRD99EOoiAGwo"], description: "Place ids" })
  placeIds: string[];

  @ApiProperty({ example: "en", description: "Language accepted", enum: LocationLanguage })
  language: LocationLanguage;
}

export const locationV2PlaceDetailsDtoSchema = Joi.object<LocationV2PlaceDetailsDto>({
  sessionToken: Joi.string(),
  placeIds: Joi.array().items(Joi.string()).required(),
  language: locationLanguageSchema.required(),
});

/**
 * Location reverse geocode
 */
export class LocationReverseGeocodeQuery {
  @ApiProperty({ required: false, example: "sessionToken", description: "Session token" })
  sessionToken?: string;

  @ApiProperty({ example: "en", description: "Languages accepted", enum: LocationLanguage })
  language: LocationLanguage;

  @ApiProperty({ example: 0, description: "Index of the location (0: origin - 1: destination)" })
  index: number;

  @ApiProperty({ example: 114.26161142551531, description: "Longitude" })
  lng: number;

  @ApiProperty({ example: 22.306779062030994, description: "Latitude" })
  lat: number;
}

export const locationReverseGeocodeQuerySchema = Joi.object<LocationReverseGeocodeQuery>({
  sessionToken: Joi.string(),
  language: locationLanguageSchema.required(),
  index: Joi.number().required(),
  lng: Joi.number().required(),
  lat: Joi.number().required(),
});
export class LocationReverseGeocodeV2Query {
  @ApiProperty({ required: false, example: "sessionToken", description: "Session token" })
  sessionToken?: string;

  @ApiProperty({ example: "en", description: "Languages accepted", enum: LocationLanguage })
  language: LocationLanguage;

  @ApiProperty({ example: 0, description: "Index of the location (0: origin - 1: destination)" })
  index: number;

  @ApiProperty({ example: 114.26161142551531, description: "Longitude" })
  lng: number;

  @ApiProperty({ example: 22.306779062030994, description: "Latitude" })
  lat: number;

  @ApiProperty({
    required: false,
    example: true,
    description: "Continue to get reverse geocode result even if there is a polygon. Defaults to false",
  })
  getIfPolygon?: boolean;
}

export const locationReverseGeocodeV2QuerySchema = Joi.object<LocationReverseGeocodeV2Query>({
  sessionToken: Joi.string(),
  language: locationLanguageSchema.required(),
  index: Joi.number().required(),
  lng: Joi.number().required(),
  lat: Joi.number().required(),
  getIfPolygon: Joi.boolean().optional().default(true),
});

export class LocationComputeRoutesQuery {
  @ApiProperty({ example: "ChIJuwh9w1YABDQRaRbZ1DgateI", description: "Origin place id" })
  originPlaceId: string;

  @ApiProperty({ example: "ChIJj6ZHpA4DBDQRD99EOoiAGwo", description: "Origin place id" })
  destinationPlaceId: string;

  @ApiProperty({ example: "en", description: "Languages accepted", enum: LocationLanguage })
  language: LocationLanguage;
}

export const locationComputeRoutesQuerySchema = Joi.object<LocationComputeRoutesQuery>({
  originPlaceId: Joi.string().required(),
  destinationPlaceId: Joi.string().required(),
  language: locationLanguageSchema.required(),
});

import { Injectable } from "@nestjs/common";

import {
  LocationReverseGeocodeQuery,
  LocationPlaceAutocompleteQuery,
  LocationPlaceDetailsQuery,
  LocationComputeRoutesQuery,
  LocationV2PlaceDetailsDto,
  LocationReverseGeocodeV2Query,
} from "./dto/meLocation.dto";
import {
  ComputeRoutesResponse,
  LocationLanguage,
  LocationPlaceAutocompleteResponse,
  LocationPlaceDetailsResponse,
  LocationReverseGeocodeResponse,
  LocationReverseGeocodeV2Response,
} from "../../../location/dto/location.dto";
import { LocationService } from "../../../location/location.service";

@Injectable()
export class MeLocationService {
  constructor(private readonly locationService: LocationService) {}

  async placeAutocomplete(query: LocationPlaceAutocompleteQuery): Promise<LocationPlaceAutocompleteResponse> {
    return this.locationService.placeAutocomplete({
      sessionToken: query.sessionToken,
      query: query.query,
      language: this.mappingZHHK(query.language),
      ...(query.lat && query.lng
        ? {
            lat: query.lat,
            lng: query.lng,
            radius: query.radius,
          }
        : {}),
    });
  }

  async placeDetails(query: LocationPlaceDetailsQuery): Promise<LocationPlaceDetailsResponse> {
    return this.locationService.getPlaceDetails(
      {
        placeId: query.placeId,
        language: this.mappingZHHK(query.language),
      },
      query.sessionToken,
    );
  }

  async reverseGeocode(query: LocationReverseGeocodeQuery): Promise<LocationReverseGeocodeResponse> {
    return this.locationService.reverseGeocode({
      sessionToken: query.sessionToken,
      language: this.mappingZHHK(query.language),
      lat: query.lat,
      lng: query.lng,
    });
  }

  async reverseGeocodeV2(query: LocationReverseGeocodeV2Query): Promise<LocationReverseGeocodeV2Response> {
    return this.locationService.reverseGeocodeV2({
      sessionToken: query.sessionToken,
      language: this.mappingZHHK(query.language),
      lat: query.lat,
      lng: query.lng,
      getIfPolygon: query.getIfPolygon,
    });
  }

  async computeRoutes(query: LocationComputeRoutesQuery): Promise<ComputeRoutesResponse> {
    return Promise.all([
      this.locationService.computeRoutes({
        ...query,
        language: this.mappingZHHK(query.language),
      }),
      this.locationService.getPlaceDetails({
        placeId: query.originPlaceId,
        language: this.mappingZHHK(query.language),
      }),
      this.locationService.getPlaceDetails({
        placeId: query.destinationPlaceId,
        language: this.mappingZHHK(query.language),
      }),
    ]).then(([routes, origin, destination]) => {
      return {
        distanceMeters: routes.distanceMeters,
        durationSeconds: routes.durationSeconds,
        encodedPolyline: routes.encodedPolyline,
        originPlaceDetails: origin,
        destinationPlaceDetails: destination,
      };
    });
  }

  mappingZHHK(language: LocationLanguage): LocationLanguage {
    return language === LocationLanguage.ZHHK ? LocationLanguage.ZH_HK : language;
  }

  async placeDetailsV2(dto: LocationV2PlaceDetailsDto): Promise<LocationPlaceDetailsResponse[]> {
    return this.locationService.getMultiplePlaceDetails(dto.placeIds, this.mappingZHHK(dto.language), dto.sessionToken);
  }
}

import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DecodedIdToken } from "firebase-admin/auth";

import { AddEventBodyMe } from "./dto/addEvent.dto";
import { UserTransaction, UserTransactionsQueryDto, UserTransactionsResponseDto } from "./dto/userTransactions.dto";
import { Rating } from "../../../appDatabase/documents/trip.document";
import Tx from "../../../database/entities/tx.entity";
import TxEvent from "../../../database/entities/txEvent.entity";
import { TxRepository } from "../../../database/repositories/tx.repository";
import { UserRepository } from "../../../database/repositories/user.repository";
import { TransactionService } from "../../../transaction/transaction.service";
import LoggerServiceAdapter from "../../../utils/logger/logger.service";
import { errorBuilder } from "../../../utils/utils/error.utils";

@Injectable()
export class MeTransactionService {
  constructor(
    private readonly transactionService: TransactionService,
    @InjectRepository(TxRepository) private readonly txRepository: TxRepository,
    @InjectRepository(UserRepository) private readonly userRepository: UserRepository,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  async unlock(transactionId: string, userId: string): Promise<Tx> {
    const transaction = await this.txRepository.findOne({ where: { id: transactionId } });

    if (!transaction) {
      throw errorBuilder.transaction.notFound(transactionId);
    }

    return this.transactionService.unlock(transaction, userId);
  }

  async getTransactions(
    userTransactionsQueryDto: UserTransactionsQueryDto,
    userAppDatabaseId: string,
  ): Promise<UserTransactionsResponseDto> {
    const foundUser = await this.userRepository.findOne({ where: { appDatabaseId: userAppDatabaseId } });
    if (!foundUser) {
      throw errorBuilder.user.notFoundInSql(userAppDatabaseId);
    }

    const [transactions, count] = await this.txRepository.getCompletedTxListByUserId(
      foundUser.id,
      userTransactionsQueryDto.take,
      userTransactionsQueryDto.skip,
    );
    const userTransactions: UserTransaction[] = transactions.map((tx) => {
      if (!tx.total) {
        this.logger.error(errorBuilder.transaction.totalAmountMissing(tx.id));
      }
      const userTransaction = new UserTransaction();
      userTransaction.id = tx.id;
      userTransaction.createdAt = tx.createdAt;
      userTransaction.total = tx.total ?? 0;
      userTransaction.type = tx.type;
      userTransaction.date = this.transactionService.getTxDate(tx);
      userTransaction.metadata = tx.metadata;

      return userTransaction;
    });
    return { transactions: userTransactions, count };
  }

  async setTip(transactionId: string, userId: string, tip: number): Promise<Tx> {
    const transaction = await this.txRepository.findOne({ where: { id: transactionId }, relations: ["merchant"] });

    if (!transaction) {
      throw errorBuilder.transaction.notFound(transactionId);
    }

    return this.transactionService.setTipFromUserApp(transaction, userId, tip);
  }

  async setRating(transactionId: string, userId: string, rating: Rating): Promise<Tx> {
    const transaction = await this.txRepository.findOne({
      where: { id: transactionId },
      relations: ["merchant"],
    });

    if (!transaction) {
      throw errorBuilder.transaction.notFound(transactionId);
    }

    return this.transactionService.setRating(transaction, userId, rating);
  }

  async addEvent(transactionId: string, addEventBody: AddEventBodyMe, user?: DecodedIdToken): Promise<TxEvent> {
    // Set a default dummy user id
    const DUMMY_USER_ID = "";
    const userId = user
      ? (await this.userRepository.findOne({ where: { appDatabaseId: user.uid } }))?.id || DUMMY_USER_ID
      : DUMMY_USER_ID;
    return this.transactionService.addEvent(transactionId, userId, addEventBody);
  }
}

import { Body, Controller, Get, Param, Patch, Post, Query, Req } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
  refs,
} from "@nestjs/swagger";
import { Request } from "express";
import Jo<PERSON> from "joi";

import {
  AddEventBodyDefault,
  AddEventBodyHailingMerchantAcceptsOrder,
  AddEventBodyHailingUserUpdatesOrder,
  AddEventBodyMe,
  addEventBodySchemaMe,
} from "./dto/addEvent.dto";
import { SetRatingBodyDto, setRatingRequestSchema } from "./dto/setRating.dto";
import { setTipRequestSchema, SetTipBodyDto } from "./dto/setTips.dto";
import {
  UserTransactionsQueryDto,
  UserTransactionsResponseDto,
  userTransactionsQuerySchema,
} from "./dto/userTransactions.dto";
import { MeTransactionService } from "./meTransaction.service";
import { AppDatabaseService } from "../../../appDatabase/appDatabase.service";
import Tx from "../../../database/entities/tx.entity";
import TxEvent from "../../../database/entities/txEvent.entity";
import { TxEventType } from "../../../transaction/dto/txEventType.dto";
import { TxTypes } from "../../../transaction/dto/txType.dto";
import { errorBuilder } from "../../../utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "../../../utils/utils/swagger.utils";
import { JoiValidationPipe } from "../../../validation/validationPipe.service";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.me)
export class MeTransactionController {
  constructor(
    private readonly meTransactionService: MeTransactionService,
    private readonly appDatabaseService: AppDatabaseService,
  ) {}

  /**
   * Unlock transaction
   * @param param TransactionId
   * @param req Request
   */
  @Post(":transactionId/unlock")
  @ApiOperation({
    summary: "Unlock transaction",
  })
  @ApiResponse({ status: 201, description: "Unlock transaction" })
  async unlock(
    @Param("transactionId", new JoiValidationPipe(Joi.string().required())) transactionId: string,
    @Req() req: Request,
  ): Promise<Tx> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.meTransactionService.unlock(transactionId, user.user_id);
  }

  /**
   * Get User's transactions list
   * @param req Request
   * @param
   * @returns {transactions, count} UserTransactionsResponseDto
   */
  @Get()
  @ApiOperation({ summary: "Get User's transactions" })
  @ApiResponse({ status: 200, description: "Get user's transactions", type: UserTransactionsResponseDto })
  @ApiNotFoundResponse({
    description: buildErrorHtml([
      errorBuilder.user.notFoundInSql("IkCO7YHOJ3c073P8RihpRZxX3yC2"),
      errorBuilder.transaction.totalAmountMissing("2c42c818-f447-4575-b2cd-784c2052de2c"),
    ]),
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async getTransactions(
    @Query(new JoiValidationPipe(userTransactionsQuerySchema)) userTransactionsQueryDto: UserTransactionsQueryDto,
    @Req() req: Request,
  ): Promise<UserTransactionsResponseDto> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.meTransactionService.getTransactions(userTransactionsQueryDto, user.user_id);
  }

  @Patch(":transactionId/tip")
  @ApiOperation({ summary: "Set tip by transaction id" })
  @ApiResponse({ status: 200, description: "Set tip by transaction id", type: Tx })
  @ApiNotFoundResponse({
    description: buildErrorHtml([errorBuilder.transaction.notFound("009d13ed-3bae-4f3c-bd37-26686ca23d30")]),
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.transaction.trip.tripAlreadyEnded("009d13ed-3bae-4f3c-bd37-26686ca23d30"),
      errorBuilder.transaction.trip.tripNotPairedWithUser("009d13ed-3bae-4f3c-bd37-26686ca23d30", "IkCO7YHO"),
    ]),
  })
  @ApiInternalServerErrorResponse({
    description: buildErrorHtml([
      errorBuilder.transaction.wrongImplement(TxTypes.TRIP, "TripService/setTipFromUserApp"),
    ]),
  })
  async setTip(
    @Param("transactionId", new JoiValidationPipe(Joi.string().required())) transactionId: string,
    @Body(new JoiValidationPipe(setTipRequestSchema)) setTipBodyDto: SetTipBodyDto,
    @Req() req: Request,
  ): Promise<Tx> {
    const maxTipsAllowedAmount = await this.appDatabaseService.configurationRepository().getMaxTipsAllowedAmount();
    if (setTipBodyDto.tip > maxTipsAllowedAmount) {
      throw errorBuilder.validation.failed(`Tip amount is greater than ${maxTipsAllowedAmount}`);
    }
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.meTransactionService.setTip(transactionId, user.user_id, setTipBodyDto.tip);
  }

  /**
   * Set Rating by transaction id
   * @param req Request
   * @returns tx Tx
   */
  @Patch(":transactionId/rating")
  @ApiOperation({ summary: "Set Rating by transaction id" })
  @ApiResponse({ status: 200, description: "Set Rating by transaction id", type: Tx })
  @ApiNotFoundResponse({
    description: buildErrorHtml([errorBuilder.transaction.notFound("009d13ed-3bae-4f3c-bd37-26686ca23d30")]),
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([
      errorBuilder.user.missing(),
      errorBuilder.transaction.trip.tripNotPairedWithUser("009d13ed-3bae-4f3c-bd37-26686ca23d30", "IkCO7YHO"),
    ]),
  })
  @ApiInternalServerErrorResponse({
    description: buildErrorHtml([errorBuilder.transaction.wrongImplement(TxTypes.TRIP, "TripService/setRating")]),
  })
  async setRating(
    @Param("transactionId", new JoiValidationPipe(Joi.string().required())) transactionId: string,
    @Body(new JoiValidationPipe(setRatingRequestSchema)) setRatingBodyDto: SetRatingBodyDto,
    @Req() req: Request,
  ): Promise<Tx> {
    const user = req.user;
    if (!user) {
      throw errorBuilder.user.missing();
    }
    return this.meTransactionService.setRating(transactionId, user.user_id, setRatingBodyDto.rating);
  }

  /**
   * Add Tx Event
   * @param req Request
   * @returns tx Tx
   */
  @Post(":transactionId/event")
  @ApiOperation({ summary: "Add Tx Event" })
  @ApiResponse({ status: 200, description: "Add Tx Event" })
  @ApiNotFoundResponse({
    description: buildErrorHtml([
      errorBuilder.transaction.notFound("009d13ed-3bae-4f3c-bd37-26686ca23d30"),
      errorBuilder.user.notFoundInSql("IkCO7YHOJ3c073P8RihpRZxX3yC2"),
    ]),
  })
  @ApiExtraModels(AddEventBodyDefault, AddEventBodyHailingMerchantAcceptsOrder, AddEventBodyHailingUserUpdatesOrder)
  @ApiBody({
    schema: {
      anyOf: refs(AddEventBodyDefault, AddEventBodyHailingMerchantAcceptsOrder, AddEventBodyHailingUserUpdatesOrder),
    },
    examples: {
      [TxEventType.HAILING_USER_CREATES_ORDER]: {
        description: "Hailing user creates order",
        value: {
          type: TxEventType.HAILING_USER_CREATES_ORDER,
        },
      },
      [TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER]: {
        description: "Hailing merchant accepts order. Adds the merchant foreign key to the tx.",
        value: {
          type: TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER,
          content: {
            phoneNumber: "+85211111111",
          },
        },
      },
      [TxEventType.HAILING_USER_CANCELS_ORDER]: {
        description:
          "Hailing user cancels order. If the driver accepted more than 3 minutes ago the user will be charged a cancellation fee. Response includes breakdown of charges.",
        value: {
          type: TxEventType.HAILING_USER_CANCELS_ORDER,
        },
      },
      [TxEventType.HAILING_USER_UPDATES_ORDER]: {
        description: "Hailing user updates order. Updates the order metadata with new data such as boost amount.",
        value: {
          type: TxEventType.HAILING_USER_UPDATES_ORDER,
          content: {
            boostAmount: 150,
            minMaxFareCalculations: {
              boost: 150,
            },
          },
        },
      },
      [TxEventType.HAILING_MERCHANT_CANCELS_ORDER]: {
        description: "Hailing merchant cancels order. Removes the merchant foreign key from the tx.",
        value: {
          type: TxEventType.HAILING_MERCHANT_CANCELS_ORDER,
        },
      },
      [TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED]: {
        description: "Hailing merchant comfirms pick up.",
        value: {
          type: TxEventType.HAILING_MERCHANT_PICK_UP_CONFIRMED,
          content: {
            txId: "009d13ed-3bae-4f3c-bd37-26686ca23d30",
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async addTxEvent(
    @Param("transactionId", new JoiValidationPipe(Joi.string().required())) transactionId: string,
    @Body(new JoiValidationPipe(addEventBodySchemaMe.required())) addEventBody: AddEventBodyMe,
    @Req() req: Request,
  ): Promise<TxEvent> {
    const user = req.user;
    // if (!user) {
    //   throw errorBuilder.user.missing();
    // }
    return this.meTransactionService.addEvent(transactionId, addEventBody, user);
  }
}

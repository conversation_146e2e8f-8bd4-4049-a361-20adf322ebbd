import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import Jo<PERSON> from "joi";

import { TxMetadata } from "../../../../transaction/dto/txMetadata.dto";
import { TxTypes } from "../../../../transaction/dto/txType.dto";

export class UserTransactionsQueryDto {
  @ApiPropertyOptional()
  take?: number;
  @ApiPropertyOptional()
  skip?: number;
}
export const userTransactionsQuerySchema = Joi.object({
  take: Joi.number().integer().min(1).allow(null).optional(),
  skip: Joi.number().integer().min(0).allow(null).optional(),
});

export class UserTransaction {
  id: string;
  createdAt: Date;
  total: number;
  type: TxTypes;
  date: Date;
  metadata: TxMetadata;
}

export class UserTransactionsResponseDto {
  @ApiProperty()
  transactions: UserTransaction[];
  @ApiProperty()
  count: number;
}

import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { MeTransactionController } from "./meTransaction.controller";
import { MeTransactionService } from "./meTransaction.service";
import { AppDatabaseModule } from "../../../appDatabase/appDatabase.module";
import { PaymentTxRepository } from "../../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../../database/repositories/tx.repository";
import { UserRepository } from "../../../database/repositories/user.repository";
import { TransactionModule } from "../../../transaction/transaction.module";

@Module({
  providers: [MeTransactionService, TxRepository, PaymentTxRepository, UserRepository],
  imports: [TransactionModule, AppDatabaseModule],
  controllers: [MeTransactionController],
  exports: [MeTransactionService],
})
export class MeTransactionModule {}

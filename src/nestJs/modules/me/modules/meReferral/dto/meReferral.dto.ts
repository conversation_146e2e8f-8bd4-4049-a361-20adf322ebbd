import { ApiProperty } from "@nestjs/swagger";
import Jo<PERSON> from "joi";

export type MeReferralRetrievalResponse = {
  code: string;
};

export class MeReferralLinkBody {
  @ApiProperty({ type: "string", example: "abc123", description: "Referral code", required: true })
  referralCode: string;
}

export const meReferralLinkBodySchema = Joi.object<MeReferralLinkBody>({
  referralCode: Joi.string().required(),
});

export type MeReferralReferredCountResponse = {
  count: number;
};

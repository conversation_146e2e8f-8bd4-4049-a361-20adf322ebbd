import { Body, Controller, Get, HttpStatus, Post, Req, Res } from "@nestjs/common";
import { ApiBadRequestResponse, ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Request, Response } from "express";

import { errorBuilder } from "@nest/modules/utils/utils/error.utils";
import { apiTags, buildErrorHtml } from "@nest/modules/utils/utils/swagger.utils";
import { JoiValidationPipe } from "@nest/modules/validation/validationPipe.service";

import {
  MeReferralLinkBody,
  meReferralLinkBodySchema,
  MeReferralReferredCountResponse,
  MeReferralRetrievalResponse,
} from "./dto/meReferral.dto";
import { MeReferralService } from "./meReferral.service";

@ApiBearerAuth()
@Controller()
@ApiTags(...apiTags.me)
export class MeReferralController {
  constructor(private readonly meReferralService: MeReferralService) {}
  /**
   * Request a referral code
   */
  @Get()
  @ApiOperation({
    summary: "Fetch user referral code",
  })
  @ApiResponse({ status: 200, description: "Fetch user referral code" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async retrieveReferralCode(@Req() req: Request): Promise<MeReferralRetrievalResponse> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }
    return this.meReferralService.getReferralCode(user.uid);
  }

  /**
   * Link to a referral code
   */
  @Post("link")
  @ApiOperation({
    summary: "Link to another user's referral code",
  })
  @ApiResponse({ status: HttpStatus.OK, description: "Link to another user's referral code" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async linkReferralCode(
    @Body(new JoiValidationPipe(meReferralLinkBodySchema)) body: MeReferralLinkBody,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }
    await this.meReferralService.linkReferralCode(user.uid, body.referralCode);
    res.sendStatus(HttpStatus.OK);
  }

  /**
   * Get number of referrals
   */
  @Get("referred")
  @ApiOperation({
    summary: "Get number of referred users",
  })
  @ApiResponse({ status: HttpStatus.OK, description: "Get number of referred users" })
  @ApiBadRequestResponse({
    description: buildErrorHtml([errorBuilder.user.missing()]),
  })
  async getReferredUsersCount(@Req() req: Request): Promise<MeReferralReferredCountResponse> {
    const user = req.user;
    if (!user || !req.headers.authorization) {
      throw errorBuilder.user.missing();
    }
    return this.meReferralService.getReferredUsersCount(user.uid);
  }
}

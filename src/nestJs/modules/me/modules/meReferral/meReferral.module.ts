import { Module } from "@nestjs/common";

import { UserModule } from "@nest/modules/user/user.module";

import { MeReferralController } from "./meReferral.controller";
import { MeReferralService } from "./meReferral.service";

@Module({
  imports: [UserModule],
  providers: [MeReferralController, MeReferralService],
  controllers: [MeReferralController],
  exports: [MeReferralController, MeReferralService],
})
export class MeReferralModule {}

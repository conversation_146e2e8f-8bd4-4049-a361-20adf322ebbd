import { Injectable } from "@nestjs/common";

import { UserService } from "@nest/modules/user/user.service";

import { MeReferralReferredCountResponse, MeReferralRetrievalResponse } from "./dto/meReferral.dto";

@Injectable()
export class MeReferralService {
  constructor(private readonly userService: UserService) {}

  async getReferralCode(userId: string): Promise<MeReferralRetrievalResponse> {
    const code = await this.userService.getOrCreateReferralCode(userId);
    return { code };
  }

  async linkReferralCode(userId: string, referralCode: string): Promise<void> {
    await this.userService.linkReferralCode(userId, referralCode);
    return undefined;
  }

  async getReferredUsersCount(userId: string): Promise<MeReferralReferredCountResponse> {
    const count = await this.userService.getReferredUsersCount(userId);
    return { count };
  }
}

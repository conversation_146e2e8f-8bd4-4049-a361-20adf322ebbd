import { <PERSON>du<PERSON> } from "@nestjs/common";

import { UserModule } from "@nest/modules/user/user.module";

import { MeLogoutController } from "./meLogout.controller";
import { MeLogoutService } from "./meLogout.service";
import { UserRepository } from "../../../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../../../database/repositories/userNotificationToken.repository";
import { MeNotificationTokenModule } from "../meNotificationToken/meNotificationToken.module";

@Module({
  providers: [MeLogoutController, MeLogoutService, UserRepository, UserNotificationTokenRepository],
  imports: [MeNotificationTokenModule, UserModule],
  controllers: [MeLogoutController],
  exports: [MeLogoutController, MeLogoutService],
})
export class MeLogoutModule {}

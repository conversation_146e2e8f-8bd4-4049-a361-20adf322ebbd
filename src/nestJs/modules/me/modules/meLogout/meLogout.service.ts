import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";

import { UserRepository } from "../../../database/repositories/user.repository";
import { MeNotificationTokenService } from "../meNotificationToken/meNotificationToken.service";

@Injectable()
export class MeLogoutService {
  constructor(
    @InjectRepository(UserRepository)
    private userRepository: UserRepository,
    private readonly meNotificationTokenService: MeNotificationTokenService,
  ) {}

  async logout(appDatabaseId: string, token: string): Promise<void> {
    const user = await this.userRepository.findAppUserById(appDatabaseId);
    if (!user) {
      return;
    }
    await this.meNotificationTokenService.removeNotificationToken(user, token);
  }
}

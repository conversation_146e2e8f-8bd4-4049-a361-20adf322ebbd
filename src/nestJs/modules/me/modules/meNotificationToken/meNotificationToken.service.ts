import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { InsertResult } from "typeorm";

import { UpsertNotificationTokenBody } from "./dto/notificationToken.dto";
import User from "../../../database/entities/user.entity";
import { UserNotificationTokenRepository } from "../../../database/repositories/userNotificationToken.repository";

@Injectable()
export class MeNotificationTokenService {
  constructor(
    @InjectRepository(UserNotificationTokenRepository)
    private userNotificationTokenRepository: UserNotificationTokenRepository,
  ) {}

  async upsertNotificationToken(content: UpsertNotificationTokenBody, user: User): Promise<InsertResult> {
    return this.userNotificationTokenRepository.upsert(
      { token: content.token, lastUpdateDate: new Date(), user: { id: user.id } },
      ["token"],
    );
  }

  async removeNotificationToken(user: User, token: string): Promise<void> {
    const existingToken = await this.userNotificationTokenRepository.findOne({
      where: {
        user: { id: user.id },
        token: token,
      },
    });

    if (existingToken) {
      await this.userNotificationTokenRepository.delete(existingToken.id);
    }
  }
}

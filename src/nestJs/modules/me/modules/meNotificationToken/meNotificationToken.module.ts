import { <PERSON>du<PERSON> } from "@nestjs/common";

import { MeNotificationTokenService } from "./meNotificationToken.service";
import { UserRepository } from "../../../database/repositories/user.repository";
import { UserNotificationTokenRepository } from "../../../database/repositories/userNotificationToken.repository";

@Module({
  providers: [MeNotificationTokenService, UserNotificationTokenRepository, UserRepository],
  imports: [],
  exports: [MeNotificationTokenService],
})
export class MeNotificationTokenModule {}

import Jo<PERSON> from "joi";

export enum ProfileAuditActionType {
  FIREBASE_AUTH_SIGN_UP = "FIREBASE_AUTH_SIGN_UP",
  FIREBASE_AUTH_SIGN_IN = "FIREBASE_AUTH_SIGN_IN",
  FIREBASE_AUTH_PROFILE_UPDATE = "FIREBASE_AUTH_PROFILE_UPDATE",
  FIREBASE_AUTH_EMAIL_VERIFIED = "FIREBASE_AUTH_EMAIL_VERIFIED",

  FIRESTORE_EMAIL_UPDATE = "FIRESTORE_EMAIL_UPDATE",
  FIRESTORE_MARKETING_SMS_UPDATE = "FIRESTORE_MARKETING_SMS_UPDATE",
  FIRESTORE_MARKETING_EMAIL_UPDATE = "FIRESTORE_MARKETING_EMAIL_UPDATE",
  FIRESTORE_MARKETING_PUSH_UPDATE = "FIRESTORE_MARKETING_PUSH_UPDATE",

  ME_API_SET_PIN = "ME_API_SET_PIN",
  ME_API_VERIFY_PIN = "ME_API_VERIFY_PIN",
  ME_API_UPDATE_PIN = "ME_API_UPDATE_PIN",
  ME_API_CREARE_PAYMENT_INSTRUMENT = "ME_API_CREARE_PAYMENT_INSTRUMENT",
  ME_API_UPDATE_PAYMENT_INSTRUMENT = "ME_API_UPDATE_PAYMENT_INSTRUMENT",
}

export const profileAuditActionTypeSchema = Joi.string<ProfileAuditActionType>().valid(
  ...Object.values(ProfileAuditActionType),
);

export enum ProfileAuditSourceType {
  FIREBASE_AUTH = "FIREBASE_AUTH",
  FIRESTORE = "FIRESTORE",
  ME_API = "ME_API",
  ADMIN = "ADMIN",
}

export const profileAuditSourceTypeSchema = Joi.string<ProfileAuditSourceType>().valid(
  ...Object.values(ProfileAuditSourceType),
);

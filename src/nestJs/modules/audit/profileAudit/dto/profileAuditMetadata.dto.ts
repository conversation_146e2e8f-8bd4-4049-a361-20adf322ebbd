import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { UserDocument } from "../../../appDatabase/documents/user.document";

export class ProfileAuditFirebaseAuthMetadata {
  @ApiProperty()
  ip: string;
  @ApiProperty()
  deviceInfo: string;
  @ApiProperty()
  messageId: string;
}

export class SetPinApiMetadata {
  @ApiProperty()
  newPin: string;
}

export class VerifyPinApiMetadata {
  @ApiProperty()
  pinInSql: string;
  @ApiProperty()
  pinToVerify: string;
  @ApiProperty()
  verified: boolean;
}

export class UpdatePinApiMetadata {
  @ApiProperty()
  oldPin: string;
  @ApiProperty()
  newPin: string;
}

export type ProfileAuditMeApiMetadata = SetPinApiMetadata | VerifyPinApiMetadata;

export type ProfileAuditMetadata = ProfileAuditFirebaseAuthMetadata | UserDocument | ProfileAuditMeApiMetadata;

export const profileAuditMetadataSchema = Joi.object<ProfileAuditMetadata>();

import { Body, Controller, Injectable, Param, Post } from "@nestjs/common";
import { ApiHeader, ApiTags } from "@nestjs/swagger";

import { apiTags } from "@nest/modules/utils/utils/swagger.utils";

import { UpdateFleetOrderDelegatee } from "./delegatees/UpdateFleetOrderDelegatee";
import { UpdateFleetOrderBody } from "./dto/fleetOrder.dto";

@ApiHeader({
  name: "X-API-Key",
  description: "API Key",
  required: true,
})
@ApiTags(...apiTags.cloudTask)
@Injectable()
@Controller("cloud-task/fleetOrders")
export class CloudTaskFleetOrderController {
  constructor(private readonly updateFleetOrderDelegatee: UpdateFleetOrderDelegatee) {}

  @Post()
  async updateFleetOrder(@Body() body: UpdateFleetOrderBody) {
    return this.updateFleetOrderDelegatee.execute(body);
  }

  @Post("/:id")
  async updateFleetOrderById(@Param("id") id: string) {
    return this.updateFleetOrderDelegatee.execute({ fleetOrderId: id });
  }
}

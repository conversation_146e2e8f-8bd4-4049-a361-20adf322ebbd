import { Inject, Injectable } from "@nestjs/common";

import FleetOrderEntity, { FleetOrderStatus } from "@nest/modules/database/entities/fleetOrder.entity";
import { SyncabApiService } from "@nest/modules/syncabApi/syncabApi.service";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { UpdateFleetOrderBody } from "../dto/fleetOrder.dto";
import { IUpdateFleetOrderDelegatee, IUpdateFleetOrderResponse } from "../interface";

@Injectable()
export class SyncabUpdateFleetOrderDelegatee implements IUpdateFleetOrderDelegatee {
  constructor(
    private readonly syncabApiService: SyncabApiService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  private getStatusMap: Record<string, FleetOrderStatus> = {
    "searching-for-driver": FleetOrderStatus.MATCHING,
    "en-route": FleetOrderStatus.ACCEPT,
    arrived: FleetOrderStatus.ARRIVED,
    "on-board": FleetOrderStatus.ON_GOING,
    completed: FleetOrderStatus.COMPLETED,
    "driver-not-found": FleetOrderStatus.TIMED_OUT,
    "heading-to": FleetOrderStatus.APPROACHING,
    "driver-canceled": FleetOrderStatus.MATCHING,
  };

  async execute(_body: UpdateFleetOrderBody, fleetOrder: FleetOrderEntity): Promise<IUpdateFleetOrderResponse> {
    this.logger.info("SyncabUpdateFleetOrderDelegatee/execute-start", { fleetOrder });

    const booking = await this.syncabApiService.getFleetOrder(fleetOrder.txId);

    if (!booking) {
      throw errorBuilder.fleetTaxi.noFleetOrderFound();
    }

    const status = this.getStatusMap[booking.status];

    const result = {
      status: status || null,
      thirdPartyStatus: booking.status,
      snapshot: booking,
      driverName: booking.driver?.first_name,
      driverPhoneNumber: booking.driver?.phone_number,
      driverLicensePlate: booking.driver?.license_number,
      driverLocation: {
        lat: booking.location?.latitude,
        lng: booking.location?.longitude,
      },
    };

    this.logger.info("SyncabUpdateFleetOrderDelegatee/execute-end", { fleetOrder, status, result });

    return result;
  }

  async getBookingReceiptSnapshot(fleetOrder: FleetOrderEntity) {
    const data = await this.syncabApiService.getFleetReceipt(fleetOrder.txId);
    const cancellationFee = data.breakdown.find((item) => item.type === "cancellation-fee")?.amount ?? 0;
    return { bookingFee: data.amount, cancellationFee, snapshot: data };
  }
}

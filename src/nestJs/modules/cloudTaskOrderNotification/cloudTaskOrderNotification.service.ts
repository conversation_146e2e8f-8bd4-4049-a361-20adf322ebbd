import { Injectable } from "@nestjs/common";

import { TeamOrderNotificationService } from "../teamOrderNotification/teamOrderNotification.service";

@Injectable()
export class CloudTaskOrderNotificationService {
  constructor(private teamOrderNotificationService: TeamOrderNotificationService) {}

  async sendPickupReminderNotification(txId: string) {
    return this.teamOrderNotificationService.sendPickupReminderNotification(txId);
  }
}

import { Controller, HttpCode, HttpStatus, Post, Query } from "@nestjs/common";
import { ApiHeader, ApiTags } from "@nestjs/swagger";

import { apiTags } from "@nest/modules/utils/utils/swagger.utils";

import { CloudTaskOrderNotificationService } from "./cloudTaskOrderNotification.service";

@ApiHeader({
  name: "x-api-key",
  description: "API Key to authenticate the call",
  required: true,
})
@ApiTags(...apiTags.cloudTask)
@Controller("cloud-task/order-notifications")
export class CloudTaskOrderNotificationController {
  constructor(private readonly cloudTaskOrderNotificationService: CloudTaskOrderNotificationService) {}

  @Post("pickup-reminder")
  @HttpCode(HttpStatus.OK)
  async sendPickupReminderNotification(@Query("txId") txId: string) {
    return this.cloudTaskOrderNotificationService.sendPickupReminderNotification(txId);
  }
}

import { Injectable } from "@nestjs/common";

import { CampaignService } from "@nest/modules/campaign/campaign.service";
import Payout from "@nest/modules/database/entities/payout.entity";
import { TxDiscounts } from "@nest/modules/discount/dto/discount.dto";
import { TxHailingMetadata } from "@nest/modules/transaction/dto/txMetadata.dto";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

import { AppDatabaseService } from "../../../../appDatabase/appDatabase.service";
import {
  NotificationDocument,
  NotificationDocumentType,
  NotificationStatus,
} from "../../../../appDatabase/documents/driver.document";
import { LockDocument } from "../../../../appDatabase/documents/lock.document";
import { Rating, TripDocument } from "../../../../appDatabase/documents/trip.document";
import { BankResponseLineItemStatus, PayoutBankFileRow } from "../../../../bank/dto/payoutBankFile.dto";
import PaymentTx from "../../../../database/entities/paymentTx.entity";
import Tx from "../../../../database/entities/tx.entity";
import { LocalizedLanguage } from "../../../../location/dto/location.dto";
import {
  notificationTemplate,
  NotificationType,
} from "../../../../message/messageFactory/modules/notification/notification.dto";
import { PaymentInformationStatus } from "../../../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../../payment/dto/paymentInformationType.dto";
import { errorBuilder } from "../../../../utils/utils/error.utils";
import { LanguageOption } from "../../../../validation/dto/language.dto";
import { isTxHailing } from "../../../dto/tx.dto";
import { ReceiptLanguageType, TxReceipt } from "../../../dto/txReceipt.dto";
import TransactionFactoryInterface from "../../transactionFactoryInterface";

/**
 * TransactionHailing service
 */
@Injectable()
export class TransactionHailingService implements TransactionFactoryInterface {
  constructor(
    private readonly appDatabaseService: AppDatabaseService,
    private readonly campaignService: CampaignService,
    private readonly logger: LoggerServiceAdapter,
  ) {}
  updateHailingTripWithDiscounts(tx: Tx, hailingTx?: Tx, meterId?: string): Promise<void> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.updateHailingTripWithDiscounts");
  }
  calculateDiscountsAndUpdateTx(
    tx: Tx,
    discounts: TxDiscounts,
  ): Promise<{ updatedTx: Tx; isDiscountMatch: boolean; discountAmountFromFirestore?: number }> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.calculateDiscounts");
  }
  calculateDiscountAndUpdateTx(
    tx: Tx,
    rule: string,
  ): Promise<{ updatedTx: Tx; isDiscountMatch: boolean; discountAmountFromFirestore?: number }> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.calculateDiscount");
  }
  async resetDiscountsForHailingRequest(tx: Tx): Promise<void> {
    if (!isTxHailing(tx)) {
      this.logger.error("[resetDiscountsForHailingRequest] Invalid hailing tx type", { tx });
      return;
    }
    // Find discounts for the hailing request
    const hailingRequest: TxHailingMetadata = tx.metadata;
    if (hailingRequest && hailingRequest.discounts) {
      if (hailingRequest.discounts.discountIdThirdParty) {
        await this.campaignService.resetOrFailDiscountById(hailingRequest.discounts.discountIdThirdParty);
      }
      if (hailingRequest.discounts.discountIdDash) {
        await this.campaignService.resetOrFailDiscountById(hailingRequest.discounts.discountIdDash);
      }
    }
  }

  /**
   * Method where we check if the transaction has customer information, like phone number
   * @param tx Tx
   * @returns boolean
   */
  hasCustomerInformation(tx: Tx): boolean {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.hasCustomerInformation");
  }
  /**
   * check if the tx is pay with Dash
   * @param tx Tx
   * @returns boolean
   */
  isPayWithDash(tx: Tx): boolean {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.isPayWithDash");
  }
  /**
   * get expiresAt for trip in driver in firestore
   * @param passedTx Tx
   * @returns Date
   */
  getExpiresAt(passedTx: Tx): Promise<Date> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.getExpiresAt");
  }
  /**
   * Send event in pubsub when payment is captured successfully to send receipt to customer
   * @param paymentTx PaymentTx
   * @param trip TripDocument
   * @returns string
   */
  sendReceipt(tx: Tx, paymentTx: PaymentTx): Promise<string> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.sendReceipt");
  }

  /**
   * triggered on pre payment capture
   * @param tx Tx
   * @returns Promise<Tx>
   */
  prePaymentProcess(tx: Tx): Promise<Tx> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.prePaymentProcess");
  }

  /**
   * get monetary info for tx
   * @param tx Tx
   * @returns Promise<Tx>
   */
  getTxMonetary(tx: Tx): { total: number; dashFee: number; payoutAmount: number; billing: Record<string, any> } {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.getTxMonetary");
  }

  /**
   * triggered on post payment capture
   * @param tx Tx
   * @returns Promise<Tx>
   */
  postPaymentProcess(tx: Tx, success: boolean): Promise<Tx> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.postPaymentProcess");
  }

  shouldCopyTripToDriverTrip(tx: Tx, trip: TripDocument): Promise<boolean> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.shouldCopyTripToDriverTrip");
  }
  /**
   * triggered on post cash payment capture
   * @param tx Tx
   * @returns Promise<Tx>
   */
  postCashPaymentProcess(tx: Tx): Promise<Tx> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.postCashPaymentProcess");
  }
  postTxEndProcess(tx: Tx): Promise<Tx> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.postTxEndProcess");
  }

  postAdjustmentProcess(tx: Tx): Promise<Tx> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.postAdjustmentProcess");
  }
  /**
   * meterTripChangeHandler for meter trip change
   * @param {Change<DocumentSnapshot>}change change from firestore
   * @param {EventContext} context of the change
   */
  meterTripChange(
    tripId: string,
    dataAfter: FirebaseFirestore.DocumentData,
    dataBefore?: FirebaseFirestore.DocumentData,
  ) {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.)");
  }

  pushNotificationToUserWhenTripEnd(tripId: string, total: number, appDatabaseId: string) {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.pushNotificationToUserWhenTripEnd");
  }

  /**
   * meterTripCreateHandler for meter trip create
   * @param {string} meterId meter id
   * @param {string} tripId trip ip
   * @param {DocumentData} data FirebaseFirestore.DocumentData
   */
  meterTripCreate(meterId: string, tripId: string, data: FirebaseFirestore.DocumentData) {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.meterTripCreate");
  }

  /**
   * calculate the trip total amount, if incorrect, will skip capture
   * @param tx Tx
   * @returns { result: boolean; reason: string }
   */
  isTxCalculationCorrect(tx: Tx, isDirectSale: boolean = false): { result: boolean; reason: string } {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.isTxCalculationCorrect");
  }

  /**
   * should update tx metadata
   * @param currentTx Tx
   * @param newTx Tx
   * @returns boolean
   */
  shouldUpdateTxMetadata(currentTx: Tx, newTx: Tx): { shouldUpdate: boolean; condition: string } {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.shouldUpdateTxMetadata");
  }

  /**
   *
   * get tx ids to payout
   * @param txs Tx[]
   */
  getTxsToPayout(
    txs: Tx[],
    fileContent: PayoutBankFileRow[],
    payouts: Payout[],
  ): { completed: Tx[]; failed: Tx[]; bankProcessing: Tx[] } {
    const merchantsTxs: Record<string, Tx[]> = txs.reduce((acc: Record<string, Tx[]>, tx: Tx) => {
      const merchant = tx.payoutMerchant ?? tx.merchant;
      if (!merchant) {
        return acc;
      }
      if (!acc[merchant.phoneNumber]) {
        acc[merchant.phoneNumber] = [];
      }
      acc[merchant.phoneNumber].push(tx);
      return acc;
    }, {});
    this.logger.info("merchantsTxs", merchantsTxs);
    const txIdAndPayoutFilePair = payouts.flatMap((payout: Payout) =>
      payout.originalRequest.txIds.map((txId: string) => ({
        txId,
        batchFile: payout.batchFile,
      })),
    );
    this.logger.info("txIdAndPayoutFilePair", txIdAndPayoutFilePair);

    return fileContent.reduce(
      (result: { completed: Tx[]; failed: Tx[]; bankProcessing: Tx[] }, fileRow) => {
        if (
          fileRow.status === BankResponseLineItemStatus.COMPLETED ||
          fileRow.status === BankResponseLineItemStatus.CONFIRMED
        ) {
          if (!merchantsTxs[fileRow.merchantId]) {
            return result;
          }
          return merchantsTxs[fileRow.merchantId]?.reduce((resultTx, tx: Tx) => {
            if (
              txIdAndPayoutFilePair.find(
                (pair) => pair.txId === tx.id && pair.batchFile === fileRow.originalBatchFileName,
              )
            ) {
              resultTx.completed.push(tx);
            }
            return resultTx;
          }, result);
        } else if (fileRow.status === BankResponseLineItemStatus.RECEIVED) {
          if (!merchantsTxs[fileRow.merchantId]) {
            return result;
          }
          return merchantsTxs[fileRow.merchantId]?.reduce((resultTx, tx: Tx) => {
            if (
              txIdAndPayoutFilePair.find(
                (pair) => pair.txId === tx.id && pair.batchFile === fileRow.originalBatchFileName,
              )
            ) {
              resultTx.bankProcessing.push(tx);
            }
            return resultTx;
          }, result);
        }
        return merchantsTxs[fileRow.merchantId]?.reduce((resultTx, tx: Tx) => {
          if (
            txIdAndPayoutFilePair.find(
              (pair) => pair.txId === tx.id && pair.batchFile === fileRow.originalBatchFileName,
            )
          ) {
            resultTx.failed.push(tx);
          }
          return resultTx;
        }, result);
      },
      { completed: [], failed: [], bankProcessing: [] },
    );
  }

  /**
   * post payout process
   * @param txs Tx[]
   * @returns Promise<Tx[]>
   */
  async postPayoutProcess(txs: Tx[]): Promise<Tx[]> {
    if (txs.length < 1) return txs;

    const isPayoutToPayoutMerchant = txs.some((tx) => tx.payoutMerchant);
    const merchantPhones = txs.reduce((merchantPhones: Set<string>, tx) => {
      if (!tx.merchant) {
        return merchantPhones;
      }
      merchantPhones.add(tx.merchant.phoneNumber);
      return merchantPhones;
    }, new Set<string>());

    await Promise.allSettled(
      Array.from(merchantPhones).map((merchantPhoneNumber) => {
        const template = isPayoutToPayoutMerchant
          ? NotificationType.PAYOUT_PAID_TO_FLEET
          : NotificationType.PAYOUT_PAID;
        const notification: NotificationDocument = {
          created_on: new Date(),
          locale: LanguageOption.ZHHK,
          message: notificationTemplate[template].body,
          status: NotificationStatus.NEW,
          title: notificationTemplate[template].title,
          type: NotificationDocumentType.PAYOUT,
        };
        return this.appDatabaseService.driverNotificationsRepository(merchantPhoneNumber).collection.add(notification);
      }),
    );

    return txs;
  }

  /**
   * Generate Receipt based on tx
   * @param tx Tx
   * @returns txReceipt
   */
  getReceiptByTx(tx: Tx, language: ReceiptLanguageType): Promise<TxReceipt> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.getReceiptByTx");
  }

  /**
   * Traverse paymentTx and find the last payment tx matching the status and type
   * @param tx Tx
   * @param types PaymentTxType[] - types
   * @param status PaymentTxStatus
   */
  static getLastPaymentTxByStatusAndType(
    tx: Tx,
    types: PaymentInformationType[],
    status: PaymentInformationStatus,
  ): PaymentTx | undefined {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.):");
  }

  /**
   * update metadata of a tx
   * @param tx Tx
   * @param data Record<string, any>
   * @returns Promise<Tx>
   */
  updateMetadata(tx: Tx, data: Record<string, any>): Promise<Tx> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.updateMetadata");
  }

  /**
   * add to user/trip from meter/trip
   * @param tx Tx
   * @param appDatabaseId string
   * @returns Promise<Tx>
   */
  addToUserCurrentTx(tx: Tx, appDatabaseId: string): Promise<Tx> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.addToUserCurrentTx");
  }

  unlock(tx: Tx, userId: string): Promise<Tx> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.unlock");
  }

  lock(tx: Tx, userId: string, timeout: number): Promise<LockDocument> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.lock");
  }

  setTipFromUserApp(tx: Tx, userId: string, tip: number): Promise<Tx> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.setTipFromUserApp");
  }

  setRating(tx: Tx, userId: string, rating: Rating): Promise<Tx> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.setRating");
  }

  getTxDate(tx: Tx): Date {
    if (!isTxHailing(tx)) {
      throw errorBuilder.transaction.wrongImplement(tx.type, "TripService/getTxDate");
    }
    return tx.createdAt;
  }

  isTxAbleToPair(tx: Tx): boolean {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.isTxAbleToPair");
  }

  addExtraMetadata(tx: Tx, language?: LocalizedLanguage): Promise<Record<string, any>> {
    throw errorBuilder.factory.notImplemented("TransactionHailingService.addExtraMetadata");
  }
}

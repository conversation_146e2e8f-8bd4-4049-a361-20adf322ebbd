import { Module } from "@nestjs/common";

import { TransactionHailingModule } from "./modules/transactionHailing/transactionHailing.module";
import { TripModule } from "./modules/trip/trip.module";
import { TransactionFactoryService } from "./transactionFactory.service";

/**
 * PaymentFactory module
 */
@Module({
  imports: [TripModule, TransactionHailingModule],
  providers: [TransactionFactoryService],
  exports: [TransactionFactoryService],
})
export class TransactionFactoryModule {}

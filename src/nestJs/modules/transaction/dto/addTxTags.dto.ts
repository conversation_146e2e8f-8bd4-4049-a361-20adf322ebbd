import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { TxTagType, tagTypeSchema } from "./txTagType.dto";

export const addTxTagSchema = Joi.object({
  tags: Joi.array()
    .items(
      Joi.object({
        tag: tagTypeSchema.required(),
        note: Joi.string().optional(),
      }),
    )
    .required(),
});

export class TxTagItem {
  @ApiProperty()
  tag: TxTagType;
  @ApiProperty()
  note?: string;
}
export class AddTxTagBodyDto {
  @ApiProperty({ isArray: true, type: TxTagItem })
  tags: TxTagItem[];
}

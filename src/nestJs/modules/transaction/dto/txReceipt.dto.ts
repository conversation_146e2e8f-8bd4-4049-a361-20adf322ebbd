import { TxTypes } from "./txType.dto";
import { Billing, Location, Vehicle } from "../../appDatabase/documents/trip.document";
import { PaymentType } from "../../payment/dto/paymentType.dto";

/**
 * Represents a transaction receipt
 */
export class TxReceipt {
  id: string;
  type: TxTypes;
  language: string;
  paymentType?: PaymentType;
  paymentCardInformation: PaymentCardInformation;
  total: number;
  dashFee: number;
}

/**
 * Represents a transaction receipt of Trip type
 */
export class TxReceiptTrip extends TxReceipt {
  type: TxTypes.TRIP;
  licensePlate: string;
  tripStart: Date;
  tripEnd: Date;
  locationStart: Location; // GeoPoint maybe?
  locationEnd: Location;
  locationStartAddress: string;
  locationEndAddress: string;

  // in meter
  distance: number;
  waitTime: number;
  adjustment: number;
  isDashMeter: boolean;
  billing?: Billing;
  vehicle?: Vehicle;

  //$ related
  fare: number;
  extra: number;
  dashTips: number;
  tripTotal: number;

  //discount related
  discountAmount: number;
  campaignName: string;

  //hailing
  additionalBookingFee?: number;
  boostAmount?: number;
  isHailing: boolean;
}

export class PaymentCardInformation {
  cardType?: string; // Trip's metadata is just a string, unless we look up from enum
  maskedPan?: string;
}

export enum ReceiptLanguageType {
  ENHK = "en-HK",
  ZHHK = "zh-HK",
}
export const receiptLanguageReplaceRegex = new RegExp(`^(?!${Object.values(ReceiptLanguageType).join("$|")}$).+`);

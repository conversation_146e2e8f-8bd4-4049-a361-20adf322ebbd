import Joi from "joi";

/**
 * Tx types enum
 */
export enum TxTypes {
  TRIP = "TRIP",
  TX_ADJUSTMENT = "TX_ADJUSTMENT",
  CARD_VERIFICATION = "CARD_VERIFICATION",
  HAILING_REQUEST = "HAILING_REQUEST",
}

export const txTypesSchema = Joi.string<TxTypes>().valid(...Object.values(TxTypes));

export enum SubTxTypes {
  HAILING = "HAILING",
  STREET = "STREET",
}

export const subTxTypesSchema = Joi.string<SubTxTypes>().valid(...Object.values(SubTxTypes));

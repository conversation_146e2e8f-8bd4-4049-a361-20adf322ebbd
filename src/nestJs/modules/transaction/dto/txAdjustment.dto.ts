import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export enum DASH_FEE_RECALCULATION {
  DISCOUNTED_AMOUNT = "DISCOUNTED_AMOUNT", // case7
  ADJUSTED_AMOUNT = "ADJUSTED_AMOUNT",
  ORIGINAL_AMOUNT = "ORIGINAL_AMOUNT",
  CUSTOM_AMOUNT = "CUSTOM_AMOUNT",
  FULL_REFUND = "FULL_REFUND",
}

export const DashFeeWithDescription: Record<DASH_FEE_RECALCULATION, string> = {
  [DASH_FEE_RECALCULATION.DISCOUNTED_AMOUNT]: "<PERSON><PERSON><PERSON> recalculated based on discounted amount",
  [DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT]: "Dash<PERSON>ee recalculated based on adjusted amount",
  [DASH_FEE_RECALCULATION.ORIGINAL_AMOUNT]: "<PERSON><PERSON>ee recalculated based on original amount",
  [DASH_FEE_RECALCULATION.CUSTOM_AMOUNT]: "Adjust Payout amount and DashFee based on custom amount",
  [DASH_FEE_RECALCULATION.FULL_REFUND]: "Refund full amount including DashFee",
};

export class TxAdjustmentAmountDto {
  @ApiProperty()
  amount: number;

  @ApiProperty({
    required: false,
    default: "",
  })
  reason: string;

  @ApiProperty({
    enum: [DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT, DASH_FEE_RECALCULATION.ORIGINAL_AMOUNT],
  })
  type: DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT | DASH_FEE_RECALCULATION.ORIGINAL_AMOUNT;
}

export class TxAdjustmentCustomAmountDto {
  @ApiProperty()
  dashFee: number;

  @ApiProperty()
  payoutAmount: number;

  @ApiProperty({
    required: false,
    default: "",
  })
  reason: string;

  @ApiProperty({
    type: DASH_FEE_RECALCULATION.CUSTOM_AMOUNT,
  })
  type: DASH_FEE_RECALCULATION.CUSTOM_AMOUNT;
}

export class TxAdjustmentDiscountDto {
  @ApiProperty()
  discount: number;

  @ApiProperty({
    required: false,
    default: "",
  })
  reason: string;

  @ApiProperty({
    type: DASH_FEE_RECALCULATION.DISCOUNTED_AMOUNT,
  })
  type: DASH_FEE_RECALCULATION.DISCOUNTED_AMOUNT;
}

export class TxAdjustmentFullRefundDto {
  @ApiProperty({
    required: false,
    default: "",
  })
  reason: string;

  @ApiProperty({
    type: DASH_FEE_RECALCULATION.FULL_REFUND,
  })
  type: DASH_FEE_RECALCULATION.FULL_REFUND;
}

export type TxAdjustmentCreateDto =
  | TxAdjustmentAmountDto
  | TxAdjustmentCustomAmountDto
  | TxAdjustmentDiscountDto
  | TxAdjustmentFullRefundDto;

// {convert: false} is used to prevent Joi from converting strings to numbers, or precision discrepancies

export const txAdjustmentAmountSchema = Joi.object<TxAdjustmentAmountDto>({
  type: Joi.string().valid(DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT, DASH_FEE_RECALCULATION.ORIGINAL_AMOUNT).required(),
  reason: Joi.string().optional().default(""),
  amount: Joi.number().precision(1).prefs({ convert: false }).required(),
}).required();

export const txAdjustmentCustomAmountSchema = Joi.object<TxAdjustmentCustomAmountDto>({
  type: Joi.string().valid(DASH_FEE_RECALCULATION.CUSTOM_AMOUNT).required(),
  reason: Joi.string().optional().default(""),
  dashFee: Joi.number().precision(1).prefs({ convert: false }).required(),
  payoutAmount: Joi.number().precision(1).prefs({ convert: false }).required(),
}).required();

export const txAdjustmentDiscountSchema = Joi.object<TxAdjustmentDiscountDto>({
  type: Joi.string().valid(DASH_FEE_RECALCULATION.DISCOUNTED_AMOUNT).required(),
  reason: Joi.string().optional().default(""),
  discount: Joi.number().precision(1).prefs({ convert: false }).required(),
}).required();

export const txAdjustmentFullRefundSchema = Joi.object<TxAdjustmentFullRefundDto>({
  type: Joi.string().valid(DASH_FEE_RECALCULATION.FULL_REFUND).required(),
  reason: Joi.string().optional().default(""),
}).required();

export const txAdjustmentCreateSchema = Joi.alternatives<TxAdjustmentCreateDto>(
  txAdjustmentAmountSchema,
  txAdjustmentCustomAmountSchema,
  txAdjustmentDiscountSchema,
  txAdjustmentFullRefundSchema,
).required();

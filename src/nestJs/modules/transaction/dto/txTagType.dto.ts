import Jo<PERSON> from "joi";

export enum TxTagType {
  DISPUTE = "DISPUTE",
  AMOUNT_MISMATCH = "AMOUNT_MISMATCH",
  UNABLE_TO_VOID = "UNABLE_TO_VOID",
  UNABLE_TO_CAPTURE = "UNABLE_TO_CAPTURE",
  OPEN_AUTH = "OPEN_AUTH",
  PAYOUT_REJECTED = "PAYOUT_REJECTED",
  OPEN_PROCESSING = "OPEN_PROCESSING",
  OPEN_SALE = "OPEN_SALE",
  ALREADY_SOLD = "ALREADY_SOLD",
  UNABLE_TO_SELL = "UNABLE_TO_SELL",
  DISCOUNT_MISMATCH = "DISCOUNT_MISMATCH",
  NO_PAYMENT_NEEDED = "NO_PAYMENT_NEEDED",
}

/**
 * Tag Type Schema
 */
export const tagTypeSchema = Joi.string<TxTagType>().valid(...Object.values(TxTagType));

import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";

import { AppDatabaseService } from "@nest/modules/appDatabase/appDatabase.service";
import { PlatformMerchantType } from "@nest/modules/database/entities/merchant.entity";
import PaymentTx from "@nest/modules/database/entities/paymentTx.entity";
import { CancelFleetOrderDelegatee } from "@nest/modules/me/modules/meFleetTaxi/delegatees/CancelFleetOrderDelegatee";
import { BookingReceiptSnapshot } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.interface";
import { PlatformType } from "@nest/modules/me/modules/meHailing/dto/meHailing.dto";
import dayjs from "@nest/modules/utils/dayjs";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

import Tx from "../../database/entities/tx.entity";
import TxEvent from "../../database/entities/txEvent.entity";
import { MerchantRepository } from "../../database/repositories/merchant.repository";
import { PaymentTxRepository } from "../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../database/repositories/tx.repository";
import { AddEventBody } from "../../me/modules/meTransaction/dto/addEvent.dto";
import { PaymentInformationStatus } from "../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../payment/dto/paymentInformationType.dto";
import { PaymentService } from "../../payment/payment.service";
import { PubSubService } from "../../pubsub/pubsub.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { TxHailingRequest } from "../dto/tx.dto";
import {
  TxEventType,
  isHailingUserCancelsOrderEvent,
  isHailingUserUpdatesOrderEvent,
  isHailingMerchantAcceptsOrderEvent,
  isHailingMerchantCancelsOrderEvent,
  HailingMerchantAcceptsOrderEvent,
  HailingUserCancelsOrderEvent,
  HailingUserUpdatesOrderEvent,
  HailingMerchantPickUpConfirmedOrderEvent,
  isHailingMerchantPickUpConfirmedOrderEvent,
  isHailingHailingScheduledOrderTimeoutOrderEvent,
  HailingAdminCancelsOrderEvent,
  isHailingAdminCancelsOrderEvent,
  isHailingMerchantArrivedDestinationEvent,
  HailingMerchantArrivedDestinationEvent,
  isHailingOrderCompletedEvent,
  HailingOrderCompletedEvent,
  isHailingMerchantApproachingDestinationEvent,
  HailingMerchantApproachingDestinationEvent,
} from "../dto/txEventType.dto";
import { TxHailingRequestStatus } from "../dto/txHailingRequest.dto";
import { TxTypes } from "../dto/txType.dto";
import { TransactionFactoryService } from "../transactionFactory/transactionFactory.service";

/**
 * Transaction service
 */
@Injectable()
export class TransactionEventHailingRequestService {
  constructor(
    @InjectRepository(MerchantRepository) private merchantRepository: MerchantRepository,
    private paymentService: PaymentService,
    @InjectRepository(TxRepository) private txRepository: TxRepository,
    @InjectRepository(PaymentTxRepository) private paymentTxRepository: PaymentTxRepository,
    private pubsubService: PubSubService,
    private readonly transactionFactoryService: TransactionFactoryService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly appDatabaseService: AppDatabaseService,
    private readonly cancelFleetOrderDelegatee: CancelFleetOrderDelegatee,
  ) {}

  async addHailingEvent(tx: TxHailingRequest, createdBy: string, addEventBody: AddEventBody): Promise<TxEvent> {
    const txEvent = TxEvent.fromAddEvent(tx, createdBy, addEventBody);

    if (!txEvent) {
      throw errorBuilder.transaction.eventTypeNotSupported(tx.id, tx.type);
    }

    let toSaveTx = tx;

    if (isHailingUserCancelsOrderEvent(txEvent)) {
      toSaveTx = await this.addHailingEventUserCancelsOrder(toSaveTx, txEvent);
    } else if (isHailingUserUpdatesOrderEvent(txEvent)) {
      toSaveTx = await this.addHailingEventUserUpdatesOrder(toSaveTx, txEvent);
    } else if (isHailingMerchantAcceptsOrderEvent(txEvent)) {
      toSaveTx = await this.addHailingEventMerchantAcceptsOrder(toSaveTx, txEvent);
    } else if (isHailingMerchantCancelsOrderEvent(txEvent)) {
      toSaveTx = await this.addHailingEventMerchantCancelsOrder(toSaveTx);
    } else if (isHailingMerchantPickUpConfirmedOrderEvent(txEvent)) {
      toSaveTx = await this.addHailingMerchantPickUpConfirmedOrderEvent(toSaveTx, txEvent);
    } else if (isHailingHailingScheduledOrderTimeoutOrderEvent(txEvent)) {
      toSaveTx = await this.addHailingScheduledOrderTimeoutOrderEvent(toSaveTx);
    } else if (isHailingAdminCancelsOrderEvent(txEvent)) {
      toSaveTx = await this.addHailingAdminCancelsOrderEvent(toSaveTx, txEvent);
    } else if (isHailingMerchantArrivedDestinationEvent(txEvent)) {
      toSaveTx = await this.addHailingMerchantArrivedDestinationEvent(toSaveTx, txEvent);
    } else if (isHailingOrderCompletedEvent(txEvent)) {
      toSaveTx = await this.addHailingOrderCompletedEvent(toSaveTx, txEvent);
    } else if (isHailingMerchantApproachingDestinationEvent(txEvent)) {
      toSaveTx = await this.addHailingMerchantApproachingDestinationEvent(toSaveTx, txEvent);
    }

    if (!toSaveTx.txEvents) {
      toSaveTx.txEvents = [];
    }
    toSaveTx.txEvents.push(txEvent);

    await this.txRepository.save(toSaveTx);

    return txEvent;
  }

  async addHailingScheduledOrderTimeoutOrderEvent(tx: TxHailingRequest) {
    const nonVoidedAuthPaymentTx = tx.paymentTx?.find(
      (paymentTx) =>
        paymentTx.type === PaymentInformationType.AUTH && paymentTx.status === PaymentInformationStatus.SUCCESS,
    );

    const isFleet = tx?.metadata?.request?.platformType === PlatformType.FLEET;

    if (!nonVoidedAuthPaymentTx) {
      throw errorBuilder.payment.paymentTxNotFound(tx.id);
    }

    const voidPaymentTx = await this.paymentService.voidPayment(nonVoidedAuthPaymentTx?.id, "SYSTEM");

    // notification

    if (!tx.paymentTx) {
      tx.paymentTx = [];
    }
    tx.paymentTx.push(voidPaymentTx);

    tx.metadata.status = TxHailingRequestStatus.CANCELLED;
    tx.metadata.cancelledAt = new Date();

    if (isFleet) {
      await this.cancelFleetOrderDelegatee.execute(tx.id);
    }

    await this.transactionFactoryService.resetDiscountsForHailingRequest(tx);

    return tx;
  }

  /**
   * Pick up confirmed order event mutator
   * @param tx TxHailingRequest
   * @param txEvent HailingMerchantPickUpConfirmedOrderEvent
   * @returns tx TxHailingRequest
   */
  async addHailingMerchantPickUpConfirmedOrderEvent(
    tx: TxHailingRequest,
    txEvent: HailingMerchantPickUpConfirmedOrderEvent,
  ) {
    const meterId = txEvent.content.fleetMockMeterId ?? txEvent.content.meterId;
    this.logger.info("addHailingMerchantPickUpConfirmedOrderEvent", { tx, txEvent, meterId });
    let foundTx = await this.txRepository.findOneBy({ id: txEvent.content.txId });

    if (!foundTx) {
      this.logger.warn("Tx not found in sql, try to get from firestore and upsert", { tx, meterId });
      const tripDocument = await this.appDatabaseService.meterTripRepository(meterId).findOneById(txEvent.content.txId);
      if (!tripDocument) {
        throw errorBuilder.meter.tripNotFound(meterId, txEvent.content.txId);
      }
      const newTx = new Tx();
      newTx.id = txEvent.content.txId;
      newTx.txApp = tx.txApp;
      newTx.type = TxTypes.TRIP;
      newTx.merchant = tx.merchant;
      newTx.user = tx.user;
      newTx.metadata = tripDocument;
      await this.txRepository.upsert(newTx, { conflictPaths: { id: true } });
      foundTx = newTx;
    }

    await Promise.all(
      tx.paymentTx?.map((paymentTx) =>
        this.paymentTxRepository.update(paymentTx.id, { ...paymentTx, tx: { id: txEvent.content.txId } }),
      ) ?? [],
    );
    tx.paymentTx = [];

    tx.parentTx = { id: txEvent.content.txId } as Tx;

    await this.transactionFactoryService.updateHailingTripWithDiscounts(foundTx, tx, meterId);

    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.ON_GOING;
    }

    return tx;
  }

  /**
   * Merchant cancels order event mutator
   * @param tx TxHailingRequest
   * @returns tx TxHailingRequest
   */
  async addHailingEventMerchantCancelsOrder(tx: TxHailingRequest) {
    tx.merchant = null;
    tx.payoutMerchant = null;
    if (tx?.metadata?.licensePlate) {
      tx.metadata.licensePlate = undefined;
    }

    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.PENDING;
    }

    return tx;
  }

  /**
   * Merchant accepts order event mutator
   * @param tx TxHailingRequest
   * @param txEvent HailingMerchantAcceptsOrderEvent
   * @returns tx TxHailingRequest
   */
  async addHailingEventMerchantAcceptsOrder(tx: TxHailingRequest, txEvent: HailingMerchantAcceptsOrderEvent) {
    const platformMerchantType = txEvent.content.platformMerchantType
      ? txEvent.content.platformMerchantType
      : PlatformMerchantType.DASH;

    const merchant = await this.merchantRepository.findOne({
      where: { phoneNumber: txEvent.content.phoneNumber, platformMerchantType },
    });

    if (!merchant) {
      throw errorBuilder.transaction.merchantNotFound(txEvent.content.phoneNumber);
    }

    if (txEvent.content.fleetMockMeterId) {
      const meter = await this.appDatabaseService.meterRepository().findOneById(txEvent.content.fleetMockMeterId);
      if (!meter) {
        throw errorBuilder.meter.notFound(txEvent.content.fleetMockMeterId);
      }
      if (meter?.settings?.settleToFleet && meter?.settings?.fleetId) {
        const fleet = await this.appDatabaseService.fleetRepository().findOneById(meter.settings.fleetId);
        if (!fleet) {
          throw errorBuilder.meter.meterFleetNotFound(meter.id);
        }
        const payoutMerchant = await this.merchantRepository.findOne({ where: { id: fleet.merchantId } });

        if (payoutMerchant) {
          tx.payoutMerchant = payoutMerchant;
        }
      }
    }

    if (txEvent.content.licensePlate) {
      this.logger.info("addHailingEventMerchantAcceptsOrder-addLicensePlate", {
        tx,
        txEvent,
        licensePlate: txEvent.content.licensePlate,
      });
      tx.metadata.licensePlate = txEvent.content.licensePlate;
    }

    tx.merchant = merchant;

    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.ACCEPTED;
    }

    return tx;
  }

  async getShouldChargeCancellationFee(tx: TxHailingRequest) {
    this.logger.info("transactionEventHailingRequestService/getShouldChargeCancellationFee-start", { tx });
    let shouldChargeCancellationFee = false;
    let isFullCancellationFee = false;

    const lastHailingMerchantAcceptsOrderEvent = tx.findLastEventType(TxEventType.HAILING_MERCHANT_ACCEPTS_ORDER);
    const lastHailingMerchantCancelsOrderEvent = tx.findLastEventType(TxEventType.HAILING_MERCHANT_CANCELS_ORDER);

    const isScheduled = Boolean(tx?.metadata?.request?.time);
    const isMatched = Boolean(
      lastHailingMerchantAcceptsOrderEvent &&
        (!lastHailingMerchantCancelsOrderEvent ||
          lastHailingMerchantCancelsOrderEvent.createdAt < lastHailingMerchantAcceptsOrderEvent.createdAt),
    );
    const requestTime = dayjs(tx?.metadata?.request?.time).tz("Asia/Hong_Kong");
    const acceptedOrderTime = lastHailingMerchantAcceptsOrderEvent
      ? dayjs(lastHailingMerchantAcceptsOrderEvent.createdAt).toDate()
      : null;

    if (isScheduled) {
      this.logger.info("transactionEventHailingRequestService/getShouldChargeCancellationFee-isScheduled", {
        currentTime: dayjs().tz("Asia/Hong_Kong").toDate(),
        requestTime,
        acceptedOrderTime,
        isMatched,
      });
      isFullCancellationFee = isMatched && requestTime && requestTime.diff(dayjs(), "minutes") <= 60;
      shouldChargeCancellationFee =
        isMatched && Boolean(acceptedOrderTime && dayjs().diff(acceptedOrderTime, "minutes") >= 3);
    } else {
      isFullCancellationFee = true;
      shouldChargeCancellationFee =
        isMatched && Boolean(acceptedOrderTime && dayjs().diff(acceptedOrderTime, "minutes") >= 3);
    }

    this.logger.info("transactionEventHailingRequestService/getShouldChargeCancellationFee-end", {
      shouldChargeCancellationFee,
      isFullCancellationFee,
      isScheduled,
      isMatched,
    });

    return { shouldChargeCancellationFee, isFullCancellationFee };
  }

  async getCancellationFeeBreakdown(
    isFleet: boolean,
    isFullCancellationFee: boolean,
    successAuth: PaymentTx,
    tx: Tx,
    feetBookingSnapshot: BookingReceiptSnapshot | null,
  ) {
    const hailConfig = await this.appDatabaseService.configurationRepository().getHailConfig();

    this.logger.info("transactionEventHailingRequestService/getCancellationFeeBreakdown-start", {
      isFleet,
      isFullCancellationFee,
      hailConfig,
      successAuth,
      tx,
    });

    const payoutAmount = isFleet ? feetBookingSnapshot?.cancellationFee ?? 0 : hailConfig.dashCancellationFee;

    const dashFee = isFleet ? 0 : 5;
    const total =
      isFleet && isFullCancellationFee ? Math.min(hailConfig.fleetCancellationFee, successAuth.amount ?? 0) : 20;

    tx.payoutAmount = payoutAmount;
    tx.total = total;
    tx.dashFee = dashFee;

    const breakdown = {
      total: tx.total,
      driverPayout: tx.payoutAmount,
      dashFee: tx.dashFee,
    };

    this.logger.info("transactionEventHailingRequestService/getCancellationFeeBreakdown-end", {
      isFleet,
      isFullCancellationFee,
      hailConfig,
      successAuth,
      breakdown,
    });

    return breakdown;
  }

  /**
   * User cancels order event mutator
   * @param tx TxHailingRequest
   * @param txEvent HailingUserCancelsOrderEvent
   * @returns tx TxHailingRequest
   */
  async addHailingEventUserCancelsOrder(tx: TxHailingRequest, txEvent: HailingUserCancelsOrderEvent) {
    const isFleet = tx?.metadata?.request?.platformType === PlatformType.FLEET;

    const { shouldChargeCancellationFee, isFullCancellationFee } = await this.getShouldChargeCancellationFee(tx);

    const successAuth = tx.findLastPaymentType(PaymentInformationType.AUTH);

    if (!successAuth) {
      throw errorBuilder.transaction.successAuthNotFound(tx.id);
    }

    let feetBookingSnapshot: BookingReceiptSnapshot | null = null;

    if (isFleet) {
      feetBookingSnapshot = await this.cancelFleetOrderDelegatee.execute(tx.id);
      tx.payoutAmount = feetBookingSnapshot?.cancellationFee ?? 0;
    }

    if (shouldChargeCancellationFee) {
      const breakdown = await this.getCancellationFeeBreakdown(
        isFleet,
        isFullCancellationFee,
        successAuth,
        tx,
        feetBookingSnapshot,
      );

      if (!txEvent.content.charges) txEvent.content.charges = { cancellationFee: breakdown.total };
      if (!tx.metadata.charges) tx.metadata.charges = { cancellationFee: breakdown.total };

      // Backward compatible: existing cancellationFee field
      txEvent.content.charges.cancellationFee = breakdown.total;
      tx.metadata.charges.cancellationFee = breakdown.total;

      // New: detailed breakdown (only when charging)
      txEvent.content.charges.cancellationFeeBreakdown = breakdown;
      tx.metadata.charges.cancellationFeeBreakdown = breakdown;

      const capturePaymentTx = await this.paymentService.processCapture(successAuth, breakdown.total);

      if (!tx.paymentTx) {
        tx.paymentTx = [];
      }
      tx.paymentTx.push(capturePaymentTx);

      if (!capturePaymentTx || capturePaymentTx.status === PaymentInformationStatus.FAILURE) {
        // Reset to simple structure on failure (no breakdown needed for $0)
        txEvent.content.charges.cancellationFee = 0;
        tx.metadata.charges.cancellationFee = 0;
        delete txEvent.content.charges.cancellationFeeBreakdown;
        delete tx.metadata.charges.cancellationFeeBreakdown;

        if (!tx.txEvents) {
          tx.txEvents = [];
        }
        tx.txEvents.push(txEvent);

        await this.txRepository.save(tx);

        throw errorBuilder.payment.captureFailed(capturePaymentTx.id, capturePaymentTx.gatewayResponse);
      }

      await this.pubsubService.publishMessageForCopyTripToDriverProcessing({
        txId: tx.id,
      });
    } else {
      const voidPaymentTx = await this.paymentService.voidPayment(successAuth.id, "SYSTEM");
      this.paymentTxRepository.save(voidPaymentTx);
      tx.paymentTx?.push(voidPaymentTx);
    }

    tx.metadata.status = TxHailingRequestStatus.CANCELLED;
    tx.metadata.cancelledAt = new Date();

    await this.transactionFactoryService.resetDiscountsForHailingRequest(tx);

    return tx;
  }

  /**
   * User updates order event mutator
   * @param tx TxHailingRequest
   * @param txEvent HailingUserUpdatesOrderEvent
   * @returns tx TxHailingRequest
   */
  async addHailingEventUserUpdatesOrder(tx: TxHailingRequest, txEvent: HailingUserUpdatesOrderEvent) {
    // Update the entire metadata with the new order data, similar to create order
    tx.metadata = {
      ...tx.metadata,
      ...txEvent.content,
    };

    return tx;
  }

  async addHailingAdminCancelsOrderEvent(tx: TxHailingRequest, txEvent: HailingAdminCancelsOrderEvent) {
    const isFleet = tx?.metadata?.request?.platformType === PlatformType.FLEET;

    const { shouldChargeCancellationFee, isFullCancellationFee } = await this.getShouldChargeCancellationFee(tx);

    const successAuth = tx.findLastPaymentType(PaymentInformationType.AUTH);

    if (!successAuth) {
      throw errorBuilder.transaction.successAuthNotFound(tx.id);
    }

    let feetBookingSnapshot: BookingReceiptSnapshot | null = null;

    if (isFleet) {
      feetBookingSnapshot = await this.cancelFleetOrderDelegatee.execute(tx.id, true);
      tx.payoutAmount = feetBookingSnapshot?.cancellationFee ?? 0;
    }

    if (shouldChargeCancellationFee) {
      const breakdown = await this.getCancellationFeeBreakdown(
        isFleet,
        isFullCancellationFee,
        successAuth,
        tx,
        feetBookingSnapshot,
      );

      if (!txEvent.content.charges) txEvent.content.charges = { cancellationFee: breakdown.total };
      if (!tx.metadata.charges) tx.metadata.charges = { cancellationFee: breakdown.total };

      txEvent.content.charges.cancellationFee = breakdown.total;
      tx.metadata.charges.cancellationFee = breakdown.total;

      txEvent.content.charges.cancellationFeeBreakdown = breakdown;
      tx.metadata.charges.cancellationFeeBreakdown = breakdown;

      const capturePaymentTx = await this.paymentService.processCapture(successAuth, breakdown.total);

      if (!tx.paymentTx) {
        tx.paymentTx = [];
      }
      tx.paymentTx.push(capturePaymentTx);

      if (!capturePaymentTx || capturePaymentTx.status === PaymentInformationStatus.FAILURE) {
        // Reset to simple structure on failure (no breakdown needed for $0)
        txEvent.content.charges.cancellationFee = 0;
        tx.metadata.charges.cancellationFee = 0;
        delete txEvent.content.charges.cancellationFeeBreakdown;
        delete tx.metadata.charges.cancellationFeeBreakdown;

        if (!tx.txEvents) {
          tx.txEvents = [];
        }
        tx.txEvents.push(txEvent);

        await this.txRepository.save(tx);

        throw errorBuilder.payment.captureFailed(capturePaymentTx.id, capturePaymentTx.gatewayResponse);
      }

      await this.pubsubService.publishMessageForCopyTripToDriverProcessing({
        txId: tx.id,
      });
    } else {
      const voidPaymentTx = await this.paymentService.voidPayment(successAuth.id, "SYSTEM");
      this.paymentTxRepository.save(voidPaymentTx);
      tx.paymentTx?.push(voidPaymentTx);
    }

    tx.metadata.status = TxHailingRequestStatus.CANCELLED;
    tx.metadata.cancelledAt = new Date();

    await this.transactionFactoryService.resetDiscountsForHailingRequest(tx);

    return tx;
  }

  async addHailingMerchantArrivedDestinationEvent(
    tx: TxHailingRequest,
    _txEvent: HailingMerchantArrivedDestinationEvent,
  ) {
    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.ARRIVED;
    }

    return tx;
  }

  async addHailingOrderCompletedEvent(tx: TxHailingRequest, _txEvent: HailingOrderCompletedEvent) {
    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.COMPLETED;
      tx.metadata.completedAt = new Date();
    }

    return tx;
  }

  async addHailingMerchantApproachingDestinationEvent(
    tx: TxHailingRequest,
    _txEvent: HailingMerchantApproachingDestinationEvent,
  ) {
    if (tx.metadata) {
      tx.metadata.status = TxHailingRequestStatus.APPROACHING;
    }

    return tx;
  }
}

import { Modu<PERSON> } from "@nestjs/common";

import { TransactionEventService } from "./transactionEvent.service";
import { TransactionEventHailingRequestModule } from "./transactionEventHailingRequest.module";
import { PaymentTxRepository } from "../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../database/repositories/tx.repository";

/**
 * Transaction module
 */
@Module({
  imports: [TransactionEventHailingRequestModule],
  providers: [PaymentTxRepository, TxRepository, TransactionEventService],
  exports: [TransactionEventService],
})
export class TransactionEventModule {}

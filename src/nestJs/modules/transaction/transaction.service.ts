import { randomUUID } from "crypto";
import * as fs from "fs";
import path from "path";

import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { createObjectCsvWriter } from "csv-writer";
import { DecodedIdToken } from "firebase-admin/auth";
import moment from "moment";
import { Brackets, In, MoreThan, SelectQueryBuilder } from "typeorm";

import { TxTagItem } from "./dto/addTxTags.dto";
import { PayoutPeriod } from "./dto/payout.dto";
import { ReconFileColumnsDto } from "./dto/reconFileColumns.dto";
import { PaymentTxExtended } from "./dto/transactionDetail.dto";
import {
  MetadataFilterKey,
  TransactionListingQueryDto,
  TransactionListingResponseDto,
  TxSortableType,
} from "./dto/transactionListing.dto";
import { TxPayoutStatus } from "./dto/txPayoutStatus.dto";
import { ReceiptLanguageType, TxReceipt } from "./dto/txReceipt.dto";
import { TxTagType } from "./dto/txTagType.dto";
import { TxTypes } from "./dto/txType.dto";
import { TransactionEventService } from "./modules/transactionEvent.service";
import { TransactionFactoryService } from "./transactionFactory/transactionFactory.service";
import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { Rating, TripDocument } from "../appDatabase/documents/trip.document";
import { BankService } from "../bank/bank.service";
import {
  DASH_FEE_RECALCULATION,
  TxAdjustmentAmountDto,
  TxAdjustmentCreateDto,
  TxAdjustmentCustomAmountDto,
  TxAdjustmentDiscountDto,
} from "./dto/txAdjustment.dto";
import { TxAdjustmentMetadata } from "./dto/txMetadata.dto";
import { BankNames } from "../bank/dto/bankName.dto";
import { PayoutBankFileRow, bankFileSchemas } from "../bank/dto/payoutBankFile.dto";
import { PayoutFileResponse } from "../bank/dto/payoutFileResponse.dto";
import Merchant, { PlatformMerchantType } from "../database/entities/merchant.entity";
import PaymentTx from "../database/entities/paymentTx.entity";
import Payout from "../database/entities/payout.entity";
import Tx from "../database/entities/tx.entity";
import TxEvent from "../database/entities/txEvent.entity";
import TxTag from "../database/entities/txTag.entity";
import { DiscountRepository } from "../database/repositories/discount.repository";
import { MerchantRepository } from "../database/repositories/merchant.repository";
import { PaymentInstrumentRepository } from "../database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../database/repositories/paymentTx.repository";
import { PayoutRepository } from "../database/repositories/payout.repository";
import { TxRepository } from "../database/repositories/tx.repository";
import { TxEventRepository } from "../database/repositories/txEvent.repository";
import { TxTagRepository } from "../database/repositories/txTag.repository";
import { UserRepository } from "../database/repositories/user.repository";
import { DiscountState } from "../discount/dto/discount.dto";
import { EmailService } from "../email/email.service";
import { LocalizedLanguage } from "../location/dto/location.dto";
import { PlatformType } from "../me/modules/meHailing/dto/meHailing.dto";
import { AddEventBody } from "../me/modules/meTransaction/dto/addEvent.dto";
import { ChannelTypes } from "../message/dto/channelType.dto";
import { NotificationType } from "../message/messageFactory/modules/notification/notification.dto";
import { MessageTeamsService } from "../messageTeams/messageTeams.service";
import { PaymentInformationStatus } from "../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../payment/dto/paymentInformationType.dto";
import { PaymentType } from "../payment/dto/paymentType.dto";
import { PaymentInstrumentState } from "../payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { PaymentService } from "../payment/payment.service";
import { PublishMessageForTripProcessingParams } from "../pubsub/dto/publishMessageForTripProcessing.dto";
import { PubSubService } from "../pubsub/pubsub.service";
import { QrCodeDecodedData } from "../qrCode/dto/qrCode.dto";
import { SecretsService } from "../secrets/secrets.service";
import { Secret } from "../secrets/types";
import { Buckets, StorageService } from "../storage/storage.service";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";
import { ZipUtils } from "../utils/utils/zip/zip.utils";
import { UtilsService } from "../utils/utils.service";
import { LanguageOption } from "../validation/dto/language.dto";

/**
 * Transaction service
 */
@Injectable()
export class TransactionService {
  static readonly TIMEZONE_OFFSET = "+08:00";
  /**
   * Transaction types
   */
  static readonly TX_TYPES = {
    TRIP: TxTypes.TRIP,
  };

  constructor(
    private readonly appDatabaseService: AppDatabaseService,
    @InjectRepository(MerchantRepository) private merchantRepository: MerchantRepository,
    private paymentService: PaymentService,
    @InjectRepository(TxRepository) private txRepository: TxRepository,
    @InjectRepository(TxTagRepository) private txTagRepository: TxTagRepository,
    @InjectRepository(PayoutRepository) private payoutRepository: PayoutRepository,
    private readonly bankService: BankService,
    private readonly storageService: StorageService,
    private pubsubService: PubSubService,
    private readonly transactionFactoryService: TransactionFactoryService,
    @Inject(UtilsService) private readonly utilsService: UtilsService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @InjectRepository(PaymentInstrumentRepository) private paymentInstrumentRepository: PaymentInstrumentRepository,
    @InjectRepository(PaymentTxRepository) private readonly paymentTxRepository: PaymentTxRepository,
    private readonly messageTeamsService: MessageTeamsService,
    private readonly transactionEventService: TransactionEventService,
    @InjectRepository(UserRepository) private readonly userRepository: UserRepository,
    private readonly secretsService: SecretsService,
    private readonly emailService: EmailService,
    @InjectRepository(DiscountRepository) private readonly discountRepository: DiscountRepository,
    @InjectRepository(TxEventRepository) private readonly txEventRepository: TxEventRepository,
  ) {}

  // commonly used function to extract parameters from a transaction
  private extractParametersFromTx(tx: Tx) {
    const originalTotal = tx.total ?? 0;
    const originalPayout = tx.payoutAmount ?? 0;
    const originalDashFee = tx.dashFee ?? 0;
    const dashFeeRate =
      (tx.metadata as TxAdjustmentMetadata)?.dashFeeRate ??
      (tx.metadata as TxAdjustmentMetadata)?.billing?.dashFeeSettings?.dashFeeRate ??
      0;
    const dashFeeConstant =
      (tx.metadata as TxAdjustmentMetadata)?.dashFeeConstant ??
      (tx.metadata as TxAdjustmentMetadata)?.billing?.dashFeeSettings?.dashFeeConstant ??
      0;

    return {
      originalTotal,
      originalPayout,
      originalDashFee,
      dashFeeRate,
      dashFeeConstant,
    };
  }

  // takes an amount and makes adjustments, but also takes into account if the fee is based on the original amount or not.
  adjustTxWithAmount({ amount, type }: TxAdjustmentAmountDto, adjustmentTx: Tx, parentTx: Tx): Tx {
    const { originalPayout, originalDashFee, dashFeeRate, dashFeeConstant } = this.extractParametersFromTx(parentTx);

    if (type === DASH_FEE_RECALCULATION.ORIGINAL_AMOUNT) {
      // no need to adjust the dash fee if it's based on the original amount.
      // take the input `amount` and set it as the total and payout amount, since we didn't change the fee.
      adjustmentTx.dashFee = 0;
      adjustmentTx.total = amount;
      adjustmentTx.payoutAmount = amount;

      return adjustmentTx;
    }

    const newPayoutAmount = originalPayout + amount; // adding or subtracting based on the sign of `amount`
    // recalculate the fee from the reusable function to be safe
    const newDashFee = this.utilsService.number.calculateDashFee(
      {
        tripTotal: newPayoutAmount,
        dashFeeRate,
        dashFeeConstant,
      },
      this.utilsService.number.roundAwayFromZero,
    );
    // find the required subtractor to end up with the new dash fee
    const adjustedDashFee = this.utilsService.number.roundAwayFromZero(newDashFee - originalDashFee);
    this.logger.debug("adjustTxWithAmount", {
      newPayoutAmount,
      originalPayout,
      amount,
      adjustedDashFee,
      newDashFee,
      originalDashFee,
    });

    adjustmentTx.dashFee = adjustedDashFee;
    adjustmentTx.payoutAmount = this.utilsService.number.roundAwayFromZero(amount);
    // the total is the sum of the input payout amount and the adjusted dash fee
    adjustmentTx.total = this.utilsService.number.roundAwayFromZero(amount + adjustedDashFee);

    return adjustmentTx;
  }

  // takes the payout amount and dash fee from the request and adjusts the transaction accordingly
  adjustTxFromCustomAmount({ dashFee, payoutAmount }: TxAdjustmentCustomAmountDto, adjustmentTx: Tx): Tx {
    adjustmentTx.dashFee = dashFee;
    adjustmentTx.payoutAmount = payoutAmount;
    adjustmentTx.total = this.utilsService.number.roundAwayFromZero(payoutAmount + dashFee);

    return adjustmentTx;
  }

  // takes the adjustment transaction and the parent transaction, and adjusts the dash fee and total to reflect a full refund
  adjustTxFromFullRefund(adjustmentTx: Tx, parentTx: Tx): Tx {
    const { originalPayout, originalDashFee } = this.extractParametersFromTx(parentTx);
    // no need to round here, as the originals were already rounded
    const dashFeeValue = -originalDashFee;
    const payoutValue = -originalPayout;

    adjustmentTx.dashFee = dashFeeValue;
    adjustmentTx.payoutAmount = payoutValue;
    // we need to round again here because we made some arithmetic operations
    adjustmentTx.total = this.utilsService.number.roundAwayFromZero(payoutValue + dashFeeValue);

    return adjustmentTx;
  }

  // takes the discount amount and deduct (or add) it from the total only
  adjustTxWithDiscount({ discount }: TxAdjustmentDiscountDto, adjustmentTx: Tx): Tx {
    adjustmentTx.dashFee = 0; // no fee change for discounted transactions
    adjustmentTx.payoutAmount = 0; // no payout adjustments for discounted transactions
    adjustmentTx.total = discount; // take it from the request

    return adjustmentTx;
  }

  async createTxAdjustment(
    txId: string,
    txAdjustmentRequest: TxAdjustmentCreateDto,
    user: DecodedIdToken,
  ): Promise<Tx> {
    const { adjustmentTx: newAdjustmentTx, parentTx } = await this.txRepository.createTxAdjustment(
      txId,
      0,
      txAdjustmentRequest.reason,
      user,
    );
    const dbUser = await this.userRepository.findOneBy({ appDatabaseId: user.uid });

    const tx = await this.txRepository.findOne({
      where: { id: txId },
      relations: ["paymentTx", "paymentTx.paymentInstrument"],
    });

    if (!tx) {
      throw errorBuilder.transaction.notFound(txId);
    }

    let adjustmentTx = newAdjustmentTx;
    switch (txAdjustmentRequest.type) {
      case DASH_FEE_RECALCULATION.ADJUSTED_AMOUNT:
      case DASH_FEE_RECALCULATION.ORIGINAL_AMOUNT: {
        adjustmentTx = this.adjustTxWithAmount(txAdjustmentRequest, newAdjustmentTx, parentTx);
        break;
      }
      case DASH_FEE_RECALCULATION.CUSTOM_AMOUNT: {
        adjustmentTx = this.adjustTxFromCustomAmount(txAdjustmentRequest, newAdjustmentTx);
        break;
      }
      case DASH_FEE_RECALCULATION.FULL_REFUND: {
        adjustmentTx = this.adjustTxFromFullRefund(newAdjustmentTx, parentTx);
        break;
      }
      case DASH_FEE_RECALCULATION.DISCOUNTED_AMOUNT: {
        // creates the adjustment transaction with the discount amount
        adjustmentTx = this.adjustTxWithDiscount(txAdjustmentRequest, newAdjustmentTx);
        // only store the discount metadata in the parent transaction
        parentTx.metadata = {
          ...parentTx.metadata,
          billing: {
            ...(parentTx.metadata as TxAdjustmentMetadata)?.billing,
            discount: txAdjustmentRequest.discount,
          },
        } as TxAdjustmentMetadata;
        break;
      }
      default: {
        // covers default case, so we don't fail silently when we add a new type later
        throw errorBuilder.transaction.adjustmentNotSupported(txId, txAdjustmentRequest);
      }
    }

    adjustmentTx.metadata = {
      reason: txAdjustmentRequest.reason,
    } as TxAdjustmentMetadata;

    const total = adjustmentTx.total!;

    const paymentTxs = tx.paymentTx;

    await this.txRepository.save(adjustmentTx);

    if (total > 0 && dbUser) {
      const usedPaymentInstrumentIds = paymentTxs
        ?.filter((ptx) => ptx.paymentInstrument && ptx.status === PaymentInformationStatus.SUCCESS)
        .map((ptx) => ptx.paymentInstrument?.id)
        .reduce((result: string[], id) => {
          if (id && !result.includes(id)) {
            result.push(id);
          }
          return result;
        }, []);

      if (!usedPaymentInstrumentIds) {
        throw errorBuilder.transaction.noPaymentInstrumentFound();
      }

      const paymentInstruments = await this.paymentInstrumentRepository.find({ where: { user: { id: dbUser.id } } });

      const sorted = paymentInstruments.sort((pi1, pi2) => {
        if (
          (usedPaymentInstrumentIds.includes(pi1.id) || pi1.isPreferred) &&
          !usedPaymentInstrumentIds.includes(pi2.id)
        ) {
          return -1;
        } else if (
          (usedPaymentInstrumentIds.includes(pi2.id) || pi2.isPreferred) &&
          !usedPaymentInstrumentIds.includes(pi1.id)
        ) {
          return 1;
        }
        return 0;
      });

      let newPaymentTx: PaymentTx | undefined = undefined;

      for await (const paymentInstrument of sorted) {
        if (!newPaymentTx) {
          await this.paymentService.processSale(adjustmentTx, paymentInstrument.id, total).then((salePaymentTx) => {
            this.paymentTxRepository.save(salePaymentTx).then(() => {
              if (salePaymentTx.status === PaymentInformationStatus.SUCCESS) {
                newPaymentTx = salePaymentTx;
              }
            });
          });
        }
      }

      if (!newPaymentTx) {
        throw errorBuilder.transaction.noSale(txId);
      }
    }

    await this.txRepository.save(parentTx);

    return this.transactionFactoryService.postAdjustmentProcess(adjustmentTx);
  }

  getTxDate(tx: Tx): Date {
    return this.transactionFactoryService.getTxDate(tx);
  }

  /**
   * Process a transaction
   * @param message functions.pubsub.Message
   * @param context functions.EventContext
   * @returns The processed transaction
   */
  async processTransaction(data: PublishMessageForTripProcessingParams, messageId: string): Promise<Tx> {
    try {
      this.logger.info("TransactionService/processTransaction-start", { data, messageId });

      /**
       * Set the Tx
       */
      const tx = new Tx();
      tx.id = data.tx.id;
      tx.txApp = data.tx.txApp;
      tx.type = data.tx.type;
      tx.metadata = data.tx.data;
      if (data.tx.data.user?.id) {
        const user = await this.userRepository.findAppUserById(data.tx.data.user.id);
        if (user) {
          tx.user = user;
        }
      }

      /**
       * Set the merchant if any
       */
      let merchant: Merchant | null = null;

      if (data.merchant?.phoneNumber) {
        const platformMerchantType =
          data.tx.data.platformType === PlatformType.FLEET ? PlatformMerchantType.SYNCAB : PlatformMerchantType.DASH;

        merchant = await this.merchantRepository.findOne({
          where: { phoneNumber: data.merchant.phoneNumber, platformMerchantType },
        });
        if (!merchant) {
          throw errorBuilder.transaction.merchantNotFound(data.merchant.phoneNumber);
        }
      }

      if (merchant) {
        tx.merchant = merchant;
      }

      if (data.settlementFleetId) {
        const fleet = await this.appDatabaseService.fleetRepository().findOneById(data.settlementFleetId);
        if (fleet && fleet.merchantId) {
          const payoutMerchant = await this.merchantRepository.findOne({ where: { id: fleet.merchantId } });
          if (payoutMerchant) {
            tx.payoutMerchant = payoutMerchant;
          }
        }
      }

      const { total, dashFee, payoutAmount, billing } = this.transactionFactoryService.getTxMonetary(tx);

      tx.total = total;
      tx.dashFee = dashFee;
      tx.payoutAmount = payoutAmount;

      let paymentTxs: PaymentTx[] = [];
      /**
       * Set the paymentTx if any
       */
      if (data.paymentTx && data.paymentTx.length > 0) {
        paymentTxs = Object.entries(
          data.paymentTx
            .map((paymentTx) => PaymentTx.fromJson(paymentTx, tx.id, this.logger))
            .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
            .reduce((acc: Record<PaymentTx["id"], PaymentTx>, paymentTx: PaymentTx) => {
              // if the id is the same as a previous one, we override the previous one, so payment_information in firestore and paymentTx in database may be differnet in this case
              acc[paymentTx.id] = paymentTx;
              return acc;
            }, {}),
        ).map(([, paymentTx]) => paymentTx);
      }

      const savedTx = await this.txRepository.upsertTxAndUpdatePaymentTx(
        tx,
        paymentTxs,
        this.transactionFactoryService.shouldUpdateTxMetadata,
      );

      let doCapture = false;
      let postSale = false;
      let lastSuccessAuthWithoutCaptureOrVoid: PaymentTx | undefined;
      if (data.txProcessed && savedTx.paymentTx && savedTx.paymentTx.length > 0) {
        lastSuccessAuthWithoutCaptureOrVoid = this.paymentService.findLastSuccessAuthWithoutCaptureOrVoid(
          savedTx.paymentTx,
        );
        doCapture = Boolean(lastSuccessAuthWithoutCaptureOrVoid);
        postSale = Boolean(this.paymentService.findLastSuccessSaleWithoutVoid(savedTx.paymentTx));
      }

      this.logger.info(`tx: ${savedTx.id} info before sending to pubsub: `, {
        payment_tx: savedTx.paymentTx?.map((paymentTx) => paymentTx.id),
        txProcessed: data.txProcessed,
        doVoid: data.doVoid,
        isDirectSale: data.isDirectSale,
        messageId: messageId,
        doCapture,
        postSale,
        isAfterTxEnd: data.isAfterTxEnd,
        isCaptureInApp: data.isCaptureInApp,
        isTripEnd: data.isTripEnd,
      });

      // Road side hailing end of trip sale processing
      if (data.isDirectSale) {
        const paymentInstrument = savedTx.paymentTx?.find((ptx) => ptx.paymentInstrument)?.paymentInstrument;

        if (paymentInstrument) {
          await this.pubsubService.publishMessageForDirectSaleProcessing({
            txId: savedTx.id,
            paymentInstrumentId: paymentInstrument.id,
          });
        }
      }

      // In App hailing end of trip capture processing
      if (data.isCaptureInApp) {
        lastSuccessAuthWithoutCaptureOrVoid = this.paymentService.findLastSuccessAuthWithoutCaptureOrVoid(
          savedTx.paymentTx,
        );
        doCapture = Boolean(lastSuccessAuthWithoutCaptureOrVoid);

        this.logger.debug("Capture in app", { doCapture, lastSuccessAuthWithoutCaptureOrVoid, savedTx });
        if (!doCapture || !lastSuccessAuthWithoutCaptureOrVoid) {
          await this.txTagRepository.addTagsToTx(savedTx, [
            TxTag.createTxTag(TxTagType.UNABLE_TO_CAPTURE, tx.id, "SYSTEM", "Could not find the AUTH to CAPTURE"),
          ]);
        } else if ((lastSuccessAuthWithoutCaptureOrVoid.amount ?? 0) < billing.total) {
          const paymentInstrument = savedTx.paymentTx?.find((ptx) => ptx.paymentInstrument)?.paymentInstrument;
          this.logger.debug("Capture in app paymentInstrument", { paymentInstrument });
          if (!paymentInstrument) {
            await this.txTagRepository.addTagsToTx(savedTx, [
              TxTag.createTxTag(
                TxTagType.UNABLE_TO_SELL,
                tx.id,
                "SYSTEM",
                "Could not find the payment instrument for the Tx",
              ),
            ]);
          } else {
            await this.pubsubService.publishMessageForDirectSaleProcessing({
              txId: savedTx.id,
              paymentInstrumentId: paymentInstrument.id,
            });
          }
        } else {
          this.logger.debug("publishMessageForCaptureProcessing", { tx: savedTx.id });
          await this.pubsubService.publishMessageForCaptureProcessing({
            tx: savedTx,
          });
        }
      }

      // Taxi POS processing
      if (data.txProcessed) {
        if (this.transactionFactoryService.isPayWithDash(savedTx)) {
          if (postSale) {
            await this.pubsubService.publishMessageForPostSaleProcessing({
              tx: savedTx,
            });
          } else if (doCapture) {
            await this.pubsubService.publishMessageForCaptureProcessing({
              tx: savedTx,
            });
          }
        } else {
          await this.transactionFactoryService.postCashPaymentProcess(savedTx);
          const openAuthOrOpenSale = this.paymentService.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(
            savedTx.paymentTx,
            true,
          );
          if (
            openAuthOrOpenSale.length > 0 &&
            openAuthOrOpenSale.some(
              (item) => item.type === PaymentInformationType.AUTH && item.status === PaymentInformationStatus.SUCCESS,
            )
          ) {
            await this.txTagRepository.addTagsToTx(savedTx, [
              TxTag.createTxTag(
                TxTagType.OPEN_AUTH,
                tx.id,
                "SYSTEM",
                "At least one auth is not handled, please capture or void it",
              ),
            ]);
          }
          if (
            openAuthOrOpenSale.length > 0 &&
            openAuthOrOpenSale.some(
              (item) => item.type === PaymentInformationType.SALE && item.status === PaymentInformationStatus.SUCCESS,
            )
          ) {
            await this.txTagRepository.addTagsToTx(savedTx, [
              TxTag.createTxTag(
                TxTagType.OPEN_SALE,
                tx.id,
                "SYSTEM",
                "At least one sale is not handled, please check it",
              ),
            ]);
          }

          // TODO: revisit
          const discount = await this.discountRepository.findOne({
            where: { tx: { id: tx.id } },
            relations: ["campaign"],
          });
          if (discount) {
            //update tx discount in service
            if (discount.campaign?.discountRules) {
              await this.discountRepository.update(discount.id, { state: DiscountState.FAILED });
            }
          }
        }

        if (this.paymentService.findAllSalesInProcessingStatus(savedTx.paymentTx).length > 0) {
          await this.txTagRepository.addTagsToTx(savedTx, [
            TxTag.createTxTag(
              TxTagType.OPEN_PROCESSING,
              tx.id,
              "SYSTEM",
              "At least one sale is in processing, please check its status",
            ),
          ]);
        }
      }

      if (data.doVoid) {
        const authsNeedToVoid = this.paymentService.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(savedTx.paymentTx);
        if (authsNeedToVoid.length > 0) {
          await this.pubsubService.publishMessageForVoidProcessing({
            tx: savedTx,
            paymentTxs: authsNeedToVoid,
          });
        }
      }

      if (data.isTripEnd) {
        await this.transactionFactoryService.postCashPaymentProcess(savedTx);
        await this.pubsubService.publishMessageForTripEndEvent({ txId: savedTx.id });
      }

      return savedTx;
    } catch (e) {
      this.logger.error("TransactionService/processTransaction-end", { data, messageId }, e as Error);
      throw e;
    }
  }

  /**
   * Remove tags, this function is called by admin
   * @param txId string
   * @param tagId number
   * @returns The saved tx
   */
  async removeTagsFromTx(txId: string, ids: number[]): Promise<void> {
    await this.txTagRepository.removeTagById(txId, ids);
  }

  /**
   * Add tags to a existing tx, this function is called by admin
   * @param txId string
   * @param tags TxTagItem[]
   * @returns The saved tx
   */
  async addNewTagsToTx(txId: string, tags: TxTagItem[], createdBy: string): Promise<Tx> {
    const foundTx = await this.txRepository.findOne({ relations: ["txTag"], where: { id: txId } });
    if (!foundTx) {
      throw errorBuilder.transaction.notFound(txId);
    }
    return this.txTagRepository.addTagsToTx(
      foundTx,
      tags.map((item) => TxTag.createTxTag(item.tag, txId, createdBy, item.note)),
    );
  }

  /**
   * Create the automatic payout file and save it to storage
   * @param bankName BankNames
   * @param requestedBy string
   * @returns Promise<PayoutFileResponse & { fileName: string }>
   */
  async createAutomaticPayout(
    bankName: BankNames,
    requestedBy: string,
    payoutMerchantId?: string,
  ): Promise<PayoutFileResponse & { fileName: string }> {
    const isMonday = moment().isoWeekday() === 1;

    let txs: Tx[] = [];
    if (payoutMerchantId) {
      const merchant = await this.merchantRepository.findOneBy({ id: payoutMerchantId });
      if (!merchant) {
        throw errorBuilder.payout.payoutMerchantNotFound(payoutMerchantId);
      }
    }

    if (isMonday) {
      const applyWeeklyQueryConditions = (queryBuilder: SelectQueryBuilder<Tx>): Promise<Tx[]> => {
        return queryBuilder
          .andWhere("tx.payoutStatus IS NULL")
          .andWhere(
            new Brackets((qb) => {
              qb.where("tx.payoutAmount > 0 AND tx.type IN (:...types)", {
                types: [TxTypes.TRIP, TxTypes.HAILING_REQUEST],
              }).orWhere("tx.type = :adjustmentType", { adjustmentType: TxTypes.TX_ADJUSTMENT });
            }),
          )
          .andWhere("tx.createdAt < date_trunc('day', now()) - interval '8 hours'")
          .andWhere("tx.createdAt >= date_trunc('day', now()) - interval '1 week' - interval '8 hours'")
          .andWhere('"txTag" IS NULL')
          .getMany();
      };

      let txQuery: SelectQueryBuilder<Tx>;
      if (payoutMerchantId) {
        txQuery = this.txRepository.manager
          .createQueryBuilder(Tx, "tx")
          .select("tx.id")
          .leftJoin("tx.txTag", "txTag")
          .where("tx.payoutMerchant = :payoutMerchant", { payoutMerchant: payoutMerchantId });
      } else {
        txQuery = this.txRepository.manager
          .createQueryBuilder(Tx, "tx")
          .select("tx.id")
          .leftJoin("tx.txTag", "txTag")
          .where("tx.payoutMerchant IS NULL");
      }
      txs = await applyWeeklyQueryConditions(txQuery);
    } else {
      const applyDailyQueryConditions = (queryBuilder: SelectQueryBuilder<Tx>): Promise<Tx[]> => {
        return queryBuilder
          .andWhere("tx.payoutStatus IS NULL")
          .andWhere(
            new Brackets((qb) => {
              qb.where("tx.payoutAmount > 0 AND tx.type IN (:...types)", {
                types: [TxTypes.TRIP, TxTypes.HAILING_REQUEST],
              }).orWhere("tx.type = :adjustmentType", { adjustmentType: TxTypes.TX_ADJUSTMENT });
            }),
          )
          .andWhere("tx.createdAt < date_trunc('day', now()) - interval '8 hours'")
          .andWhere("tx.createdAt >= date_trunc('day', now()) - interval '1 day' - interval '8 hours'")
          .andWhere('"txTag" IS NULL')
          .getMany();
      };

      let txQuery: SelectQueryBuilder<Tx>;
      if (payoutMerchantId) {
        txQuery = this.txRepository.manager
          .createQueryBuilder(Tx, "tx")
          .select("tx.id")
          .leftJoin("tx.merchant", "merchant")
          .leftJoin("tx.payoutMerchant", "payoutMerchant")
          .leftJoin("tx.txTag", "txTag")
          .where("tx.payoutMerchant = :payoutMerchant", { payoutMerchant: payoutMerchantId })
          .andWhere("payoutMerchant.payoutPeriod = :payoutPeriod", { payoutPeriod: PayoutPeriod.DAILY });
      } else {
        txQuery = await this.txRepository.manager
          .createQueryBuilder(Tx, "tx")
          .select("tx.id")
          .leftJoin("tx.merchant", "merchant")
          .leftJoin("tx.txTag", "txTag")
          .where("tx.payoutMerchant IS NULL")
          .andWhere("merchant.payoutPeriod = :payoutPeriod", { payoutPeriod: PayoutPeriod.DAILY });
      }
      txs = await applyDailyQueryConditions(txQuery);
    }

    return this.createPayout(
      txs.map((tx) => tx.id),
      bankName,
      requestedBy,
    );
  }

  /**
   * Create the payout file and save it to storage
   * @param txIds string[]
   * @param bankName BankNames
   * @param requestedBy string
   * @returns Promise<PayoutFileResponse & { fileName: string }>
   */
  async createPayout(
    txIds: string[],
    bankName: BankNames,
    requestedBy: string,
  ): Promise<PayoutFileResponse & { fileName: string }> {
    const { content, processed, unprocessed, reasons } = await this.bankService.generatePayoutFileContent(
      bankName,
      txIds,
    );

    this.logger.debug(`createPayout: ${txIds.join(", ")}`);

    if (!processed.length) {
      throw errorBuilder.global.unprocessableEntity("No transaction to process", { reasons });
    }

    const payout = new Payout({ originalRequest: { txIds, bankName }, requestedBy });

    await Promise.all([
      this.payoutRepository.save(payout),
      this.storageService.savePayoutFile(payout.batchFile, content),
      this.messageTeamsService.sendPayoutNotificationToTeams(payout.batchFile),
    ]);

    return { content, processed, unprocessed, fileName: payout.batchFile, reasons };
  }

  async processPayoutFileFromBank(bankName: BankNames, filePath: string) {
    if (filePath.includes("processed") || !filePath.includes(".xlsx")) return;

    const fileContent = await this.storageService.getXlsxFileContentFromBucketFile(
      this.storageService.bucketsNames[Buckets.PAYOUT_RESPONSE],
      filePath,
      bankFileSchemas[bankName],
      this.bankService.bankFileTransformer(bankName),
    );
    this.logger.info("fileContent: ", fileContent);

    if (!fileContent || fileContent.length === 0) {
      throw errorBuilder.payout.noContentInBankFile(filePath);
    }

    const originalBatchFileNames = fileContent.reduce(
      (fileNamesList: string[], payoutBankFileRow: PayoutBankFileRow) => {
        if (!fileNamesList.includes(payoutBankFileRow.originalBatchFileName)) {
          fileNamesList.push(payoutBankFileRow.originalBatchFileName);
        }
        return fileNamesList;
      },
      [],
    );
    this.logger.debug("originalBatchFileNames: ", originalBatchFileNames);

    const payouts = await this.payoutRepository.find({ where: { batchFile: In(originalBatchFileNames) } });

    if (!payouts || payouts.length === 0) {
      throw errorBuilder.payout.notFound(originalBatchFileNames.join(", "), { filePath });
    }

    const txIds = payouts.map((payout) => payout.originalRequest.txIds).flat();

    const transactions = await this.txRepository.find({
      where: {
        id: In(txIds),
      },
      relations: ["merchant", "payoutMerchant", "parentTx"],
    });

    if (!transactions || transactions.length === 0) {
      throw errorBuilder.transaction.notFound(txIds.join(", "), {
        filePath,
        payouts: payouts.map((payout) => payout.id).join(", "),
      });
    }

    const txIdsStillPreReleaseBeforeUpdating = transactions
      .filter((tx) => tx.payoutStatus === TxPayoutStatus.PRERELEASED)
      .map((tx) => tx.id);
    const txIdsReleaseBeforeUpdating = transactions
      .filter(
        (tx) =>
          tx.payoutStatus === TxPayoutStatus.RELEASED || tx.payoutStatus === TxPayoutStatus.RELEASED_TO_PAYOUT_MERCHANT,
      )
      .map((tx) => tx.id);
    this.logger.debug("txIdsStillPreReleaseBeforeUpdating: ", txIdsStillPreReleaseBeforeUpdating);
    this.logger.debug("txIdsReleaseBeforeUpdating: ", txIdsReleaseBeforeUpdating);
    const txs = this.transactionFactoryService.getTxsToPayout(transactions, fileContent, payouts);

    this.logger.info("txs: ", {
      completed: txs.completed.map((tx) => tx.id),
      failed: txs.failed.map((tx) => tx.id),
      bankProcessing: txs.bankProcessing.map((tx) => tx.id),
    });

    const txsByPayoutTypeCompleted = txs.completed.reduce<{ merchant: Tx[]; payoutMerchant: Tx[] }>(
      (acc, tx) => {
        if (tx.payoutMerchant) {
          acc.payoutMerchant.push(tx);
        } else {
          acc.merchant.push(tx);
        }
        return acc;
      },
      { merchant: [], payoutMerchant: [] },
    );

    await this.txRepository.update(
      { id: In(txsByPayoutTypeCompleted.merchant.map((tx) => tx.id)) },
      { payoutStatus: TxPayoutStatus.RELEASED },
    );

    await this.txRepository.update(
      { id: In(txsByPayoutTypeCompleted.payoutMerchant.map((tx) => tx.id)) },
      { payoutStatus: TxPayoutStatus.RELEASED_TO_PAYOUT_MERCHANT },
    );

    const txsByPayoutTypeBankProcessing = txs.bankProcessing.reduce<{ merchant: Tx[]; payoutMerchant: Tx[] }>(
      (acc, tx) => {
        if (tx.payoutMerchant) {
          acc.payoutMerchant.push(tx);
        } else {
          acc.merchant.push(tx);
        }
        return acc;
      },
      { merchant: [], payoutMerchant: [] },
    );

    await this.txRepository.update(
      { id: In(txsByPayoutTypeBankProcessing.merchant.map((tx) => tx.id)) },
      { payoutStatus: TxPayoutStatus.BANK_PROCESSING },
    );

    await this.txRepository.update(
      { id: In(txsByPayoutTypeBankProcessing.payoutMerchant.map((tx) => tx.id)) },
      { payoutStatus: TxPayoutStatus.BANK_PROCESSING },
    );

    const tags = txs.failed.map((tx) => {
      return TxTag.createTxTag(TxTagType.PAYOUT_REJECTED, tx.id, "SYSTEM", "Payout rejected by bank");
    });

    await this.txTagRepository.save(tags);
    await this.txTagRepository.update({ tx: In(txs.completed.map((tx) => tx.id)) }, { removedAt: new Date() });

    const completedAndBankProcessingTxs = txs.completed.concat(txs.bankProcessing);
    if (completedAndBankProcessingTxs && completedAndBankProcessingTxs.length > 0) {
      await this.pubsubService.publishMessageForCopyTripToDriverProcessing({
        txIds: completedAndBankProcessingTxs.map((tx) => tx.id),
      });
      const shouldNotifyTxs = completedAndBankProcessingTxs.filter((tx) =>
        txIdsStillPreReleaseBeforeUpdating.some((id) => id === tx.id),
      );
      shouldNotifyTxs.forEach((tx) => {
        if (tx.merchant?.phoneNumber) {
          this.pubsubService.publishMessageForMessageProcessingParams({
            /**
             * Using the phone number as we use it as topic for the notification.
             * Later on we may want to change the topic to something different
             * so if a merchant uses the merchant app and a user app we can send
             * the notification to the correct app
             */
            recipient: { id: tx.merchant.phoneNumber.replace("+", "") },
            channel: ChannelTypes.NOTIFICATION,
            template: tx.payoutMerchant ? NotificationType.PAYOUT_PAID_TO_FLEET : NotificationType.PAYOUT_PAID,
            metadata: {
              schemeVersion: "1.3",
              createdAt: new Date(),
            },
            tranId: tx.id,
            language: LanguageOption.EN,
            params: {},
            messageId: randomUUID(),
          });
        }
      });
      this.transactionFactoryService.postPayoutProcess(shouldNotifyTxs);
    }
    if (txs.completed && txs.completed.length > 0) {
      if (txsByPayoutTypeCompleted.payoutMerchant.length > 0) {
        const txsByPayoutMerchant = txsByPayoutTypeCompleted.payoutMerchant.reduce<{ [key: string]: Tx[] }>(
          (acc, tx) => {
            if (tx.payoutMerchant && !txIdsReleaseBeforeUpdating.some((id) => id === tx.id)) {
              if (!acc[tx.payoutMerchant.id]) {
                acc[tx.payoutMerchant.id] = [];
              }
              acc[tx.payoutMerchant.id].push(tx);
            }
            return acc;
          },
          {},
        );

        for (const [payoutMerchantId, txs] of Object.entries(txsByPayoutMerchant)) {
          const payoutMerchant = await this.merchantRepository.findOneBy({ id: payoutMerchantId });
          if (!payoutMerchant) {
            this.logger.error(`PayoutMerchant ${payoutMerchantId} not found`);
            continue;
          }

          const txIds = txs.map((tx) => tx.id);
          this.logger.info(`Txs paid out to PayoutMerchant ${payoutMerchant.id}: `, {
            txs: txIds,
          });

          const fileName = `${moment().utcOffset(TransactionService.TIMEZONE_OFFSET).format("YYYYMMDD")}-${
            payoutMerchant.id
          }-recon`;
          this.logger.debug(`Recon File to generate: ${fileName}.csv`);
          const writableTempDirectory = "/tmp";
          const csvFilePath = path.join(writableTempDirectory, `${fileName}.csv`);
          const zipFilePath = path.join(writableTempDirectory, `${fileName}.zip`);
          await this.generateReconCsvForPayout(csvFilePath, txs);
          const csvFileContent = fs.readFileSync(csvFilePath).toString("utf-8");
          await this.storageService.savePayoutFile(`recon/${payoutMerchant.id}/${fileName}.csv`, csvFileContent);

          if (payoutMerchant.email) {
            this.logger.info(`Sending payout report to ${payoutMerchant.email}: `, {
              txs: txIds,
            });
            const encryptionPassword = await this.secretsService.getSecret(Secret.PAYOUT_MERCHANT_RECON_PASSWORD);
            await ZipUtils.zipFile(csvFilePath, zipFilePath, { password: encryptionPassword });
            const payoutPeriod = payoutMerchant.payoutPeriod === PayoutPeriod.DAILY ? "Daily" : "Weekly";
            const todayFormatted = moment().utcOffset("+0800").format("Do MMMM");
            const attachment = fs.readFileSync(zipFilePath).toString("base64");
            await this.emailService.sendNotificationEmail({
              to: payoutMerchant.email,
              attachments: [
                {
                  content: attachment,
                  filename: `${fileName}.zip`,
                  type: "application/zip",
                  disposition: "attachment",
                },
              ],
              dynamicTemplateData: {
                subject: `Your D-ASH Payout Report: ${payoutPeriod}`,
                heading: "Your Payout Summary is Ready.",
                body: `Please find attached your ${payoutPeriod.toLowerCase()} payout report for ${todayFormatted}. You may use the previously provided password to access the file.`,
              },
            });
          }

          if (fs.existsSync(csvFilePath)) {
            fs.unlinkSync(csvFilePath);
          }
          if (fs.existsSync(zipFilePath)) {
            fs.unlinkSync(zipFilePath);
          }
        }
      }
    }

    await this.payoutRepository.update(
      payouts.map((payout) => payout.id),
      { status: TxPayoutStatus.RELEASED, processedAt: new Date(), bankFile: filePath },
    );

    if (txs.failed.length === 0) {
      await this.storageService.moveFile(
        this.storageService.bucketsNames[Buckets.PAYOUT_RESPONSE],
        filePath,
        `${bankName}/processed/${filePath.split("/").pop()}`,
      );
      await Promise.all(
        originalBatchFileNames.map((fileName) => {
          return this.storageService.moveFile(
            this.storageService.bucketsNames[Buckets.PAYOUT],
            fileName,
            `${bankName}/processed/${fileName}`,
          );
        }),
      );
    }

    return {
      completed: txs.completed.map((tx) => tx.id),
      failed: txs.failed.map((tx) => tx.id),
      bankProcessing: txs.bankProcessing.map((tx) => tx.id),
    };
  }

  /**
   * get transactions list
   * @param txId string
   * @param tags TxTagItem[]
   * @returns The saved tx
   */
  async getTransactions(listingQueryDto: TransactionListingQueryDto): Promise<TransactionListingResponseDto> {
    const alias = "tx";
    let where = "1=1";
    where += listingQueryDto.txId ? ` AND ${alias}.id = '${listingQueryDto.txId}'` : "";
    where += listingQueryDto.type ? ` AND ${alias}.type = '${listingQueryDto.type}'` : "";
    where += listingQueryDto.payoutStatus
      ? listingQueryDto.payoutStatus === "NONE"
        ? ` AND ${alias}.payoutStatus IS NULL`
        : ` AND ${alias}.payoutStatus = '${listingQueryDto.payoutStatus}'`
      : "";
    where += listingQueryDto.userId ? ` AND ${alias}.user = '${listingQueryDto.userId}'` : "";
    where += listingQueryDto.total ? ` AND ${alias}.total = ${listingQueryDto.total}` : "";

    if (listingQueryDto.fromDate) {
      const start = moment(listingQueryDto.fromDate).startOf("day").toISOString();

      where += ` AND ${alias}."createdAt" >= '${start}'`;
    }

    if (listingQueryDto.toDate) {
      const end = moment(listingQueryDto.toDate).endOf("day").toISOString();

      where += ` AND ${alias}."createdAt" < '${end}'`;
    }

    if (listingQueryDto.noDriver) {
      where += ` AND ( ${alias}.metadata->'driver'->>'id' IS NULL OR ${alias}.metadata->'driver'->>'id' = '' )`;
    }

    if (listingQueryDto.noPayoutAmount) {
      where += ` AND ( ${alias}."payoutAmount" IS NULL OR ${alias}."payoutAmount" = 0 )`;
    }

    if (listingQueryDto.noTripEnd) {
      where += ` AND ( ${alias}.metadata->>'tripEnd' IS NULL OR ${alias}.metadata->>'tripEnd' = '' )`;
    }

    if (
      listingQueryDto.compareTypes &&
      listingQueryDto.metadataFilterKeys &&
      listingQueryDto.metadataFilterValues &&
      listingQueryDto.compareTypes.length === listingQueryDto.metadataFilterKeys.length &&
      listingQueryDto.compareTypes.length === listingQueryDto.metadataFilterValues.length
    ) {
      for (let index = 0; index < listingQueryDto.metadataFilterKeys.length; index++) {
        const compareType = listingQueryDto.compareTypes[index];
        const key = listingQueryDto.metadataFilterKeys[index];
        const value = listingQueryDto.metadataFilterValues[index];
        if (key === MetadataFilterKey.TRIP_START || key === MetadataFilterKey.TRIP_END) {
          where += ` AND to_timestamp(${alias}.metadata->>'${key}', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"') ${compareType} to_timestamp('${new Date(
            value,
          ).toISOString()}', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')`;
        } else if (typeof value === "number") {
          where += ` AND COALESCE(${alias}.metadata->>'${key}', '0')::numeric ${compareType} ${value}`;
        } else {
          where += ` AND ${alias}.metadata->>'${key}' ${compareType} '${value}'`;
        }
      }
    }
    where += listingQueryDto.paymentType
      ? ` AND ${alias}.metadata->>'paymentType' = '${listingQueryDto.paymentType}'`
      : "";

    if (listingQueryDto.merchantPhone) {
      where += ` AND EXISTS (
        SELECT 1 FROM merchant
        WHERE merchant.id = ${alias}."merchantId"
        AND merchant."phoneNumber" LIKE '%${listingQueryDto.merchantPhone}%'
        AND merchant."platformMerchantType" = 'DASH'
      )`;
    }

    if (listingQueryDto.merchantId) {
      where += ` AND ${alias}."merchantId" = '${listingQueryDto.merchantId}'`;
    }

    if (listingQueryDto.merchantIds) {
      where += ` AND ${alias}."merchantId" IN (${listingQueryDto.merchantIds.map((id) => `'${id}'`).join(",")})`;
    }

    if (listingQueryDto.paymentStatus) {
      where += ` AND EXISTS (
        SELECT 1 FROM payment_tx
        WHERE payment_tx."txId" = tx.id
        AND payment_tx.status = '${listingQueryDto.paymentStatus}'
      )`;
    }

    where += listingQueryDto.tag
      ? listingQueryDto.tag === "NONE"
        ? ' AND NOT EXISTS(SELECT * FROM tx_tag WHERE tx_tag."txId" = tx."id" AND tx_tag."removedAt" IS NULL)'
        : ` AND EXISTS (SELECT * FROM tx_tag WHERE tx_tag.tag = '${listingQueryDto.tag}' AND tx_tag."txId" = tx."id" AND tx_tag."removedAt" IS NULL)`
      : "";

    let sortBy;
    if (listingQueryDto.sort) {
      if (
        listingQueryDto.sort === TxSortableType.TYPE ||
        listingQueryDto.sort === TxSortableType.PAYOUT_STATUS ||
        listingQueryDto.sort === TxSortableType.TOTAL
      ) {
        sortBy = `${alias}.${listingQueryDto.sort}`;
      } else {
        sortBy = `${alias}.metadata->>'${listingQueryDto.sort}'`;
      }
    }

    const [transactions, count] = await this.utilsService.api.paginateRaw<Tx>(
      this.txRepository,
      alias,
      where,
      {
        sort: sortBy,
        orderBy: listingQueryDto.direction,
        limit: listingQueryDto.limit,
        offset: listingQueryDto.offset,
      },
      [{ property: `${alias}.merchant`, alias: "merchant" }],
      [
        {
          subQueryFactory: (qb) => {
            return qb
              .select("tx_tag.txId")
              .addSelect("JSON_AGG(tx_tag) AS tags")
              .from("tx_tag", "tx_tag")
              .groupBy("tx_tag.txId");
          },
          alias: "TX_TAGS",
          condition: '"TX_TAGS"."txId" =  "tx"."id"',
        },
      ],
      [],
      ["tx.*", 'to_json("merchant") AS "merchant"', 'COALESCE("TX_TAGS"."tags", \'[]\') AS "tags"'],
      true,
    );
    return { count, transactions };
  }

  /**
   * Get transaction by id
   * @param txId string
   * @returns Tx
   */
  async getTransactionById(txId: string): Promise<Tx & { payouts: any[] }> {
    const tx = await this.txRepository.findOne({
      relations: ["paymentTx", "paymentTx.paymentInstrument", "merchant", "txTag", "parentTx", "childrenTxs"],
      where: { id: txId },
    });
    if (!tx) {
      throw errorBuilder.transaction.notFound(txId);
    }
    if (tx?.txTag && tx.txTag.length > 0) {
      tx.txTag.forEach((tag) => delete tag.tx);
    }
    const payouts = await this.findPayoutsByTxId(txId);
    (tx as any).payouts = payouts;
    return tx as Tx & { payouts: any[] };
  }

  /**
   * Get tx_event records by transaction ID
   * @param txId The ID of the transaction
   * @returns A list of TxEvent objects
   * @throws Error if no tx_events are found for the given txId
   */
  async getTxEventsByTxId(txId: string): Promise<TxEvent[]> {
    const txEvents = await this.txEventRepository.findByTxId(txId);
    if (!txEvents || txEvents.length === 0) {
      throw new Error(`No tx_events found for txId: ${txId}`);
    }
    return txEvents;
  }

  /**
   * convert payment tx from array to tree
   * @param paymentTxs PaymentTx[]
   * @returns paymentTxExtendeds PaymentTxExtended[]
   */
  convertPaymentTxFromArrayToTree(paymentTxs: PaymentTx[]): PaymentTxExtended[] {
    return paymentTxs
      .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
      .reduce((acc: PaymentTxExtended[], item: PaymentTx) => {
        item.tx = new Tx();
        if (!item.parent) {
          const paymentTxExtended: PaymentTxExtended = item;
          paymentTxExtended.canDoVoidOrCapture =
            (item.type === PaymentInformationType.AUTH || item.type === PaymentInformationType.SALE) &&
            item.status === PaymentInformationStatus.SUCCESS;
          acc.push(paymentTxExtended);
        } else {
          const parent = acc.find((parentItem) => parentItem.id === item.parent?.id);
          if (parent) {
            //for void/capture an auth, or refund a sale
            if (!parent.children) {
              parent.children = [];
            }
            delete item.parent;
            parent.children.push(item);
            if (
              (item.type === PaymentInformationType.VOID || item.type === PaymentInformationType.CAPTURE) &&
              item.status === PaymentInformationStatus.SUCCESS
            ) {
              parent.canDoVoidOrCapture = false;
            }
          } else {
            //for refund a capture
            for (const accItem of acc) {
              const childrenOfTopParent = accItem.children;
              const subParent = childrenOfTopParent?.find((child) => child.id === item.parent?.id);
              if (subParent) {
                if (!subParent.children) {
                  subParent.children = [];
                }
                delete item.parent;
                subParent.children.push(item);
                if (
                  (item.type === PaymentInformationType.VOID || item.type === PaymentInformationType.CAPTURE) &&
                  item.status === PaymentInformationStatus.SUCCESS
                ) {
                  subParent.canDoVoidOrCapture = false;
                }
                break;
              }
            }
          }
        }
        return acc;
      }, []);
  }

  /**
   * Get receipt by tx
   * @param tx Tx
   * @returns TxReceipt
   */
  getReceiptByTx(tx: Tx, language: ReceiptLanguageType): Promise<TxReceipt> {
    return this.transactionFactoryService.getReceiptByTx(tx, language);
  }

  /**
   * update metadata of a tx
   * @param tx Tx
   * @param data Record<string, any>
   * @returns Promise<Tx>
   */
  updateMetadata(tx: Tx, data: Record<string, any>): Promise<Tx> {
    return this.transactionFactoryService.updateMetadata(tx, data);
  }

  /**
   * unlock a tx
   * @param tx Tx
   * @param userId string
   * @returns Promise<Tx>
   */
  unlock(tx: Tx, userId: string): Promise<Tx> {
    return this.transactionFactoryService.unlock(tx, userId);
  }

  /**
   * pair a qrcode
   * @param qrCodeData QrCodeDecodedData
   * @param appDatabaseId string
   * @returns Promise<LockDocument>
   */
  async pair(
    qrCodeData: QrCodeDecodedData,
    appDatabaseId: string,
    paymentInstrumentId: string,
    language: LocalizedLanguage,
  ): Promise<Tx> {
    const qrCode = await this.appDatabaseService.qrRepository().findOneById(qrCodeData.qrCodeId);

    if (!qrCode) {
      throw errorBuilder.qrcode.notFound(qrCodeData.qrCodeId);
    }

    let tx = await this.txRepository.findOne({ where: { id: qrCode.transactionId }, relations: ["user"] });
    if (!tx) {
      throw errorBuilder.transaction.notFound(qrCode.transactionId);
    }
    this.transactionFactoryService.isTxAbleToPair(tx);
    // if (tx.user) {
    //   throw errorBuilder.qrcode.alreadyPaired(qrCodeData.qrCodeId);
    // }

    //payment instrument
    const paymentInstrument = await this.paymentInstrumentRepository.findOne({
      relations: ["user"],
      where: {
        user: { appDatabaseId },
        id: paymentInstrumentId,
        state: PaymentInstrumentState.VERIFIED,
        expirationDate: MoreThan(new Date()),
      },
    });
    if (!paymentInstrument) {
      throw errorBuilder.payment.instrument.notFound(paymentInstrumentId);
    }

    await this.transactionFactoryService.lock(tx, appDatabaseId, 90);

    const paymentTx = await this.paymentService.processAuth(tx, paymentInstrument, 0);
    await this.paymentTxRepository.save(paymentTx);
    if (paymentTx.status === PaymentInformationStatus.FAILURE) {
      throw errorBuilder.payment.authFailed(paymentTx.gatewayResponse);
    }

    //extra metadata, like update trip_itinerary
    let extraMetadata: Record<string, any> | undefined = undefined;
    if (language === LocalizedLanguage.EN) {
      extraMetadata = await this.transactionFactoryService.addExtraMetadata(tx, language);
    }

    tx = await this.transactionFactoryService.updateMetadata(tx, {
      user: {
        id: appDatabaseId,
        phone: paymentInstrument.user.phoneNumber,
      },
      paymentInstrument: {
        id: paymentInstrument.id,
        cardType: paymentInstrument.cardType,
        cardSuffix: paymentInstrument.cardSuffix,
      },
      paymentMethodSelected: paymentInstrument.cardType,
      paymentType: PaymentType.DASH,
      appUserLanguage: language,
      ...extraMetadata,
    });

    await this.transactionFactoryService.addToUserCurrentTx(tx, appDatabaseId);

    tx.user = paymentInstrument.user;
    await this.txRepository.save(tx);

    return tx;
  }

  /**
   * Set Tip from user app
   * @param tx Tx
   * @param userId string
   * @param tip number
   * @returns Promise<Tx>
   */
  async setTipFromUserApp(tx: Tx, userId: string, tip: number): Promise<Tx> {
    return this.transactionFactoryService.setTipFromUserApp(tx, userId, tip);
  }

  /**
   * Set Rating from user app
   * @param tx Tx
   * @param userId string
   * @param rating number
   * @returns Promise<Tx>
   */
  async setRating(tx: Tx, userId: string, rating: Rating): Promise<Tx> {
    return this.transactionFactoryService.setRating(tx, userId, rating);
  }

  /**
   * Add event
   * @param transactionId string
   * @param createdBy string
   * @param addEventBody AddEventBody
   * @returns Promise<TxEvent>
   */
  async addEvent(transactionId: string, createdBy: string, addEventBody: AddEventBody): Promise<TxEvent> {
    return this.transactionEventService.addEvent(transactionId, createdBy, addEventBody);
  }

  private async generateReconCsvForPayout(filePath: string, txs: Tx[]): Promise<string> {
    const columns: { id: keyof ReconFileColumnsDto; title: string }[] = [
      { id: "tripId", title: "Trip ID" },
      { id: "parentTxId", title: "Parent Tx" },
      { id: "type", title: "Type" },
      { id: "driverPhoneNumber", title: "Driver Phone" },
      { id: "driverName", title: "Driver Name" },
      { id: "licensePlate", title: "License Plate" },
      { id: "startLocation", title: "Start Location" },
      { id: "endLocation", title: "End Location" },
      { id: "startDateTime", title: "Start Time" },
      { id: "endDateTime", title: "End Time" },
      { id: "fare", title: "Fare" },
      { id: "extra", title: "Extra" },
      { id: "tripTotal", title: "Trip Total" },
      { id: "tip", title: "Tip" },
      { id: "adjustmentReason", title: "Adjustment Reason" },
      { id: "payoutAmount", title: "Payout Amount" },
    ];

    const csvWriter = createObjectCsvWriter({
      path: filePath,
      header: columns,
      encoding: "utf8",
    });

    const records = txs.map<ReconFileColumnsDto>((tx) => {
      let metadata: TripDocument;
      switch (tx.type) {
        case TxTypes.TX_ADJUSTMENT:
          if (!tx.parentTx) {
            throw errorBuilder.transaction.parentTxNotFound(tx.id);
          }
          metadata = tx.parentTx.metadata as TripDocument;

          return {
            tripId: tx.id,
            parentTxId: tx.parentTx?.id,
            type: tx.type,
            driverPhoneNumber: metadata.driver?.id ?? "",
            driverName: metadata.driver?.name ?? "",
            licensePlate: metadata.licensePlate,
            adjustmentReason:
              tx.type === TxTypes.TX_ADJUSTMENT ? (tx.metadata as TxAdjustmentMetadata)?.reason ?? "" : "",
            payoutAmount: tx.total ?? 0,
          };
        case TxTypes.TRIP:
        case TxTypes.HAILING_REQUEST:
        default:
          metadata = tx.metadata as TripDocument;

          return {
            tripId: tx.id,
            parentTxId: tx.parentTx?.id ?? "",
            type: tx.type,
            driverPhoneNumber: metadata.driver?.id ?? "",
            driverName: metadata.driver?.name ?? "",
            licensePlate: metadata.licensePlate,
            startLocation: metadata.locationStartAddress,
            endLocation: metadata.locationEndAddress,
            startDateTime: moment(metadata.tripStart)
              .utcOffset(TransactionService.TIMEZONE_OFFSET)
              .format("YYYY-MM-DD HH:mm:ss"),
            endDateTime: moment(metadata.tripEnd)
              .utcOffset(TransactionService.TIMEZONE_OFFSET)
              .format("YYYY-MM-DD HH:mm:ss"),
            fare: metadata.billing?.fare ?? metadata.fare,
            extra: metadata.billing?.extra ?? metadata.extra,
            tripTotal: metadata.billing?.tripTotal ?? metadata.tripTotal,
            tip: metadata.billing?.dashTips ?? metadata.dashTips ?? 0,
            payoutAmount: tx.payoutAmount ?? 0,
          };
      }
    });

    await csvWriter.writeRecords(records);

    return filePath;
  }

  async copyTripToDriver(transactionId: string): Promise<Tx> {
    const tx = await this.txRepository.findOne({
      where: { id: transactionId },
    });
    if (!tx) {
      throw errorBuilder.transaction.notFound(transactionId);
    }
    const expiresAt = await this.transactionFactoryService.getExpiresAt(tx);
    await this.pubsubService.publishMessageForCopyTripToDriverProcessing({
      txId: tx.id,
      expiresAt: expiresAt,
    });

    return tx;
  }

  async saveTransaction(tx: Tx): Promise<Tx> {
    await this.txRepository.save(tx);
    return tx;
  }

  async findPayoutsByTxId(txId: string) {
    return this.payoutRepository
      .createQueryBuilder("payout")
      .where('("payout"."originalRequest"::jsonb)->\'txIds\' @> :txId', { txId: JSON.stringify([txId]) })
      .getMany();
  }
}

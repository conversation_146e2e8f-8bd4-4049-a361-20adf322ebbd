import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";

import { phoneNumbersSchema } from "@nest/modules/cloud-task-notification-handler/dto/request.dto";
import { NotificationUserType } from "@nest/modules/database/entities/notificationTask.entity";
import { MerchantRepository } from "@nest/modules/database/repositories/merchant.repository";
import { MerchantNotificationTokenRepository } from "@nest/modules/database/repositories/merchantNotificationToken.repository";
import { PreferredLanguageType } from "@nest/modules/identity/dto/user.dto";
import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";
import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import { INotificationUserTokenParam } from "../notification-method/notification-method.interface";
import { IUserNotificationSender } from "../notification.interface";

@Injectable()
export class MerchantNotificationService implements IUserNotificationSender {
  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @InjectRepository(MerchantRepository) private readonly merchantRepository: MerchantRepository,
    @InjectRepository(MerchantNotificationTokenRepository)
    private readonly merchantNotificationTokenRepository: MerchantNotificationTokenRepository,
  ) {}

  async getValidPushNotificationTokens(phoneNumbers: string[]): Promise<INotificationUserTokenParam[]> {
    // we already checked phone numbers in validateUserPhoneNumbers, no need to check again
    const merchantTokens = await this.merchantNotificationTokenRepository.findDashMerchantByPhoneNumbers(phoneNumbers);
    this.logger.info(`[MerchantNotificationService] Sending notification to ${merchantTokens.length}`, {
      merchantTokens,
    });

    const userTokens: INotificationUserTokenParam[] = merchantTokens.map((token) => ({
      tokenId: token.id,
      userId: token.merchant.id,
      token: token.token,
      preferredLanguage: PreferredLanguageType.ZHHK, // assuming all merchants use ZHHK as preferred language
      userType: NotificationUserType.MERCHANTS,
    }));

    return userTokens;
  }

  /**
   * Validate the given phone numbers by checking if they conform to the
   * phoneNumbersSchema and if they match any users in the database. If any
   * validation errors are found or if some phone numbers do not match any users,
   * an error is thrown.
   * @throws {NotificationErrorDto} If phone numbers are invalid or do not match any users
   */
  async validateUserPhoneNumbers(phoneNumbers: string[]): Promise<void> {
    const validationResult = phoneNumbersSchema.validate(phoneNumbers, {
      abortEarly: false,
    });
    if (validationResult.error) {
      this.logger.warn(
        `[MerchantNotificationService] Invalid phone numbers provided for notifications: ${validationResult.error}`,
      );
      throw errorBuilder.notification.invalidPhoneNumbers(validationResult.error);
    }

    const foundUsers = await this.merchantRepository.findDashMerchantsByPhoneNumbers(phoneNumbers);
    if (foundUsers.length !== phoneNumbers.length) {
      const foundPhoneNumbers = foundUsers.map((user) => user.phoneNumber);
      const notFoundPhoneNumbers = phoneNumbers.filter(
        (phoneNumber: string) => !foundPhoneNumbers.includes(phoneNumber),
      );

      this.logger.warn("[MerchantNotificationService] Some phone numbers do not match any users", {
        notFoundPhoneNumbers,
      });
      throw errorBuilder.notification.noUsersFound(notFoundPhoneNumbers);
    }
  }
}

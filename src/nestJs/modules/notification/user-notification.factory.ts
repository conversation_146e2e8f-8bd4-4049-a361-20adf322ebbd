import { Injectable } from "@nestjs/common";

import { AppUserNotificationService } from "./app-user-notification/app-user-notification.service";
import { MerchantNotificationService } from "./merchant-notification/merchant-notification.service";
import { IUserNotificationSender, IUserPhoneValidator } from "./notification.interface";
import { NotificationUserType } from "../database/entities/notificationTask.entity";
import { NotificationTaskRepository } from "../database/repositories/notificationTask.repository";
import { errorBuilder } from "../utils/utils/error.utils";

@Injectable()
export class UserNotificationFactory {
  constructor(
    private readonly appUserNotificationService: AppUserNotificationService,
    private readonly merchantNotificationService: MerchantNotificationService,
    private readonly notificationTaskRepository: NotificationTaskRepository,
  ) {}

  getSender(userType: NotificationUserType): IUserNotificationSender {
    switch (userType) {
      case NotificationUserType.USERS:
        return this.appUserNotificationService;
      case NotificationUserType.MERCHANTS:
        return this.merchantNotificationService;
      default:
        throw errorBuilder.notification.invalidUserType(userType);
    }
  }

  getValidator(userType: NotificationUserType): IUserPhoneValidator {
    switch (userType) {
      case NotificationUserType.USERS:
        return this.appUserNotificationService;
      case NotificationUserType.MERCHANTS:
        return this.merchantNotificationService;
      default:
        throw errorBuilder.notification.invalidUserType(userType);
    }
  }

  // useful for conditions where we only have taskId and need to get the manager
  async getValidatorByTaskId(taskId: string): Promise<IUserPhoneValidator> {
    const task = await this.notificationTaskRepository.findOne({
      where: {
        id: taskId,
      },
    });
    if (!task) {
      throw errorBuilder.notification.taskNotFound(taskId);
    }

    return this.getValidator(task.userType);
  }
}

import Joi from "joi";

import {
  CreateManyNotificationRequestDto,
  phoneNumbersSchema,
  UpdateManyNotificationRequestDto,
} from "../cloud-task-notification-handler/dto/request.dto";
import NotificationTask, { NotificationTaskType } from "../database/entities/notificationTask.entity";
import { PaginatedResponseDto } from "../utils/paginated.dto";
import { NotificationTaskFilterDto } from "./dto/notification-filters.dto";
import { notificationRequestSchema } from "./dto/notification.dto";
import { INotificationUserTokenParam } from "./notification-method/notification-method.interface";

type CloudTaskCallback = (task: NotificationTask) => Promise<string>;

export interface IUserNotificationTaskManager {
  createNotificationTask(
    createManyNotificationRequestDto: CreateManyNotificationRequestDto,
    createdBy: string,
    cloudTaskCallback: CloudTaskCallback,
  ): Promise<NotificationTask>;

  updateNotificationAfterCallback(
    taskId: string,
    updatedBy: string,
    updateManyNotificationRequestDto: UpdateManyNotificationRequestDto,
    cloudTaskCallback: CloudTaskCallback,
  ): Promise<NotificationTask>;

  deleteNotificationAfterCallback(
    taskId: string,
    deletedBy: string,
    cloudTaskCallback: CloudTaskCallback,
  ): Promise<NotificationTask>;
}

export type NotificationSenderRequestPayload = Pick<
  CreateManyNotificationRequestDto,
  "name" | "type" | "phoneNumbers" | "notificationRequest"
>;

export const notificationSenderRequestSchema = Joi.object<NotificationSenderRequestPayload>({
  name: Joi.string().required(),
  type: Joi.string()
    .valid(...Object.values(NotificationTaskType))
    .required(),
  phoneNumbers: phoneNumbersSchema.required(),
  notificationRequest: notificationRequestSchema.required(),
});

export interface IUserNotificationSender {
  getValidPushNotificationTokens(phoneNumbers: string[]): Promise<INotificationUserTokenParam[]>;
  // sendNotifications(
  //   type: NotificationTaskType,
  //   { phoneNumbers, notificationRequest }: NotificationSenderRequestPayload,
  // ): Promise<void>;
}

// separate interface for readers
export interface IUserNotificationReader {
  getNotificationTasks(filter: NotificationTaskFilterDto): Promise<PaginatedResponseDto<NotificationTask>>;
}

export interface IUserPhoneValidator {
  // throws an error if phone numbers are invalid or do not match any users
  validateUserPhoneNumbers(phoneNumbers: string[]): Promise<void>;
}

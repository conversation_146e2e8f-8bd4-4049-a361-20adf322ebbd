import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export class NotificationRequestDto {
  @ApiProperty({
    description: "English title of the notification",
    example: "Test Notification",
    minimum: 1,
    maximum: 100,
  })
  titleEn: string;

  @ApiProperty({
    description: "Traditional Chinese title of the notification",
    example: "測試通知",
    minimum: 1,
    maximum: 100,
  })
  titleHk: string;

  @ApiProperty({
    description: "English body of the notification",
    example: "This is a test notification",
    minimum: 1,
    maximum: 500,
  })
  bodyEn: string;

  @ApiProperty({
    description: "Traditional Chinese body of the notification",
    example: "這是一個測試通知",
    minimum: 1,
    maximum: 500,
  })
  bodyHk: string;

  @ApiProperty({
    description: "What the app should do when the notification is clicked",
    example: "OPEN_APP",
  })
  clickAction?: string;
}

const titleSchema = Joi.string().min(1).max(100);
const bodySchema = Joi.string().min(1).max(500);

export const notificationRequestSchema = Joi.object({
  titleEn: titleSchema.required(),
  titleHk: titleSchema.required(),
  bodyEn: bodySchema.required(),
  bodyHk: bodySchema.required(),
  clickAction: Joi.string().optional(),
});

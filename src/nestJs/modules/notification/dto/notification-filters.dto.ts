import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { NotificationTaskStatus, NotificationTaskType } from "@nest/modules/database/entities/notificationTask.entity";

export enum NotificationTaskSortKey {
  CREATED_AT = "createdAt",
  SCHEDULED_AT = "scheduledAt",
  CREATED_BY = "createdBy",
  NAME = "name",
  USER_TYPE = "userType",
}

export enum NotificationTaskSortOrder {
  ASC = "ASC",
  DESC = "DESC",
}

export class NotificationTaskFilterDto {
  @ApiProperty({ required: false, type: "uuid" })
  id?: string;

  @ApiProperty({ required: false })
  createdBy?: string;

  @ApiProperty({ required: false, enum: Object.values(NotificationTaskType) })
  type?: NotificationTaskType;

  @ApiProperty({ required: false, enum: Object.values(NotificationTaskStatus) })
  status?: NotificationTaskStatus;

  @ApiProperty({
    required: false,
    minimum: 1,
    default: 1,
  })
  page: number;

  @ApiProperty({
    required: false,
    minimum: 1,
    maximum: 100,
    default: 10,
  })
  pageSize: number;

  @ApiProperty({
    required: false,
    enum: NotificationTaskSortOrder,
    default: NotificationTaskSortOrder.DESC,
  })
  sort: NotificationTaskSortOrder;

  @ApiProperty({
    required: false,
    enum: NotificationTaskSortKey,
    default: NotificationTaskSortKey.CREATED_AT,
  })
  sortBy: NotificationTaskSortKey;
}

export const notificationTaskFilterSchema = Joi.object<NotificationTaskFilterDto>({
  id: Joi.string().uuid().optional(),
  createdBy: Joi.string().optional(),
  type: Joi.string()
    .valid(...Object.values(NotificationTaskType))
    .optional(),
  status: Joi.string()
    .valid(...Object.values(NotificationTaskStatus))
    .optional(),

  page: Joi.number().integer().positive().min(1).optional().default(1),
  pageSize: Joi.number().integer().positive().min(1).max(100).optional().default(10),
  sort: Joi.string()
    .valid(...Object.values(NotificationTaskSortOrder))
    .optional()
    .default(NotificationTaskSortOrder.DESC),
  sortBy: Joi.string()
    .valid(...Object.values(NotificationTaskSortKey))
    .optional()
    .default(NotificationTaskSortKey.CREATED_AT),
});

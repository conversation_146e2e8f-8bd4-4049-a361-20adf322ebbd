import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";

import { UserNotificationTokenRepository } from "@nest/modules/database/repositories/userNotificationToken.repository";
import { PreferredLanguageType } from "@nest/modules/identity/dto/user.dto";

import { phoneNumbersSchema } from "../../cloud-task-notification-handler/dto/request.dto";
import { NotificationUserType } from "../../database/entities/notificationTask.entity";
import { UserRepository } from "../../database/repositories/user.repository";
import LoggerServiceAdapter from "../../utils/logger/logger.service";
import { errorBuilder } from "../../utils/utils/error.utils";
import { INotificationUserTokenParam } from "../notification-method/notification-method.interface";
import { IUserNotificationSender, IUserPhoneValidator } from "../notification.interface";

@Injectable()
export class AppUserNotificationService implements IUserNotificationSender, IUserPhoneValidator {
  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @InjectRepository(UserRepository) private readonly userRepository: UserRepository,
    @InjectRepository(UserNotificationTokenRepository)
    private readonly userNotificationTokenRepository: UserNotificationTokenRepository,
  ) {}

  async getValidPushNotificationTokens(phoneNumbers: string[]): Promise<INotificationUserTokenParam[]> {
    const users = await this.userNotificationTokenRepository.findByPhoneNumbers(phoneNumbers);
    this.logger.info(`[AppUserNotificationService] Sending notification to ${users.length}`, { users });

    const userTokens: INotificationUserTokenParam[] = users.map((user) => ({
      userId: user.user.id,
      token: user.token,
      preferredLanguage: user.user.preferredLanguage || PreferredLanguageType.EN,
      tokenId: user.id,
      userType: NotificationUserType.USERS,
    }));

    return userTokens;
  }

  /**
   * Validate the given phone numbers by checking if they conform to the
   * phoneNumbersSchema and if they match any users in the database. If any
   * validation errors are found or if some phone numbers do not match any users,
   * an error is thrown.
   * @throws {NotificationErrorDto} If phone numbers are invalid or do not match any users
   */
  async validateUserPhoneNumbers(phoneNumbers: string[]): Promise<void> {
    const validationResult = phoneNumbersSchema.validate(phoneNumbers, {
      abortEarly: false,
    });
    if (validationResult.error) {
      this.logger.warn(
        `[AppUserNotificationService] Invalid phone numbers provided for notifications: ${validationResult.error}`,
      );
      throw errorBuilder.notification.invalidPhoneNumbers(validationResult.error);
    }

    const foundUsers = await this.userRepository.findAppUsersByPhoneNumbers(phoneNumbers);
    if (foundUsers.length !== phoneNumbers.length) {
      const foundPhoneNumbers = foundUsers.map((user) => user.phoneNumber);
      const notFoundPhoneNumbers = phoneNumbers.filter(
        (phoneNumber: string) => !foundPhoneNumbers.includes(phoneNumber),
      );

      this.logger.warn("[AppUserNotificationService] Some phone numbers do not match any users.", {
        notFoundPhoneNumbers,
      });
      throw errorBuilder.notification.noUsersFound(notFoundPhoneNumbers);
    }
  }
}

import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { NotificationHistoryRepository } from "@nest/modules/database/repositories/notificationHistory.repository";
import { LoggerModule } from "@nest/modules/utils/logger/logger.module";

import { EmailNotificationService } from "./email-notification.service";
import { NotificationFactory } from "./notification.factory";
import { PushNotificationService } from "./push-notification.service";
import { SmsNotificationService } from "./sms-notification.service";

@Module({
  imports: [LoggerModule],
  providers: [
    EmailNotificationService,
    SmsNotificationService,
    PushNotificationService,
    NotificationHistoryRepository,
    NotificationFactory,
  ],
  exports: [EmailNotificationService, SmsNotificationService, PushNotificationService, NotificationFactory],
  controllers: [],
})
export class NotificationMethodsModule {}

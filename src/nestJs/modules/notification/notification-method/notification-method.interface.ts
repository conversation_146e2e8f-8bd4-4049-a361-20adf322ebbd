import { NotificationUserType } from "@nest/modules/database/entities/notificationTask.entity";

import { NotificationRequestDto } from "../dto/notification.dto";

export interface INotificationUserTokenParam {
  tokenId: string;
  userId: string;
  token: string;
  preferredLanguage: string;
  userType: NotificationUserType;
}

export interface INotificationService {
  notify(token: INotificationUserTokenParam, message: NotificationRequestDto): Promise<string>;
  notifyMany(tokens: INotificationUserTokenParam[], message: NotificationRequestDto): Promise<string[]>;
}

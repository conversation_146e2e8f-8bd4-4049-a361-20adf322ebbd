import { Injectable } from "@nestjs/common";

import { INotificationService, INotificationUserTokenParam } from "./notification-method.interface";
import { NotificationTaskType } from "../../database/entities/notificationTask.entity";
import { errorBuilder } from "../../utils/utils/error.utils";
import { NotificationRequestDto } from "../dto/notification.dto";

@Injectable()
export class SmsNotificationService implements INotificationService {
  notify(tokens: INotificationUserTokenParam, message: NotificationRequestDto): Promise<string> {
    throw errorBuilder.notification.notImplemented(NotificationTaskType.SMS);
  }

  notifyMany(tokens: INotificationUserTokenParam[], message: NotificationRequestDto): Promise<string[]> {
    throw errorBuilder.notification.notImplemented(NotificationTaskType.SMS);
  }
}

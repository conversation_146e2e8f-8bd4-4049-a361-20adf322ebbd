import { Inject, Injectable } from "@nestjs/common";

import { EmailNotificationService } from "./email-notification.service";
import { INotificationService } from "./notification-method.interface";
import { PushNotificationService } from "./push-notification.service";
import { SmsNotificationService } from "./sms-notification.service";
import { NotificationTaskType } from "../../database/entities/notificationTask.entity";

@Injectable()
export class NotificationFactory {
  private notificationTypeMap: Record<NotificationTaskType, INotificationService>;

  constructor(
    @Inject(PushNotificationService) private readonly pushNotificationService: INotificationService,
    @Inject(EmailNotificationService) private readonly emailNotificationService: INotificationService,
    @Inject(SmsNotificationService) private readonly smsNotificationService: INotificationService,
  ) {
    this.notificationTypeMap = {
      [NotificationTaskType.PUSH]: this.pushNotificationService,
      [NotificationTaskType.EMAIL]: this.emailNotificationService,
      [NotificationTaskType.SMS]: this.smsNotificationService,
    };
  }

  get(type: NotificationTaskType): INotificationService {
    const service = this.notificationTypeMap[type];
    if (!service) {
      throw new Error(`Notification service for type ${type} not found`);
    }

    return service;
  }
}

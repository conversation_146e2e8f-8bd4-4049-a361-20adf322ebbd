import { Controller } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";

import { apiTags } from "@nest/modules/utils/utils/swagger.utils";

import { TeamOrderNotificationService } from "./teamOrderNotification.service";
import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";

@ApiTags(...apiTags.teamsNotification)
@Controller("team-order-notifications")
export class TeamOrderNotificationController {
  constructor(private readonly teamOrderNotificationService: TeamOrderNotificationService) {}

  @CorrelationContext()
  async schedulePickupReminderNotification(txId: string) {
    return this.teamOrderNotificationService.schedulePickupReminderNotification(txId);
  }

  @CorrelationContext()
  async orderCreatedNotification(txId: string) {
    return this.teamOrderNotificationService.sendOrderCreatedNotification(txId);
  }

  @CorrelationContext()
  async orderStatusChangedNotification(txId: string, status: string) {
    return this.teamOrderNotificationService.sendOrderStatusChangedNotification(txId, status);
  }
}

import { INestApplication, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { NestFactory } from "@nestjs/core";
import { ExpressAdapter } from "@nestjs/platform-express";
import { SwaggerModule, DocumentBuilder } from "@nestjs/swagger";
import express, { Express } from "express";
import { logger } from "firebase-functions/v2";

import { AppModule } from "../../app.module";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { DashExceptionFilter } from "../utils/utils/error.utils";
import { apiTags } from "../utils/utils/swagger.utils";

type FctType<Event> = (app: INestApplication, event: Event) => any;

/**
 * Nest service
 */
@Injectable()
export class NestService {
  server: Express;
  app: INestApplication;
  isStarting = false;

  constructor() {
    this.server = express();
  }

  /**
   * Wrapper function to make sure the Nest application is available when we call the callback function
   * @param main The Nest application
   * @param callback The callback function
   * @returns The result of the callback function (ReturnType)
   */
  listenerWrapper =
    <Event>(callback: FctType<Event>) =>
    async (event: Event) => {
      if (this.isStarting) {
        await new Promise<void>((resolve) => {
          const waitInterval = setInterval(() => {
            if (this.app) {
              clearInterval(waitInterval);
              resolve();
            }
          }, 100);
        });
      }

      if (!this.app) {
        this.isStarting = true;
        await this.createNestServer();
        this.isStarting = false;
      }

      return callback(this.app, event);
    };

  /**
   * Create the Nest application
   * @returns The Nest application
   */
  createNestServer = async () => {
    try {
      if (!this.app) {
        const startInitAt = performance.now();
        this.app = await NestFactory.create(AppModule, new ExpressAdapter(this.server));
        this.app.useGlobalFilters(new DashExceptionFilter(this.app.get(LoggerServiceAdapter)));
        this.app.enableCors();
        const configService = this.app.get(ConfigService);
        const swaggerBasePath = configService.get<string>("SWAGGER_BASE_PATH") ?? "";
        const projectId = configService.get<string>("GCLOUD_PROJECT") ?? "";
        const configBuilder = new DocumentBuilder()
          .addServer(swaggerBasePath)
          .setTitle(`Dash API : ${projectId}`)
          .setVersion("1.0.0")
          .addBearerAuth();
        // add tags as global tags before building the document
        for (const tag of Object.values(apiTags).flat()) {
          configBuilder.addTag(tag);
        }
        const config = configBuilder.build();
        const document = SwaggerModule.createDocument(this.app, config);
        SwaggerModule.setup("api", this.app, document, {
          swaggerOptions: {
            tagsSorter: "alpha",
            operationsSorter: "alpha",
          },
        });

        await this.app.init();
        const endInitAt = performance.now();
        logger.info(`NestJS application initialized in ${endInitAt - startInitAt}ms`);
      }

      return this.app;
    } catch (error) {
      logger.error("Error while creating NestJS application", { error });
      throw error;
    }
  };
}

export const nestServiceInstance = new NestService();

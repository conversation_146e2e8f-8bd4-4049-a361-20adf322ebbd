import Joi from "joi";

import { TxTypes, SubTxTypes } from "../../transaction/dto/txType.dto";

export enum PaymentInstrumentType {
  VISA = "VISA",
  ALIPAY = "ALIPAY",
  CUP = "CUP",
  MASTERCARD = "MASTERCARD",
  MASTER = "MASTER",
  WECHAT = "WECHAT",
  OCTOPUS = "OCTOPUS",
}
export const paymentInstrumentTypeSchema = Joi.string<PaymentInstrumentType>().valid(
  ...Object.values(PaymentInstrumentType),
);

export enum PaymentChannelType {
  TAXIPOS = "TAXIPOS",
  APP = "APP",
}
export const paymentChannelTypeSchema = Joi.string<PaymentChannelType>().valid(...Object.values(PaymentChannelType));

export enum CampaignSponsorType {
  DASH = "DASH",
  THIRD_PARTY = "THIRD_PARTY",
}

export const campaignSponsorTypeSchema = Joi.string<CampaignSponsorType>().valid(...Object.values(CampaignSponsorType));

export class ApplicationRule {
  paymentInstrumentType: PaymentInstrumentType;
  paymentChannel: PaymentChannelType;
}
export const applicationRuleSchema = Joi.object({
  paymentInstrumentType: paymentInstrumentTypeSchema,
  paymentChannel: paymentChannelTypeSchema,
});

export const campaignIdSchema = Joi.string().uuid();

export class FilterCampaignByApplicationRule {
  userId?: string;
  transactionType?: TxTypes;
  transactionSubtype?: SubTxTypes;
  paymentInstrumentType?: PaymentInstrumentType;
  paymentChannel?: PaymentChannelType;
}

export const CampaignEventTriggers = {
  USER_SIGNED_UP: "USER_SIGNED_UP",
  USER_SIGNED_UP_REFEREE: "USER_SIGNED_UP_REFEREE",
  USER_SIGNED_UP_REFERRER: "USER_SIGNED_UP_REFERRER",
};

import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export class UpdateCampaignBodyDto {
  @ApiProperty({ nullable: true })
  nameEn?: string;
  @ApiProperty({ nullable: true })
  nameTc?: string;
  @ApiProperty({ nullable: true })
  descriptionEn?: string;
  @ApiProperty({ nullable: true })
  descriptionTc?: string;
  @ApiProperty({ nullable: true })
  imageEn?: string;
  @ApiProperty({ nullable: true })
  imageTc?: string;
  @ApiProperty({ nullable: true })
  startAt?: Date;
  @ApiProperty({ nullable: true })
  endAt?: Date;
  @ApiProperty({ nullable: true })
  maxCount?: number;
  @ApiProperty()
  priority?: number;
  @ApiProperty({ example: '{"if" : [ {">" : [{"var":"tripTotal"}, 0]}, -5, 0 ]}' })
  applicationRules?: string;
  @ApiProperty({ example: '{"if" : [ {">" : [{"var":"tripTotal"}, 0]}, -5, 0 ]}' })
  discountRules?: string;
  @ApiProperty({ example: '{"if" : [ {">" : [{"var":"tripTotal"}, 0]}, 5, 0 ]}' })
  bonusRules?: string;
  @ApiProperty()
  campaignMaxCount?: number;
}

export const updateCampaignBodySchema = Joi.object({
  nameEn: Joi.string().max(100).optional(),
  nameTc: Joi.string().max(100).optional(),
  descriptionEn: Joi.string().max(4000).optional(),
  descriptionTc: Joi.string().max(4000).optional(),
  imageEn: Joi.string().uri().max(200).optional(),
  imageTc: Joi.string().uri().max(200).optional(),
  startAt: Joi.date().optional(),
  endAt: Joi.date().optional(),
  maxCount: Joi.number().positive().integer().optional(),
  priority: Joi.number().positive().integer().optional(),
  applicationRules: Joi.string().optional(),
  discountRules: Joi.string().optional(),
  bonusRules: Joi.string().optional(),
  campaignMaxCount: Joi.number().positive().integer().optional(),
});

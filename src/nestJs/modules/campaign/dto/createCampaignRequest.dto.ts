import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export class CreateCampaignBodyDto {
  @ApiProperty()
  nameEn: string;
  @ApiProperty()
  nameTc: string;
  @ApiProperty()
  descriptionEn: string;
  @ApiProperty()
  descriptionTc: string;
  @ApiProperty()
  imageEn: string;
  @ApiProperty()
  imageTc: string;
  @ApiProperty()
  startAt: Date;
  @ApiProperty()
  endAt: Date;
  @ApiProperty()
  maxCount: number;
  @ApiProperty()
  priority: number;
  @ApiProperty({ example: '{"if" : [ {">" : [{"var":"tripTotal"}, 0]}, -5, 0 ]}' })
  applicationRules: string;
  @ApiProperty({ example: '{"if" : [ {">" : [{"var":"tripTotal"}, 0]}, -5, 0 ]}' })
  discountRules?: string;
  @ApiProperty({ example: '{"if" : [ {">" : [{"var":"tripTotal"}, 0]}, 5, 0 ]}' })
  bonusRules?: string;
  @ApiProperty()
  campaignMaxCount?: number;
}

export const createCampaignBodySchema = Joi.object({
  nameEn: Joi.string().max(100).required(),
  nameTc: Joi.string().max(100).required(),
  descriptionEn: Joi.string().max(4000).required(),
  descriptionTc: Joi.string().max(4000).required(),
  imageEn: Joi.string().uri().max(200).required(),
  imageTc: Joi.string().uri().max(200).required(),
  startAt: Joi.date().required(),
  endAt: Joi.date().greater(Joi.ref("startAt")).required(),
  maxCount: Joi.number().positive().integer().required(),
  priority: Joi.number().positive().integer().required(),
  applicationRules: Joi.string().required(),
  discountRules: Joi.string().optional(),
  bonusRules: Joi.string().optional(),
  campaignMaxCount: Joi.number().positive().integer().optional(),
});

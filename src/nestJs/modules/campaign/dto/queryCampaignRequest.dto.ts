import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import Jo<PERSON> from "joi";
import { Timestamp } from "typeorm";

import Campaign from "../../database/entities/campaign.entity";
import { BaseListingQueryDto } from "../../validation/dto/listingSchema.dto";

export class QueryCampaignRequestDto extends BaseListingQueryDto {
  @ApiPropertyOptional({ type: Timestamp })
  startAt?: Date;
  @ApiPropertyOptional({ type: Timestamp })
  endAt?: Date;
}

export const queryCampaignBodySchema = Joi.object({
  startAt: Joi.date().optional(),
  endAt: Joi.date().optional(),
});

export class CampaignListingResponseDto {
  @ApiProperty()
  campaigns: Campaign[];

  @ApiProperty()
  count: number;
}

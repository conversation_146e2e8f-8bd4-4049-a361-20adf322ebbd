import { forwardRef, Module } from "@nestjs/common";

import { CampaignService } from "./campaign.service";
import { CampaignRepository } from "../database/repositories/campaign.repository";
import { DiscountRepository } from "../database/repositories/discount.repository";
import { UserModule } from "../user/user.module";

@Module({
  providers: [CampaignRepository, CampaignService, DiscountRepository],
  imports: [forwardRef(() => UserModule)],
  controllers: [],
  exports: [CampaignService],
})
export class CampaignModule {}

import { Injectable } from "@nestjs/common";

import { LocalizedLanguage } from "../../../location/dto/location.dto";
import { TransactionService } from "../../../transaction/transaction.service";
import { errorBuilder } from "../../../utils/utils/error.utils";
import {
  QrCodeDecodedData,
  QrCodeDecodedDataType,
  QrCodeDecodedDataVersion,
  QrCodePairResponse,
} from "../../dto/qrCode.dto";

type FactoryServicesTypes = {
  service: TransactionService;
};

/**
 * Dash factory service
 */
@Injectable()
export class DashFactoryService {
  dashFactoryServices: Record<QrCodeDecodedDataVersion, Record<QrCodeDecodedDataType, FactoryServicesTypes>>;

  constructor(private readonly transctionService: TransactionService) {
    this.dashFactoryServices = {
      [QrCodeDecodedDataVersion.ONE]: {
        [QrCodeDecodedDataType.T2]: {
          service: this.transctionService,
        },
      },
    };
  }

  /**
   * Check baseUrl factory
   * @param qrCodeData QrCodeDecodedData
   * @returns FactoryServicesTypes
   */
  private checkFactory(qrCodeData: QrCodeDecodedData): FactoryServicesTypes {
    const { type, version } = qrCodeData;
    if (!version) {
      throw errorBuilder.global.requiredParam("version");
    }
    if (!type) {
      throw errorBuilder.global.requiredParam("type");
    }

    const factory = this.dashFactoryServices?.[version]?.[type];
    if (!factory || !factory.service) {
      throw errorBuilder.qrcode.factoryNotImplemented(`${version} ${type}`);
    }

    return factory;
  }

  /**
   * pair a qrcode
   * @param qrCodeData QrCodeDecodedData
   * @param userId string
   * @returns Promise<QrCodePairResponse>
   */
  async pair(
    qrCodeData: QrCodeDecodedData,
    userId: string,
    paymentInstrumentId: string,
    language: LocalizedLanguage,
  ): Promise<QrCodePairResponse> {
    const factory = this.checkFactory(qrCodeData);
    return factory.service.pair(qrCodeData, userId, paymentInstrumentId, language);
  }
}

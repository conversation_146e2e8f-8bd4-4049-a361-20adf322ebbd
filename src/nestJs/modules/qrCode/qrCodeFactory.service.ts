import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import { QrCodeDecodedData, QrCodePairResponse } from "./dto/qrCode.dto";
import { DashFactoryService } from "./modules/Dash/dashFactory.service";
import { LocalizedLanguage } from "../location/dto/location.dto";
import { errorBuilder } from "../utils/utils/error.utils";

type FactoryServicesMap = {
  service: DashFactoryService;
};

/**
 * Qr code factory service
 */
@Injectable()
export class QrCodeFactoryService {
  readonly qrCodeFactoryServices: Record<string, FactoryServicesMap>;

  constructor(private readonly configService: ConfigService, private readonly dashService: DashFactoryService) {
    const dashUrl: string = this.configService.getOrThrow("QR_CODE_URL_DASH");
    this.qrCodeFactoryServices = {
      [dashUrl]: {
        service: this.dashService,
      },
    };
  }

  /**
   * Check baseUrl factory
   * @param baseUrl string
   * @returns FactoryServicesMap
   */
  private checkFactory(baseUrl: string): FactoryServicesMap {
    if (!baseUrl) {
      throw errorBuilder.global.requiredParam("baseUrl");
    }

    const factory = this.qrCodeFactoryServices[baseUrl];
    if (!factory || !factory.service) {
      throw errorBuilder.qrcode.factoryNotImplemented(baseUrl);
    }

    return factory;
  }

  /**
   * pair a qrcode
   * @param qrCodeData QrCodeDecodedData
   * @param userId string
   * @returns Promise<QrCodePairResponse>
   */
  async pair(
    qrCodeData: QrCodeDecodedData,
    userId: string,
    paymentInstrumentId: string,
    language: LocalizedLanguage,
  ): Promise<QrCodePairResponse> {
    const factory = this.checkFactory(qrCodeData.baseUrl);
    return factory.service.pair(qrCodeData, userId, paymentInstrumentId, language);
  }
}

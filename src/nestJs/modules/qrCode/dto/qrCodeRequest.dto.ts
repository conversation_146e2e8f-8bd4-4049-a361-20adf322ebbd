import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { QrType, qrTypeSchema } from "./qrType";

export class qrCodeRequestBodyDto {
  @ApiProperty()
  txId: string;

  @ApiProperty()
  type: QrType;

  @ApiProperty()
  licensePlate: string;
}

export const qrRequestBodySchema = Joi.object({
  txId: Joi.string().required(),
  type: qrTypeSchema.required(),
  licensePlate: Joi.string().required(),
});

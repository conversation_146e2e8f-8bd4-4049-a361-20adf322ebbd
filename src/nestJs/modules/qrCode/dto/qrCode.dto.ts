import { ApiProperty } from "@nestjs/swagger";

import { QrCodeDocument } from "../../appDatabase/documents/qrCode.document";
import Tx from "../../database/entities/tx.entity";

export enum QrCodeDecodedDataVersion {
  ONE = "1",
}

export enum QrCodeDecodedDataType {
  T2 = "T2",
}

export type QrCodeDecodedData = {
  baseUrl: string;
  version: QrCodeDecodedDataVersion;
  type: QrCodeDecodedDataType;
  qrCodeId: string;
  contextualCustomData: string;
};

// Add other types later when needed
export type QrCodePairResponse = Tx;

/**
 * Transform the link passed in param to a QrCodeDecodedData
 * example: https://dash.com/1T20SClCNCCDJTqGzxIrLddDASH02T
 *   - https://dash.com/: base url
 *   - 1: version (position 1 after the base url)
 *   - T2: type (position 2-3 after the base url)
 *   - 0SClCNCCDJTqGzxIrLdd: qrCode Id (position 4-23 after the base url)
 *   - DASH02T: contextual custom data (position 24-... after the base url)
 * @param link
 * @returns QrCodeDecodedData
 */
export const qrCodeLinkTransform = (link: string): QrCodeDecodedData => {
  const [, , baseUrl, search] = link.split("/");
  const urlParams = new URLSearchParams(search);
  const data = urlParams.get("t") ?? "";
  const version = data.charAt(0) as QrCodeDecodedDataVersion;
  const type = data.slice(1, 3) as QrCodeDecodedDataType;
  const qrCodeId = data.slice(3, 23);
  const contextualCustomData = data.slice(23);
  return {
    baseUrl,
    version,
    type,
    qrCodeId,
    contextualCustomData,
  };
};

/**
 * encode to a QR string
 */

class QrCode {
  @ApiProperty()
  qrString: string;

  @ApiProperty()
  id: string;

  static fromQrDocument(qrCodeDocument: QrCodeDocument): QrCode {
    const qrCode = new QrCode();
    qrCode.id = qrCodeDocument.id || "";
    qrCode.qrString = `https://${qrCodeDocument.baseUrl}/ee2yz?l=${qrCodeDocument.metadata?.licensePlate}&t=${QrCodeDecodedDataVersion.ONE}${QrCodeDecodedDataType.T2}${qrCodeDocument.id}${qrCodeDocument?.metadata?.licensePlate}`;
    return qrCode;
  }
}

export default QrCode;

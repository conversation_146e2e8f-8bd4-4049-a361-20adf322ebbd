import { MiddlewareConsumer, Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { DashModule } from "./modules/Dash/dash.module";
import { QrCodeController } from "./qrCode.controller";
import { QrCodeService } from "./qrCode.service";
import { QrCodeFactoryService } from "./qrCodeFactory.service";
import { MeterDeviceAuthMiddleware } from "../../infrastructure/middlewares/meterDeviceAuth.middleware";
import { AppDatabaseModule } from "../appDatabase/appDatabase.module";

@Module({
  providers: [QrCodeFactoryService, QrCodeService],
  controllers: [QrCodeController],
  imports: [ConfigModule, DashModule, AppDatabaseModule],
  exports: [QrCodeFactoryService],
})
export class QrCodeModule {
  /**
   * Configure middleware
   * @param consumer MiddlewareConsumer
   */
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(MeterDeviceAuthMiddleware).forRoutes("qrcode");
  }
}

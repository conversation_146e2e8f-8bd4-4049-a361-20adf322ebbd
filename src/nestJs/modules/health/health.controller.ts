import { Controller, Get } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { HealthCheckService, HealthCheck, TypeOrmHealthIndicator } from "@nestjs/terminus";

import { apiTags } from "../utils/utils/swagger.utils";

@Controller("health")
@ApiTags(...apiTags.public)
export class HealthController {
  constructor(private health: HealthCheckService, private db: TypeOrmHealthIndicator) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([() => this.db.pingCheck("database")]);
  }
}

import { KeyManagementServiceClient } from "@google-cloud/kms";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import { errorBuilder } from "../utils/utils/error.utils";

export enum KeyRing {
  MERCHANT_KEYRING = "merchant-keyring",
}

export enum CryptoKey {
  MERCHANT_KEYS = "merchant-keys",
}

@Injectable()
export class KmsEncryptionService {
  private static RESOURCE_DOES_NOT_EXIST_ERROR_CODE = 5;

  private readonly client: KeyManagementServiceClient;
  private readonly locationPath: string;
  private readonly projectId: string;
  private readonly region: string;

  constructor(private readonly configService: ConfigService) {
    this.client = new KeyManagementServiceClient();

    this.projectId = this.configService.getOrThrow("GCLOUD_PROJECT");
    this.region = "asia-east2";

    this.locationPath = this.client.locationPath(this.projectId, this.region);
  }

  async getKeyRing(keyRingId: KeyRing) {
    try {
      const [keyRings] = await this.client.getKeyRing({
        name: `${this.locationPath}/keyRings/${keyRingId}`,
      });

      if (!keyRings) {
        throw errorBuilder.kmsEncryption.keyRingNotFound(keyRingId);
      }

      return keyRings;
    } catch (error: any) {
      if (error.code === KmsEncryptionService.RESOURCE_DOES_NOT_EXIST_ERROR_CODE) {
        return await this.createKeyRing(keyRingId);
      }

      throw error;
    }
  }

  async createKeyRing(keyRingId: KeyRing) {
    const [keyRing] = await this.client.createKeyRing({
      parent: this.locationPath,
      keyRingId: keyRingId,
    });

    if (!keyRing || !keyRing.name) {
      throw errorBuilder.kmsEncryption.keyRingNotCreated(keyRingId);
    }

    return keyRing;
  }

  async getCryptoKey(keyRingId: KeyRing, cryptoKeyId: CryptoKey) {
    try {
      const [cryptyKey] = await this.client.getCryptoKey({
        name: this.client.cryptoKeyPath(this.projectId, this.region, keyRingId, cryptoKeyId),
      });

      if (!cryptyKey) {
        throw errorBuilder.kmsEncryption.cryptoKeyNotFound(keyRingId);
      }

      return cryptyKey;
    } catch (error: any) {
      if (error.code === KmsEncryptionService.RESOURCE_DOES_NOT_EXIST_ERROR_CODE) {
        const [cryptyKey] = await this.createCryptoKey(keyRingId, cryptoKeyId);

        return cryptyKey;
      }

      throw error;
    }
  }

  async createCryptoKey(keyRingId: KeyRing, cryptoKeyId: CryptoKey) {
    const cryptoKey = await this.client.createCryptoKey({
      parent: this.client.keyRingPath(this.projectId, this.region, keyRingId),
      cryptoKeyId: cryptoKeyId,
      cryptoKey: {
        purpose: "ENCRYPT_DECRYPT",
      },
    });

    if (!cryptoKey) {
      throw errorBuilder.kmsEncryption.cryptoKeyNotCreated(cryptoKeyId);
    }

    return cryptoKey;
  }

  async encrypt(content: string, keyRingId: KeyRing, cryptoKeyId: CryptoKey): Promise<Uint8Array> {
    await this.getKeyRing(keyRingId);
    await this.getCryptoKey(keyRingId, cryptoKeyId);

    const cryptoKeyPath = this.client.cryptoKeyPath(this.projectId, this.region, keyRingId, cryptoKeyId);

    const [encryptResponse] = await this.client.encrypt({
      name: cryptoKeyPath,
      plaintext: Buffer.from(content, "utf8"),
    });

    if (!encryptResponse.ciphertext) {
      throw errorBuilder.kmsEncryption.encryptionFailed();
    }

    return typeof encryptResponse.ciphertext === "string"
      ? Buffer.from(encryptResponse.ciphertext)
      : encryptResponse.ciphertext;
  }

  async decrypt(cipherText: string | Uint8Array, keyRingId: KeyRing, cryptoKeyId: CryptoKey): Promise<string> {
    const [decryptResponse] = await this.client.decrypt({
      name: this.client.cryptoKeyPath(this.projectId, this.region, keyRingId, cryptoKeyId),
      ciphertext: cipherText,
    });

    if (!decryptResponse || !decryptResponse.plaintext) {
      throw errorBuilder.kmsEncryption.decryptionFailed(cipherText);
    }

    return decryptResponse.plaintext.toString();
  }
}

import { Injectable } from "@nestjs/common";
import { verify } from "jws";

import { SecretsService } from "../secrets/secrets.service";
import { Secret } from "../secrets/types";

@Injectable()
export class RSAEncryptionService {
  private readonly encryptionAlg = "RS256";

  constructor(private readonly secretsService: SecretsService) {}

  private normalizePublicKey(key: string): string {
    return (
      "-----BEGIN PUBLIC KEY-----\n" +
      key.replace(/\\s|-----(BEGIN|END) PUBLIC KEY-----/g, "") +
      "\n-----END PUBLIC KEY-----\n"
    );
  }

  isAlgorithmSupported(alg: string): boolean {
    return alg === this.encryptionAlg;
  }

  async devicePublicVerify(deviceToken: string): Promise<boolean> {
    const devicePublicKey = await this.secretsService.getSecret(Secret.DEVICE_PUBLIC_KEY);
    return verify(deviceToken, this.encryptionAlg, this.normalizePublicKey(devicePublicKey));
  }
}

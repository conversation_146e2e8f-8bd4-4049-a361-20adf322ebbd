import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { KmsEncryptionService } from "./kmsEncryption.service";
import { RSAEncryptionService } from "./rsaEncryption.service";
import { SecretsModule } from "../secrets/secrets.module";

@Module({
  imports: [SecretsModule, ConfigModule],
  providers: [RSAEncryptionService, KmsEncryptionService],
  exports: [RSAEncryptionService, KmsEncryptionService],
})
export class EncryptionModule {}

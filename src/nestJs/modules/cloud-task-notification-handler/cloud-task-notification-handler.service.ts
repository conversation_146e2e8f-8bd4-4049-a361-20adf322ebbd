import { Inject, Injectable } from "@nestjs/common";

import { NotificationTaskRepository } from "../database/repositories/notificationTask.repository";
import { NotificationFactory } from "../notification/notification-method/notification.factory";
import {
  NotificationSenderRequestPayload,
  notificationSenderRequestSchema,
} from "../notification/notification.interface";
import { UserNotificationFactory } from "../notification/user-notification.factory";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";
import { ValidationService } from "../validation/validation.service";

@Injectable()
export class CloudTaskNotificationHandlerService {
  constructor(
    @Inject(LoggerServiceAdapter) private readonly logger: LoggerServiceAdapter,
    private readonly userNotificationDelegator: UserNotificationFactory,
    private readonly notificationTaskRepository: NotificationTaskRepository,
    private readonly notificationFactory: NotificationFactory,
  ) {}

  async handleNotificationTask(taskId: string) {
    const task = await this.notificationTaskRepository.findOne({
      where: {
        id: taskId,
      },
    });

    if (!task) {
      throw errorBuilder.notification.taskNotFound(taskId);
    }

    task.process();
    await this.notificationTaskRepository.save(task);

    const notificationSender = this.userNotificationDelegator.getSender(task.userType);

    try {
      this.logger.info(`[CloudTaskNotificationHandlerService] Validating notification task ${task.id}`, {
        task,
      });

      // just remove some extra fields
      const { scheduledAt: _, updatedBy: __, ...taskPayload } = task.payload;

      const [validPayload] = ValidationService.validate<NotificationSenderRequestPayload[]>(
        [
          {
            schema: notificationSenderRequestSchema,
            value: taskPayload,
          },
        ],
        true,
      );

      this.logger.info(
        `[CloudTaskNotificationHandlerService] Sending notifications to ${validPayload.phoneNumbers.length} merchants`,
        { validPayload, task, payload: taskPayload },
      );

      const userTokens = await notificationSender.getValidPushNotificationTokens(validPayload.phoneNumbers);

      try {
        await this.notificationFactory.get(task.type).notifyMany(userTokens, validPayload.notificationRequest);
      } catch (error) {
        throw errorBuilder.notification.errorSending();
      }

      task.complete();
      return this.notificationTaskRepository.save(task);
    } catch (err) {
      this.logger.error(
        `[CloudTaskNotificationHandlerService] Error handling notification task ${task.id}`,
        {},
        err as Error,
      );
      // Note: most errors are children of Error class, so they all have .message
      task.fail((err as Error).message);
      await this.notificationTaskRepository.save(task);
      throw err;
    }
  }
}

import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { CloudTaskNotificationHandlerController } from "./cloud-task-notification-handler.controller";
import { CloudTaskNotificationHandlerService } from "./cloud-task-notification-handler.service";
import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { NotificationTaskRepository } from "../database/repositories/notificationTask.repository";
import { UserNotificationTokenRepository } from "../database/repositories/userNotificationToken.repository";
import { AppUserNotificationService } from "../notification/app-user-notification/app-user-notification.service";
import { NotificationModule } from "../notification/notification.module";
import { UserNotificationFactory } from "../notification/user-notification.factory";

@Module({
  imports: [NotificationModule, AppDatabaseModule],
  controllers: [CloudTaskNotificationHandlerController],
  providers: [
    UserNotificationFactory,
    CloudTaskNotificationHandlerService,
    AppUserNotificationService,
    NotificationTaskRepository,
    UserNotificationTokenRepository,
  ],
  exports: [CloudTaskNotificationHandlerService],
})
export class CloudTaskNotificationModule {}

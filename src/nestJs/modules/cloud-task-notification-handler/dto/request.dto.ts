import { ApiProperty, PartialType } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";
import moment from "moment-timezone";

import { NotificationTaskType, NotificationUserType } from "@nest/modules/database/entities/notificationTask.entity";
import { NotificationRequestDto, notificationRequestSchema } from "@nest/modules/notification/dto/notification.dto";

export enum NotificationTaskRequestStatus {
  DRAFT = "DRAFT",
  SCHEDULED = "SCHEDULED",
}

export const notificationUserTypeSchema = Joi.string().valid(...Object.values(NotificationUserType));

export class CreateManyNotificationRequestDto {
  @ApiProperty({
    description: "Campaign notification name",
    example: "Test Notification",
  })
  name: string;

  @ApiProperty({
    description: "Notification type",
    example: NotificationTaskType.PUSH,
  })
  type: NotificationTaskType;

  @ApiProperty({
    description: "Whether the notification is draft or scheduled",
    example: NotificationTaskRequestStatus.DRAFT,
  })
  status: NotificationTaskRequestStatus;

  @ApiProperty({
    description: "Notification schedule time. Make sure it's in HK Timezone (+08:00)",
    example: "2023-01-01T00:00:00+08:00",
    required: false,
  })
  scheduledAt: Date;

  @ApiProperty({
    description: "List of phone numbers to send notifications to",
    example: ["000000000000", "000000000000"],
  })
  phoneNumbers: string[];

  @ApiProperty({
    description: "Notification request",
    example: {
      titleEn: "Test Notification",
      titleHk: "測試通知",
      bodyEn: "This is a test notification",
      bodyHk: "這是一個測試通知",
    },
  })
  notificationRequest: NotificationRequestDto;

  @ApiProperty({
    description: "The user type for the notification",
    examples: [NotificationUserType.USERS, NotificationUserType.MERCHANTS],
    required: true,
    default: NotificationUserType.USERS,
  })
  userType: NotificationUserType;
}

const timeZone = "Asia/Hong_Kong";

export const futureDateSchemaWithDefaultNow = Joi.date()
  .optional()
  .min("now")
  .message("The date cannot be in the past")
  .default(() => moment().tz(timeZone).toDate()); // Added default value using current datetime in UTC+8 timezone

const phoneNumberSchema = Joi.string().not(Joi.string().trim().empty()); // Ensure phone number is not empty or just whitespace
export const phoneNumbersSchema = Joi.array().items(phoneNumberSchema);

export const createManyNotificationRequestSchema = Joi.object<CreateManyNotificationRequestDto>({
  name: Joi.string().required(),
  type: Joi.string()
    .valid(...Object.values(NotificationTaskType))
    .required(),
  status: Joi.string()
    .valid(...Object.values(NotificationTaskRequestStatus))
    .default(NotificationTaskRequestStatus.DRAFT),
  scheduledAt: futureDateSchemaWithDefaultNow,
  phoneNumbers: phoneNumbersSchema.required(),
  notificationRequest: notificationRequestSchema.required(),
  userType: Joi.string()
    .required()
    .valid(...Object.values(NotificationUserType)),
});

export class UpdateManyNotificationRequestDto extends PartialType(CreateManyNotificationRequestDto) {}

export const updateManyNotificationRequestSchema = Joi.object<UpdateManyNotificationRequestDto>({
  name: Joi.string().optional(),
  type: Joi.string()
    .valid(...Object.values(NotificationTaskType))
    .optional(),
  status: Joi.string()
    .valid(...Object.values(NotificationTaskRequestStatus))
    .optional(),
  scheduledAt: Joi.date().optional().min("now").message("The date cannot be in the past"),
  phoneNumbers: phoneNumbersSchema.optional(),
  notificationRequest: notificationRequestSchema.optional(),
  userType: Joi.string()
    .optional()
    .valid(...Object.values(NotificationUserType)),
});

import { <PERSON>, HttpCode, HttpStatus, <PERSON><PERSON>, Post } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from "@nestjs/swagger";

import { CloudTaskNotificationHandlerService } from "./cloud-task-notification-handler.service";
import NotificationTask from "../database/entities/notificationTask.entity";
import { apiTags } from "../utils/utils/swagger.utils";
import genericSchemas from "../validation/dto/genericSchemas.dto";
import { JoiValidationPipe } from "../validation/validationPipe.service";

@ApiHeader({
  name: "x-api-key",
  description: "API Key to authenticate the call",
  required: true,
})
@ApiTags(...apiTags.cloudTask)
@Controller("cloud-task/notifications")
export class CloudTaskNotificationHandlerController {
  constructor(private readonly cloudTaskNotificationHandlerService: CloudTaskNotificationHandlerService) {}

  @Post("/:taskId")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Handle a callback from Cloud Task to send notifications" })
  @ApiOkResponse({
    description: "Success, and get the Cloud Task reference",
    example: [
      "projects/dash-dev2-edcb3/locations/asia-east2/queues/notifications-task-queue/tasks/85386878860425152551",
    ],
  })
  @ApiNotFoundResponse({
    description: "Task not found",
  })
  @ApiBadRequestResponse({
    description: "Invalid task payload",
  })
  @ApiInternalServerErrorResponse({
    description: "Internal server error",
  })
  async handleNotificationTask(
    @Param("taskId", new JoiValidationPipe(genericSchemas.uuid.required())) taskId: string,
  ): Promise<NotificationTask> {
    return this.cloudTaskNotificationHandlerService.handleNotificationTask(taskId);
  }
}

/**
 * DTOs for Microsoft Teams Incoming Webhook notifications
 */

export interface TeamsWebhookConfig {
  webhookUrl: string;
}

export interface TeamsWebhookMessage {
  text?: string;
  title?: string;
  summary?: string;
  themeColor?: string;
  sections?: TeamsWebhookSection[];
  potentialAction?: TeamsWebhookAction[];
}

export interface TeamsWebhookSection {
  activityTitle?: string;
  activitySubtitle?: string;
  activityImage?: string;
  facts?: TeamsWebhookFact[];
  markdown?: boolean;
  text?: string;
}

export interface TeamsWebhookFact {
  name: string;
  value: string;
}

export interface TeamsWebhookAction {
  "@type": string;
  name: string;
  targets?: TeamsWebhookActionTarget[];
  url?: string;
}

export interface TeamsWebhookActionTarget {
  os: string;
  uri: string;
}

export class SendTeamsWebhookMessageDto {
  webhookUrl?: string;
  message: string;
  title?: string;
  summary?: string;
  themeColor?: string;
  facts?: TeamsWebhookFact[];
  actions?: TeamsWebhookAction[];
}

export class TeamsWebhookNotificationResult {
  success: boolean;
  error?: string;
  statusCode?: number;
}

// Predefined theme colors for different message types
export enum TeamsThemeColor {
  SUCCESS = "00FF00",
  WARNING = "FFFF00",
  ERROR = "FF0000",
  INFO = "0078D4",
  DEFAULT = "0078D5",
}

// Message card types for structured messages
export interface TeamsMessageCard {
  "@type": "MessageCard";
  "@context": "http://schema.org/extensions";
  summary: string;
  title?: string;
  text?: string;
  themeColor?: string;
  sections?: TeamsWebhookSection[];
  potentialAction?: TeamsWebhookAction[];
}

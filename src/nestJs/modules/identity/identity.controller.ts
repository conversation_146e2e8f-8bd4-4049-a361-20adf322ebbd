import { Controller, Inject } from "@nestjs/common";
import { CloudEvent } from "firebase-functions/v2";
import { MessagePublishedData } from "firebase-functions/v2/pubsub";

import { IdentityService } from "./identity.service";
import { PublishMessageForIdentityProcessingParams } from "../pubsub/dto/publishMessageForIdentityProcessingParams.dto";
import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";
import LoggerServiceAdapter from "../utils/logger/logger.service";

@Controller()
export class IdentityController {
  constructor(
    private identityService: IdentityService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  @CorrelationContext()
  async processIdentityEvent(event: CloudEvent<MessagePublishedData<PublishMessageForIdentityProcessingParams>>) {
    const data = event.data.message.json;
    this.logger.info("nest trigger: processIdentityEvent", { tranId: data.jsonPayload.methodName });
    return this.identityService.processIdentityEvent(data);
  }
}

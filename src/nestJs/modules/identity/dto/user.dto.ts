import Joi from "joi";

/**
 * Gender enum
 */
export enum GenderType {
  MALE = "MALE",
  FEMALE = "FEMALE",
  PNTS = "PNTS",
}

export const genderSchema = Joi.string<GenderType>().valid(...Object.values(GenderType));

/**
 * PreferredLanguage enum
 */
export enum PreferredLanguageType {
  ZHHK = "zhHK",
  EN = "en",
}

export const preferredLanguageSchema = Joi.string<PreferredLanguageType>().valid(
  ...Object.values(PreferredLanguageType),
);

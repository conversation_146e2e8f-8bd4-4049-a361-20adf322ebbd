import Joi from "joi";

export class SetAccountInfoResponse {
  email: string;
  emailVerified: boolean;
  localId: string; // this is userId
  newEmail: string;
  providerUserInfo: ProviderUserInfo[];
}

interface ProviderUserInfo {
  phoneNumber: string;
  providerId: string;
}

export class SignInWithPhoneNumberResponse {
  localId: string; // this is userId
  phoneNumber: string;
}

export type IdentityResponse = SignInWithPhoneNumberResponse | SetAccountInfoResponse;

export const identityResponseSchema = Joi.object<IdentityResponse>().options({ allowUnknown: true });

export const isSetAccountInfoResponse = (response?: IdentityResponse): response is SetAccountInfoResponse => {
  if (!response) return false;
  return (
    (response as SetAccountInfoResponse).email !== undefined &&
    (response as SetAccountInfoResponse).emailVerified !== undefined &&
    (response as SetAccountInfoResponse).localId !== undefined
  );
};

export const isSignInWithPhoneNumberResponse = (
  response?: IdentityResponse,
): response is SignInWithPhoneNumberResponse => {
  if (!response) return false;
  return (
    (response as SignInWithPhoneNumberResponse).localId !== undefined &&
    (response as SignInWithPhoneNumberResponse).phoneNumber !== undefined
  );
};

import Joi from "joi";

export enum IdentityEventMethodNameType {
  DELETE_ACCOUNT = "google.cloud.identitytoolkit.v1.AccountManagementService.DeleteAccount",
  GET_ACCOUNT_INFO = "google.cloud.identitytoolkit.v1.AccountManagementService.GetAccountInfo",
  GET_OOB_CODE = "google.cloud.identitytoolkit.v1.AccountManagementService.GetOobCode",
  RESET_PASSWORD = "google.cloud.identitytoolkit.v1.AccountManagementService.ResetPassword",
  SET_ACCOUNT_INFO = "google.cloud.identitytoolkit.v1.AccountManagementService.SetAccountInfo",

  CREATE_AUTH_URI = "google.cloud.identitytoolkit.v1.AuthenticationService.CreateAuthUri",
  GET_RECAPTCHA_PARAM = "google.cloud.identitytoolkit.v1.AuthenticationService.GetRecaptchaParam",
  SEND_VERIFICATION_CODE = "google.cloud.identitytoolkit.v1.AuthenticationService.SendVerificationCode",
  SIGN_IN_WITH_CUSTOM_TOKEN = "google.cloud.identitytoolkit.v1.AuthenticationService.SignInWithCustomToken",
  SIGN_IN_WITH_EMAIL_LINK = "google.cloud.identitytoolkit.v1.AuthenticationService.SignInWithEmailLink",
  SIGN_IN_WITH_GAME_CENTER = "google.cloud.identitytoolkit.v1.AuthenticationService.SignInWithGameCenter",
  SIGN_IN_WITH_IDP = "google.cloud.identitytoolkit.v1.AuthenticationService.SignInWithIdp",
  SIGN_IN_WITH_PASSWORD = "google.cloud.identitytoolkit.v1.AuthenticationService.SignInWithPassword",
  SIGN_IN_WITH_PHONE_NUMBER = "google.cloud.identitytoolkit.v1.AuthenticationService.SignInWithPhoneNumber",
  SIGN_UP = "google.cloud.identitytoolkit.v1.AuthenticationService.SignUp",
  VERIFY_IOS_CLIENT = "google.cloud.identitytoolkit.v1.AuthenticationService.VerifyIosClient",

  GET_PROJECT_CONFIG = "google.cloud.identitytoolkit.v1.ProjectConfigService.GetProjectConfig",
}

export const identityEventMethodNameTypeSchema = Joi.string<IdentityEventMethodNameType>().valid(
  ...Object.values(IdentityEventMethodNameType),
);

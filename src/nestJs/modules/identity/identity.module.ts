import { <PERSON>du<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { IdentityController } from "./identity.controller";
import { IdentityService } from "./identity.service";
import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { ProfileAuditRepository } from "../database/repositories/profileAudit.repository";
import { UserModule } from "../user/user.module";

@Module({
  providers: [IdentityService, IdentityController, ProfileAuditRepository],
  imports: [ConfigModule, AppDatabaseModule, UserModule],
  controllers: [IdentityController],
  exports: [IdentityService, IdentityController],
})
export class IdentityModule {}

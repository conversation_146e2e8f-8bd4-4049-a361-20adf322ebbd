import { Injectable } from "@nestjs/common";
import { getAuth } from "firebase-admin/auth";

import { IdentityEventMethodNameType } from "./dto/methodNameType.dto";
import { isSetAccountInfoResponse, isSignInWithPhoneNumberResponse } from "./dto/response.dto";
import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import { UserDocument } from "../appDatabase/documents/user.document";
import { ProfileAuditActionType, ProfileAuditSourceType } from "../audit/profileAudit/dto/profileAudit.dto";
import { ProfileAuditFirebaseAuthMetadata } from "../audit/profileAudit/dto/profileAuditMetadata.dto";
import User from "../database/entities/user.entity";
import { ProfileAuditRepository } from "../database/repositories/profileAudit.repository";
import { PublishMessageForIdentityProcessingParams } from "../pubsub/dto/publishMessageForIdentityProcessingParams.dto";
import { UserService } from "../user/user.service";
import { errorBuilder } from "../utils/utils/error.utils";

@Injectable()
export class IdentityService {
  constructor(
    private readonly userService: UserService,
    private readonly profileAuditRepository: ProfileAuditRepository,
    private readonly appDatabaseService: AppDatabaseService,
  ) {}

  async processIdentityEvent(data: PublishMessageForIdentityProcessingParams) {
    switch (data.jsonPayload.methodName) {
      case IdentityEventMethodNameType.SIGN_IN_WITH_PHONE_NUMBER: //this is triggered when user sign in with phone number
        if (isSignInWithPhoneNumberResponse(data.jsonPayload.response)) {
          const { localId } = data.jsonPayload.response;
          let userInFirebaseAuth;
          try {
            userInFirebaseAuth = await getAuth().getUser(localId);
          } catch (err) {
            throw errorBuilder.user.notFoundInFirebaseAuth(localId);
          }
          if (userInFirebaseAuth) {
            const newUser = new User();
            newUser.appDatabaseId = localId;
            if (!userInFirebaseAuth.phoneNumber) {
              throw errorBuilder.user.notPhoneNumberInFirebaseAuth(localId);
            }
            newUser.phoneNumber = userInFirebaseAuth.phoneNumber;
            const createdUser = await this.userService.createUser(newUser);
            if (createdUser) {
              await this.userService.setUserInFirestore(localId, createdUser);
              const profileAuditFirebaseAuthMetadata = new ProfileAuditFirebaseAuthMetadata();
              profileAuditFirebaseAuthMetadata.ip = data.jsonPayload.requestMetadata.callerIp;
              profileAuditFirebaseAuthMetadata.deviceInfo = data.jsonPayload.requestMetadata.callerSuppliedUserAgent;
              profileAuditFirebaseAuthMetadata.messageId = data.insertId;
              await this.profileAuditRepository.createProfileAudit(
                ProfileAuditActionType.FIREBASE_AUTH_SIGN_IN,
                createdUser,
                profileAuditFirebaseAuthMetadata,
                ProfileAuditSourceType.FIREBASE_AUTH,
              );
            }
          }
        }
        break;
      case IdentityEventMethodNameType.SET_ACCOUNT_INFO: //this is triggered when user click the link in verification email from google
        if (isSetAccountInfoResponse(data.jsonPayload.response)) {
          const { email, emailVerified, localId, newEmail } = data.jsonPayload.response;
          if (emailVerified && newEmail && email && email === newEmail) {
            const updateUser = await this.userService.updateUserEmail(localId, email);
            if (updateUser) {
              const userDocument: UserDocument = {
                id: localId,
                unverifiedEmailAddress: "",
              };
              await this.appDatabaseService.userRepository().set(userDocument);

              const profileAuditFirebaseAuthMetadata = new ProfileAuditFirebaseAuthMetadata();
              profileAuditFirebaseAuthMetadata.ip = data.jsonPayload.requestMetadata.callerIp;
              profileAuditFirebaseAuthMetadata.deviceInfo = data.jsonPayload.requestMetadata.callerSuppliedUserAgent;
              profileAuditFirebaseAuthMetadata.messageId = data.insertId;
              await this.profileAuditRepository.createProfileAudit(
                ProfileAuditActionType.FIREBASE_AUTH_EMAIL_VERIFIED,
                updateUser,
                profileAuditFirebaseAuthMetadata,
                ProfileAuditSourceType.FIREBASE_AUTH,
              );
            }
          }
        }
        break;
      default:
        break;
    }
  }
}

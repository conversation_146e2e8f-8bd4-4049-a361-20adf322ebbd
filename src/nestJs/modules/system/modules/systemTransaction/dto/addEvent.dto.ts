import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { TxEventType } from "../../../../transaction/dto/txEventType.dto";

export class AddEventBodyDefaultSystem {
  @ApiProperty({
    description: "Tx event type",
    example: TxEventType.HAILING_SCHEDULED_ORDER_TIMEOUT,
  })
  type: TxEventType.HAILING_SCHEDULED_ORDER_TIMEOUT;
}

export type AddEventBodySystem = AddEventBodyDefaultSystem;

export const addEventBodySchemaSystem = Joi.alternatives([
  Joi.object<AddEventBodySystem>({
    type: Joi.string().valid(TxEventType.HAILING_SCHEDULED_ORDER_TIMEOUT).required(),
  }),
]);

import { Modu<PERSON> } from "@nestjs/common";

import { SystemTransactionController } from "./systemTransaction.controller";
import { SystemTransactionService } from "./systemTransaction.service";
import { TransactionModule } from "../../../transaction/transaction.module";

@Module({
  providers: [SystemTransactionService],
  imports: [TransactionModule],
  controllers: [SystemTransactionController],
  exports: [SystemTransactionService],
})
export class SystemTransactionModule {}

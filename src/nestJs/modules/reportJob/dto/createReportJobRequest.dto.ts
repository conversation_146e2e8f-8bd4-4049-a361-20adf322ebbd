import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import Jo<PERSON> from "joi";

import {
  ReportJobMerchantMetadata,
  ReportType,
  reportTypeSchema,
  QueryMetadata,
  queryMetadataSchema,
  reportJobMerchantMetadataSchema,
} from "./reportJob.dto";

export class CreateReportJobBodyDto {
  @ApiProperty()
  name: string;
  @ApiProperty()
  cron: string;
  @ApiProperty()
  reportType: ReportType;
  @ApiProperty()
  destination: string;
  @ApiPropertyOptional()
  queryMetadata?: QueryMetadata;
  @ApiPropertyOptional()
  queryString?: string;
  @ApiProperty()
  merchantMetadata: ReportJobMerchantMetadata;
}

export const createReportJobBodySchema = Joi.object({
  name: Joi.string().required(),
  cron: Joi.string().required(),
  reportType: reportTypeSchema.required(),
  destination: Joi.string().required(),
  queryMetadata: queryMetadataSchema.optional(),
  queryString: Joi.string().optional(),
  merchantMetadata: reportJobMerchantMetadataSchema.required(),
});

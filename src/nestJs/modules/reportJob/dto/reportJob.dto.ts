import { ApiProperty } from "@nestjs/swagger";
import Joi from "joi";

export enum ReportType {
  TRIP = "TRIP",
  HEARTBEAT = "HEARTBEAT",
  SYSTEM_ALERT = "SYSTEM_ALERT",
}

export const reportTypeSchema = Joi.string().valid(...Object.values(ReportType));

export class FleetTripReportMerchantMetadata {
  @ApiProperty()
  fleetId: string;
  @ApiProperty()
  merchantId: string;
}

export const fleetTripReportMerchantMetadataSchema = Joi.object<FleetTripReportMerchantMetadata>({
  fleetId: Joi.string().required(),
  merchantId: Joi.string().required(),
});

export class HeartbeatMerchantMetadata {}

export const heartbeatMerchantMetadataSchema = Joi.object<HeartbeatMerchantMetadata>();

export enum SystemAlertType {
  FARE = "FARE",
}
export class SystemAlertMetadata {
  @ApiProperty()
  alertType: SystemAlertType;
  @ApiProperty()
  summary: string;
}

export const systemAlertMetadataSchema = Joi.object<SystemAlertMetadata>({
  alertType: Joi.string()
    .valid(...Object.values(SystemAlertType))
    .required(),
  summary: Joi.string().required(),
});

export type ReportJobMerchantMetadata =
  | FleetTripReportMerchantMetadata
  | HeartbeatMerchantMetadata
  | SystemAlertMetadata;

export const reportJobMerchantMetadataSchema = Joi.object().when("reportType", {
  switch: [
    {
      is: ReportType.TRIP,
      then: fleetTripReportMerchantMetadataSchema.required(),
    },
    {
      is: ReportType.HEARTBEAT,
      then: heartbeatMerchantMetadataSchema.required(),
    },
    {
      is: ReportType.SYSTEM_ALERT,
      then: systemAlertMetadataSchema.required(),
    },
  ],
  otherwise: Joi.forbidden(),
});

export enum QueryOrderDirection {
  ASC = "ASC",
  DESC = "DESC",
}

export class QueryOrderOptions {
  @ApiProperty()
  field: string;
  @ApiProperty()
  direction: QueryOrderDirection;
}

export class QueryMetadata {
  @ApiProperty()
  fields: string[];
  @ApiProperty()
  orderBy: QueryOrderOptions;
}

export const queryMetadataSchema = Joi.object<QueryMetadata>({
  fields: Joi.array().items(Joi.string()).min(1).required(),
  orderBy: Joi.object<QueryOrderOptions>({
    field: Joi.string().required(),
    direction: Joi.string()
      .valid(...Object.values(QueryOrderDirection))
      .required(),
  }).required(),
});

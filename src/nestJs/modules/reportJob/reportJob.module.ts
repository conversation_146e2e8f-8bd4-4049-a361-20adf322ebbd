import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { ReportJobController } from "./reportJob.controller";
import { ReportJobService } from "./reportJob.service";
import { ReportJobRepository } from "../database/repositories/reportJob.repository";
import { EmailModule } from "../email/email.module";
import { MessageTeamsModule } from "../messageTeams/messageTeams.module";
import { PubSubService } from "../pubsub/pubsub.service";

@Module({
  providers: [ReportJobService, ReportJobRepository, PubSubService],
  controllers: [ReportJobController],
  imports: [ConfigModule, EmailModule, MessageTeamsModule],
  exports: [ReportJobService],
})
export class ReportJobModule {}

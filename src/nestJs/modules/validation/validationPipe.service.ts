import { Injectable, PipeTransform, ArgumentMetadata } from "@nestjs/common";
import { Schema } from "joi";

import { ValidationService } from "./validation.service";

@Injectable()
export class JoiValidationPipe<T> implements PipeTransform {
  constructor(private schema: Schema) {}

  transform(value: any, _metadata: ArgumentMetadata): T {
    const [result] = ValidationService.validate<[T]>([{ value: value, schema: this.schema }], false);
    return result;
  }
}

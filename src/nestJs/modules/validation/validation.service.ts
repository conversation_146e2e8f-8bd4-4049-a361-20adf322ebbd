import { Injectable } from "@nestjs/common";
import <PERSON><PERSON> from "joi";

import { errorBuilder } from "../utils/utils/error.utils";

type Validator = {
  value: any;
  schema: Jo<PERSON>.Schema;
};

/**
 * Validation service
 */
@Injectable()
export class ValidationService {
  constructor() {}

  static validate<T>(validators: Validator[], strict = true): T {
    const { errors, values } = validators.reduce(
      (result: { errors: Joi.ValidationError[]; values: any[] }, v: Validator) => {
        const { error, value } = v.schema.options({ abortEarly: false }).strict(strict).validate(v.value);
        const res = result;
        if (error) {
          res.errors.push(error);
        }
        res.values.push(value);
        return res;
      },
      { errors: [], values: [] },
    );

    if (errors && errors.length) {
      let errorsString;
      try {
        errorsString = JSON.stringify(errors);
      } catch (e) {
        errorsString = errors;
      }

      throw errorBuilder.validation.failed(errors.map((error) => error.message.replace(/"/g, "'")).join(", "), {
        errorsString: errorsString.toString(),
      });
    }

    return values as T;
  }
}

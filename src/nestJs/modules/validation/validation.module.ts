import { DynamicModule, Module } from "@nestjs/common";

import { ValidationService } from "./validation.service";

/**
 * Validation module
 */
@Module({
  providers: [ValidationService],
  exports: [ValidationService],
})
export class ValidationModule {
  /**
   * Validation module for root
   * @returns DynamicModule
   */
  static forRoot(): DynamicModule {
    return {
      global: true,
      module: ValidationModule,
      providers: [ValidationService],
      exports: [ValidationService],
    };
  }
}

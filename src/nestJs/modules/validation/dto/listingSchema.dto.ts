import { ApiPropertyOptional } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export enum DirectionType {
  ASC = "ASC",
  DESC = "DESC",
}

export function baseListingQuerySchema<T>(): Joi.ObjectSchema<BaseListingQueryDto & T> {
  return Joi.object<BaseListingQueryDto & T>({
    limit: Joi.number().min(1).allow(null).optional(),
    offset: Joi.number().min(0).allow(null).optional(),
    sort: Joi.string().allow(null).optional(),
    direction: Joi.string<DirectionType>()
      .valid(...Object.values(DirectionType))
      .allow(null)
      .optional(),
  });
}

export class BaseListingQueryDto {
  @ApiPropertyOptional()
  limit?: number;
  @ApiPropertyOptional()
  offset?: number;
  @ApiPropertyOptional()
  sort?: string;
  @ApiPropertyOptional()
  direction?: DirectionType;
}

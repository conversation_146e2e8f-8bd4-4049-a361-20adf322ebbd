import { join } from "path";

import { DynamicModule, forwardRef } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import { DataSourceOptions } from "typeorm";
import { PostgresConnectionOptions } from "typeorm/driver/postgres/PostgresConnectionOptions";

import { DatabaseLogger } from "./database.logger";
import { DatabaseModule } from "./database.module";
import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { AppDatabaseService } from "../appDatabase/appDatabase.service";

/**
 * Build the data source options
 * @param configService ConfigService
 * @returns DataSourceOptions
 */
export function buildDataSourceOptions(
  configService: ConfigService,
  databaseLogger?: DatabaseLogger,
): PostgresConnectionOptions {
  const defaultDataSourceOptions: Partial<PostgresConnectionOptions> = {
    entities: [join(__dirname, "./entities/*.entity.{ts,js}")],
    migrations: [join(__dirname, "../../../__migrations__/*.{ts,js}")],
    logger: databaseLogger,
    maxQueryExecutionTime: 200,
  };

  // const parsePort = parseInt(configService.get("PGSQL_PORT") ?? "5432", 10);
  // console.debug("PGSQL_PORT", parsePort);
  // console.debug("configService", configService);
  // console.debug("DEBUG -- All Environment Variables:", process.env);
  const isSocketAccess = !configService.get("PGSQL_HOST");

  let dataSourceOptions: PostgresConnectionOptions;

  if (isSocketAccess) {
    dataSourceOptions = {
      type: "postgres",
      replication: {
        master: {
          host: configService.get("PGSQL_SOCKET_MASTER"),
          port: parseInt(configService.get("PGSQL_PORT") ?? "5432", 10),
          username: configService.get("PGSQL_USERNAME"),
          password: configService.get("PGSQL_PASSWORD"),
          database: configService.get("PGSQL_DATABASE"),
        },
        slaves: [
          {
            host: configService.get("PGSQL_SOCKET_SLAVE"),
            port: parseInt(configService.get("PGSQL_PORT") ?? "5432", 10),
            username: configService.get("PGSQL_USERNAME"),
            password: configService.get("PGSQL_PASSWORD"),
            database: configService.get("PGSQL_DATABASE"),
          },
        ],
      },
      ...defaultDataSourceOptions,
    };
  } else {
    dataSourceOptions = {
      type: "postgres",
      host: configService.get("PGSQL_HOST"),
      port: parseInt(configService.get("PGSQL_PORT") ?? "5432", 10),
      username: configService.get("PGSQL_USERNAME"),
      password: configService.get("PGSQL_PASSWORD"),
      database: configService.get("PGSQL_DATABASE"),
      ...defaultDataSourceOptions,
    };
  }

  return dataSourceOptions;
}
/**
 * Database provider
 */
export const DatabaseProvider: DynamicModule = TypeOrmModule.forRootAsync({
  imports: [ConfigModule, forwardRef(() => DatabaseModule), AppDatabaseModule],
  useFactory: (configService: ConfigService, databaseLogger: DatabaseLogger) =>
    buildDataSourceOptions(configService, databaseLogger) as DataSourceOptions,
  inject: [ConfigService, DatabaseLogger, AppDatabaseService],
});

import { Inject, Injectable } from "@nestjs/common";
import { EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent, RemoveEvent, DataSource } from "typeorm";

import LoggerServiceAdapter from "../utils/logger/logger.service";

@Injectable()
@EventSubscriber()
export class EntityLoggerSubscriber implements EntitySubscriberInterface {
  constructor(@Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter, private dataSource: DataSource) {
    this.dataSource.subscribers.push(this);
  }

  afterLoad(entity: any) {
    const model = entity.constructor.name;
    this.logger.info(`DB Query Entity Loaded for Model: ${model}`, {
      type: "entity-load",
      model,
      action: "LOAD",
      entity: this.sanitizeResult(entity) || "unknown",
    });
  }

  afterInsert(event: InsertEvent<any>) {
    const entity = event.entity;
    const model = entity.constructor.name;

    this.logger.debug(`DB Query Entity Inserted for Model: ${model}`, {
      type: "entity-insert",
      model,
      action: "INSERT",
      entity: this.sanitizeResult(entity) || "unknown",
    });
  }

  afterUpdate(event: UpdateEvent<any>) {
    const entity = event.entity;
    const model = entity?.constructor?.name;
    this.logger.debug(`DB Query Entity Updated for Model: ${model}`, {
      type: "entity-update",
      model,
      action: "UPDATE",
      entity: this.sanitizeResult(entity) || "unknown",
    });
  }

  afterRemove(event: RemoveEvent<any>) {
    const entity = event.entity;
    const model = entity?.constructor?.name;
    this.logger.debug(`DB Query Entity Removed for Model: ${model}`, {
      type: "entity-remove",
      model,
      action: "DELETE",
      entity: this.sanitizeResult(entity) || "unknown",
    });
  }

  private sanitizeResult(result: any): any {
    if (!result) return null;

    // Handle arrays
    if (Array.isArray(result)) {
      return result.map((item) => this.sanitizeResult(item));
    }

    // Handle objects (including nested objects)
    if (result && typeof result === "object" && !Array.isArray(result)) {
      const sanitized = { ...result };

      // Process each property
      for (const key in sanitized) {
        if (Object.prototype.hasOwnProperty.call(sanitized, key)) {
          // Check if it's a sensitive field
          if (this.isSensitiveField(key)) {
            sanitized[key] = "****";
          }
          // Recursively sanitize nested objects and arrays
          else if (sanitized[key] && typeof sanitized[key] === "object") {
            sanitized[key] = this.sanitizeResult(sanitized[key]);
          }
        }
      }
      return sanitized;
    }

    // Return primitives as is
    return result;
  }

  private isSensitiveField(fieldName: string): boolean {
    const sensitiveFields = [
      "password",
      "publicKey",
      "privateKey",
      "tokenKraken",
      "token",
      "cardHolderName",
      "instrumentIdentifier",
    ];
    return sensitiveFields.includes(fieldName);
  }
}

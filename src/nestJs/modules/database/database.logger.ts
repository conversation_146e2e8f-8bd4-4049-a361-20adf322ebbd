import { Inject, Injectable } from "@nestjs/common";
import { Logger, QueryRunner } from "typeorm";

import { AppDatabaseService } from "../appDatabase/appDatabase.service";
import LoggerServiceAdapter from "../utils/logger/logger.service";
@Injectable()
export class DatabaseLogger implements Logger {
  constructor(
    private readonly appDatabaseService: AppDatabaseService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  async getEnableSqlLogging(): Promise<boolean> {
    return await this.appDatabaseService.configurationRepository().getEnableSqlLogging();
  }

  logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner) {
    if (!this.getEnableSqlLogging()) {
      return;
    }

    const { model, action } = this.extractModelAndAction(query, queryRunner);

    if (!model || !action) {
      return;
    }

    this.logger.debug(`DB Query for ${model} - ${action}`, {
      type: "query",
      model,
      action,
      parameters: this.sanitizeParams(parameters),
    });
  }

  logQueryError(error: string, query: string, parameters?: any[], queryRunner?: QueryRunner) {
    if (!this.getEnableSqlLogging()) {
      return;
    }
    const { model, action } = this.extractModelAndAction(query, queryRunner);

    this.logger.error(`DB Query Error for ${model} - ${action}`, {
      type: "query-error",
      model,
      action,
      error,
      query,
      parameters: this.sanitizeParams(parameters),
    });
  }

  logQuerySlow(time: number, query: string, parameters?: any[], queryRunner?: QueryRunner) {
    if (!this.getEnableSqlLogging()) {
      return;
    }
    const { model, action } = this.extractModelAndAction(query, queryRunner);

    this.logger.warn(`DB Query Slow: ${time}ms for ${model} - ${action}`, {
      type: "query-slow",
      model,
      action,
      time,
      query,
      parameters: this.sanitizeParams(parameters),
    });
  }

  logSchemaBuild(message: string, queryRunner?: QueryRunner) {
    if (!this.getEnableSqlLogging()) {
      return;
    }
    this.logger.info(message, { type: "schema-build" });
  }

  logMigration(message: string, queryRunner?: QueryRunner) {
    if (!this.getEnableSqlLogging()) {
      return;
    }
    this.logger.info(message, { type: "migration" });
  }

  log(level: "log" | "info" | "warn", message: any, queryRunner?: QueryRunner) {
    if (!this.getEnableSqlLogging()) {
      return;
    }
  }

  // Parse query to extract action and table (model)
  private extractModelAndAction(query: string, queryRunner?: QueryRunner): { model: string; action: string } {
    // Default values
    let model = "unknown";
    let action = "unknown";

    // Clean up the query and extract action from the first meaningful word
    const cleanQuery = query.trim().replace(/^\(+/, ""); // Remove leading parentheses
    const queryWords = cleanQuery.split(/\s+/);
    action = queryWords[0].toUpperCase(); // e.g., SELECT, INSERT, UPDATE, DELETE

    // Extract table name (model) from query
    if (action === "SELECT") {
      const fromIndex = cleanQuery.toUpperCase().indexOf(" FROM ");
      if (fromIndex !== -1) {
        const fromClause = cleanQuery.slice(fromIndex + 6).trim();
        const tableName = fromClause.split(/\s+/)[0].replace(/["`]/g, ""); // Extract table name, remove quotes
        // Handle subqueries and complex table references
        if (tableName && !tableName.startsWith("(") && tableName !== "SELECT") {
          model = tableName;
        }
      }
    } else if (["INSERT", "UPDATE", "DELETE"].includes(action)) {
      const tableMatch = cleanQuery.match(/(?:INTO|UPDATE|FROM)\s+["`]?(\w+)["`]?/i);
      if (tableMatch) {
        model = tableMatch[1]; // Extract table name
      }
    }

    // Optionally, map table name to entity name using metadata (if available)
    if (queryRunner && queryRunner.data && queryRunner.data.entity) {
      model = queryRunner.data.entity.name || model;
    }

    return { model, action };
  }

  private sanitizeParams(params?: any[]): any[] | undefined {
    if (!params) return undefined;
    return params.map((param) => (typeof param === "string" && param.includes("password") ? "****" : param));
  }
}

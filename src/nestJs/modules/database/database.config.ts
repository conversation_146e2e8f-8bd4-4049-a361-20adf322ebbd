import { ConfigModule, ConfigService } from "@nestjs/config";
import { DataSource } from "typeorm";

import { buildDataSourceOptions } from "./database.provider";

/**
 * Database config module
 * This will load environment values.
 */
ConfigModule.forRoot({});

/**
 * DataSource exportation of the database provider
 * This will be used by the cli
 */
export default new DataSource(buildDataSourceOptions(new ConfigService()));

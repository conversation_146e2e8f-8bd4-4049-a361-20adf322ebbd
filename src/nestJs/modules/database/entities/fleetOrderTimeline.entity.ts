import { PrimaryGeneratedColumn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import FleetOrderEntity, { FleetOrderStatus } from "./fleetOrder.entity";

@Entity({ name: "fleet_order_timeline" })
export default class FleetOrderTimelineEntity extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ nullable: false })
  fleetOrderId: string;

  @ManyToOne(() => FleetOrderEntity, (fleetOrder) => fleetOrder.id)
  @JoinColumn({ foreignKeyConstraintName: "FK_fleet_order_timeline_fleet_order_id" })
  fleetOrder: FleetOrderEntity;

  @Column({ nullable: true, enum: FleetOrderStatus })
  status?: FleetOrderStatus;

  @Column({ nullable: false })
  thirdPartyStatus: string;

  @Column({ type: "jsonb", nullable: false, default: {} })
  snapshot: any;
}

import { PrimaryGeneratedColumn, Column, ManyToOne, Entity, OneToMany, OneToOne, JoinColumn } from "typeorm";

import { PartnerKey } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.interface";

import { DefaultEntity } from "./defaultEntity";
import FleetOrderTimelineEntity from "./fleetOrderTimeline.entity";
import Tx from "./tx.entity";
import User from "./user.entity";

export enum FleetOrderStatus {
  MATCHING = "MATCHING",
  ACCEPT = "ACCEPT",
  APPROACHING = "APPROACHING",
  ARRIVED = "ARRIVED",
  ON_GOING = "ON_GOING",
  COMPLETED = "COMPLETED",
  CANCELLED = "CANCELLED",
  TIMED_OUT = "TIMED_OUT",
}

@Entity({ name: "fleet_order" })
export default class FleetOrderEntity extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ nullable: false })
  txId: string;

  @Column({ nullable: false })
  hailingRequestId: string;

  @Column({ nullable: true })
  tripTxId?: string;

  @Column({ nullable: false })
  thirdPartyOrderId: string;

  @Column({ nullable: false, enum: FleetOrderStatus })
  status: FleetOrderStatus;

  @Column({ nullable: false })
  partnerKey: PartnerKey;

  @ManyToOne(() => User, (user) => user.id)
  @JoinColumn({ foreignKeyConstraintName: "FK_fleet_order_user_id" })
  user: User;

  @Column({ type: "jsonb", nullable: true })
  bookingReceiptSnapshot?: JSON;

  @OneToMany(() => FleetOrderTimelineEntity, (fleetOrderTimeline) => fleetOrderTimeline.fleetOrder)
  @JoinColumn({ foreignKeyConstraintName: "FK_fleet_order_timeline_fleet_order_id" })
  fleetOrderTimelineList: FleetOrderTimelineEntity[];

  @OneToOne(() => Tx, (tx) => tx.fleetOrder)
  @JoinColumn({ foreignKeyConstraintName: "FK_fleet_order_tx_id" })
  tx: Tx;

  @OneToOne(() => Tx, (tx) => tx.fleetOrder)
  @JoinColumn({ foreignKeyConstraintName: "FK_fleet_order_trip_tx_id" })
  tripTx?: Tx;
}

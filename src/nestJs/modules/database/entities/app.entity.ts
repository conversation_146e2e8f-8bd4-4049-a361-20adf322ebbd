import { randomUUID } from "crypto";

import { Entity, PrimaryGeneratedColumn, Index, Column } from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import { TxAppsNames } from "../../apps/dto/Apps.dto";
import { errorBuilder } from "../../utils/utils/error.utils";

@Entity()
export default class TxApp extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Index("app_name_index")
  @Column({ unique: true })
  name: TxAppsNames;

  static fromJson = (data: Partial<TxApp>) => {
    const txApp = new TxApp();
    txApp.id = data.id ?? randomUUID();
    if (!data.name) {
      throw errorBuilder.txApp.txAppNameRequired();
    }
    txApp.name = data.name;
    return txApp;
  };
}

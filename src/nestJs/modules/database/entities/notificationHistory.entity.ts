import { ApiProperty } from "@nestjs/swagger";
import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from "typeorm";

import WithStatusDefaultEntity from "./entityWithStatus";
import NotificationTask, { NotificationUserType } from "./notificationTask.entity";

export enum NotificationHistoryStatusEnum {
  SUCCESS = "SUCCESS",
  FAILURE = "FAILURE",
}

@Entity()
export default class NotificationHistory extends WithStatusDefaultEntity<NotificationHistoryStatusEnum> {
  @PrimaryGeneratedColumn("uuid")
  @ApiProperty()
  id: string;

  @ApiProperty({ type: () => NotificationTask })
  @ManyToOne(() => NotificationTask, (notificationTask) => notificationTask.id, {
    cascade: ["insert", "update"],
    onDelete: "RESTRICT",
  })
  @JoinColumn({
    name: "notificationTaskId",
    referencedColumnName: "id",
    foreignKeyConstraintName: "FK_notification_history__notificationTaskIdx",
  })
  notificationTask: NotificationTask;

  @ApiProperty({ nullable: true })
  @Column({ type: "uuid", nullable: true })
  userId?: string;

  @ApiProperty({ nullable: false })
  @Column({ nullable: false, default: NotificationUserType.USERS, type: "varchar" })
  userType: NotificationUserType;

  @ApiProperty()
  @Column()
  title: string;

  @ApiProperty()
  @Column()
  body: string;

  // for PUSH, we'll include the tokenId and messageId
  @ApiProperty()
  @Column("jsonb", { comment: "Stores anything that enriches our context of this notification" })
  metadata: Record<string, any>;
}

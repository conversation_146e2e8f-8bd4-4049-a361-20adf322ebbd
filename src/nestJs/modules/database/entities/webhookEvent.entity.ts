import { Column, Entity, Index, ManyToOne, PrimaryGeneratedColumn } from "typeorm";

import { WebhookEventType } from "@nest/modules/pubsub/dto/PublishMessageForWebhookProcessing.dto";

import { DefaultEntity } from "./defaultEntity";
import Webhook from "./webhook.entity";

@Entity()
@Index("webhook_type", ["webhook", "type"])
export default class WebhookEvent extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "enum",
    enum: WebhookEventType,
  })
  type: WebhookEventType;

  @ManyToOne(() => Webhook, { nullable: false, orphanedRowAction: "delete" })
  webhook: Webhook;
}

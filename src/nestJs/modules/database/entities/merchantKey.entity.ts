import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGeneratedC<PERSON>umn, ManyToOne, DeleteDateColumn } from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import Merchant from "./merchant.entity";

export enum KeyType {
  SIGNATURE_KEY = "SIGNATURE_KEY",
  API_KEY = "API_KEY",
}

/**
 * Merchant key entity to store secrets and api keys
 */
@Entity()
export default class MerchantKey extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    type: "enum",
    enum: KeyType,
  })
  type: KeyType;

  @Column({ type: "bytea", nullable: false, unique: true })
  encryptedKey: string | Uint8Array;

  @ManyToOne(() => Merchant)
  merchant: Merchant;

  @DeleteDateColumn()
  deletedAt?: Date;

  /**
   * Get token identifier based on key type
   */
  static getTokenIdentifier(type: KeyType) {
    switch (type) {
      case KeyType.SIGNATURE_KEY:
        return "sig";
      case KeyType.API_KEY:
        return "api";
      default:
        return "";
    }
  }
}

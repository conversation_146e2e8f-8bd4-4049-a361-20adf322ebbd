import { ApiProperty } from "@nestjs/swagger";
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from "typeorm";

import WithStatusDefaultEntity from "./entityWithStatus";

export enum NotificationTaskType {
  PUSH = "PUSH",
  EMAIL = "EMAIL",
  SMS = "SMS",
}

export enum NotificationTaskStatus {
  DRAFT = "DRAFT",
  SCHEDULED = "SCHEDULED", // task created
  PROCESSING = "PROCESSING", // task started/in-progress
  COMPLETED = "COMPLETED", // task completed
  FAILED = "FAILED", // task completed but failed
  PENDING_DELETION = "PENDING_DELETION",
  DELETED = "DELETED",
}

export class NotificationTaskStatusError extends Error {
  taskId: string;
  currentState: NotificationTaskStatus;
  newState: NotificationTaskStatus;

  constructor(taskId: string, currentState: NotificationTaskStatus, newState: NotificationTaskStatus) {
    super(`Invalid state transition for Task ${taskId}: ${currentState} -> ${newState}`);
    this.name = "NotificationTaskStatusError";
    this.taskId = taskId;
    this.currentState = currentState;
    this.newState = newState;
  }
}

export const NotificationTaskStateOrder: Record<NotificationTaskStatus, NotificationTaskStatus[]> = {
  [NotificationTaskStatus.DRAFT]: [NotificationTaskStatus.SCHEDULED, NotificationTaskStatus.PENDING_DELETION],
  [NotificationTaskStatus.SCHEDULED]: [NotificationTaskStatus.PROCESSING, NotificationTaskStatus.PENDING_DELETION],
  [NotificationTaskStatus.PROCESSING]: [NotificationTaskStatus.COMPLETED, NotificationTaskStatus.FAILED],
  [NotificationTaskStatus.PENDING_DELETION]: [NotificationTaskStatus.DELETED],
  // final states cannot transition out
  [NotificationTaskStatus.COMPLETED]: [],
  [NotificationTaskStatus.FAILED]: [],
  [NotificationTaskStatus.DELETED]: [],
};

export enum NotificationUserType {
  USERS = "USERS",
  MERCHANTS = "MERCHANTS",
}

@Entity()
export default class NotificationTask extends WithStatusDefaultEntity<NotificationTaskStatus> {
  @ApiProperty()
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ApiProperty()
  @Column()
  name: string;

  @ApiProperty({ enum: NotificationTaskType })
  @Column()
  type: NotificationTaskType;

  @ApiProperty({ nullable: false })
  @Column({ nullable: false, default: NotificationUserType.USERS, type: "varchar" })
  userType: NotificationUserType;

  @ApiProperty()
  @Column()
  createdBy: string;

  @ApiProperty()
  @CreateDateColumn({ type: "timestamp", default: () => "CURRENT_TIMESTAMP(6)" })
  scheduledAt: Date;

  @ApiProperty()
  @Column({ nullable: true })
  cloudTaskReference: string;

  @ApiProperty()
  @Column("jsonb")
  payload: Record<string, any>;

  draft() {
    this.transition(NotificationTaskStatus.DRAFT);
  }

  schedule() {
    this.transition(NotificationTaskStatus.SCHEDULED);
  }

  process() {
    this.transition(NotificationTaskStatus.PROCESSING);
  }

  forDeletion() {
    this.transition(NotificationTaskStatus.PENDING_DELETION);
  }

  delete() {
    this.transition(NotificationTaskStatus.DELETED);
  }

  fail(reason: string) {
    this.failureReason = reason;
    this.transition(NotificationTaskStatus.FAILED);
  }

  complete() {
    this.transition(NotificationTaskStatus.COMPLETED);
  }

  canTransition(toNewState: NotificationTaskStatus): boolean {
    const currentState = this.status;

    const validStates = NotificationTaskStateOrder[currentState];
    return validStates.includes(toNewState);
  }

  transition(newState: NotificationTaskStatus) {
    const currentState = this.status;

    if (!this.canTransition(newState)) {
      throw new NotificationTaskStatusError(this.id, currentState, newState);
    }

    this.status = newState;
  }
}

import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, Index, ManyToOne, PrimaryGeneratedColumn } from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import Merchant from "./merchant.entity";

@Entity()
export default class MerchantNotificationToken extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Index({ unique: true })
  @Column()
  token: string;

  @Column()
  lastUpdateDate: Date;

  @ManyToOne(() => Merchant, (merchant) => merchant.id)
  @ApiProperty({ type: () => Merchant })
  merchant: Merchant;
}

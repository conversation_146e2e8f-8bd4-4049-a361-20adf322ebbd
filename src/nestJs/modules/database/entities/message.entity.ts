import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import User from "./user.entity";
import { ChannelTypes } from "../../message/dto/channelType.dto";
import { MessageMetadata } from "../../message/dto/messageMetadata.dto";
import { MessageParams } from "../../message/dto/messageParams.dto";
import { TemplateTypesText } from "../../message/dto/templateType.dto";
import { NotificationType } from "../../message/messageFactory/modules/notification/notification.dto";

@Entity()
export default class Message extends DefaultEntity {
  @PrimaryGeneratedColumn()
  id: string;

  @Column()
  type: ChannelTypes;

  @ManyToOne(() => User, { cascade: ["insert", "update"], nullable: true })
  user?: User;

  @Column()
  template: TemplateTypesText | NotificationType;

  @Column()
  language: string;

  @Column("json")
  params: MessageParams;

  @Column("json")
  metadata: MessageMetadata;

  @Column()
  messageProviderId: string;

  static fromJson(json: Record<string, any>): Message {
    const message = new Message();
    message.type = json.channel;
    message.metadata = json.metadata;
    message.template = json.template;
    message.language = json.language;
    message.params = json.params;
    return message;
  }
}

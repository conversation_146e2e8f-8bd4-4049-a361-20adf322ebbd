import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

import { PartnerK<PERSON> } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.interface";

import { DefaultEntity } from "./defaultEntity";

export interface FleetPartnerConfig {
  METER_ID?: string;
  MERCHANT_ID?: string;
}

@Entity({ name: "fleet_partner" })
export default class FleetPartnerEntity extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ nullable: false, unique: true })
  partnerKey: PartnerKey;

  @Column({ nullable: false, type: "jsonb" })
  config: FleetPartnerConfig;

  @Column({ nullable: false, type: "int", default: 2 })
  matching_task_interval: number;

  @Column({ nullable: false, type: "int", default: 60 * 30 })
  accept_task_interval: number;

  @Column({ nullable: false, type: "int", default: 60 * 1 })
  approaching_task_interval: number;

  @Column({ nullable: false, type: "int", default: 60 * 5 })
  ongoing_task_interval: number;
}

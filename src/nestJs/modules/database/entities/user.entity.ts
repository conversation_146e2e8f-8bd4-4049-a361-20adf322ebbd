import { ApiProperty } from "@nestjs/swagger";
import {
  Entity,
  PrimaryGeneratedColumn,
  Index,
  Column,
  DeleteDateColumn,
  OneToMany,
  JoinColumn,
  ManyToOne,
} from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import PaymentInstrument from "./paymentInstrument.entity";
import { GenderType, PreferredLanguageType } from "../../identity/dto/user.dto";

@Entity()
@Index("registered_user_phoneNumber_appDatabaseId_deletedAt", ["phoneNumber"], {
  unique: true,
  where: '"deletedAt" IS NULL AND "appDatabaseId" IS NOT NULL',
})
@Index("anonymous_user_phoneNumber_appDatabaseId_deletedAt", ["phoneNumber"], {
  unique: true,
  where: '"deletedAt" IS NULL AND "appDatabaseId" IS NULL',
})
@Index("registered_user_email_deletedAt", ["email"], {
  unique: true,
  where: '"deletedAt" IS NULL',
})
export default class User extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Index("phoneNumber_index_for_user")
  @Column()
  phoneNumber: string;

  @Index("email_index")
  @Column({ nullable: true })
  email?: string;

  @Index("appDatabase_index_for_user")
  @Column({ unique: true, nullable: true })
  appDatabaseId?: string;

  @DeleteDateColumn({ type: "timestamp", nullable: true })
  deletedAt?: Date;

  @Column({ nullable: true })
  @ApiProperty({ type: "enum", enum: GenderType })
  gender?: GenderType;

  @Column({ type: "timestamp", nullable: true })
  dateOfBirth?: Date;

  @Column("varchar", { nullable: true, length: 50 })
  firstName?: string;

  @Column("varchar", { nullable: true, length: 50 })
  lastName?: string;

  @Column({ nullable: true })
  salt?: string;

  @Column({ nullable: true })
  hashedPin?: string;

  @Column({ nullable: true })
  @ApiProperty({ type: "enum", enum: PreferredLanguageType })
  preferredLanguage?: PreferredLanguageType;

  @Column({ nullable: true, default: false })
  marketingPreferenceEmail: boolean;

  @Column({ nullable: true, default: false })
  marketingPreferenceSMS: boolean;

  @Column({ nullable: true, default: false })
  marketingPreferencePush: boolean;

  @Column({ nullable: true, default: false })
  marketingConsent: boolean;

  @Column({ nullable: true })
  privateKey?: string;

  @Column({ nullable: true })
  publicKey?: string;

  @Column({ nullable: false, default: 0 })
  pinErrorCount: number;

  @OneToMany(() => PaymentInstrument, (paymentInstrument) => paymentInstrument.user, { nullable: false })
  paymentInstruments: PaymentInstrument[];

  @Column("varchar", { nullable: true, unique: true })
  referralCode?: string;

  @Column({ nullable: true })
  referrerId?: string;

  @ManyToOne(() => User, { nullable: true, lazy: true })
  @JoinColumn({ name: "referrerId" })
  referrer?: Promise<User>;

  /**Partially obfuscated phone number for displaying to clients */
  get maskedPhoneNumber(): string {
    return `***${this.phoneNumber.slice(-4)}`;
  }
}

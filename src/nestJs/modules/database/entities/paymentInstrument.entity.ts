import { randomUUID } from "crypto";

import { Entity, PrimaryGeneratedColumn, Index, Column, ManyToOne, DeleteDateColumn } from "typeorm";

import { DefaultEntity } from "./defaultEntity";
import User from "./user.entity";
import { UserPaymentInstrumentDocument } from "../../appDatabase/documents/userPaymentInstrument.document";
import { PaymentGatewayTypes } from "../../payment/dto/paymentGatewayTypes.dto";
import {
  PaymentInstrumentState,
  PaymentInstrumentType,
} from "../../payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { errorBuilder } from "../../utils/utils/error.utils";

@Entity()
@Index("user_token_deletedAt", ["user", "token"], {
  unique: true,
  where: '"deletedAt" IS NULL',
})
@Index("user_instrumentIdentifier_deletedAt", ["user", "instrumentIdentifier"], {
  unique: true,
  where: '"deletedAt" IS NULL',
})
export default class PaymentInstrument extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Index("card_token_index")
  @Column()
  token: string;

  @Index("card_token_kraken_index")
  @Column()
  tokenKraken: string;

  @Index("card_payment_instrument_identifier_index")
  @Column()
  instrumentIdentifier: string;

  @ManyToOne(() => User, { nullable: false })
  user: User;

  @Column()
  cardHolderName: string;

  @Column({ type: "timestamp", nullable: true })
  verifiedAt?: Date | null;

  @Column()
  expirationDate: Date;

  @Column()
  expirationYear: string;

  @Column()
  expirationMonth: string;

  @Column({ type: "enum", enum: Object.values(PaymentInstrumentState) })
  state: PaymentInstrumentState;

  @Column({ type: "varchar", length: 6 })
  cardPrefix: string;

  @Column({ type: "varchar", length: 4 })
  cardSuffix: string;

  @Column({ type: "varchar", nullable: true })
  verificationTransactionId?: string | null;

  @Column({ type: "boolean", nullable: true })
  isPayerAuthEnroled?: boolean | null;

  @Column({ type: "boolean", default: false })
  isPreferred: boolean;

  @Column({ type: "enum", enum: Object.values(PaymentInstrumentType) })
  cardType: PaymentInstrumentType;

  @DeleteDateColumn({ nullable: true })
  deletedAt: Date;

  @Column({ type: "enum", enum: PaymentGatewayTypes })
  paymentGateway: PaymentGatewayTypes;

  updateExpirationDate = () => {
    this.expirationDate = new Date(
      parseInt(this.expirationYear, 10),
      parseInt(this.expirationMonth, 10),
      0,
      23,
      59,
      59,
    );
  };

  get cardNumber() {
    return `${this.cardPrefix}****${this.cardSuffix}`;
  }

  static fromJson = (data: Partial<PaymentInstrument>) => {
    const paymentInstrument = new PaymentInstrument();
    paymentInstrument.id = data.id ?? randomUUID();
    paymentInstrument.verifiedAt = data.verifiedAt;
    paymentInstrument.verificationTransactionId = data.verificationTransactionId;
    paymentInstrument.isPayerAuthEnroled = data.isPayerAuthEnroled;
    paymentInstrument.isPreferred = !!data.isPreferred;

    if (!data.token) {
      throw errorBuilder.global.requiredParam("token");
    }
    paymentInstrument.token = data.token;

    if (!data.instrumentIdentifier) {
      throw errorBuilder.global.requiredParam("instrumentIdentifier");
    }
    paymentInstrument.instrumentIdentifier = data.instrumentIdentifier;

    if (!data.expirationYear) {
      throw errorBuilder.global.requiredParam("expirationYear");
    }
    paymentInstrument.expirationYear = data.expirationYear;

    if (!data.expirationMonth) {
      throw errorBuilder.global.requiredParam("expirationMonth");
    }
    paymentInstrument.expirationMonth = data.expirationMonth;

    paymentInstrument.updateExpirationDate();

    if (!data.cardHolderName) {
      throw errorBuilder.global.requiredParam("cardHolderName");
    }
    paymentInstrument.cardHolderName = data.cardHolderName;

    if (!data.cardPrefix) {
      throw errorBuilder.global.requiredParam("cardPrefix");
    }
    paymentInstrument.cardPrefix = data.cardPrefix;

    if (!data.cardSuffix) {
      throw errorBuilder.global.requiredParam("cardSuffix");
    }
    paymentInstrument.cardSuffix = data.cardSuffix;

    if (!data.state) {
      throw errorBuilder.global.requiredParam("state");
    }
    paymentInstrument.state = data.state;

    if (!data.cardType) {
      throw errorBuilder.global.requiredParam("cardType");
    }
    paymentInstrument.cardType = data.cardType;

    if (!data.paymentGateway) {
      throw errorBuilder.global.requiredParam("paymentGateway");
    }
    paymentInstrument.paymentGateway = data.paymentGateway;

    if (data.user?.id) {
      const user = new User();
      user.id = data.user.id;
      paymentInstrument.user = user;
    }

    return paymentInstrument;
  };

  toJson = (): UserPaymentInstrumentDocument => {
    return {
      id: this.id,
      token: this.token,
      instrumentIdentifier: this.instrumentIdentifier,
      verifiedAt: this.verifiedAt,
      expirationDate: this.expirationDate,
      expirationYear: this.expirationYear,
      expirationMonth: this.expirationMonth,
      cardHolderName: this.cardHolderName,
      cardPrefix: this.cardPrefix,
      cardSuffix: this.cardSuffix,
      state: this.state,
      isPreferred: this.isPreferred,
      cardType: this.cardType,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      verificationTransactionId: this.verificationTransactionId,
      isPayerAuthEnroled: this.isPayerAuthEnroled,
    };
  };

  get publicData() {
    return {
      id: this.id,
      token: this.token,
      instrumentIdentifier: this.instrumentIdentifier,
      cardNumber: this.cardNumber,
      cardHolderName: this.cardHolderName,
      expirationDate: this.expirationDate,
      cardType: this.cardType,
      isPreferred: this.isPreferred,
      state: this.state,
      cardPrefix: this.cardPrefix,
      cardSuffix: this.cardSuffix,
      expirationMonth: this.expirationMonth,
      expirationYear: this.expirationYear,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}

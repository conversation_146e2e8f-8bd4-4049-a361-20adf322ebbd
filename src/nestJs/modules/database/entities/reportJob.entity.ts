import { ApiProperty, ApiPropertyOptional, getSchemaPath } from "@nestjs/swagger";
import { Entity, PrimaryGeneratedColumn, Column } from "typeorm";

import {
  FleetTripReportMerchantMetadata,
  HeartbeatMerchantMetadata,
  QueryMetadata,
  ReportJobMerchantMetadata,
  ReportType,
} from "@nest/modules/reportJob/dto/reportJob.dto";

import { DefaultEntity } from "./defaultEntity";

@Entity()
export default class ReportJob extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  @ApiProperty()
  id: string;

  @Column()
  @ApiProperty()
  name: string;

  @Column({ nullable: true })
  @ApiPropertyOptional()
  lastRunAt?: Date;

  @Column()
  @ApiPropertyOptional()
  nextRunAt: Date;

  @Column()
  @ApiProperty()
  cron: string;

  @Column({ type: "enum", enum: ReportType })
  @ApiProperty({ type: "enum", enum: ReportType })
  reportType: ReportType;

  @Column()
  @ApiProperty()
  destination: string;

  @Column({ type: "jsonb", nullable: true })
  @ApiPropertyOptional()
  queryMetadata?: QueryMetadata;

  @Column({ nullable: true })
  @ApiPropertyOptional()
  queryString?: string;

  @Column("jsonb")
  @ApiProperty({
    oneOf: [
      { type: getSchemaPath(FleetTripReportMerchantMetadata) },
      { type: getSchemaPath(HeartbeatMerchantMetadata) },
    ],
  })
  merchantMetadata: ReportJobMerchantMetadata;

  @Column({ nullable: true })
  @ApiPropertyOptional()
  createdBy?: string;
}

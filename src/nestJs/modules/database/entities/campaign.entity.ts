import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from "typeorm";

import { CampaignSponsorType } from "@nest/modules/campaign/dto/campaign.dto";

import { DefaultEntity } from "./defaultEntity";
import Discount from "./discount.entity";

@Entity()
export default class Campaign extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  @ApiProperty()
  id: string;

  @Column("varchar", { length: 100 })
  @ApiProperty()
  nameEn: string;

  @Column("varchar", { length: 100 })
  @ApiProperty()
  nameTc: string;

  @Column("varchar", { length: 4000 })
  @ApiProperty()
  descriptionEn: string;

  @Column("varchar", { length: 4000 })
  @ApiProperty()
  descriptionTc: string;

  @Column("varchar", { length: 400, nullable: true })
  @ApiProperty()
  validityDescriptionEn?: string;

  @Column("varchar", { length: 400, nullable: true })
  @ApiProperty()
  validityDescriptionTc?: string;

  @Column("varchar", { length: 200 })
  @ApiProperty()
  imageEn: string;

  @Column("varchar", { length: 200 })
  @ApiProperty()
  imageTc: string;

  @Column({ type: "timestamp" })
  @ApiProperty()
  startAt: Date;

  @Column({ type: "timestamp" })
  @ApiProperty()
  endAt: Date;

  @Column({ nullable: false, type: "integer" })
  @ApiProperty()
  maxCount: number;

  @Column("varchar")
  @ApiProperty()
  applicationRules: string;

  @Column("varchar", { nullable: true })
  @ApiProperty()
  discountRules?: string;

  @Column("varchar", { nullable: true })
  @ApiProperty()
  bonusRules?: string;

  @Column({ nullable: false, type: "integer", default: 0 })
  @ApiProperty()
  priority: number;

  @Column({ nullable: true, type: "integer" })
  @ApiProperty()
  campaignMaxCount?: number;

  @OneToMany(() => Discount, (discount) => discount.campaign)
  discounts: Discount[];

  @Column({ type: "boolean", nullable: false, default: true })
  @ApiProperty()
  isOnDemand: boolean;

  @Column("varchar", { nullable: true, array: true })
  @ApiProperty()
  eventTriggers?: string[];

  @Column("text", { nullable: true })
  @ApiProperty()
  expiryRules?: string;

  @Column({ type: "enum", enum: CampaignSponsorType, default: CampaignSponsorType.THIRD_PARTY })
  @ApiProperty({ type: "enum", enum: CampaignSponsorType })
  sponsorType: CampaignSponsorType;

  @Column({ type: "double precision" })
  @ApiProperty()
  availableValue: number;
}

import { CreateDateColumn, UpdateDateColumn } from "typeorm";

/**
 * Default entity to add createdAt and updatedAt columns to all entities
 * This needs to be extended by all entities
 */
export class DefaultEntity {
  @CreateDateColumn({ type: "timestamp", default: () => "CURRENT_TIMESTAMP(6)" })
  public createdAt: Date;

  @UpdateDateColumn({ type: "timestamp", default: () => "CURRENT_TIMESTAMP(6)", onUpdate: "CURRENT_TIMESTAMP(6)" })
  public updatedAt: Date;
}

import { ApiProperty } from "@nestjs/swagger";
import { Column } from "typeorm";

import { DefaultEntity } from "./defaultEntity";

/**
 * Shared by notification_history and notification_task
 *
 * Overengineered DRY columns with typed status
 */

export default abstract class WithStatusDefaultEntity<T> extends DefaultEntity {
  @ApiProperty({ type: () => String })
  // stored as string instead of db enums for simplicity
  @Column({ type: String })
  status: T;

  @ApiProperty()
  @Column({ nullable: true })
  failureReason?: string;
}

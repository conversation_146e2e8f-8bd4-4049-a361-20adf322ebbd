import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from "typeorm";

import { PartnerKey } from "@nest/modules/me/modules/meFleetTaxi/meFleetTaxi.interface";

import { DefaultEntity } from "./defaultEntity";
import User from "./user.entity";

@Entity({ name: "fleet_quote" })
export default class FleetQuoteEntity extends DefaultEntity {
  @PrimaryGeneratedColumn("uuid")
  @ApiProperty()
  id: string;

  @Column({ nullable: false })
  @ApiProperty()
  userId: string;

  @Column({ nullable: false })
  @ApiProperty()
  thirdPartyQuoteId: string;

  @Column("jsonb", { nullable: false, default: {} })
  @ApiProperty()
  snapshot: any;

  @ApiProperty()
  @Column({ nullable: false })
  partnerKey: PartnerKey;

  @ManyToOne(() => User, (user) => user.id)
  @ApiProperty({ type: () => User })
  user: User;
}

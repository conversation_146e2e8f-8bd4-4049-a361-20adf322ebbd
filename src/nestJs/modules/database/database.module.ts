import { DynamicModule, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { DatabaseLogger } from "./database.logger";
import { DatabaseProvider } from "./database.provider";
import { EntityLoggerSubscriber } from "./database.subscriber";
import TxApp from "./entities/app.entity";
import Campaign from "./entities/campaign.entity";
import Discount from "./entities/discount.entity";
import FleetOrderEntity from "./entities/fleetOrder.entity";
import FleetOrderTimelineEntity from "./entities/fleetOrderTimeline.entity";
import FleetPartnerEntity from "./entities/fleetPartner.entity";
import FleetQuoteEntity from "./entities/fleetQuote.entity";
import Merchant from "./entities/merchant.entity";
import MerchantKey from "./entities/merchantKey.entity";
import MerchantNotificationToken from "./entities/merchantNotificationToken.entity";
import Message from "./entities/message.entity";
import NotificationHistory from "./entities/notificationHistory.entity";
import NotificationTask from "./entities/notificationTask.entity";
import PaymentInstrument from "./entities/paymentInstrument.entity";
import PaymentTx from "./entities/paymentTx.entity";
import Payout from "./entities/payout.entity";
import ProfileAudit from "./entities/profileAudit.entity";
import ReportJob from "./entities/reportJob.entity";
import Tx from "./entities/tx.entity";
import TxEvent from "./entities/txEvent.entity";
import TxTag from "./entities/txTag.entity";
import User from "./entities/user.entity";
import UserNotificationToken from "./entities/userNotificationToken.entity";
import Webhook from "./entities/webhook.entity";

/**
 * Database module
 */
@Module({
  imports: [DatabaseProvider],
  exports: [DatabaseProvider, DatabaseLogger, EntityLoggerSubscriber],
  providers: [DatabaseLogger, EntityLoggerSubscriber],
})
export class DatabaseModule {
  static forRoot(): DynamicModule {
    return {
      global: true,
      module: DatabaseModule,
      imports: [
        DatabaseProvider,
        TypeOrmModule.forFeature([
          TxApp,
          Tx,
          PaymentTx,
          Merchant,
          Payout,
          TxTag,
          Message,
          User,
          PaymentInstrument,
          ProfileAudit,
          Campaign,
          Discount,
          UserNotificationToken,
          TxEvent,
          MerchantNotificationToken,
          MerchantKey,
          ReportJob,
          Webhook,
          FleetOrderEntity,
          FleetOrderTimelineEntity,
          FleetQuoteEntity,
          FleetPartnerEntity,
          NotificationTask,
          NotificationHistory,
        ]),
      ],
      exports: [DatabaseProvider, DatabaseLogger, TypeOrmModule, EntityLoggerSubscriber],
      providers: [DatabaseLogger, EntityLoggerSubscriber],
    };
  }
}

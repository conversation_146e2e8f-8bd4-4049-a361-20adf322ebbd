import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import FleetOrderTimelineEntity from "../entities/fleetOrderTimeline.entity";

@Injectable()
export class FleetOrderTimelineRepository extends Repository<FleetOrderTimelineEntity> {
  constructor(
    @InjectRepository(FleetOrderTimelineEntity)
    fleetOrderTimelineRepository: Repository<FleetOrderTimelineEntity>,
  ) {
    super(
      fleetOrderTimelineRepository.target,
      fleetOrderTimelineRepository.manager,
      fleetOrderTimelineRepository.queryRunner,
    );
  }

  async findLastTimelineByFleetOrderId(fleetOrderId: string): Promise<FleetOrderTimelineEntity | null> {
    return this.findOne({
      where: { fleetOrderId },
      order: { createdAt: "DESC" },
    });
  }
}

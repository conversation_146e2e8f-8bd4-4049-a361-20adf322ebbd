import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import ReportJob from "../entities/reportJob.entity";

@Injectable()
export class ReportJobRepository extends Repository<ReportJob> {
  constructor(@InjectRepository(ReportJob) repository: Repository<ReportJob>) {
    super(repository.target, repository.manager, repository.queryRunner);
  }
}

import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import FleetQuoteEntity from "../entities/fleetQuote.entity";

@Injectable()
export class FleetQuoteRepository extends Repository<FleetQuoteEntity> {
  constructor(@InjectRepository(FleetQuoteEntity) fleetQuoteRepository: Repository<FleetQuoteEntity>) {
    super(fleetQuoteRepository.target, fleetQuoteRepository.manager, fleetQuoteRepository.queryRunner);
  }
}

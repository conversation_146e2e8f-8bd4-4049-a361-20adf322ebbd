import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import Webhook from "../entities/webhook.entity";

@Injectable()
export class WebhookRepository extends Repository<Webhook> {
  constructor(@InjectRepository(Webhook) webhookRepository: Repository<Webhook>) {
    super(webhookRepository.target, webhookRepository.manager, webhookRepository.queryRunner);
  }
}

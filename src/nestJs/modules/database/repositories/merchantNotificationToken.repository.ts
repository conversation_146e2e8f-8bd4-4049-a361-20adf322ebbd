import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { In, Repository } from "typeorm";

import { PlatformMerchantType } from "../entities/merchant.entity";
import MerchantNotificationToken from "../entities/merchantNotificationToken.entity";

@Injectable()
export class MerchantNotificationTokenRepository extends Repository<MerchantNotificationToken> {
  constructor(
    @InjectRepository(MerchantNotificationToken)
    merchantNotificationTokenRepository: Repository<MerchantNotificationToken>,
  ) {
    super(
      merchantNotificationTokenRepository.target,
      merchantNotificationTokenRepository.manager,
      merchantNotificationTokenRepository.queryRunner,
    );
  }

  async findDashMerchantByPhoneNumbers(phoneNumbers: string[]): Promise<MerchantNotificationToken[]> {
    return this.find({
      where: {
        merchant: {
          phoneNumber: In(phoneNumbers),
          platformMerchantType: PlatformMerchantType.DASH,
          // applicationStatus: ApplicationStatus.APPROVED, // Uncomment if you want to filter by application status
          // activatedAt: Not(IsNull()), // Uncomment if you want to filter by activated merchants
        },
      },
      relations: ["merchant"],
    });
  }
}

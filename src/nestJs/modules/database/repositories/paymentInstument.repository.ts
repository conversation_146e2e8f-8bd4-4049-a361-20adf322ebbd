import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import PaymentInstrument from "../entities/paymentInstrument.entity";

@Injectable()
export class PaymentInstrumentRepository extends Repository<PaymentInstrument> {
  constructor(@InjectRepository(PaymentInstrument) paymentInstrumentRepository: Repository<PaymentInstrument>) {
    super(
      paymentInstrumentRepository.target,
      paymentInstrumentRepository.manager,
      paymentInstrumentRepository.queryRunner,
    );
  }
}

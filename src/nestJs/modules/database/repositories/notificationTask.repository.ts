import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import NotificationTask from "../entities/notificationTask.entity";

@Injectable()
export class NotificationTaskRepository extends Repository<NotificationTask> {
  constructor(@InjectRepository(NotificationTask) repository: Repository<NotificationTask>) {
    super(repository.target, repository.manager, repository.queryRunner);
  }
}

import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { In, Repository } from "typeorm";

import Merchant, { PlatformMerchantType } from "../entities/merchant.entity";

@Injectable()
export class MerchantRepository extends Repository<Merchant> {
  constructor(@InjectRepository(Merchant) merchantRepository: Repository<Merchant>) {
    super(merchantRepository.target, merchantRepository.manager, merchantRepository.queryRunner);
  }

  async findDashMerchantsByPhoneNumbers(phoneNumbers: string[]): Promise<Merchant[]> {
    // save time by not checking isAppUser one-by-one, instead filter all at once by SQL
    const users = await this.find({
      where: {
        phoneNumber: In(phoneNumbers),
        platformMerchantType: PlatformMerchantType.DASH,
        // applicationStatus: ApplicationStatus.APPROVED, // Uncomment if you want to filter by application status
        // activatedAt: Not(IsNull()), // Uncomment if you want to filter by activated merchants
      },
    });

    return users;
  }
}

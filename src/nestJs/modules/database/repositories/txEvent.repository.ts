import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm/repository/Repository";

import TxEvent from "../entities/txEvent.entity";

@Injectable()
export class TxEventRepository extends Repository<TxEvent> {
  constructor(@InjectRepository(TxEvent) repository: Repository<TxEvent>) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  /**
   * Find tx_event records by txId
   * @param txId string
   * @returns Promise<TxEvent[]>
   */
  async findByTxId(txId: string): Promise<TxEvent[]> {
    return this.createQueryBuilder("tx_event")
      .select([
        "tx_event.id",
        "tx_event.type",
        "tx_event.createdBy",
        "tx_event.content",
        "tx_event.createdAt",
        "tx_event.updatedAt",
      ])
      .where("tx_event.tx = :txId", { txId })
      .getMany();
  }
}

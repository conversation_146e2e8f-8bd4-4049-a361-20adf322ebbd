import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import Campaign from "../entities/campaign.entity";

@Injectable()
export class CampaignRepository extends Repository<Campaign> {
  constructor(@InjectRepository(Campaign) campaignRepository: Repository<Campaign>) {
    super(campaignRepository.target, campaignRepository.manager, campaignRepository.queryRunner);
  }
}

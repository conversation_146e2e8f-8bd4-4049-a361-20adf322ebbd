import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import NotificationHistory from "../entities/notificationHistory.entity";

@Injectable()
export class NotificationHistoryRepository extends Repository<NotificationHistory> {
  constructor(@InjectRepository(NotificationHistory) repository: Repository<NotificationHistory>) {
    super(repository.target, repository.manager, repository.queryRunner);
  }
}

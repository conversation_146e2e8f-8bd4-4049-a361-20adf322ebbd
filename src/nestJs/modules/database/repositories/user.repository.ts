import { generateKeyPairSync } from "crypto";

import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { genSaltSync } from "bcrypt";
import { In, IsNull, Not, Repository } from "typeorm";

import { AppUser, isAppUser } from "../../user/dto/user.dto";
import { errorBuilder } from "../../utils/utils/error.utils";
import User from "../entities/user.entity";

@Injectable()
export class UserRepository extends Repository<User> {
  constructor(@InjectRepository(User) private userRepository: Repository<User>) {
    super(userRepository.target, userRepository.manager, userRepository.queryRunner);
  }

  async findAnonymousUserByPhoneNumber(phoneNumber: string): Promise<User> {
    let user = await this.userRepository.findOne({ where: { phoneNumber: phoneNumber, appDatabaseId: undefined } });
    if (!user) {
      user = new User();
      user.phoneNumber = phoneNumber;
    }
    return user;
  }

  async findAppUserByPhoneNumber(phoneNumber: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { phoneNumber: phoneNumber } });
    if (!user) {
      throw errorBuilder.user.notFoundPhoneNumberInSql(phoneNumber);
    }

    if (!isAppUser(user)) {
      throw errorBuilder.user.invalidUserType(phoneNumber);
    }

    return user;
  }

  async findAppUsersByPhoneNumbers(phoneNumbers: string[]): Promise<User[]> {
    // save time by not checking isAppUser one-by-one, instead filter all at once by SQL
    const users = await this.userRepository.find({
      where: {
        phoneNumber: In(phoneNumbers),
        appDatabaseId: Not(IsNull()),
        salt: Not(IsNull()),
        publicKey: Not(IsNull()),
        privateKey: Not(IsNull()),
      },
    });

    return users;
  }

  async findAppUserById(appDatabaseId: string, relations?: (keyof User)[] | undefined): Promise<AppUser> {
    const user = await this.userRepository.findOne({ where: { appDatabaseId: appDatabaseId }, relations });
    if (!user) {
      throw errorBuilder.user.notFoundInSql(appDatabaseId);
    }
    if (!isAppUser(user)) {
      throw errorBuilder.user.invalidUserType(appDatabaseId);
    }
    return user as AppUser;
  }

  async find2ndRowUserByPhoneNumberOrCreate(phoneNumber: string): Promise<User> {
    const users = await this.userRepository.find({ where: { phoneNumber: phoneNumber } });
    if (users.some((user) => Boolean(user.appDatabaseId))) {
      throw errorBuilder.user.alreadyRegisteredShouldGoToApp(phoneNumber);
    }
    if (users.length === 0) {
      const newUser = new User();
      newUser.phoneNumber = phoneNumber;
      return this.userRepository.save(newUser);
    }
    return users[0];
  }

  async find2ndRowUserByPhoneNumberOrCreateIgnoreRegisteredUser(phoneNumber: string): Promise<User> {
    const users = await this.userRepository.find({ where: { phoneNumber: phoneNumber } });
    const filteredUsers = users.filter((user) => !user.appDatabaseId);
    if (filteredUsers.length === 0) {
      const newUser = new User();
      newUser.phoneNumber = phoneNumber;
      return this.userRepository.save(newUser);
    }
    return filteredUsers[0];
  }

  async createUser(userData: Partial<User>): Promise<User> {
    const newUser = new User();
    if (!userData.appDatabaseId) {
      throw errorBuilder.global.requiredParam("appDatabaseId for user");
    }
    const existUser = await this.userRepository.findOne({ where: { appDatabaseId: userData.appDatabaseId } });
    if (existUser) {
      return existUser;
    }
    if (!userData.phoneNumber) {
      throw errorBuilder.global.requiredParam("phoneNumber for user");
    }
    newUser.appDatabaseId = userData.appDatabaseId;
    newUser.phoneNumber = userData.phoneNumber;
    newUser.salt = genSaltSync();
    const { publicKey, privateKey } = generateKeyPairSync("rsa", {
      modulusLength: 2048,
      publicKeyEncoding: { type: "pkcs1", format: "pem" },
      privateKeyEncoding: { type: "pkcs1", format: "pem" },
    });
    newUser.publicKey = publicKey;
    newUser.privateKey = privateKey;
    newUser.pinErrorCount = 0;
    return this.userRepository.save(newUser);
  }
}

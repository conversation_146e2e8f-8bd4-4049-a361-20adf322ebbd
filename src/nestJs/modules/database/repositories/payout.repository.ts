import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import Payout from "../entities/payout.entity";

@Injectable()
export class PayoutRepository extends Repository<Payout> {
  constructor(@InjectRepository(Payout) payoutRepository: Repository<Payout>) {
    super(payoutRepository.target, payoutRepository.manager, payoutRepository.queryRunner);
  }
}

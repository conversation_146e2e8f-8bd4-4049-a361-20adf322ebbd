import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { FleetOrderStatus } from "../entities/fleetOrder.entity";
import FleetPartnerEntity from "../entities/fleetPartner.entity";

@Injectable()
export class FleetPartnerRepository extends Repository<FleetPartnerEntity> {
  constructor(@InjectRepository(FleetPartnerEntity) fleetPartnerRepository: Repository<FleetPartnerEntity>) {
    super(fleetPartnerRepository.target, fleetPartnerRepository.manager, fleetPartnerRepository.queryRunner);
  }

  getUpdateInterval(fleetPartner: FleetPartnerEntity, status: FleetOrderStatus) {
    switch (status) {
      case FleetOrderStatus.MATCHING:
        return fleetPartner.matching_task_interval;
      case FleetOrderStatus.ACCEPT:
        return fleetPartner.accept_task_interval;
      case FleetOrderStatus.ARRIVED:
        return fleetPartner.approaching_task_interval;
      case FleetOrderStatus.APPROACHING:
        return fleetPartner.approaching_task_interval;
      case FleetOrderStatus.ON_GOING:
        return fleetPartner.ongoing_task_interval;
      default:
        return 0;
    }
  }
}

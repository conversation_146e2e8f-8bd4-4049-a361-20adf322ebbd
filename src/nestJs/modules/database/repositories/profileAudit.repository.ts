import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { ProfileAuditActionType, ProfileAuditSourceType } from "../../audit/profileAudit/dto/profileAudit.dto";
import { ProfileAuditMetadata } from "../../audit/profileAudit/dto/profileAuditMetadata.dto";
import ProfileAudit from "../entities/profileAudit.entity";
import User from "../entities/user.entity";

@Injectable()
export class ProfileAuditRepository extends Repository<ProfileAudit> {
  constructor(@InjectRepository(ProfileAudit) profileAuditRepository: Repository<ProfileAudit>) {
    super(profileAuditRepository.target, profileAuditRepository.manager, profileAuditRepository.queryRunner);
  }

  async createProfileAudit(
    action: ProfileAuditActionType,
    user: User,
    metadata: ProfileAuditMetadata,
    source: ProfileAuditSourceType,
    actionBy: string = "SYSTEM",
  ): Promise<ProfileAudit> {
    const profileAudit = new ProfileAudit();
    profileAudit.user = user;
    profileAudit.action = action;
    profileAudit.valueAfter = user;
    profileAudit.actionBy = actionBy;
    profileAudit.metadata = metadata;
    profileAudit.source = source;
    return this.save(profileAudit);
  }
}

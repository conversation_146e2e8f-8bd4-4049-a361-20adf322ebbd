import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import Discount from "../entities/discount.entity";

@Injectable()
export class DiscountRepository extends Repository<Discount> {
  constructor(@InjectRepository(Discount) discountRepository: Repository<Discount>) {
    super(discountRepository.target, discountRepository.manager, discountRepository.queryRunner);
  }
}

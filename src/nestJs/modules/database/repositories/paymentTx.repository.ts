import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import PaymentTx from "../entities/paymentTx.entity";

@Injectable()
export class PaymentTxRepository extends Repository<PaymentTx> {
  constructor(@InjectRepository(PaymentTx) paymentTxRepository: Repository<PaymentTx>) {
    super(paymentTxRepository.target, paymentTxRepository.manager, paymentTxRepository.queryRunner);
  }

  async upsertPaymentTx(paymentTxs: PaymentTx[]) {
    await this.upsert(paymentTxs, { conflictPaths: ["id"], skipUpdateIfNoValuesChanged: true });
    return paymentTxs;
  }
}

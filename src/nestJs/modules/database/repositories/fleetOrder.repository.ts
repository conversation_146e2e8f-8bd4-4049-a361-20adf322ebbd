import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import FleetOrderEntity from "../entities/fleetOrder.entity";

@Injectable()
export class FleetOrderRepository extends Repository<FleetOrderEntity> {
  constructor(@InjectRepository(FleetOrderEntity) fleetOrderRepository: Repository<FleetOrderEntity>) {
    super(fleetOrderRepository.target, fleetOrderRepository.manager, fleetOrderRepository.queryRunner);
  }
}

import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { In, IsNull, Not, Repository } from "typeorm";

import UserNotificationToken from "../entities/userNotificationToken.entity";

@Injectable()
export class UserNotificationTokenRepository extends Repository<UserNotificationToken> {
  constructor(
    @InjectRepository(UserNotificationToken) userNotificationTokenRepository: Repository<UserNotificationToken>,
  ) {
    super(
      userNotificationTokenRepository.target,
      userNotificationTokenRepository.manager,
      userNotificationTokenRepository.queryRunner,
    );
  }

  async findByPhoneNumbers(phoneNumbers: string[]): Promise<UserNotificationToken[]> {
    return this.find({
      where: {
        user: {
          phoneNumber: In(phoneNumbers),
          appDatabaseId: Not(IsNull()),
          salt: Not(IsNull()),
          publicKey: Not(IsNull()),
          privateKey: Not(IsNull()),
        },
      },
      relations: ["user"],
    });
  }
}

import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { TxAppsNames } from "../../apps/dto/Apps.dto";
import { errorBuilder } from "../../utils/utils/error.utils";
import TxApp from "../entities/app.entity";

@Injectable()
export class TxAppRepository extends Repository<TxApp> {
  constructor(@InjectRepository(TxApp) private appRepository: Repository<TxApp>) {
    super(appRepository.target, appRepository.manager, appRepository.queryRunner);
  }

  async appByNameOrCreate(name: TxAppsNames): Promise<TxApp> {
    const app = await this.appRepository.findOne({ where: { name } });

    if (app) {
      return app;
    } else {
      await this.appRepository.save(TxApp.from<PERSON>son({ name }));
    }

    const createdApp = await this.appRepository.findOne({ where: { name } });

    if (!createdApp) {
      throw errorBuilder.txApp.notFound(name);
    }

    return createdApp;
  }
}

import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import MerchantKey from "../entities/merchantKey.entity";

@Injectable()
export class MerchantKeyRepository extends Repository<MerchantKey> {
  constructor(@InjectRepository(MerchantKey) merchantKeyRepository: Repository<MerchantKey>) {
    super(merchantKeyRepository.target, merchantKeyRepository.manager, merchantKeyRepository.queryRunner);
  }
}

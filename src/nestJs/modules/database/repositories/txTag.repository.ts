import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { In, IsNull } from "typeorm";
import { Repository } from "typeorm/repository/Repository";

import { errorBuilder } from "../../utils/utils/error.utils";
import Tx from "../entities/tx.entity";
import TxTag from "../entities/txTag.entity";

@Injectable()
export class TxTagRepository extends Repository<TxTag> {
  constructor(@InjectRepository(TxTag) txTagRepository: Repository<TxTag>) {
    super(txTagRepository.target, txTagRepository.manager, txTagRepository.queryRunner);
  }

  /**
   * Remove Tag by set RemovedAt(if one tag has removedAt, means issue fixed)
   * @param id Tag.id
   */
  async removeTagById(txId: string, ids: number[]): Promise<void> {
    const result = await this.softDelete({ id: In(ids), tx: { id: txId }, removedAt: IsNull() });
    if (!result.affected || result.affected === 0) {
      throw errorBuilder.transaction.tag.notFoundOrRemoved(txId, ids);
    }
  }

  /**
   * Add tags to a existing tx, this function is called by backend only
   * @param tx Tx
   * @param txTags TxTagType[]
   * @returns The saved tx
   */
  async addTagsToTx(tx: Tx, txTags: TxTag[]): Promise<Tx> {
    const currentTags: TxTag[] = tx.txTag?.filter((txTag) => !txTag.removedAt) ?? [];
    const tagsSet = new Set(currentTags.map((tag) => tag.tag));
    txTags.forEach((txTag) => {
      if (!tagsSet.has(txTag.tag)) {
        tagsSet.add(txTag.tag);
        currentTags.push(txTag);
      }
    });
    await this.save(currentTags);
    tx.txTag = currentTags;
    return tx;
  }
}

import { MiddlewareConsumer, Module } from "@nestjs/common";

import { MerchantAuthMiddleware } from "@nest/infrastructure/middlewares/merchantAuth.middleware";

import { WebhookController } from "./webhook.controller";
import { WebhookService } from "./webhook.service";
import { MerchantKeyRepository } from "../database/repositories/merchantKey.repository";
import { WebhookRepository } from "../database/repositories/webhook.repository";
import { MerchantKeysModule } from "../merchant/merchantKeys/merchantKeys.module";

@Module({
  imports: [MerchantKeysModule],
  controllers: [WebhookController],
  providers: [WebhookService, WebhookRepository, MerchantKeyRepository],
  exports: [WebhookService],
})
export class WebhookModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(MerchantAuthMiddleware).forRoutes("/webhooks");
  }
}

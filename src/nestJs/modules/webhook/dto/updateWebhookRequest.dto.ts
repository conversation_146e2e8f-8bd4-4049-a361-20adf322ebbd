import { ApiPropertyOptional } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { WebhookEventType } from "@nest/modules/pubsub/dto/PublishMessageForWebhookProcessing.dto";

export class UpdateWebhookRequestDto {
  @ApiPropertyOptional({
    description: "The endpoint to send the webhook to",
    example: "https://example.com/webhook",
  })
  url?: string;

  @ApiPropertyOptional({
    description: "The merchant signature key id",
    example: "a70ae70a-7264-43fd-96ec-534f524faee0",
  })
  signatureKeyId?: string;

  @ApiPropertyOptional({
    description: "Webhook event types",
    example: [WebhookEventType.METER_HEARTBEAT],
  })
  events?: WebhookEventType[];
}

export const updateWebhookRequestSchema = Joi.object<UpdateWebhookRequestDto>({
  url: Joi.string().uri(),
  signatureKeyId: Joi.string().uuid(),
  events: Joi.array().items(Joi.string().valid(...Object.values(WebhookEventType))),
});

import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

import { WebhookEventType } from "@nest/modules/pubsub/dto/PublishMessageForWebhookProcessing.dto";

export class CreateWebhookRequestDto {
  @ApiProperty({
    description: "The endpoint to send the webhook to",
    example: "https://example.com/webhook",
  })
  url: string;

  @ApiProperty({
    description: "The merchant signature key id",
    example: "a70ae70a-7264-43fd-96ec-534f524faee0",
  })
  signatureKeyId: string;

  @ApiProperty({
    description: "Webhook event types",
    example: [WebhookEventType.METER_HEARTBEAT],
  })
  events: WebhookEventType[];
}

export const createWebhookRequestSchema = Joi.object<CreateWebhookRequestDto>({
  url: Joi.string().uri().required(),
  signatureKeyId: Joi.string().uuid().required(),
  events: Joi.array()
    .items(Joi.string().valid(...Object.values(WebhookEventType)))
    .required(),
});

import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import axios from "axios";
import { Cache } from "cache-manager";
import { IsNull } from "typeorm";

import { CreateWebhookRequestDto } from "./dto/createWebhookRequest.dto";
import { UpdateWebhookRequestDto } from "./dto/updateWebhookRequest.dto";
import { WebhookCache } from "./types";
import { KeyType } from "../database/entities/merchantKey.entity";
import Webhook from "../database/entities/webhook.entity";
import WebhookEvent from "../database/entities/webhookEvent.entity";
import { MerchantKeyRepository } from "../database/repositories/merchantKey.repository";
import { WebhookRepository } from "../database/repositories/webhook.repository";
import { MerchantKeysService } from "../merchant/merchantKeys/merchantKeys.service";
import {
  PublishMessageForWebhookProcessing,
  WebhookEventType,
} from "../pubsub/dto/PublishMessageForWebhookProcessing.dto";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import casesUtils from "../utils/utils/case/case.utils";
import { ChecksumUtils } from "../utils/utils/checksum.utils";
import { errorBuilder } from "../utils/utils/error.utils";

@Injectable()
export class WebhookService {
  static readonly CHECKSUM_HEADER = "x-dash-checksum-256";
  static readonly SIG_ID_HEADER = "x-dash-sig-id";

  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @InjectRepository(WebhookRepository) private readonly webhookRepository: WebhookRepository,
    private readonly merchantKeysService: MerchantKeysService,
    @InjectRepository(MerchantKeyRepository) private readonly merchantKeyRepository: MerchantKeyRepository,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  get cacheKeyMap() {
    return {
      [WebhookEventType.METER_HEARTBEAT]: (merchantId: string) => `meter:heartbeat:${merchantId}`,
      [WebhookEventType.METER_SESSION_START]: (merchantId: string) => `meter:session:start:${merchantId}`,
      [WebhookEventType.METER_SESSION_END]: (merchantId: string) => `meter:session:end:${merchantId}`,
    };
  }

  async getWebhooksByMerchantId(merchantId: string): Promise<Webhook[]> {
    return this.webhookRepository.find({
      where: { merchant: { id: merchantId }, deletedAt: IsNull() },
      relations: ["signatureKey", "events"],
    });
  }

  async createWebhook(merchantId: string, createWebhookRequestDto: CreateWebhookRequestDto): Promise<Webhook> {
    const signatureKey = await this.merchantKeyRepository.findOneBy({
      id: createWebhookRequestDto.signatureKeyId,
      type: KeyType.SIGNATURE_KEY,
      merchant: { id: merchantId },
      deletedAt: IsNull(),
    });
    if (!signatureKey) {
      throw errorBuilder.webhook.invalidSignatureKeyId(createWebhookRequestDto.signatureKeyId);
    }
    const webhook = await this.webhookRepository.save({
      url: createWebhookRequestDto.url,
      merchant: { id: merchantId },
      signatureKey,
      events: createWebhookRequestDto.events.map((event) => ({ type: event })),
    });

    await this.clearCacheKeysForMerchant(merchantId, createWebhookRequestDto.events);

    return webhook;
  }

  async updateWebhook(
    webhookId: string,
    merchantId: string,
    updateWebhookRequestDto: UpdateWebhookRequestDto,
  ): Promise<Webhook> {
    const webhook = await this.webhookRepository.findOne({
      where: { id: webhookId, merchant: { id: merchantId }, deletedAt: IsNull() },
      relations: ["signatureKey"],
    });
    if (!webhook) {
      throw errorBuilder.webhook.notFound(webhookId);
    }

    if (updateWebhookRequestDto.signatureKeyId) {
      const signatureKey = await this.merchantKeyRepository.findOneBy({
        id: updateWebhookRequestDto.signatureKeyId,
        type: KeyType.SIGNATURE_KEY,
        merchant: { id: merchantId },
        deletedAt: IsNull(),
      });
      if (!signatureKey) {
        throw errorBuilder.webhook.invalidSignatureKeyId(updateWebhookRequestDto.signatureKeyId);
      }
      webhook.signatureKey = signatureKey;
    }

    if (updateWebhookRequestDto.url) {
      webhook.url = updateWebhookRequestDto.url;
    }

    const eventsToClear = new Set<WebhookEventType>([
      ...(updateWebhookRequestDto.events ?? []),
      ...(webhook.events?.map((event) => event.type) ?? []),
    ]);
    if (updateWebhookRequestDto.events) {
      webhook.events = updateWebhookRequestDto.events.map((eventType) => {
        const event = new WebhookEvent();
        event.type = eventType;
        return event;
      });
    }

    await this.webhookRepository.save(webhook);

    await this.clearCacheKeysForMerchant(merchantId, Array.from(eventsToClear));
    await this.cacheManager.del(this.getWebhookCacheKey(webhookId));

    return webhook;
  }

  async deleteWebhook(webhookId: string, merchantId: string) {
    const webhookToDelete = await this.webhookRepository.findOne({
      where: { id: webhookId, merchant: { id: merchantId }, deletedAt: IsNull() },
    });
    const deleteResults = await this.webhookRepository.softDelete({
      id: webhookId,
      merchant: { id: merchantId },
      deletedAt: IsNull(),
    });
    await this.clearCacheKeysForMerchant(merchantId, webhookToDelete?.events?.map((event) => event.type) ?? []);
    await this.cacheManager.del(this.getWebhookCacheKey(webhookId));

    return deleteResults;
  }

  async processWebhookMessage(message: PublishMessageForWebhookProcessing) {
    const webhookCacheTtlMs = 600000;

    const webhook = await this.cacheManager.wrap<WebhookCache>(
      this.getWebhookCacheKey(message.webhookId),
      async () => {
        const foundWebhook = await this.webhookRepository.findOne({
          where: { id: message.webhookId },
          relations: ["signatureKey"],
        });
        if (!foundWebhook) {
          throw errorBuilder.webhook.notFound(message.webhookId);
        }

        const signatureSecret = await this.merchantKeysService.findByIdAndDecrypt(foundWebhook.signatureKey.id);
        if (!signatureSecret) {
          throw errorBuilder.webhook.signatureSecretNotFound(foundWebhook.signatureKey.id);
        }

        return {
          url: foundWebhook.url,
          signatureSecretKey: signatureSecret.key,
          signatureSecretId: signatureSecret.id,
        };
      },
      webhookCacheTtlMs,
    );

    const checksum = ChecksumUtils.generateChecksum(webhook.signatureSecretKey, JSON.stringify(message.message));
    const payload = casesUtils.snakeKeys(message.message);
    try {
      const response = await axios.post(webhook.url, payload, {
        headers: {
          [WebhookService.CHECKSUM_HEADER]: checksum,
          [WebhookService.SIG_ID_HEADER]: webhook.signatureSecretId,
        },
      });
      this.logger.info("Webhook message sent", { status: response.status, payload });
    } catch (err) {
      this.logger.error(err);
      throw errorBuilder.webhook.failedToSend(err);
    }
  }

  getCacheKeyForEvent(merchantId: string, event: WebhookEventType): string {
    switch (event) {
      case WebhookEventType.METER_HEARTBEAT:
        return `merchant:${merchantId}:meter:heartbeat`;
      case WebhookEventType.METER_SESSION_START:
        return `merchant:${merchantId}:meter:session:start`;
      case WebhookEventType.METER_SESSION_END:
        return `merchant:${merchantId}:meter:session:end`;
      default:
        throw errorBuilder.webhook.notImplemented(event);
    }
  }

  clearCacheKeysForMerchant(merchantId: string, events: WebhookEventType[]) {
    const keysToInvalidate = events.map((event) => this.getCacheKeyForEvent(merchantId, event));
    this.logger.info(`Clearing webhook cache keys for merchant ${merchantId}`, { keysToInvalidate });
    return Promise.all(keysToInvalidate.map((key) => this.cacheManager.del(key)));
  }

  private getWebhookCacheKey(webhookId: string) {
    return `webhook:${webhookId}`;
  }
}

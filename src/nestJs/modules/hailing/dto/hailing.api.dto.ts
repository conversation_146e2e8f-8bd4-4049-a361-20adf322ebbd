import { PaymentInstrumentType } from "../../payment/modules/paymentInstrument/dto/paymentInstrument.dto";
import { TxHailingRequestStatus } from "../../transaction/dto/txHailingRequest.dto";

export class HailingApiGetPriceEstimationResponse {
  min: number;
  max: number;
}

export class HailingApiBoostUpdateResponse {
  success: boolean;
  totalBoostAmount: number;
  addedBoostAmount: number;
  message: string;
  updated_at: string;
}

export interface BoostUpdateResponse {
  success: boolean;
  totalBoostAmount: number;
  addedBoostAmount: number;
  message: string;
  updatedAt: string;
}

export class HailingApiCreateOrderResponse {
  id: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  origin: Location;
  destination: Location;
  time: string;
  preferenceMatrix: PreferenceMatrix;
  filters: Filters;
  status: TxHailingRequestStatus;
  cancelledAt: Date;
  driverId?: string;
  paymentDetails: PaymentDetails;
  driverCountByRadius: { [key: string]: number };
}

interface Filters {
  isAssistant: boolean;
  isPetFriendly: boolean;
}

interface PreferenceMatrix {
  [vehicleClass: string]: string[];
}

interface Location {
  lat: number;
  lng: number;
  name: string;
}

interface PaymentDetails {
  cardPrefix: string;
  cardSuffix: string;
  cardType: PaymentInstrumentType;
}

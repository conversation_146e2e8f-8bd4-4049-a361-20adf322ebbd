import { ApiProperty } from "@nestjs/swagger";

import { LocalizedLanguage } from "../../location/dto/location.dto";

export type HailingCreateOrderResponse = object;

export class HailingItineraryStepRequest {
  @ApiProperty({ example: 0, description: "Index of the location (0: origin - 1: destination)" })
  index: number;

  @ApiProperty({ example: "ChIJE-FWMvMDBDQRBCxQzoVA-Ao", description: "Place Id" })
  placeId: string;

  @ApiProperty({ example: 114.26161142551531, description: "Longitude" })
  lng: number;

  @ApiProperty({ example: 22.306779062030994, description: "Latitude" })
  lat: number;

  @ApiProperty({ example: "Hysan Place", description: "Display Name" })
  displayName: string;

  @ApiProperty({ example: "Hysan Place, 500 Hennessy Rd, Causeway Bay", description: "Formatted Address" })
  formattedAddress: string;
}

export type HailingItineraryStepResponse = {
  index: number;
  placeId: string;
  lat: number;
  lng: number;
  i18n: {
    [key in LocalizedLanguage]?: { displayName?: string | null; formattedAddress?: string | null };
  };
};

import { GetTopicsResponse, PubSub, Topic } from "@google-cloud/pubsub";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Inject, Injectable, OnApplicationBootstrap } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { <PERSON>ron } from "@nestjs/schedule";
import { Cache } from "cache-manager";

import ClsContextStorageService from "../utils/context/clsContextStorage.service";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import genericSchemas from "../validation/dto/genericSchemas.dto";
import { ValidationService } from "../validation/validation.service";
import {
  publishMessageForCampaignTriggerProcessingParams,
  PublishMessageForCampaignTriggerProcessingParams,
} from "./dto/publishMessageForCampaignTriggerProcessingParams";
import {
  PublishMessageForCaptureProcessingParams,
  publishMessageForCaptureProcessingSchema,
} from "./dto/publishMessageForCaptureProcessingParams.dto";
import {
  PublishMessageForCopyTripToDriverProcessingParams,
  publishMessageForCopyTripToDriverProcessingSchema,
} from "./dto/publishMessageForDriverTripProcessing.dto";
import {
  PublishMessageForHailingStatusChangedParams,
  publishMessageForHailingStatusChangedParamsSchema,
} from "./dto/publishMessageForHailingStatusChangedParams.dto";
import {
  PublishMessageForHailingTxCreatedParams,
  publishMessageForHailingTxCreatedParamsSchema,
} from "./dto/publishMessageForHailingTxCreatedParams.dto";
import {
  PublishMessageForMessageProcessingParams,
  publishMessageForMessageProcessingSchema,
} from "./dto/publishMessageForMessageProcessing.dto";
import {
  PublishMessageForPickupReminderParams,
  publishMessageForPickupReminderParamsSchema,
} from "./dto/publishMessageForPickupReminderParams.dto";
import {
  PublishMessageForReportJobProcessingParams,
  publishMessageForReportJobProcessingParamsSchema,
} from "./dto/publishMessageForReportJobProcessingParams.dto";
import {
  PublishMessageForSaleProcessingParams,
  publishMessageForSaleProcessingSchema,
} from "./dto/PublishMessageForSaleProcessingParams.dto";
import {
  PublishMessageForTripEndEventParams,
  publishMessageForTripEndEventSchema,
} from "./dto/publishMessageForTripEndEvent.dto";
import {
  PublishMessageForTripProcessingParams,
  publishMessageForTripProcessingSchema,
} from "./dto/publishMessageForTripProcessing.dto";
import {
  PublishMessageForVoidProcessingParams,
  publishMessageForVoidProcessingSchema,
} from "./dto/publishMessageForVoidProcessingParams.dto";
import {
  PublishMessageForWebhookProcessing,
  publishMessageForWebhookProcessingSchema,
} from "./dto/PublishMessageForWebhookProcessing.dto";
import {
  PublishMessageForDirectSaleProcessingParams,
  publishMessageForDirectSaleProcessingParamsSchema,
} from "./dto/publishMessageToProcessSale.dto";
import { TopicNamesType, topicNameSchema } from "./dto/topicName.dto";

/**
 * PubSub service
 */
@Injectable()
export class PubSubService implements OnApplicationBootstrap {
  /**
   * PubSub instance
   */
  private readonly pubsub: PubSub;
  /**
   * Topic names
   */
  static readonly TOPIC_NAMES = {
    TRIP_PROCESSING: TopicNamesType.TRIP_PROCESSING,
    MESSAGE_PROCESSING: TopicNamesType.MESSAGE_PROCESSING,
    VOID_PROCESSING: TopicNamesType.VOID_PROCESSING,
    CAPTURE_PROCESSING: TopicNamesType.CAPTURE_PROCESSING,
    COPY_TRIP_TO_DRIVER_PROCESSING: TopicNamesType.COPY_TRIP_TO_DRIVER_PROCESSING,
    POST_SALE_PROCESSING: TopicNamesType.POST_SALE_PROCESSING,
    IDENTITY_PROCESSING: TopicNamesType.IDENTITY_PROCESSING,
    DIRECT_SALE_PROCESSING: TopicNamesType.DIRECT_SALE_PROCESSING,
    DEAD_LETTER_QUEUE: TopicNamesType.DEAD_LETTER_QUEUE,
    PUSH_NOTIFICATION_PROCESSING: TopicNamesType.PUSH_NOTIFICATION_PROCESSING,
    ADD_TX_EVENT_FOR_SYSTEM: TopicNamesType.ADD_TX_EVENT_FOR_SYSTEM,
    REPORT_JOB_PROCESSING: TopicNamesType.REPORT_JOB_PROCESSING,
    HEARTBEAT_CHANGE_PROCESSING: TopicNamesType.HEARTBEAT_CHANGE_PROCESSING,
    WEBHOOK_MESSAGE_PROCESSING: TopicNamesType.WEBHOOK_MESSAGE_PROCESSING,
    CAMPAIGN_TRIGGER_PROCESSING: TopicNamesType.CAMPAIGN_TRIGGER_PROCESSING,
    HAIL_BRONZE_TOPIC: TopicNamesType.HAIL_BRONZE_TOPIC,
    TRIP_END_EVENT: TopicNamesType.TRIP_END_EVENT,
    HAILING_TX_CREATED: TopicNamesType.HAILING_TX_CREATED,
    HAILING_STATUS_CHANGED: TopicNamesType.HAILING_STATUS_CHANGED,
    PICKUP_REMINDER: TopicNamesType.PICKUP_REMINDER,
  };

  constructor(
    private readonly configService: ConfigService,
    @Inject(ClsContextStorageService) private clsService: ClsContextStorageService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {
    this.pubsub = new PubSub({ projectId: this.configService.getOrThrow("GCLOUD_PROJECT") });
  }

  /**
   * Returns the created topic
   * @param topicName string
   * @returns The topic (Topic)
   */
  async createTopic(topicName: TopicNamesType): Promise<Topic> {
    ValidationService.validate([{ value: topicName, schema: topicNameSchema }]);

    return await this.pubsub.createTopic(topicName).then(([topic]) => topic);
  }

  /**
   * Returns the topic from the topicName
   * @param topicName string
   * @returns The topic (Topic)
   */
  async getTopic(topicName: TopicNamesType): Promise<Topic> {
    ValidationService.validate([{ value: topicName, schema: topicNameSchema }]);

    let topics = await this.cacheManager.get<GetTopicsResponse>("topics");

    if (!topics) {
      topics = await this.getAndCacheTopics();
    }
    const topic = topics.flat().find((t: any) => t.name?.includes(topicName));

    if (!topic || !(topic instanceof Topic)) {
      return await this.createTopic(topicName);
    }

    return topic;
  }

  /**
   * Sends a message to a topic
   * @param topicName TYPE_TOPIC_NAMES
   * @param message string
   * @returns The message ID (string)
   */
  async publishMessage(topicName: TopicNamesType, message: string): Promise<string> {
    ValidationService.validate([
      { value: topicName, schema: topicNameSchema },
      { value: message, schema: genericSchemas.string },
    ]);

    const topic = await this.getTopic(topicName);

    return topic
      .publishMessage({
        data: Buffer.from(message),
      })
      .then((messageId) => {
        this.logger.info("Message published in topic", { messageId, topicName, message });
        return messageId;
      })
      .catch((error) => {
        this.logger.error("Error publishing message", { error, message });
        throw error;
      });
  }

  /**
   * Publishes a message to the trip processing topic
   * @param param PublishMessageForTripProcessingParams
   */
  publishMessageForTripProcessing(message: PublishMessageForTripProcessingParams) {
    ValidationService.validate([{ value: message, schema: publishMessageForTripProcessingSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.TRIP_PROCESSING,
      JSON.stringify({
        ...message,
        ...this.clsService.getContext(),
      }),
    );
  }

  /**
   * Publishes a message to the message processing topic
   * @param param PublishMessageForMessageProcessingParams
   */
  publishMessageForMessageProcessingParams(message: PublishMessageForMessageProcessingParams) {
    ValidationService.validate([{ value: message, schema: publishMessageForMessageProcessingSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.MESSAGE_PROCESSING,
      JSON.stringify({
        ...message,
        ...this.clsService.getContext(),
      }),
    );
  }

  /**
   * Publishes a message to the capture processing topic
   * @param param PublishMessageForCaptureProcessingParams
   */
  publishMessageForCaptureProcessing(message: PublishMessageForCaptureProcessingParams) {
    ValidationService.validate([{ value: message, schema: publishMessageForCaptureProcessingSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.CAPTURE_PROCESSING,
      JSON.stringify({
        ...message,
        ...this.clsService.getContext(),
      }),
    );
  }

  /**
   * Publishes a message to the sale processing topic
   * @param param publishMessageForPostSaleProcessing
   */
  publishMessageForPostSaleProcessing(message: PublishMessageForSaleProcessingParams) {
    ValidationService.validate([{ value: message, schema: publishMessageForSaleProcessingSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.POST_SALE_PROCESSING,
      JSON.stringify({
        ...message,
        ...this.clsService.getContext(),
      }),
    );
  }

  /**
   * Publishes a message to the void processing topic
   * @param param PublishMessageForVoidProcessingParams
   */
  publishMessageForVoidProcessing(message: PublishMessageForVoidProcessingParams) {
    ValidationService.validate([{ value: message, schema: publishMessageForVoidProcessingSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.VOID_PROCESSING,
      JSON.stringify({
        ...message,
        ...this.clsService.getContext(),
      }),
    );
  }

  /**
   * Publishes a message to the copy trip to driver in firestore processing topic
   * @param param PublishMessageForCopyTripToDriverProcessingParams
   */
  publishMessageForCopyTripToDriverProcessing(message: PublishMessageForCopyTripToDriverProcessingParams) {
    ValidationService.validate([{ value: message, schema: publishMessageForCopyTripToDriverProcessingSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.COPY_TRIP_TO_DRIVER_PROCESSING,
      JSON.stringify({
        ...message,
        ...this.clsService.getContext(),
      }),
    );
  }

  /**
   * Publishes a message to process a sale
   * @param message PublishMessageForCopyTripToDriverProcessingParams
   */
  publishMessageForDirectSaleProcessing(message: PublishMessageForDirectSaleProcessingParams) {
    ValidationService.validate([{ value: message, schema: publishMessageForDirectSaleProcessingParamsSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.DIRECT_SALE_PROCESSING,
      JSON.stringify({
        ...message,
        ...this.clsService.getContext(),
      }),
    );
  }

  /**
   * Publishes a message to the process report job
   */
  publishMessageForReportJobProcessing(message: PublishMessageForReportJobProcessingParams) {
    ValidationService.validate([{ value: message, schema: publishMessageForReportJobProcessingParamsSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.REPORT_JOB_PROCESSING,
      JSON.stringify({
        ...message,
        ...this.clsService.getContext(),
      }),
    );
  }

  /**
   * Published a message to emit a webhook message
   */
  publishMessageForWebhookMessageProcessing(message: PublishMessageForWebhookProcessing) {
    ValidationService.validate([{ value: message, schema: publishMessageForWebhookProcessingSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.WEBHOOK_MESSAGE_PROCESSING,
      JSON.stringify({
        ...message,
        ...this.clsService.getContext(),
      }),
    );
  }

  publishMessageForCampaignTriggerProcessing(message: PublishMessageForCampaignTriggerProcessingParams) {
    ValidationService.validate([{ value: message, schema: publishMessageForCampaignTriggerProcessingParams }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.CAMPAIGN_TRIGGER_PROCESSING,
      JSON.stringify({
        ...message,
        ...this.clsService.getContext(),
      }),
    );
  }

  /**
   * Publish for trip end event
   */
  publishMessageForTripEndEvent(message: PublishMessageForTripEndEventParams) {
    ValidationService.validate([{ value: message, schema: publishMessageForTripEndEventSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.TRIP_END_EVENT,
      JSON.stringify({
        ...message,
        ...this.clsService.getContext(),
      }),
    );
  }

  publishMessageForHailingTxCreated(params: PublishMessageForHailingTxCreatedParams) {
    ValidationService.validate([{ value: params, schema: publishMessageForHailingTxCreatedParamsSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.HAILING_TX_CREATED,
      JSON.stringify({
        ...params,
        correlationId: this.clsService.getContextId(),
      }),
    );
  }

  publishMessageForHailingStatusChanged(params: PublishMessageForHailingStatusChangedParams) {
    ValidationService.validate([{ value: params, schema: publishMessageForHailingStatusChangedParamsSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.HAILING_STATUS_CHANGED,
      JSON.stringify({
        ...params,
        correlationId: this.clsService.getContextId(),
      }),
    );
  }

  publishMessageForPickupReminder(params: PublishMessageForPickupReminderParams) {
    ValidationService.validate([{ value: params, schema: publishMessageForPickupReminderParamsSchema }]);
    return this.publishMessage(
      PubSubService.TOPIC_NAMES.PICKUP_REMINDER,
      JSON.stringify({
        ...params,
        correlationId: this.clsService.getContextId(),
      }),
    );
  }

  async onApplicationBootstrap() {
    await this.getAndCacheTopics();
  }

  //get topics every minute, and save it to cache using cache-manager
  @Cron("0 * * * * *", { name: "getTopics" })
  async scheduleGetTopics() {
    await this.getAndCacheTopics();
  }

  async getAndCacheTopics(): Promise<GetTopicsResponse> {
    const topics = await this.pubsub.getTopics();
    await this.cacheManager.set("topics", topics, 90000);
    return topics;
  }
}

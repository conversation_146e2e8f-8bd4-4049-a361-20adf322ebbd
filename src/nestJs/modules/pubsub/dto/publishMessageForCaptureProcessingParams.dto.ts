import Joi from "joi";

import { appSchema } from "../../apps/dto/Apps.dto";
import PaymentTx from "../../database/entities/paymentTx.entity";
import Tx from "../../database/entities/tx.entity";
import TxTag from "../../database/entities/txTag.entity";
import { merchantSchema } from "../../merchant/dto/merchant.dto";
import { PaymentGatewayResponse } from "../../payment/dto/paymentGatewayResponses.model";
import { paymentGatewayTypesSchema } from "../../payment/dto/paymentGatewayTypes.dto";
import { paymentInformationStatusSchema } from "../../payment/dto/paymentInformationStatus.dto";
import { paymentInformationTypeSchema } from "../../payment/dto/paymentInformationType.dto";
import { txMetadataSchema } from "../../transaction/dto/txMetadata.dto";
import { txPayoutStatusSchema } from "../../transaction/dto/txPayoutStatus.dto";
import { tagTypeSchema } from "../../transaction/dto/txTagType.dto";
import { txTypesSchema } from "../../transaction/dto/txType.dto";

/**
 * Params for publish message for capture-processing
 */
export interface PublishMessageForCaptureProcessingParams {
  tx: Tx;
}

export const baseTxSchemaForPublishMessage = Joi.object<Tx>({
  txApp: appSchema.required(),
  createdAt: Joi.date().optional(),
  updatedAt: Joi.date().optional(),
  id: Joi.string().required(),
  type: txTypesSchema.optional(),
  metadata: txMetadataSchema.unknown(true).optional(),
  payoutStatus: txPayoutStatusSchema.allow(null).optional(),
  total: Joi.number().allow(null).optional(),
  adjustment: Joi.number().allow(null).optional(),
  dashFee: Joi.number().allow(null).optional(),
  payoutAmount: Joi.number().allow(null).optional(),
  discount: Joi.number().negative().allow(null, 0).optional(),
  bonus: Joi.number().positive().allow(null, 0).optional(),
  createdBy: Joi.string().allow(null).optional(),
}).unknown(true);

export const basePaymentTxSchemaForPublishMessage = Joi.object<PaymentTx>({
  createdAt: Joi.date().required(),
  updatedAt: Joi.date().optional(),
  id: Joi.string().required(),
  amount: Joi.number().allow(null).optional(),
  gatewayTransactionId: Joi.string().allow(null, "").optional(),
  cardNumber: Joi.string().allow(null).optional(),
  paymentMethod: Joi.string().allow(null).optional(),
  tx: Joi.object().optional(),
  gateway: paymentGatewayTypesSchema.required(),
  gatewayResponse: Joi.object<PaymentGatewayResponse>().optional(),
  status: paymentInformationStatusSchema.optional(),
  type: paymentInformationTypeSchema.optional(),
  requestedBy: Joi.string().allow(null).optional(),
  paymentInstrument: Joi.object().allow(null).optional(),
}).unknown(true);

export const baseTxTagSchemaForPublishMessage = Joi.object<TxTag>({
  createdAt: Joi.date().required(),
  updatedAt: Joi.date().optional(),
  id: Joi.number().required(),
  tx: Joi.object().optional(),
  tag: tagTypeSchema.required(),
  createdBy: Joi.string().required(),
  note: Joi.string().allow(null).optional(),
  removedAt: Joi.date().allow(null).optional(),
}).unknown(true);

/**
 * Joi Publish message for capture-processing schema
 */
export const publishMessageForCaptureProcessingSchema = Joi.object<PublishMessageForCaptureProcessingParams>({
  tx: baseTxSchemaForPublishMessage
    .keys({
      paymentTx: Joi.array()
        .items(
          basePaymentTxSchemaForPublishMessage.keys({
            parent: basePaymentTxSchemaForPublishMessage.allow(null).optional(),
          }),
        )
        .optional(),
      txTag: Joi.array().items(baseTxTagSchemaForPublishMessage).optional(),
      merchant: merchantSchema.keys({ txs: Joi.array().items(Joi.object().optional()).optional() }).optional(),
      payoutMerchant: merchantSchema.keys({ txs: Joi.array().items(Joi.object().optional()).optional() }).optional(),
      user: Joi.object().optional(),
    })
    .required(),
}).unknown(true);

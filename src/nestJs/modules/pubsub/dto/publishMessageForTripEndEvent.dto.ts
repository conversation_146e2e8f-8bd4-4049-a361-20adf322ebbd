import Joi from "joi";

/**
 * Params for publish message for trip end event, send only the txId
 */
export interface PublishMessageForTripEndEventParams {
  txId: string;
}

/**
 * Joi Publish message for trip processing schema
 * @returns Joi.ObjectSchema<PublishMessageForTripProcessingParams>
 */
export const publishMessageForTripEndEventSchema = Joi.object<PublishMessageForTripEndEventParams>({
  txId: Joi.string().required(),
});

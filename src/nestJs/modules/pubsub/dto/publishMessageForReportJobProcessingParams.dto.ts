import Joi from "joi";

import {
  QueryMetadata,
  queryMetadataSchema,
  ReportJobMerchantMetadata,
  reportJobMerchantMetadataSchema,
  ReportType,
  reportTypeSchema,
} from "@nest/modules/reportJob/dto/reportJob.dto";

/**
 * Params for publish message to process report job
 */
export interface PublishMessageForReportJobProcessingParams {
  name: string;
  lastRunAt?: Date;
  nowRunAt: Date;
  reportType: ReportType;
  destination: string;
  queryMetadata?: QueryMetadata;
  queryString?: string;
  merchantMetadata: ReportJobMerchantMetadata;
}

/**
 * Joi Publish message to process report job schema
 * @returns Joi.ObjectSchema<PublishMessageForReportJobProcessingParams>
 */
export const publishMessageForReportJobProcessingParamsSchema = Joi.object<PublishMessageForReportJobProcessingParams>({
  name: Joi.string().required(),
  lastRunAt: Joi.date().allow(null).optional(),
  nowRunAt: Joi.date().required(),
  reportType: reportTypeSchema.required(),
  destination: Joi.string().required(),
  queryMetadata: queryMetadataSchema.allow(null).optional(),
  queryString: Joi.string().allow(null).optional(),
  merchantMetadata: reportJobMerchantMetadataSchema.required(),
});

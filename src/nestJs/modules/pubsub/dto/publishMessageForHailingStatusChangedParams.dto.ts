import Joi from "joi";

/**
 * Params for publish message for hailing status changed
 */
export interface PublishMessageForHailingStatusChangedParams {
  txId: string;
  status: string;
}

/**
 * Joi Publish message for hailing status changed schema
 */
export const publishMessageForHailingStatusChangedParamsSchema =
  Joi.object<PublishMessageForHailingStatusChangedParams>({
    txId: Joi.string().required(),
    status: Joi.string().required(),
  });

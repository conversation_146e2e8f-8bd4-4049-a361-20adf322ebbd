import Joi from "joi";

export interface PublishMessageForCampaignTriggerProcessingParams {
  eventTrigger: string;
  userId: string;
  generateToLimit?: boolean;
}

export const publishMessageForCampaignTriggerProcessingParams =
  Joi.object<PublishMessageForCampaignTriggerProcessingParams>({
    eventTrigger: Joi.string().required(),
    userId: Joi.string().required(),
    generateToLimit: Joi.boolean().default(false),
  }).unknown(true);

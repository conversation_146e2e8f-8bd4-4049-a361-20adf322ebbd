import Joi from "joi";

import { txIdSchema } from "../../transaction/dto/tx.dto";

/**
 * Params for publish message for driverTrip processing
 */
export interface PublishMessageForCopyTripToDriverProcessingParams {
  txId?: string;
  expiresAt?: Date;
  txIds?: string[];
}

/**
 * Joi Publish message for driverTrip processing schema
 * @returns Joi.ObjectSchema<PublishMessageForDriverTripProcessingParams>
 */
export const publishMessageForCopyTripToDriverProcessingSchema =
  Joi.object<PublishMessageForCopyTripToDriverProcessingParams>({
    txId: txIdSchema.optional(),
    expiresAt: Joi.date().optional(),
    txIds: Joi.array().items(txIdSchema).optional(),
  });

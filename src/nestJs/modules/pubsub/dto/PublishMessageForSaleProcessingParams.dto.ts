import Joi from "joi";

import {
  baseTxSchemaForPublishMessage,
  basePaymentTxSchemaForPublishMessage,
  baseTxTagSchemaForPublishMessage,
} from "./publishMessageForCaptureProcessingParams.dto";
import Tx from "../../database/entities/tx.entity";
import { merchantSchema } from "../../merchant/dto/merchant.dto";

export interface PublishMessageForSaleProcessingParams {
  tx: Tx;
}

/**
 * Joi Publish message for sale-processing schema
 */
export const publishMessageForSaleProcessingSchema = Joi.object<PublishMessageForSaleProcessingParams>({
  tx: baseTxSchemaForPublishMessage
    .keys({
      paymentTx: Joi.array()
        .items(
          basePaymentTxSchemaForPublishMessage.keys({
            parent: basePaymentTxSchemaForPublishMessage.allow(null).optional(),
          }),
        )
        .optional(),
      txTag: Joi.array().items(baseTxTagSchemaForPublishMessage).optional(),
      merchant: merchantSchema.keys({ txs: Joi.array().items(Joi.object().optional()).optional() }).optional(),
      payoutMerchant: merchantSchema.keys({ txs: Joi.array().items(Joi.object().optional()).optional() }).optional(),
      user: Joi.object().optional(),
    })
    .required(),
});

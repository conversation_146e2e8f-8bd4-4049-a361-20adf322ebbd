import Joi from "joi";

import { addEventBodySchemaSystem, AddEventBodySystem } from "../../system/modules/systemTransaction/dto/addEvent.dto";

/**
 * Params for publish message for tx event system
 */
export type PublishMessageForTxEventSystemParams = {
  content: AddEventBodySystem;
  transactionId: string;
};

/**
 * Joi Publish message for tx event system schema
 */
export const publishMessageForTxEventSystemParamsSchema = Joi.object<PublishMessageForTxEventSystemParams>({
  content: addEventBodySchemaSystem.required(),
  transactionId: Joi.string().required(),
});

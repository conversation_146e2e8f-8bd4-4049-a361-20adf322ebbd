import Joi from "joi";

export enum WebhookEventType {
  METER_HEARTBEAT = "meter.heartbeat",
  METER_SESSION_START = "meter.session.start",
  METER_SESSION_END = "meter.session.end",
}

type WebhookBaseMessage = {
  id: string;
};

type WebhookMeterHeartbeatMessage = WebhookBaseMessage & {
  event: WebhookEventType.METER_HEARTBEAT;
  data: {
    createdAt: string;
    heartbeatId: string;
    licensePlate: string;
    location: {
      lat: number;
      lng: number;
    };
    bearing?: number;
    speed?: number;
  };
};

type MeterSessionData = {
  createdAt: string;
  sessionId: string;
  licensePlate: string;
  driverPhoneNumber: string;
  driverId: string;
};

type WebhookMeterSessionStartMessage = WebhookBaseMessage & {
  event: WebhookEventType.METER_SESSION_START;
  data: MeterSessionData;
};

type WebhookMeterSessionEndMessage = WebhookBaseMessage & {
  event: WebhookEventType.METER_SESSION_END;
  data: MeterSessionData;
};

export type WebhookMessage =
  | WebhookMeterHeartbeatMessage
  | WebhookMeterSessionStartMessage
  | WebhookMeterSessionEndMessage;

/**
 * Webhook message payload
 */
export type PublishMessageForWebhookProcessing = {
  webhookId: string;
  message: WebhookMessage;
  correlationId?: string;
};

export const meterSessionDataSchema = Joi.object<MeterSessionData>({
  createdAt: Joi.string().required(),
  sessionId: Joi.string().required(),
  licensePlate: Joi.string().required(),
  driverPhoneNumber: Joi.string().required(),
  driverId: Joi.string().required(),
}).required();

export const webhookMessageSchema = Joi.object<WebhookMessage>({
  event: Joi.string()
    .valid(...Object.values(WebhookEventType))
    .required(),
  id: Joi.string().required(),
  data: Joi.alternatives().conditional("event", {
    switch: [
      {
        is: WebhookEventType.METER_HEARTBEAT,
        then: Joi.object<WebhookMeterHeartbeatMessage["data"]>({
          createdAt: Joi.string().required(),
          heartbeatId: Joi.string().required(),
          licensePlate: Joi.string().required(),
          location: Joi.object({
            lat: Joi.number().required(),
            lng: Joi.number().required(),
          }).required(),
          bearing: Joi.number(),
          speed: Joi.number(),
        }).required(),
      },
      {
        is: WebhookEventType.METER_SESSION_START,
        then: meterSessionDataSchema,
      },
      { is: WebhookEventType.METER_SESSION_END, then: meterSessionDataSchema },
    ],
  }),
}).required();

export const publishMessageForWebhookProcessingSchema = Joi.object<PublishMessageForWebhookProcessing>({
  webhookId: Joi.string().required(),
  message: webhookMessageSchema,
  correlationId: Joi.string(),
});

import Joi from "joi";

export type Timestamp = {
  _seconds: number;
  _nanoseconds: number;
};

export type GeoPoint = {
  _latitude: number;
  _longitude: number;
};

export type HeartbeatPayload = {
  server_time: Timestamp;
  bearing?: number;
  location: GeoPoint;
  id: string;
  gps_type?: string;
  speed?: number;
  device_time: Timestamp;
};

/**
 * Heartbeat event payload
 */
export type PublishMessageForHeartbeatProcessing = {
  /**
   * The firestore document path
   *  @example "meters/A000/heartbeat/bbd8799e-b1c4-4a84-a2f0-6c7c91cd6b42"
   * */
  document: string;
  after: HeartbeatPayload;
};

export const heartbeatPayloadSchema = Joi.object<HeartbeatPayload>({
  server_time: Joi.object({
    _seconds: Joi.number().required(),
    _nanoseconds: Joi.number().required(),
  }).required(),
  bearing: Joi.number(),
  location: Joi.object({
    _latitude: Joi.number().required(),
    _longitude: Joi.number().required(),
  }).required(),
  id: Joi.string().required(),
  gps_type: Joi.string(),
  speed: Joi.number(),
  device_time: Joi.object({
    _seconds: Joi.number().required(),
    _nanoseconds: Joi.number().required(),
  }).required(),
}).unknown(true);

export const publishMessageForHeartbeatProcessingSchema = Joi.object<PublishMessageForHeartbeatProcessing>({
  document: Joi.string().required(),
  after: heartbeatPayloadSchema.required(),
});

import Joi from "joi";

/**
 * Params for publish message to process sale
 */
export interface PublishMessageForDirectSaleProcessingParams {
  txId: string;
  paymentInstrumentId: string;
  overwriteAmount?: number;
}

/**
 * Joi Publish message to process sale schema
 * @returns Joi.ObjectSchema<PublishMessageToProcessSaleParams>
 */
export const publishMessageForDirectSaleProcessingParamsSchema =
  Joi.object<PublishMessageForDirectSaleProcessingParams>({
    txId: Joi.string().required(),
    paymentInstrumentId: Joi.string().required(),
    overwriteAmount: Joi.number().precision(1).positive().optional(),
  });

import Jo<PERSON> from "joi";

import { appSchema } from "../../apps/dto/Apps.dto";
import TxApp from "../../database/entities/app.entity";
import { PaymentGatewayResponse } from "../../payment/dto/paymentGatewayResponses.model";
import { paymentGatewayTypesSchema } from "../../payment/dto/paymentGatewayTypes.dto";
import { paymentInformationStatusSchema } from "../../payment/dto/paymentInformationStatus.dto";
import { paymentInformationTypeSchema } from "../../payment/dto/paymentInformationType.dto";
import PaymentTxFromDocument from "../../payment/dto/paymentTxFromDocument.model";
import { TxTypes, txTypesSchema } from "../../transaction/dto/txType.dto";
import { merchantSchema } from "../../validation/dto/merchant.dto";

/**
 * Params for publish message for trip processing
 */
export interface PublishMessageForTripProcessingParams {
  tx: PublishMessageForTripProcessingTxParams;
  merchant?: {
    phoneNumber: string;
    name?: string;
  };
  paymentTx: PaymentTxFromDocument[];
  txProcessed?: boolean;
  doVoid?: boolean;
  isDirectSale?: boolean;
  isAfterTxEnd?: boolean;
  isCaptureInApp?: boolean;
  settlementFleetId?: string;
  isTripEnd?: boolean;
}

interface PublishMessageForTripProcessingTxParams {
  txApp: TxApp;
  type: TxTypes.TRIP;
  id: string;
  data: any;
}

/**
 * Joi Publish message for trip processing schema
 * @returns Joi.ObjectSchema<PublishMessageForTripProcessingParams>
 */
export const publishMessageForTripProcessingSchema = Joi.object<PublishMessageForTripProcessingParams>({
  tx: Joi.object<PublishMessageForTripProcessingTxParams>({
    txApp: appSchema.required(),
    type: txTypesSchema.required(),
    id: Joi.string().required(),
    data: Joi.object().required(),
  }).required(),
  merchant: merchantSchema.optional(),
  paymentTx: Joi.array()
    .items(
      Joi.object<PaymentTxFromDocument>({
        id: Joi.string().required(),
        gateway: paymentGatewayTypesSchema.required(),
        createdAt: Joi.date().required(),
        type: paymentInformationTypeSchema.required(),
        gatewayResponse: Joi.object<PaymentGatewayResponse>().optional(),
        status: paymentInformationStatusSchema.optional(),
        amount: Joi.number().optional(),
        gatewayTransactionId: Joi.string().allow(null, "").optional(),
        cardNumber: Joi.string().optional(),
        paymentMethod: Joi.string().optional(),
      }).unknown(),
    )
    .required(),
  txProcessed: Joi.boolean().required(),
  doVoid: Joi.boolean().required(),
  isDirectSale: Joi.boolean().required(),
  isAfterTxEnd: Joi.boolean().required(),
  isCaptureInApp: Joi.boolean().required(),
  settlementFleetId: Joi.string().optional(),
  isTripEnd: Joi.boolean().optional(),
});

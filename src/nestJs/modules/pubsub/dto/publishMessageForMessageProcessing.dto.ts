import <PERSON><PERSON> from "joi";

import { ChannelTypes } from "../../message/dto/channelType.dto";
import { MessageMetadata, messageMetadataSchema } from "../../message/dto/messageMetadata.dto";
import { MessageParams, messageParamsSchema } from "../../message/dto/messageParams.dto";
import { PhoneRecipientType, recipientTypeSchema } from "../../message/dto/recipientType.dto";
import { TemplateTypesText, templateTypesTextSchema } from "../../message/dto/templateType.dto";
import {
  NotificationRecipientType,
  NotificationType,
  notificationRecipientTypeSchema,
  notificationTypeSchema,
} from "../../message/messageFactory/modules/notification/notification.dto";
import { LanguageOption, languageOptionSchema } from "../../validation/dto/language.dto";

/**
 * Params for publish message for message-processing
 */
export interface PublishMessageForMessageProcessingParamsImplementation {
  metadata: MessageMetadata;
  recipient: any;
  tranId: string;
  channel: ChannelTypes;
  language: LanguageOption;
  params: MessageParams;
  messageId: string;
  template: any;
}

/**
 * Base class for publish message for message-processing
 */
export class PublishMessageForMessageProcessingParamsBase {
  metadata: MessageMetadata;
  tranId: string;
  language: LanguageOption;
  params: MessageParams;
  messageId: string;
}

/***********************************************************
 * WhatsApp
 ***********************************************************/
/**
 * WhatsApp Publish message for message-processing class
 */
export class PublishMessageForMessageProcessingParamsWhatsApp
  extends PublishMessageForMessageProcessingParamsBase
  implements PublishMessageForMessageProcessingParamsImplementation
{
  recipient: PhoneRecipientType;
  channel: ChannelTypes.WHATSAPP;
  template: TemplateTypesText;
}

/**
 * Joi WhatsApp Publish message for message-processing schema
 */
export const publishMessageForMessageProcessingParamsWhatsAppSchema =
  Joi.object<PublishMessageForMessageProcessingParamsWhatsApp>({
    metadata: messageMetadataSchema.required(),
    recipient: recipientTypeSchema.required(),
    tranId: Joi.string().required(),
    channel: Joi.string().valid(ChannelTypes.WHATSAPP).required(),
    language: languageOptionSchema.required(),
    params: messageParamsSchema.required(),
    messageId: Joi.string().required(),
    template: templateTypesTextSchema.required(),
  });

/***********************************************************
 * SMS
 ***********************************************************/
/**
 * SMS Publish message for message-processing class
 */
export class PublishMessageForMessageProcessingParamsSms
  extends PublishMessageForMessageProcessingParamsBase
  implements PublishMessageForMessageProcessingParamsImplementation
{
  recipient: PhoneRecipientType;
  channel: ChannelTypes.SMS;
  template: TemplateTypesText;
}

/**
 * Joi SMS Publish message for message-processing schema
 */
export const publishMessageForMessageProcessingParamsSmsSchema =
  Joi.object<PublishMessageForMessageProcessingParamsSms>({
    metadata: messageMetadataSchema.required(),
    recipient: recipientTypeSchema.required(),
    tranId: Joi.string().required(),
    channel: Joi.string().valid(ChannelTypes.SMS).required(),
    language: languageOptionSchema.required(),
    params: messageParamsSchema.required(),
    messageId: Joi.string().required(),
    template: templateTypesTextSchema.required(),
  });

/***********************************************************
 * Notifications
 ***********************************************************/
/**
 * Notifications Publish message for message-processing class
 */
export class PublishMessageForMessageProcessingParamsNotification
  extends PublishMessageForMessageProcessingParamsBase
  implements PublishMessageForMessageProcessingParamsImplementation
{
  recipient: NotificationRecipientType;
  channel: ChannelTypes.NOTIFICATION;
  template: NotificationType;
}

/**
 * Joi Notifications Publish message for message-processing schema
 */
export const publishMessageForMessageProcessingParamsNotificationSchema =
  Joi.object<PublishMessageForMessageProcessingParamsNotification>({
    metadata: messageMetadataSchema.required(),
    recipient: notificationRecipientTypeSchema.required(),
    tranId: Joi.string().required(),
    channel: Joi.string().valid(ChannelTypes.NOTIFICATION).required(),
    language: languageOptionSchema.required(),
    params: messageParamsSchema.required(),
    messageId: Joi.string().required(),
    template: notificationTypeSchema.required(),
  });

/**
 * Publish message for message-processing type
 */
export type PublishMessageForMessageProcessingParams =
  | PublishMessageForMessageProcessingParamsWhatsApp
  | PublishMessageForMessageProcessingParamsSms
  | PublishMessageForMessageProcessingParamsNotification;

/**
 * Joi Publish message for message-processing schema
 */
export const publishMessageForMessageProcessingSchema = Joi.alternatives([
  publishMessageForMessageProcessingParamsWhatsAppSchema,
  publishMessageForMessageProcessingParamsSmsSchema,
  publishMessageForMessageProcessingParamsNotificationSchema,
]);

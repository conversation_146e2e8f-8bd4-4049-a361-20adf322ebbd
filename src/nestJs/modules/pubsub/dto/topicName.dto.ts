import Jo<PERSON> from "joi";

/**
 * Topic names enum
 */
export enum TopicNamesType {
  TRIP_PROCESSING = "trip-processing",
  MESSAGE_PROCESSING = "message-processing",
  VOID_PROCESSING = "void-processing",
  CAPTURE_PROCESSING = "capture-processing",
  COPY_TRIP_TO_DRIVER_PROCESSING = "copy-trip-to-driver-processing",
  POST_SALE_PROCESSING = "post-sale-processing",
  IDENTITY_PROCESSING = "identity-event",
  DIRECT_SALE_PROCESSING = "direct-sale-processing",
  DEAD_LETTER_QUEUE = "dead-letter-queue",
  PUSH_NOTIFICATION_PROCESSING = "push-notification-processing",
  ADD_TX_EVENT_FOR_SYSTEM = "add-tx-event-for-system",
  REPORT_JOB_PROCESSING = "report-job-processing",
  HEARTBEAT_CHANGE_PROCESSING = "heartbeat-change-processing",
  WEBHO<PERSON>_MESSAGE_PROCESSING = "webhook-message-processing",
  CAMPAIGN_TRIGGER_PROCESSING = "campaign-trigger-processing",
  HAIL_BRONZE_TOPIC = "hail-bronze-topic",
  TRIP_END_EVENT = "trip-end-event",
  HAILING_TX_CREATED = "hailing-tx-created",
  HAILING_STATUS_CHANGED = "hailing-status-changed",
  PICKUP_REMINDER = "pickup-reminder",
}

/**
 * Topic names schema
 */
export const topicNameSchema = Joi.string().valid(...Object.values(TopicNamesType));

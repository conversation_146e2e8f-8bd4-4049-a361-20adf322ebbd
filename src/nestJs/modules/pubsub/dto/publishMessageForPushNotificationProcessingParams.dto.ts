import Joi from "joi";

import {
  NotificationRecipientType,
  notificationRecipientTypeSchema,
  NotificationTriggerEventType,
  notificationTriggerEventTypeSchema,
} from "../../fcm/types";

export interface PublishMessageForPushNotificationProcessingParams {
  event: NotificationTriggerEventType;
  recipientType: NotificationRecipientType;
  recipientId: string;
}

export const publishMessageForIdentityProcessingSchema = Joi.object<PublishMessageForPushNotificationProcessingParams>({
  event: notificationTriggerEventTypeSchema.required(),
  recipientType: notificationRecipientTypeSchema.required(),
  recipientId: Joi.string().uuid().required(),
});

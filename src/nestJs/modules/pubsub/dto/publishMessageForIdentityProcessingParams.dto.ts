import Joi from "joi";

import { IdentityEventMethodNameType, identityEventMethodNameTypeSchema } from "../../identity/dto/methodNameType.dto";
import { IdentityResponse, identityResponseSchema } from "../../identity/dto/response.dto";

export interface PublishMessageForIdentityProcessingParams {
  insertId: string;
  jsonPayload: {
    methodName: IdentityEventMethodNameType;
    response?: IdentityResponse;
    requestMetadata: {
      callerIp: string;
      callerSuppliedUserAgent: string;
    };
  };
}

export const publishMessageForIdentityProcessingSchema = Joi.object<PublishMessageForIdentityProcessingParams>({
  insertId: Joi.string().required(),
  jsonPayload: Joi.object({
    methodName: identityEventMethodNameTypeSchema.required(),
    response: identityResponseSchema.required(),
    requestMatadata: Joi.object({
      callerIp: Joi.string().required(),
      callerSuppliedUserAgent: Joi.string().required(),
    }).required(),
  }).required(),
});

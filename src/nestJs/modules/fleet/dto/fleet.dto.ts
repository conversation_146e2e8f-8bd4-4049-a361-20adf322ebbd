import Joi from "joi";

export enum VehicleClass {
  STANDARD = "STANDARD",
  COMFORT = "COMFORT",
  LUXURY = "LUXURY",
  FOUR_SEATER = "FOUR_SEATER",
  FIVE_SEATER = "FIVE_SEATER",
}
export const vehicleClassSchema = Joi.string().valid(...Object.values(VehicleClass));

export type FleetId = string;

export type FleetVehicleClass = {
  [key in VehicleClass]?: FleetId[];
};
export const fleetVehicleClassSchema = Joi.object<FleetVehicleClass>().pattern(
  vehicleClassSchema.required(),
  Joi.array().items(Joi.string()).required(),
);

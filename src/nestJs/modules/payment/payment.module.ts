import { MiddlewareConsumer, Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { PaymentController } from "./payment.controller";
import { PaymentService } from "./payment.service";
import { PaymentFactoryModule } from "./paymentFactory/paymentFactory.module";
import { MeterDeviceAuthMiddleware } from "../../infrastructure/middlewares/meterDeviceAuth.middleware";
import { AppDatabaseModule } from "../appDatabase/appDatabase.module";
import { CampaignModule } from "../campaign/campaign.module";
import { DiscountRepository } from "../database/repositories/discount.repository";
import { PaymentInstrumentRepository } from "../database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../database/repositories/paymentTx.repository";
import { TxRepository } from "../database/repositories/tx.repository";
import { TxTagRepository } from "../database/repositories/txTag.repository";
import { PubSubService } from "../pubsub/pubsub.service";
import { StorageModule } from "../storage/storage.module";
import { TransactionFactoryModule } from "../transaction/transactionFactory/transactionFactory.module";

/**
 * Payment module
 */
@Module({
  providers: [
    PaymentService,
    PaymentController,
    PubSubService,
    TxTagRepository,
    TxRepository,
    PaymentTxRepository,
    PaymentInstrumentRepository,
    DiscountRepository,
    AppDatabaseModule,
  ],
  imports: [
    PaymentFactoryModule,
    StorageModule,
    TransactionFactoryModule,
    ConfigModule,
    CampaignModule,
    AppDatabaseModule,
  ],
  controllers: [PaymentController],
  exports: [PaymentService, PaymentController],
})
export class PaymentModule {
  /**
   * Configure middleware
   * @param consumer MiddlewareConsumer
   */
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(MeterDeviceAuthMiddleware).forRoutes("/payments");
  }
}

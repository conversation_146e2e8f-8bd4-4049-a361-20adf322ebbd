import { Module } from "@nestjs/common";

import { GlobalPaymentPaymentModule } from "./modules/globalPayment/globalPaymentPayment.module";
import { ManualModule } from "./modules/manual/manual.module";
import { SoepayModule } from "./modules/soepay/soepay.module";
import { PaymentFactoryService } from "./paymentFactory.service";
import { KrakenModule } from "../modules/paymentInstrument/modules/kraken/kraken.module";

/**
 * PaymentFactory module
 */
@Module({
  imports: [SoepayModule, ManualModule, GlobalPaymentPaymentModule, KrakenModule],
  providers: [PaymentFactoryService],
  exports: [PaymentFactoryService],
})
export class PaymentFactoryModule {}

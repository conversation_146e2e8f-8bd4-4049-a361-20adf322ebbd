import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { SoepayService } from "./soepay.service";
import { PaymentTxRepository } from "../../../../database/repositories/paymentTx.repository";

/**
 * Soepay module
 */
@Module({
  imports: [ConfigModule, HttpModule],
  providers: [SoepayService, PaymentTxRepository],
  exports: [SoepayService],
})
export class SoepayModule {}

import { randomUUID } from "crypto";

import { HttpService } from "@nestjs/axios";
import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectRepository } from "@nestjs/typeorm";
import axios, { AxiosError, isAxiosError } from "axios";
import axiosRetry, { exponentialDelay } from "axios-retry";
import CryptoJS from "crypto-js";
import { catchError, firstValueFrom } from "rxjs";

import PaymentInstrument from "../../../../database/entities/paymentInstrument.entity";
import PaymentTx from "../../../../database/entities/paymentTx.entity";
import Tx from "../../../../database/entities/tx.entity";
import { PaymentTxRepository } from "../../../../database/repositories/paymentTx.repository";
import LoggerServiceAdapter from "../../../../utils/logger/logger.service";
import { errorBuilder } from "../../../../utils/utils/error.utils";
import { EnquiryResponse, PaymentGatewayResponse } from "../../../dto/paymentGatewayResponses.model";
import { PaymentGatewayTypes } from "../../../dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../../dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../../dto/paymentInformationType.dto";
import PaymentTxFromDocument from "../../../dto/paymentTxFromDocument.model";
import { PaymentTxPaymentMethod } from "../../../dto/paymentTxPaymentMethod.dto";
import paymentFactoryInterface from "../../paymentFactoryInterface";
import {
  SoePayCaptureResponse,
  SoePayEnquiryPaymentStatus,
  SoePayEnquiryResponse,
  SoePayRefundResponse,
  SoePayTranStatus,
  SoePayVoidResponse,
  isSoePayAuthFailure,
  isSoePayAuthSuccess,
  isSoePayCaptureFailure,
  isSoePayCaptureSuccess,
  isSoePayEnquirySuccess,
  isSoePayRefundSuccess,
  isSoePayResponse,
  isSoePaySale,
  isSoePaySaleFailure,
  isSoePaySaleSuccess,
  isSoePayVoidFailure,
  isSoePayVoidSuccess,
} from "./dto/soepay.model";
/**
 * SoePay service
 */
@Injectable()
export class SoepayService implements paymentFactoryInterface {
  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    @InjectRepository(PaymentTxRepository) private paymentTxRepository: PaymentTxRepository,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  /**
   * Extract payment transaction information from document
   * @param document
   * @returns PaymentTxFromDocument
   */
  readonly extractPaymentTxInfoFromDocument = (
    document: PaymentGatewayResponse,
    options?: any,
  ): PaymentTxFromDocument => {
    if (!isSoePayResponse(document)) {
      if (options && options.createdAt && options.type && options.transactionId) {
        return {
          id: options.transactionId,
          gateway: PaymentGatewayTypes.SOEPAY,
          gatewayResponse: document,
          createdAt: options.createdAt ?? new Date(),
          type: PaymentInformationType[options.type as PaymentInformationType],
          status: PaymentInformationStatus.FAILURE,
        };
      }
      throw errorBuilder.payment.soePay.invalidResponse(document);
    }

    const common = {
      gateway: PaymentGatewayTypes.SOEPAY,
      gatewayResponse: document,
      createdAt: options?.createdAt ?? new Date(),
    };

    if (isSoePayCaptureSuccess(document)) {
      return {
        id: document.data.messageId,
        amount: document.data.totalAmount,
        gatewayTransactionId: document.data.tranId,
        cardNumber: `${document.data.payment.payerFirst}******${document.data.payment.payerLast}`,
        paymentMethod: document.data.payment.paymentMethod,
        status: PaymentInformationStatus.SUCCESS,
        type: PaymentInformationType.CAPTURE,
        ...common,
        createdAt: new Date(document.data.createTime),
      };
    }
    if (isSoePayCaptureFailure(document)) {
      return {
        id: randomUUID(),
        status: PaymentInformationStatus.FAILURE,
        type: PaymentInformationType.CAPTURE,
        ...common,
      };
    }
    if (isSoePayAuthSuccess(document)) {
      return {
        id: document.messageId,
        amount: document.baseAmount,
        gatewayTransactionId: document.tranId,
        cardNumber: `${document.payment.payerFirst}******${document.payment.payerLast}`,
        paymentMethod: document.payment.paymentMethod,
        status: PaymentInformationStatus.SUCCESS,
        type: PaymentInformationType.AUTH,
        ...common,
        createdAt: new Date(document.createTime),
      };
    }
    if (isSoePayVoidSuccess(document)) {
      return {
        id: document.messageId,
        amount: document.baseAmount,
        gatewayTransactionId: document.tranId,
        cardNumber: `${document.data.payment.payerFirst}******${document.data.payment.payerLast}`,
        paymentMethod: document.data.payment.paymentMethod,
        status: PaymentInformationStatus.SUCCESS,
        type: PaymentInformationType.VOID,
        ...common,
        createdAt: new Date(document.data.createTime),
      };
    }
    if (isSoePayAuthFailure(document)) {
      return {
        id: document.messageId,
        amount: document.baseAmount,
        gatewayTransactionId: document.tranId,
        status: PaymentInformationStatus.FAILURE,
        type: PaymentInformationType.AUTH,
        ...common,
      };
    }
    if (isSoePayVoidFailure(document)) {
      return {
        id: randomUUID(),
        status: PaymentInformationStatus.FAILURE,
        type: PaymentInformationType.VOID,
        ...common,
      };
    }
    if (isSoePaySaleSuccess(document)) {
      return {
        id: document.messageId,
        amount: document.baseAmount,
        gatewayTransactionId: document.tranId,
        cardNumber: `${document.payment.payerFirst}******${document.payment.payerLast}`,
        paymentMethod: document.payment.paymentMethod,
        status: PaymentInformationStatus.SUCCESS,
        type: PaymentInformationType.SALE,
        ...common,
        createdAt: new Date(document.createTime),
      };
    }
    if (isSoePaySaleFailure(document)) {
      return {
        id: document.messageId,
        amount: document.baseAmount,
        gatewayTransactionId: document.tranId,
        status: PaymentInformationStatus.FAILURE,
        type: PaymentInformationType.SALE,
        ...common,
      };
    }
    throw errorBuilder.payment.soePay.invalidResponse(document);
  };

  readonly processCapture = async (paymentTx: PaymentTx, total: number, requestedBy?: string) => {
    if (!paymentTx.gatewayTransactionId) {
      throw errorBuilder.global.requiredParam("gatewayTransactionId for paymentTx");
    }

    if (!total || total < 0) {
      throw errorBuilder.global.invalidParam("total amount for paymentTx");
    }

    const captureResult = await this.capture(paymentTx.gatewayTransactionId, total);

    const newPaymentTx = new PaymentTx();

    newPaymentTx.gatewayResponse = captureResult;
    newPaymentTx.type = PaymentInformationType.CAPTURE;
    newPaymentTx.gateway = PaymentGatewayTypes.SOEPAY;
    newPaymentTx.tx = paymentTx.tx;
    newPaymentTx.createdAt = new Date();
    newPaymentTx.parent = paymentTx;
    newPaymentTx.requestedBy = requestedBy;

    if (isSoePayCaptureSuccess(captureResult)) {
      newPaymentTx.gatewayTransactionId = captureResult.data.tranId;
      newPaymentTx.amount = captureResult.data.totalAmount;
      newPaymentTx.cardNumber = `${captureResult.data.payment.payerFirst}******${captureResult.data.payment.payerLast}`;
      newPaymentTx.paymentMethod = captureResult.data.payment.paymentMethod as PaymentTxPaymentMethod;
      newPaymentTx.id = captureResult.data.messageId;
      newPaymentTx.createdAt = new Date(captureResult.data.createTime);
      newPaymentTx.status = PaymentInformationStatus.SUCCESS;
    } else {
      const enquiryResult = await this.doEnquiry(paymentTx);
      if (
        isSoePayEnquirySuccess(enquiryResult) &&
        enquiryResult.data.payment.status === SoePayEnquiryPaymentStatus.PAID &&
        enquiryResult.data.tranStatus === SoePayTranStatus.APPORVED
      ) {
        newPaymentTx.gatewayResponse = enquiryResult;
        newPaymentTx.gatewayTransactionId = enquiryResult.data.tranId;
        newPaymentTx.amount = enquiryResult.data.totalAmount;
        newPaymentTx.cardNumber = `${enquiryResult.data.payment.payerFirst}******${enquiryResult.data.payment.payerLast}`;
        newPaymentTx.paymentMethod = enquiryResult.data.payment.paymentMethod as PaymentTxPaymentMethod;
        newPaymentTx.id = randomUUID();
        newPaymentTx.status = PaymentInformationStatus.SUCCESS;
      } else {
        newPaymentTx.id = randomUUID();
        newPaymentTx.status = PaymentInformationStatus.FAILURE;
      }
    }

    this.paymentTxRepository.save(newPaymentTx);

    return newPaymentTx;
  };

  /**
   * capture payment from soepay
   * @param tranId
   * @param amount
   * @returns
   */
  readonly capture = async (tranId: string, amount: number): Promise<SoePayCaptureResponse> => {
    // Get timestamp for encryption
    const timestamp = Date.now();
    axiosRetry(this.httpService.axiosRef, {
      retries: 3,
      retryDelay: exponentialDelay,
      retryCondition: (error: AxiosError) => {
        // Only retry on network errors or 5xx server errors
        return !error.response || error.response.status >= 500;
      },
    });
    try {
      const { data } = await firstValueFrom(
        await this.httpService
          .post(
            this.configService.getOrThrow("SOEPAY_URL") + "/transaction/capture",
            this.encryptedCaptureBody(timestamp, tranId, amount),
            {
              headers: {
                "api-key": this.configService.getOrThrow("SOEPAY_API_KEY"),
                identity: this.configService.getOrThrow("SOEPAY_IDENTITY"),
                "app-id": this.configService.getOrThrow("SOEPAY_APP_ID"),
                source: "SOFTPOS",
                "captcha-source": "SOFTPOS",
                "Content-Type": "text/plain",
                "Accept-Encoding": "gzip,deflate,compress",
                timestamp: timestamp,
              },
            },
          )
          .pipe(
            catchError((error: AxiosError) => {
              throw errorBuilder.payment.soePay.apiFailure(error);
            }),
          ),
      );
      return data;
    } catch (error) {
      if (isAxiosError(error) && error.response?.status !== 200) {
        throw errorBuilder.payment.soePay.maxAttemptCalled(error.response?.status ?? 500);
      } else {
        throw errorBuilder.global.unknown(error);
      }
    }
  };

  readonly processVoid = async (paymentTxs: PaymentTx[], requestedBy?: string) => {
    const promises = paymentTxs.map((paymentTx) => {
      if (paymentTx.paymentMethod === PaymentTxPaymentMethod.WECHAT) {
        return this.processRefund(paymentTx);
      }
      return this.voidPaymentTx(paymentTx, requestedBy);
    });
    const results = await Promise.allSettled(promises);
    const { fulfilled: sortedResults, rejected: errorReasons } = results.reduce(
      (acc, result) => {
        if (result.status === "fulfilled") {
          acc.fulfilled.push(result.value);
        } else if (result.status === "rejected") {
          acc.rejected.push(result.reason);
        }
        return acc;
      },
      { fulfilled: [] as PaymentTx[], rejected: [] as unknown[] },
    );
    if (errorReasons.length > 0) {
      throw errorBuilder.payment.soePay.voidFailed(errorReasons.length, errorReasons.join(", "));
    }
    return sortedResults.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
  };

  readonly voidPaymentTx = async (paymentTx: PaymentTx, requestedBy?: string): Promise<PaymentTx> => {
    if (!paymentTx.gatewayTransactionId) {
      throw errorBuilder.global.requiredParam("gatewayTransactionId for paymentTx");
    }
    const voidResult = await this.doVoid(paymentTx.gatewayTransactionId, paymentTx.id);
    const newPaymentTx = new PaymentTx();

    newPaymentTx.gatewayResponse = voidResult;
    newPaymentTx.type = PaymentInformationType.VOID;
    newPaymentTx.gateway = PaymentGatewayTypes.SOEPAY;
    newPaymentTx.parent = paymentTx;
    newPaymentTx.tx = paymentTx.tx;
    newPaymentTx.createdAt = new Date();
    newPaymentTx.requestedBy = requestedBy;

    if (isSoePayVoidSuccess(voidResult)) {
      newPaymentTx.gatewayTransactionId = voidResult.data.tranId;
      newPaymentTx.amount = voidResult.data.totalAmount;
      newPaymentTx.cardNumber = `${voidResult.data.payment.payerFirst}******${voidResult.data.payment.payerLast}`;
      newPaymentTx.paymentMethod = voidResult.data.payment.paymentMethod as PaymentTxPaymentMethod;
      newPaymentTx.id = voidResult.data.messageId;
      newPaymentTx.createdAt = new Date(voidResult.data.createTime);
      newPaymentTx.status = PaymentInformationStatus.SUCCESS;
    } else {
      const enquiryResult = await this.doEnquiry(paymentTx);
      if (
        isSoePayEnquirySuccess(enquiryResult) &&
        enquiryResult.data.payment.status === SoePayEnquiryPaymentStatus.VOIDED &&
        enquiryResult.data.tranStatus === SoePayTranStatus.VOIDED
      ) {
        newPaymentTx.gatewayResponse = enquiryResult;
        newPaymentTx.gatewayTransactionId = enquiryResult.data.tranId;
        newPaymentTx.amount = enquiryResult.data.totalAmount;
        newPaymentTx.cardNumber = `${enquiryResult.data.payment.payerFirst}******${enquiryResult.data.payment.payerLast}`;
        newPaymentTx.paymentMethod = enquiryResult.data.payment.paymentMethod as PaymentTxPaymentMethod;
        newPaymentTx.id = randomUUID();
        newPaymentTx.status = PaymentInformationStatus.SUCCESS;
      } else {
        newPaymentTx.id = randomUUID();
        newPaymentTx.status = PaymentInformationStatus.FAILURE;
      }
    }

    await this.paymentTxRepository.save(newPaymentTx);
    return newPaymentTx;
  };

  readonly doVoid = async (tranId: string, originMessageId: string): Promise<SoePayVoidResponse> => {
    this.logger.info("nest doVoid: ", { tranId, originMessageId });
    // Get timestamp for encryption
    const timestamp = Date.now();
    axiosRetry(this.httpService.axiosRef, {
      retries: 3,
      retryDelay: exponentialDelay,
      retryCondition: (error: AxiosError) => {
        // Only retry on network errors or 5xx server errors
        return !error.response || error.response.status >= 500;
      },
    });
    try {
      const { data } = await firstValueFrom(
        await this.httpService
          .post(
            this.configService.getOrThrow("SOEPAY_URL") + "/transaction/cancel",
            this.encryptedVoidBody(timestamp, tranId, originMessageId),
            {
              headers: {
                "api-key": this.configService.getOrThrow("SOEPAY_API_KEY"),
                identity: this.configService.getOrThrow("SOEPAY_IDENTITY"),
                "app-id": this.configService.getOrThrow("SOEPAY_APP_ID"),
                source: "SOFTPOS",
                "captcha-source": "SOFTPOS",
                "Content-Type": "text/plain",
                "Accept-Encoding": "gzip,deflate,compress",
                timestamp: timestamp,
              },
            },
          )
          .pipe(
            catchError((e: AxiosError) => {
              throw errorBuilder.payment.soePay.apiFailure(e);
            }),
          ),
      );
      return data;
    } catch (err) {
      if (isAxiosError(err) && err.response?.status !== 200) {
        throw errorBuilder.payment.soePay.maxAttemptCalled(err.response?.status ?? 500);
      } else {
        throw errorBuilder.global.unknown(err);
      }
    }
  };

  readonly processRefund = async (
    paymentTx: PaymentTx,
    requestedBy?: string,
    customAmount?: number,
  ): Promise<PaymentTx> => {
    this.logger.info("Soepay processRefund: ", { paymentTx, requestedBy, customAmount });
    const timestamp = Date.now();
    const toRefundAmount = customAmount ?? paymentTx.amount ?? 0;

    if (toRefundAmount <= 0) {
      throw errorBuilder.payment.soePay.apiFailure(new Error("cannot refund 0, check database"));
    }
    if (customAmount && paymentTx.amount && customAmount > paymentTx.amount) {
      throw errorBuilder.payment.soePay.apiFailure(new Error("refund amount is greater than paymentTx amount"));
    }

    if (!paymentTx.gatewayTransactionId) {
      throw errorBuilder.global.requiredParam("gatewayTransactionId for paymentTx");
    }

    if (paymentTx.paymentMethod === PaymentTxPaymentMethod.OCTOPUS) {
      throw errorBuilder.payment.soePay.apiFailure(new Error("Refund for Octopus payment method is not supported"));
    }

    this.logger.info("Soepay processRefund: ", {
      timestamp,
      gatewayTransactionId: paymentTx.gatewayTransactionId,
      toRefundAmount,
    });

    return await axios
      .post<SoePayRefundResponse>(
        `${this.configService.getOrThrow("SOEPAY_URL")}/transaction/refund`,
        this.encryptedRefundBody(timestamp, paymentTx.gatewayTransactionId, toRefundAmount),
        {
          headers: {
            "api-key": this.configService.getOrThrow("SOEPAY_API_KEY"),
            identity: this.configService.getOrThrow("SOEPAY_IDENTITY"),
            "app-id": this.configService.getOrThrow("SOEPAY_APP_ID"),
            source: "SOFTPOS",
            "captcha-source": "SOFTPOS",
            "Content-Type": "text/plain",
            "Accept-Encoding": "gzip,deflate,compress",
            timestamp: timestamp,
          },
        },
      )
      .then(async (response) => {
        response.data;
        const newPaymentTx = new PaymentTx();
        newPaymentTx.type = PaymentInformationType.REFUND;
        newPaymentTx.gatewayResponse = response.data;
        newPaymentTx.gateway = PaymentGatewayTypes.SOEPAY;
        newPaymentTx.tx = paymentTx.tx;
        newPaymentTx.createdAt = new Date();
        newPaymentTx.parent = paymentTx;
        newPaymentTx.requestedBy = requestedBy;
        newPaymentTx.cardNumber = paymentTx.cardNumber;
        newPaymentTx.paymentMethod = paymentTx.paymentMethod;
        newPaymentTx.paymentInstrument = paymentTx.paymentInstrument;
        newPaymentTx.amount = toRefundAmount;
        newPaymentTx.id = randomUUID();

        const res = response.data;

        newPaymentTx.status = isSoePayRefundSuccess(res)
          ? PaymentInformationStatus.SUCCESS
          : PaymentInformationStatus.FAILURE;

        if (isSoePayRefundSuccess(res)) {
          newPaymentTx.gatewayTransactionId = res.data.tranId;
        }

        await this.paymentTxRepository.save(newPaymentTx);

        return newPaymentTx;
      });
  };

  readonly doEnquiry = async (paymentTx: PaymentTx, requestedBy?: string): Promise<SoePayEnquiryResponse> => {
    const originMessageId = paymentTx.id;

    if (!originMessageId) {
      throw errorBuilder.global.requiredParam("gatewayTransactionId for paymentTx");
    }

    this.logger.info("nest doEnquiry: ", { originMessageId });
    const timestamp = Date.now();

    const enquirePaymentTx = this.paymentTxRepository.create({
      id: randomUUID(),
      tx: paymentTx.tx,
      amount: paymentTx.amount,
      gatewayTransactionId: "",
      cardNumber: paymentTx.cardNumber,
      paymentMethod: paymentTx.paymentMethod,
      gateway: PaymentGatewayTypes.SOEPAY,
      createdAt: new Date().toISOString(),
      status: PaymentInformationStatus.PENDING,
      type: PaymentInformationType.ENQUIRY,
      parent: paymentTx,
      requestedBy,
    });

    await this.paymentTxRepository.save(enquirePaymentTx);

    axiosRetry(this.httpService.axiosRef, {
      retries: 3,
      retryDelay: exponentialDelay,
      retryCondition: (error: AxiosError) => {
        // Only retry on network errors or 5xx server errors
        return !error.response || error.response.status >= 500;
      },
    });
    try {
      const { data } = await firstValueFrom(
        await this.httpService
          .get(this.configService.getOrThrow("SOEPAY_URL") + "/payment/info/sale?messageId=" + originMessageId, {
            headers: {
              "api-key": this.configService.getOrThrow("SOEPAY_API_KEY"),
              identity: this.configService.getOrThrow("SOEPAY_IDENTITY"),
              "app-id": this.configService.getOrThrow("SOEPAY_APP_ID"),
              source: "SOFTPOS",
              "captcha-source": "SOFTPOS",
              "Content-Type": "text/plain",
              "Accept-Encoding": "gzip,deflate,compress",
              timestamp: timestamp,
              sign: this.createSignatureForGetRequest(
                [{ key: "messageId", value: originMessageId }],
                timestamp,
                this.configService.getOrThrow("SOEPAY_API_KEY"),
              ),
            },
          })
          .pipe(
            catchError((e: AxiosError) => {
              throw errorBuilder.payment.soePay.apiFailure(e);
            }),
          ),
      );
      if (isSoePayEnquirySuccess(data)) {
        enquirePaymentTx.gatewayTransactionId = data.data.tranId;
        enquirePaymentTx.status = PaymentInformationStatus.SUCCESS;

        enquirePaymentTx.gatewayResponse = data;
      } else {
        enquirePaymentTx.status = PaymentInformationStatus.FAILURE;
        enquirePaymentTx.gatewayResponse = data;
      }

      await this.paymentTxRepository.save(enquirePaymentTx);

      return data;
    } catch (err) {
      if (isAxiosError(err) && err.response?.status !== 200) {
        throw errorBuilder.payment.soePay.maxAttemptCalled(err.response?.status ?? 500);
      } else {
        throw errorBuilder.global.unknown(err);
      }
    }
  };
  /**
   * encryptedCaptureBody
   * @param timestamp
   * @param tranId
   * @param amount
   * @returns
   */
  encryptedCaptureBody = (timestamp: number, tranId: string, amount: number): string => {
    const body = {
      tranId: tranId,
      signatureOnPaper: false,
      amount: amount,
    };
    return this.getEncryptedBody(timestamp, body);
  };

  /**
   * encryptedVoidBody
   * @param timestamp
   * @param tranId
   * @param originMessageId
   * @returns
   */
  encryptedVoidBody = (timestamp: number, tranId: string, originMessageId: string): string => {
    const body = {
      tranId: tranId,
      originMessageId: originMessageId,
    };
    return this.getEncryptedBody(timestamp, body);
  };

  /**
   * encryptedRefundBody
   * @param timestamp
   * @param tranId
   * @param amount
   * @returns
   */
  encryptedRefundBody = (timestamp: number, tranId: string, amount: number): string => {
    const body = {
      tranId,
      amount,
    };
    this.logger.info("encryptedRefundBody: ", { body });
    return this.getEncryptedBody(timestamp, body);
  };

  /**
   * getEncryptedBody
   * @param timestamp
   * @param body
   * @returns
   */
  getEncryptedBody = (timestamp: number, body: any): string =>
    CryptoJS.AES.encrypt(
      CryptoJS.enc.Utf8.parse(JSON.stringify(body)),
      CryptoJS.enc.Utf8.parse(
        CryptoJS.SHA256(`${this.configService.getOrThrow("SOEPAY_API_KEY")}${timestamp}`.toLowerCase())
          .toString()
          .substring(16, 48),
      ),
      {
        iv: CryptoJS.enc.Hex.parse("0000000000000000"),
        mode: CryptoJS.mode.CBC, // Block mode = CBC
        padding: CryptoJS.pad.Pkcs7, // Padding = PKCS7
        salt: "", // Salt is not required
      },
    ).toString();

  /**
   * createSignatureForGetRequest
   * @param query
   * @param timestamp
   * @param apiKey
   * @returns
   */
  createSignatureForGetRequest = (query: { key: string; value: string }[], timestamp: number, apiKey: string) => {
    const map: Record<string, any> = query.reduce(
      (prev: Record<string, any>, param) => {
        if (param.value !== "" && param.value !== null) {
          prev[param.key] = param.value.trim();
        }
        return prev;
      },
      { timestamp: timestamp, apiKey: apiKey },
    );
    return this.signGetRequestMap(this.mapToSortedQueryString(map));
  };

  /**
   * Convert map to query string format
   *
   * @param {string} key The query string to be signed
   * @return {string} The signed query string
   */
  signGetRequestMap = (queryString: string): string => {
    return CryptoJS.SHA256(queryString).toString().substring(16, 48);
  };

  /**
   * Convert map to query string format
   *
   * @param {{key: string, value: string}} key The map to be convert
   * @return {string} The converted map in query string format
   */
  mapToSortedQueryString = (map: Record<string, any>): string => {
    const sortedKey = Object.keys(map);
    sortedKey.sort();
    return sortedKey.reduce((prev, key) => {
      if (prev !== "") prev += "&";
      return prev + `${key}=${map[key]}`;
    }, "");
  };

  /**
   *
   */
  isStillProcessing = (paymentInfo: EnquiryResponse) => {
    return isSoePayEnquirySuccess(paymentInfo) && paymentInfo.data.tranStatus === SoePayTranStatus.PROCESSING;
  };

  /**
   * Find all sales in processing status
   * @param paymentTxs
   * @returns PaymentTx[]
   */
  readonly findAllSalesInProcessingStatus = (paymentTxs: PaymentTx[]): PaymentTx[] => {
    return paymentTxs.filter(
      (paymentTx) =>
        paymentTx.type === PaymentInformationType.SALE &&
        paymentTx.gatewayResponse &&
        isSoePaySale(paymentTx.gatewayResponse) &&
        paymentTx.gatewayResponse?.tranStatus === SoePayTranStatus.PROCESSING,
    );
  };

  readonly processSale = (tx: Tx, paymentInstrument: PaymentInstrument) => {
    throw errorBuilder.factory.notImplemented("SoepayService/processSale");
  };

  readonly processAuth = (tx: Tx, paymentInstrument: PaymentInstrument, overwriteAmount?: number) => {
    throw errorBuilder.factory.notImplemented("SoepayService/processAuth");
  };

  readonly searchPayment = (_paymentTx: PaymentTx, requestedBy?: string) => {
    throw errorBuilder.factory.notImplemented("SoepayService/searchPayment");
  };
}

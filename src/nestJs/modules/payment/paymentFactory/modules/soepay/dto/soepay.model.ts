import { EnquiryResponse, PaymentGatewayResponse } from "../../../../dto/paymentGatewayResponses.model";

export type SoePayResponse =
  | SoePayCaptureResponse
  | SoePayAuthResponse
  | SoePayVoidResponse
  | SoePayEnquiryResponse
  | SoePaySaleResponse;

export type SoePayCaptureResponse = SoePayCaptureSuccessResponse | SoePayFailureResponse;
export type SoePayAuthResponse = SoePayAuthSuccessResponse | SoePayAuthFailureResponse;
export type SoePayVoidResponse = SoePayVoidSuccessResponse | SoePayFailureResponse;
export type SoePayRefundResponse = SoePayRefundSuccessResponse | SoePayFailureResponse;
export type SoePayEnquiryResponse = SoePayEnquirySuccessResponse | SoePayFailureResponse;
export type SoePaySaleResponse = SoePaySaleSuccessResponse | SoePaySaleFailureResponse;

/**
 * Check if the response is SoePay response
 * @param content SoePayResponse
 * @returns boolean
 */
export const isSoePayResponse = (content: PaymentGatewayResponse): content is SoePayResponse =>
  isSoePayCaptureSuccess(content) ||
  isSoePayCaptureFailure(content) ||
  isSoePayAuthSuccess(content) ||
  isSoePayAuthFailure(content) ||
  isSoePayVoidSuccess(content) ||
  isSoePayVoidFailure(content) ||
  isSoePaySaleSuccess(content) ||
  isSoePaySaleFailure(content);

/**
 * Check if the response is a success response
 * @param content SoePayResponse
 * @returns boolean
 */
export const isSoePaySuccess = (
  content: PaymentGatewayResponse,
): content is SoePayCaptureSuccessResponse | SoePayVoidSuccessResponse | SoePayEnquirySuccessResponse =>
  Object.prototype.hasOwnProperty.call(content, "data");

/**
 * Check if the response is a capture response
 * @param content SoePayResponse
 * @returns boolean
 */
export const isSoePayCapture = (
  content: PaymentGatewayResponse,
): content is SoePayFailureResponse | SoePayCaptureSuccessResponse =>
  Object.prototype.hasOwnProperty.call(content, "result");

export const isSoePayCaptureSuccess = (content: PaymentGatewayResponse): content is SoePayCaptureSuccessResponse =>
  isSoePayCapture(content) &&
  content.result === 0 &&
  isSoePaySuccess(content) &&
  content.data.tranType === SoePayTranType.AUTH_COMPLETE;

export const isSoePayCaptureFailure = (content: PaymentGatewayResponse): content is SoePayFailureResponse =>
  isSoePayCapture(content) && content.result !== 0;

export const isSoePayRefundSuccess = (content: SoePayRefundResponse): content is SoePayRefundSuccessResponse =>
  content.result === 0;

/**
 * Check if the response is an tran response (with a "tranType")
 * @param content SoePayResponse
 * @returns boolean
 */
export const isSoePayTran = (
  content: PaymentGatewayResponse,
): content is Exclude<SoePayResponse, SoePayFailureResponse | SoePayCaptureSuccessResponse> =>
  Object.prototype.hasOwnProperty.call(content, "tranType");

/**
 * Check if the response is an auth response
 * @param content SoePayResponse
 * @returns boolean
 */
export const isSoePayAuth = (
  content: PaymentGatewayResponse,
): content is SoePayAuthFailureResponse | SoePayAuthSuccessResponse =>
  isSoePayTran(content) && content.tranType === SoePayTranType.AUTH;

export const isSoePayAuthSuccess = (content: PaymentGatewayResponse): content is SoePayAuthSuccessResponse =>
  isSoePayAuth(content) && content.tranStatus === SoePayTranStatus.APPORVED;

export const isSoePayAuthFailure = (content: PaymentGatewayResponse): content is SoePayAuthFailureResponse =>
  isSoePayAuth(content) &&
  (content.tranStatus === SoePayTranStatus.ERROR || content.tranStatus === SoePayTranStatus.DECLINED);
/**
 * Check if the response is a void response
 * @param content SoePayResponse
 * @returns boolean
 */
export const isSoePayVoid = (
  content: PaymentGatewayResponse,
): content is SoePayFailureResponse | SoePayVoidSuccessResponse =>
  Object.prototype.hasOwnProperty.call(content, "result");

export const isSoePayVoidSuccess = (content: PaymentGatewayResponse): content is SoePayVoidSuccessResponse =>
  isSoePayVoid(content) &&
  content.result === 0 &&
  isSoePaySuccess(content) &&
  content.data.tranType === SoePayTranType.VOID;

export const isSoePayVoidFailure = (content: PaymentGatewayResponse): content is SoePayFailureResponse =>
  isSoePayVoid(content) && content.result !== 0;

/**
 * check if response is a enquiry response
 * @param content SoePayResponse
 * @returns boolean
 */
export const isSoePayEnquiry = (
  content: EnquiryResponse,
): content is SoePayFailureResponse | SoePayEnquirySuccessResponse =>
  Object.prototype.hasOwnProperty.call(content, "result");

export const isSoePayEnquirySuccess = (content: EnquiryResponse): content is SoePayEnquirySuccessResponse =>
  isSoePayEnquiry(content) && content.result === 0 && isSoePaySuccess(content);

export const isSoePayEnquiryFailure = (content: EnquiryResponse): content is SoePayFailureResponse =>
  isSoePayEnquiry(content) && content.result !== 0;

/**
 * Check if the response is a sale response
 */
export const isSoePaySale = (
  content: PaymentGatewayResponse,
): content is SoePaySaleFailureResponse | SoePaySaleSuccessResponse =>
  isSoePayTran(content) && content.tranType === SoePayTranType.SALE;

export const isSoePaySaleSuccess = (content: PaymentGatewayResponse): content is SoePaySaleSuccessResponse =>
  isSoePaySale(content) && content.tranStatus === SoePayTranStatus.APPORVED;

export const isSoePaySaleFailure = (content: PaymentGatewayResponse): content is SoePaySaleFailureResponse =>
  isSoePaySale(content) &&
  (content.tranStatus === SoePayTranStatus.ERROR ||
    content.tranStatus === SoePayTranStatus.DECLINED ||
    content.tranStatus === SoePayTranStatus.PROCESSING);

export enum SoePayTranStatus {
  APPORVED = "APPROVED",
  ERROR = "ERROR",
  VOIDED = "VOIDED",
  DECLINED = "DECLINED",
  PROCESSING = "PROCESSING",
}

export enum SoePayTranType {
  AUTH_COMPLETE = "AUTH_COMPLETE",
  AUTH = "AUTH",
  VOID = "VOID",
  SALE = "SALE",
}

/**
 * @typedef SoePayCaptureSuccessResponse Success capture response from SoePay
 */
export interface SoePayCaptureSuccessResponse {
  result: 0;
  data: SoePaySuccessResponseData;
  message: "SUCCESS";
}

interface SoePaySuccessResponseData {
  createTime: string;
  lastUpdateTime: string;
  receiptOffline: boolean;
  messageId: string;
  payment: SoePaySuccessResponseDataPayment;
  entryMode: string;
  remainingValue?: any;
  discountMessage?: any;
  commitTime: string;
  settledTime?: any;
  ip: string;
  outTranId: string;
  approvalCode: string;
  stan: string;
  mid: string;
  tid: string;
  ecrReference?: any;
  qrVoucherNumber?: any;
  partnerReference?: any;
  processorReference: string;
  processorMessage?: any;
  processorResult: string;
  cardholderVerification: string;
  batch: string;
  lineItem: boolean;
  requireSignature: boolean;
  signatureUrl?: any;
  cardData: SoePaySuccessResponseDataCardData;
  app: SoePaySuccessResponseDataApp;
  challengeMessage?: any;
  challengeVersion?: any;
  billingInfo?: any;
  signatureOnPaper: boolean;
  createByName: string;
  createById: string;
  cancelable: boolean;
  refundable: boolean;
  capturable: boolean;
  createByType: string;
  tipAmount: number;
  receiptOnline: boolean;
  settled: boolean;
  totalAmount: number;
  trace: number;
  tranId: string;
  tranType: SoePayTranType;
  baseAmount: number;
  tranParent: SoePayCaptureSuccessResponseDataTranParent;
  mdr: number;
  tranParentId: string;
  tranStatus: SoePayTranStatus.APPORVED;
  errorCode?: any;
}

interface SoePayCaptureSuccessResponseDataTranParent {
  trace: number;
  tranId: string;
  tranType: string;
}

interface SoePaySuccessResponseDataApp {
  appType: string;
  equipment: SoePaySuccessResponseDataAppEquipment;
  id: string;
}

interface SoePaySuccessResponseDataAppEquipment {
  createTime: string;
  name: string;
  model: string;
  os: string;
  id: string;
}

interface SoePaySuccessResponseDataCardData {
  aid: string;
  approvalCode: string;
  localTranDateTime: string;
  stan: string;
  mid: string;
  tid: string;
}

interface SoePaySuccessResponseDataPayment {
  createTime: string;
  lastUpdateTime: string;
  organize: SoePaySuccessResponseDataPaymentOrganize;
  currency: string;
  baseAmount: number;
  tipAmount: number;
  originTranType: string;
  netAmount: number;
  directMode: boolean;
  description?: any;
  paymentMethod: string;
  processor: string;
  status: string;
  payerName?: any;
  payerFirst: string;
  payerLast: string;
  countryCode?: any;
  orderId: string;
  cancelable: boolean;
  refundable: boolean;
  capturable: boolean;
  adjustable: boolean;
  paymentTrace: number;
  paymentId: string;
  payer: string;
}

interface SoePaySuccessResponseDataPaymentOrganize {
  name: string;
  email?: any;
  address: string;
  logo?: any;
  phone: string;
  autoSettlement: boolean;
  settlementTime: string;
  organizeId: string;
}

/**
 * @typedef SoePayCaptureFailureResponse Failure capture/void response from SoePay
 */
export interface SoePayFailureResponse {
  result: number;
  message: string;
}

/**
 * @typedef SoePayAuthSuccessResponse Success auth response from SoePay
 */
export interface SoePayAuthSuccessResponse {
  baseAmount: number;
  batch: number;
  cancelable: boolean;
  capturable: boolean;
  cardData: SoePayAuthSuccessResponseCardData;
  createById: string;
  createByName: string;
  createTime: string;
  entryMode: string;
  ip: string;
  lastUpdateTime: string;
  lineItem: boolean;
  mappingId: string;
  messageId: string;
  outTranId?: string;
  payment: SoePayAuthSuccessResponsePayment;
  processorReference: string;
  processorResult: string;
  receiptOffline: boolean;
  receiptOnline: boolean;
  refundable: boolean;
  requireSignature: boolean;
  settled: boolean;
  tipAmount: number;
  totalAmount: number;
  trace: number;
  tranId: string;
  tranStatus: SoePayTranStatus.APPORVED;
  tranType: SoePayTranType.AUTH;
}

interface SoePayAuthSuccessResponsePayment {
  adjustable: boolean;
  baseAmount: number;
  cancelable: boolean;
  createTime: string;
  directMode: boolean;
  lastUpdateTime: string;
  netAmount: number;
  orderId: string;
  organize: SoePayAuthSuccessResponsePaymentOrganize;
  originTranType: string;
  payer: string;
  payerFirst: string;
  payerLast: string;
  paymentId: string;
  paymentMethod: string;
  paymentTrace: number;
  refundable: boolean;
  tipAmount: number;
}

interface SoePayAuthSuccessResponsePaymentOrganize {
  address: string;
  name: string;
}

interface SoePayAuthSuccessResponseCardData {
  aid: string;
  appName?: string;
  approvalCode?: string;
  mid?: string;
  tc: string;
  tid?: string;
  tsi: string;
  tvr: string;
}

/**
 * @typedef SoePayAuthFailureResponse Failure auth response from SoePay
 */
export interface SoePayAuthFailureResponse {
  baseAmount: number;
  lineItem: boolean;
  mappingId: string;
  errorCode: number;
  messageId: string;
  payment: SoePayAuthFailureResponsePayment;
  processorMessage: string;
  receiptOnline: boolean;
  trace: number;
  tranId: string;
  tranStatus: SoePayTranStatus.ERROR | SoePayTranStatus.DECLINED;
  tranType: SoePayTranType.AUTH;
  //optional
  [key: string]: any;
}

interface SoePayAuthFailureResponsePayment {
  paymentId: string;
  //optional
  [key: string]: any;
}

/**
 * @typedef SoePayVoidSuccessResponse Success void response from SoePay
 */
export interface SoePayVoidSuccessResponse {
  result: 0;
  data: SoePaySuccessResponseData;
  message: "SUCCESS";
}

export interface SoePayRefundSuccessResponse {
  result: 0;
  data: SoePaySuccessResponseData;
  message: "SUCCESS";
}

export interface SoePayEnquirySuccessResponse {
  result: 0;
  checksum: string;
  data: SoePayEnquirySuccessResponseData;
  message: "SUCCESS";
}

interface SoePayEnquirySuccessResponseData extends Omit<SoePaySuccessResponseData, "cardData" | "tranStatus"> {
  payment: SoePayEnquirySuccessResponseDataPayment;
  instalmentPlanId?: any;
  cardData: SoePayEnquirySuccessResponseDataCardData;
  processorAdditionalData?: any;
  requestAdditionalData?: any;
  tranStatus: SoePayTranStatus;
}

interface SoePayEnquirySuccessResponseDataPayment extends SoePaySuccessResponseDataPayment {
  issuerCountryCode?: any;
  status: SoePayEnquiryPaymentStatus;
  instalmentStatus?: any;
  instalmentCancelable: boolean;
  tags: SoePayEnquirySuccessResponseDataPaymentTag[];
}

interface SoePayEnquirySuccessResponseDataPaymentTag {
  id: string;
  tag: string;
}

interface SoePayEnquirySuccessResponseDataCardData {
  aid: string;
  tc: string;
  tsi: string;
  request: any;
}

export enum SoePayEnquiryPaymentStatus {
  AUTHORIZED = "AUTHORIZED",
  VOIDED = "VOIDED",
  PAID = "PAID",
}

export interface SoePaySaleFailureResponse {
  baseAmount: number;
  entryMode: string;
  lineItem: boolean;
  mappingId: string;
  errorCode: number;
  messageId: string;
  payment: SoePaySaleFailureResponsePayment;
  processorMessage: string;
  receiptOnline: boolean;
  trace: number;
  tranId: string;
  tranStatus: SoePayTranStatus.ERROR | SoePayTranStatus.DECLINED | SoePayTranStatus.PROCESSING;
  tranType: SoePayTranType.SALE;
  //optional
  [key: string]: any;
}

interface SoePaySaleFailureResponsePayment {
  paymentId: string;
  //optional
  [key: string]: any;
}

export interface SoePaySaleSuccessResponse {
  baseAmount: number;
  batch: number;
  cancelable: boolean;
  capturable: boolean;
  cardData?: SoePaySaleSuccessResponseCardData;
  createById: string;
  createByName: string;
  createTime: string;
  entryMode: string;
  ip: string;
  lastUpdateTime: string;
  lineItem: boolean;
  mappingId: string;
  messageId: string;
  outTranId?: string;
  payment: SoePaySaleSuccessResponsePayment;
  processorReference: string;
  processorResult: string;
  receiptOffline: boolean;
  receiptOnline: boolean;
  refundable: boolean;
  requireSignature: boolean;
  settled: boolean;
  tipAmount: number;
  totalAmount: number;
  trace: number;
  tranId: string;
  tranStatus: SoePayTranStatus.APPORVED;
  tranType: SoePayTranType.SALE;
}

interface SoePaySaleSuccessResponseCardData {
  aid: string;
  appName?: string;
  approvalCode?: string;
  mid?: string;
  tc: string;
  tid?: string;
  tsi: string;
  tvr: string;
}

interface SoePaySaleSuccessResponsePayment {
  adjustable: boolean;
  baseAmount: number;
  cancelable: boolean;
  createTime: string;
  directMode: boolean;
  lastUpdateTime: string;
  netAmount: number;
  orderId: string;
  organize: SoePayAuthSuccessResponsePaymentOrganize;
  originTranType: string;
  payer: string;
  payerFirst: string;
  payerLast: string;
  payerName?: string;
  paymentId: string;
  paymentMethod: string;
  paymentTrace: number;
  refundable: boolean;
  tipAmount: number;
}

export enum EntryMode {
  UNKNOWN = "UNKNOWN",
  MANUAL = "MANUAL",
  MAGSTRIPE = "MAGSTRIPE",
  FALLBACK = "FALLBACK",
  CONTACTLESS = "CONTACTLESS",
  CHIP = "CHIP",
  QR_SCAN = "QR_SCAN",
  QR_PRESENT = "QR_PRESENT",
  CASH = "CASH",
  CONTACTLESS_MAGSTRIPE = "CONTACTLESS_MAGSTRIPE",
  QR_STATIC = "QR_STATIC",
  OCTOPUS = "OCTOPUS",
  ECOMMERCE = "ECOMMERCE",
}

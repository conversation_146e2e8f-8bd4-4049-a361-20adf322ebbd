export interface GlobalPaymentResponse {
  status: number;
  response: GlobalPaymentResponseResponse;
}

interface GlobalPaymentResponseResponse {
  req: Req;
  status: number;
  text: string;
}

interface Req {
  method: string;
  url: string;
}

export const GlobalPaymentCardTypeMap = {
  "001": "VISA",
  "002": "MASTERCARD",
  "003": "AMERICAN EXPRESS",
  "004": "DISCOVER",
  "005": "DINERS CLUB",
  "006": "CARTE BLANCHE",
  "007": "JCB",
  "014": "ENROUTE",
  "021": "JAL",
  "024": "MAESTRO (UK DOMESTIC)",
  "033": "VISA ELECTRON",
  "034": "DANKORT",
  "036": "CARTES BANCAIRES",
  "037": "CARTA SI",
  "039": "ENCODED ACCOUNT NUMBER",
  "040": "UATP",
  "042": "<PERSON><PERSON><PERSON><PERSON> (INTERNATIONAL)",
  "050": "HIPERCARD",
  "051": "AURA",
  "054": "ELO",
  "062": "CHINA UNIONPAY",
  "058": "CARNET",
};

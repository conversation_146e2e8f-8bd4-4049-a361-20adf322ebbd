import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";

import PaymentInstrument from "../../../../database/entities/paymentInstrument.entity";
import PaymentTx from "../../../../database/entities/paymentTx.entity";
import Tx from "../../../../database/entities/tx.entity";
import { PaymentTxRepository } from "../../../../database/repositories/paymentTx.repository";
import { errorBuilder } from "../../../../utils/utils/error.utils";
import { PaymentGatewayResponse } from "../../../dto/paymentGatewayResponses.model";
import PaymentTxFromDocument from "../../../dto/paymentTxFromDocument.model";
import { GlobalPaymentService } from "../../../modules/paymentInstrument/modules/globalPayment/globalPayment.service";
import paymentFactoryInterface from "../../paymentFactoryInterface";

/**
 * GlobalPayment service
 */
@Injectable()
export class GlobalPaymentPaymentService implements paymentFactoryInterface {
  constructor(
    private readonly globalPaymentService: GlobalPaymentService,
    @InjectRepository(PaymentTxRepository) private paymentTxRepository: PaymentTxRepository,
  ) {}

  /**
   * Extract payment transaction information from document
   * @param document
   * @returns PaymentTxFromDocument
   */
  readonly extractPaymentTxInfoFromDocument = (
    document: PaymentGatewayResponse,
    options?: any,
  ): PaymentTxFromDocument => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/extractPaymentTxInfoFromDocument");
  };

  readonly processCapture = async (authPaymentTx: PaymentTx, total: number, requestedBy?: string) => {
    return await this.globalPaymentService.capturePayment(authPaymentTx, total, requestedBy);
  };

  /**
   * capture payment
   * @param tranId
   * @param amount
   * @returns
   */
  readonly capture = async (tranId: string, amount: number): Promise<any> => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/capture");
  };

  readonly processVoid = async (paymentTxs: PaymentTx[], requestedBy?: string) => {
    const newPaymentTx = await Promise.all(
      paymentTxs.map(async (paymentTx) => {
        return this.globalPaymentService.voidPayment(paymentTx, requestedBy);
      }),
    );

    await this.paymentTxRepository.save(newPaymentTx);

    return newPaymentTx;
  };

  readonly voidPaymentTx = async (paymentTx: PaymentTx, requestedBy?: string): Promise<PaymentTx> => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/voidPaymentTx");
  };

  readonly doVoid = async (tranId: string, originMessageId: string): Promise<any> => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/doVoid");
  };

  readonly doEnquiry = async (paymentTx: PaymentTx, requestedBy?: string): Promise<any> => {
    return this.globalPaymentService.doEnquiry(paymentTx, requestedBy);
  };
  /**
   * encryptedCaptureBody
   * @param timestamp
   * @param tranId
   * @param amount
   * @returns
   */
  encryptedCaptureBody = (timestamp: number, tranId: string, amount: number): string => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/encryptedCaptureBody");
  };

  /**
   * encryptedVoidBody
   * @param timestamp
   * @param tranId
   * @param originMessageId
   * @returns
   */
  encryptedVoidBody = (timestamp: number, tranId: string, originMessageId: string): string => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/encryptedVoidBody");
  };

  /**
   * getEncryptedBody
   * @param timestamp
   * @param body
   * @returns
   */
  getEncryptedBody = (timestamp: number, body: any): string => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/getEncryptedBody");
  };

  /**
   * createSignatureForGetRequest
   * @param query
   * @param timestamp
   * @param apiKey
   * @returns
   */
  createSignatureForGetRequest = (query: { key: string; value: string }[], timestamp: number, apiKey: string) => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/createSignatureForGetRequest");
  };

  /**
   * Convert map to query string format
   *
   * @param {string} key The query string to be signed
   * @return {string} The signed query string
   */
  signGetRequestMap = (queryString: string): string => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/signGetRequestMap");
  };

  /**
   * Convert map to query string format
   *
   * @param {{key: string, value: string}} key The map to be convert
   * @return {string} The converted map in query string format
   */
  mapToSortedQueryString = (map: Record<string, any>): string => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/mapToSortedQueryString");
  };

  /**
   *
   */
  isStillProcessing = (paymentInfo: any) => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/isStillProcessing");
  };

  /**
   * Find all sales in processing status
   * @param paymentTxs
   * @returns PaymentTx[]
   */
  readonly findAllSalesInProcessingStatus = (paymentTxs: PaymentTx[]): PaymentTx[] => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/findAllSalesInProcessingStatus");
  };

  /**
   * process sale tx
   * @param tx Tx
   * @param paymentInstrument PaymentInstrument
   * @returns PaymentTx
   */
  processSale = async (
    tx: Tx,
    paymentInstrument: PaymentInstrument,
    overwriteAmount?: number,
    requestedBy?: string,
  ): Promise<PaymentTx> => {
    return await this.globalPaymentService.createPayment(tx, paymentInstrument, true, overwriteAmount, requestedBy);
  };

  /**
   * process auth tx
   * @param tx Tx
   * @param paymentInstrument PaymentInstrument
   * @returns PaymentTx
   */
  processAuth = async (
    tx: Tx,
    paymentInstrument: PaymentInstrument,
    overwriteAmount?: number,
    requestedBy?: string,
  ): Promise<PaymentTx> => {
    return await this.globalPaymentService.createPayment(tx, paymentInstrument, false, overwriteAmount, requestedBy);
  };

  /**
   * search a paymentTX
   * @param paymentTx PaymentTx
   * @param requestedBy string
   * @returns PaymentTx
   */
  searchPayment = async (paymentTx: PaymentTx, requestedBy?: string): Promise<PaymentTx> => {
    return await this.globalPaymentService.searchPayment(paymentTx, requestedBy);
  };

  /**
   * process refund
   * @param paymentTx PaymentTx
   * @param requestedBy string
   * @param customAmount number
   * @returns PaymentTx
   */
  readonly processRefund = async (
    paymentTx: PaymentTx,
    requestedBy?: string,
    customAmount?: number,
  ): Promise<PaymentTx> => {
    throw errorBuilder.factory.notImplemented("GlobalPaymentService/processRefund");
  };
}

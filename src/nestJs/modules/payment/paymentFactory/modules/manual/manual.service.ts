import { Injectable } from "@nestjs/common";

import PaymentInstrument from "../../../../database/entities/paymentInstrument.entity";
import PaymentTx from "../../../../database/entities/paymentTx.entity";
import Tx from "../../../../database/entities/tx.entity";
import { errorBuilder } from "../../../../utils/utils/error.utils";
import { EnquiryResponse } from "../../../dto/paymentGatewayResponses.model";
import { PaymentGatewayTypes } from "../../../dto/paymentGatewayTypes.dto";
import paymentFactoryInterface from "../../paymentFactoryInterface";

/**
 * Manual service
 */
@Injectable()
export class ManualService implements paymentFactoryInterface {
  constructor() {}

  /**
   * Extract payment transaction information from document
   * TODO: implement the document type in parameter
   * @param document
   * @returns PaymentTxFromDocument
   */
  readonly extractPaymentTxInfoFromDocument = (document: any) => {
    throw errorBuilder.payment.factoryNotImplemented(PaymentGatewayTypes.MANUAL);
  };

  readonly processCapture = (paymentTx: PaymentTx) => {
    throw errorBuilder.payment.factoryNotImplemented(PaymentGatewayTypes.MANUAL);
  };

  readonly processVoid = (paymentTxs: PaymentTx[]) => {
    throw errorBuilder.payment.factoryNotImplemented(PaymentGatewayTypes.MANUAL);
  };

  readonly doEnquiry = (paymentTx: PaymentTx) => {
    throw errorBuilder.payment.factoryNotImplemented(PaymentGatewayTypes.MANUAL);
  };

  readonly isStillProcessing = (paymentInfo: EnquiryResponse) => {
    throw errorBuilder.payment.factoryNotImplemented(PaymentGatewayTypes.MANUAL);
  };

  readonly findAllSalesInProcessingStatus = (paymentTxs: PaymentTx[]) => {
    throw errorBuilder.payment.factoryNotImplemented(PaymentGatewayTypes.MANUAL);
  };

  readonly processSale = (tx: Tx, paymentInstrument: PaymentInstrument) => {
    throw errorBuilder.payment.factoryNotImplemented(PaymentGatewayTypes.MANUAL);
  };

  readonly processAuth = (tx: Tx, paymentInstrument: PaymentInstrument, overwriteAmount?: number) => {
    throw errorBuilder.payment.factoryNotImplemented(PaymentGatewayTypes.MANUAL);
  };

  readonly processRefund = async (
    paymentTx: PaymentTx,
    requestedBy?: string,
    customAmount?: number,
  ): Promise<PaymentTx> => {
    throw errorBuilder.payment.factoryNotImplemented(PaymentGatewayTypes.MANUAL);
  };

  readonly searchPayment = async (paymentTx: PaymentTx, requestedBy?: string): Promise<PaymentTx> => {
    throw errorBuilder.payment.factoryNotImplemented(PaymentGatewayTypes.MANUAL);
  };
}

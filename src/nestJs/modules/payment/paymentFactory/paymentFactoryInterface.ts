import PaymentInstrument from "../../database/entities/paymentInstrument.entity";
import PaymentTx from "../../database/entities/paymentTx.entity";
import Tx from "../../database/entities/tx.entity";
import { EnquiryResponse, PaymentGatewayResponse } from "../dto/paymentGatewayResponses.model";
import PaymentTxFromDocument from "../dto/paymentTxFromDocument.model";

/**
 * Service interface to be implemented by all payment gateway services
 */
export default interface paymentFactoryInterface {
  readonly extractPaymentTxInfoFromDocument: (document: PaymentGatewayResponse, options?: any) => PaymentTxFromDocument;
  readonly processCapture: (paymentTx: PaymentTx, total: number) => Promise<PaymentTx>;
  readonly processVoid: (paymentTxs: PaymentTx[]) => Promise<PaymentTx[]>;
  readonly processRefund: (paymentTx: PaymentTx, requestedBy?: string, customAmount?: number) => Promise<PaymentTx>;
  readonly doEnquiry: (paymentTx: PaymentTx) => Promise<EnquiryResponse>;
  readonly isStillProcessing: (paymentInfo: EnquiryResponse) => boolean;
  readonly findAllSalesInProcessingStatus: (paymentTxs: PaymentTx[]) => PaymentTx[];
  readonly processSale: (tx: Tx, paymentInstrument: PaymentInstrument) => Promise<PaymentTx>;
  readonly processAuth: (tx: Tx, paymentInstrument: PaymentInstrument) => Promise<PaymentTx>;
}

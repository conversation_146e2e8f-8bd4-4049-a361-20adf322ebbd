import { Controller, Get, Inject, Param, Query } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { InjectRepository } from "@nestjs/typeorm";
import { CloudEvent } from "firebase-functions/v2";
import { MessagePublishedData } from "firebase-functions/v2/pubsub";

import { EnquiryResponse } from "./dto/paymentGatewayResponses.model";
import { PaymentInformationStatus } from "./dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "./dto/paymentInformationType.dto";
import { paymentTxIdSchema } from "./dto/paymentRequest.dto";
import { GetPaymentQuery, getPaymentQuerySchema } from "./dto/paymentTx.dto";
import { PaymentService } from "./payment.service";
import { CampaignService } from "../campaign/campaign.service";
import PaymentTx from "../database/entities/paymentTx.entity";
import Tx from "../database/entities/tx.entity";
import TxTag from "../database/entities/txTag.entity";
import { PaymentTxRepository } from "../database/repositories/paymentTx.repository";
import { TxRepository } from "../database/repositories/tx.repository";
import { TxTagRepository } from "../database/repositories/txTag.repository";
import { TxDiscounts } from "../discount/dto/discount.dto";
import { PublishMessageForCaptureProcessingParams } from "../pubsub/dto/publishMessageForCaptureProcessingParams.dto";
import { PublishMessageForVoidProcessingParams } from "../pubsub/dto/publishMessageForVoidProcessingParams.dto";
import { PublishMessageForDirectSaleProcessingParams } from "../pubsub/dto/publishMessageToProcessSale.dto";
import { PubSubService } from "../pubsub/pubsub.service";
import { TxTagType } from "../transaction/dto/txTagType.dto";
import { TransactionFactoryService } from "../transaction/transactionFactory/transactionFactory.service";
import { CorrelationContext } from "../utils/context/decorators/correlation-context.decorator";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { errorBuilder } from "../utils/utils/error.utils";
import { apiTags } from "../utils/utils/swagger.utils";
import { UtilsService } from "../utils/utils.service";
import { JoiValidationPipe } from "../validation/validationPipe.service";

/**
 * Payment controller
 */
@ApiBearerAuth()
@Controller("payments")
@ApiTags(...apiTags.meter)
export class PaymentController {
  constructor(
    private readonly paymentService: PaymentService,
    private readonly transactionFactoryService: TransactionFactoryService,
    private readonly campaignService: CampaignService,
    @InjectRepository(TxTagRepository) private txTagRepository: TxTagRepository,
    @InjectRepository(TxRepository) private txRepository: TxRepository,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(UtilsService) private readonly utilsService: UtilsService,
    private pubsubService: PubSubService,
    @InjectRepository(PaymentTxRepository) private paymentTxRepository: PaymentTxRepository,
  ) {}

  /**
   * Get the paymentTx information
   * if raw=true then return the raw paymentTx information from payment source
   */
  @Get(":paymentTxId")
  @ApiOperation({ summary: "Get payment transaction by id" })
  @ApiResponse({ status: 200, description: "Get payment transaction by id" })
  async getPayment(
    @Param("paymentTxId", new JoiValidationPipe(paymentTxIdSchema.lowercase().required()))
    paymentTxId: string,
    @Query(new JoiValidationPipe(getPaymentQuerySchema)) query: GetPaymentQuery,
  ): Promise<EnquiryResponse | PaymentTx> {
    const { secondsTimeout = 0 } = query;
    const { result: paymentTx, elapsed } = await this.utilsService.retry.retryFct<PaymentTx>(
      () => this.paymentService.getPaymentTxById(paymentTxId),
      secondsTimeout,
    );

    if (query.raw) {
      const { result: paymentInfo } = await this.utilsService.retry.retryFct<EnquiryResponse>(
        () => this.paymentService.enquirePaymentTx(paymentTx),
        secondsTimeout - elapsed,
        (result, count) => this.paymentService.isStillProcessing(paymentTx.gateway, result),
      );
      return paymentInfo;
    }

    return paymentTx;
  }

  /**
   * process capture
   * @param tx: Tx
   * @returns paymentTx: PaymentTx
   */
  @CorrelationContext()
  async processCapture(event: CloudEvent<MessagePublishedData<PublishMessageForCaptureProcessingParams>>) {
    const data = event.data.message.json;
    this.logger.info("nest trigger: captureProcessing", { tx: data.tx.id });

    let tx: Tx = Tx.fromJson(data.tx, this.logger);

    const paymentTx = await this.paymentTxRepository.find({
      where: { tx: { id: tx.id } },
      relations: { tx: true },
      loadEagerRelations: false,
    });

    const txDiscounts = await this.campaignService.getTxDiscounts(tx);

    if (txDiscounts.thirdParty || txDiscounts.dash) {
      const { updatedTx, isDiscountMatch } = await this.transactionFactoryService.calculateDiscountsAndUpdateTx(
        tx,
        txDiscounts,
      );
      tx = updatedTx;
      if (!isDiscountMatch) {
        await this.txTagRepository.addTagsToTx(tx, [
          TxTag.createTxTag(TxTagType.DISCOUNT_MISMATCH, tx.id, "SYSTEM", "Discount mismatch"),
        ]);
      }
    }

    const isCalcCorrect = this.transactionFactoryService.isTxCalculationCorrect(tx);

    if (!isCalcCorrect.result || tx.total === undefined) {
      await this.txTagRepository.addTagsToTx(tx, [
        TxTag.createTxTag(TxTagType.AMOUNT_MISMATCH, tx.id, "SYSTEM", isCalcCorrect.reason),
      ]);
      await this.transactionFactoryService.postPaymentProcess(tx, false);
      await this.campaignService.resetOrFailDiscounts(txDiscounts);
      throw errorBuilder.transaction.incorrectCalculation(tx.id);
    }

    // Special case: if transaction is 0 and has discount skip capture
    if (tx.total == 0 && tx.discount) {
      await this.processVoidAfterNoCaptureNecessary(tx);
      await this.processNoPaymentNecessary(tx, txDiscounts);
      return;
    }

    const lastSuccessAuthWithoutCapture = this.paymentService.findLastSuccessAuthWithoutCaptureOrVoid(paymentTx);

    if (!lastSuccessAuthWithoutCapture) {
      const reason = "No last success auth without capture found";
      this.logger.info(reason, { tx: tx.id });
      await this.txTagRepository.addTagsToTx(tx, [
        TxTag.createTxTag(TxTagType.UNABLE_TO_CAPTURE, tx.id, "SYSTEM", reason),
      ]);
      await this.transactionFactoryService.postPaymentProcess(tx, false);
      await this.campaignService.resetOrFailDiscounts(txDiscounts);
      return;
    }

    let capturedPaymentTx;
    try {
      capturedPaymentTx = await this.paymentService.processCapture(lastSuccessAuthWithoutCapture, tx.total);
    } catch (error: any) {
      await this.txTagRepository.addTagsToTx(tx, [
        TxTag.createTxTag(TxTagType.UNABLE_TO_CAPTURE, tx.id, "SYSTEM", error?.message ?? "UNKNOWN"),
      ]);
      await this.transactionFactoryService.postPaymentProcess(tx, false);
      await this.campaignService.resetOrFailDiscounts(txDiscounts);
      throw error;
    }

    if (
      capturedPaymentTx.type === PaymentInformationType.CAPTURE &&
      capturedPaymentTx.status === PaymentInformationStatus.SUCCESS
    ) {
      await this.transactionFactoryService.postPaymentProcess(tx, true);
      await this.campaignService.redeemDiscounts(txDiscounts);
      if (this.transactionFactoryService.hasCustomerInformation(tx)) {
        await this.transactionFactoryService.sendReceipt(tx, capturedPaymentTx);
      }
      await this.processVoidAfterCaptureSuccess(tx, capturedPaymentTx);
    } else {
      await this.txTagRepository.addTagsToTx(tx, [
        TxTag.createTxTag(
          TxTagType.UNABLE_TO_CAPTURE,
          tx.id,
          "SYSTEM",
          "Can't proceed to postPayment or sendReceipt as last paymentTx is not CAPTURE SUCCESS",
        ),
      ]);
      await this.transactionFactoryService.postPaymentProcess(tx, false);
      await this.campaignService.resetOrFailDiscounts(txDiscounts);
    }

    return capturedPaymentTx;
  }

  /**
   * process capture
   * @param tx: Tx
   * @returns paymentTx: PaymentTx
   */
  @CorrelationContext()
  async processPostSale(event: CloudEvent<MessagePublishedData<PublishMessageForCaptureProcessingParams>>) {
    const data = event.data.message.json;
    this.logger.info("nest trigger: postSaleProcessing", { tx: data.tx.id });

    const tx: Tx = Tx.fromJson(data.tx, this.logger);

    await this.transactionFactoryService.prePaymentProcess(tx);

    const txDiscounts = await this.campaignService.getTxDiscounts(tx);

    if (txDiscounts.thirdParty || txDiscounts.dash) {
      const { isDiscountMatch, discountAmountFromFirestore } =
        await this.transactionFactoryService.calculateDiscountsAndUpdateTx(tx, txDiscounts);
      if (!isDiscountMatch) {
        await this.txTagRepository.addTagsToTx(tx, [
          TxTag.createTxTag(TxTagType.DISCOUNT_MISMATCH, tx.id, "SYSTEM", "Discount mismatch"),
        ]);
        if (discountAmountFromFirestore !== undefined && discountAmountFromFirestore < 0) {
          await this.campaignService.redeemDiscounts(txDiscounts);
        } else {
          await this.campaignService.resetOrFailDiscounts(txDiscounts);
        }
      } else {
        await this.campaignService.redeemDiscounts(txDiscounts);
      }
    }

    const paymentTx = tx.paymentTx ?? [];

    const lastSuccessSaleWithoutVoid = this.paymentService.findLastSuccessSaleWithoutVoid(paymentTx);
    if (lastSuccessSaleWithoutVoid) {
      await this.transactionFactoryService.postPaymentProcess(tx, true);
      if (this.transactionFactoryService.hasCustomerInformation(tx)) {
        await this.transactionFactoryService.sendReceipt(tx, lastSuccessSaleWithoutVoid);
      }
      await this.processVoidAndSaleAfterSaleSuccess(tx, lastSuccessSaleWithoutVoid);
    }
    return lastSuccessSaleWithoutVoid;
  }

  /**
   * void previous auth
   * @param tx: Tx
   * @returns paymentTxs: PaymentTx[]
   */
  @CorrelationContext()
  async processVoid(event: CloudEvent<MessagePublishedData<PublishMessageForVoidProcessingParams>>) {
    const data = event.data.message.json;
    this.logger.info("nest trigger: voidProcessing", { tx: data.tx.id });

    const tx: Tx = Tx.fromJson(data.tx, this.logger);
    const paymentTxs = data.paymentTxs.map((paymentTx) => PaymentTx.fromJson(paymentTx, tx.id, this.logger));

    return this.paymentService.processAuthsToVoid(tx, paymentTxs);
  }

  /**
   * void previous auth after no capture necessary
   * @param tx
   * @returns
   */
  async processVoidAfterNoCaptureNecessary(tx: Tx) {
    const authsNeedToVoid = this.paymentService.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(tx.paymentTx, true);

    return this.paymentService.processAuthsToVoid(tx, authsNeedToVoid);
  }

  /**
   * void previous auth after capture success
   * @param tx: Tx
   * @returns paymentTxs: PaymentTx[]
   */
  async processVoidAfterCaptureSuccess(tx: Tx, capturedPaymentTx: PaymentTx) {
    tx.paymentTx?.push(capturedPaymentTx);

    const authsNeedToVoid = this.paymentService.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(tx.paymentTx, true);

    return this.paymentService.processAuthsToVoid(tx, authsNeedToVoid);
  }

  /**
   * void previous auth/sale after sale success
   * @param tx: Tx
   * @returns paymentTxs: PaymentTx[]
   */
  async processVoidAndSaleAfterSaleSuccess(tx: Tx, salePaymentTx: PaymentTx) {
    const paymentTxs = tx.paymentTx ?? [];
    const index = paymentTxs.indexOf(salePaymentTx);
    if (index !== -1) {
      paymentTxs.splice(index, 1);
    }
    const authsNeedToVoid = this.paymentService.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(paymentTxs, true);

    return this.paymentService.processAuthsToVoid(tx, authsNeedToVoid);
  }

  /**
   * process direct sale
   * @param txId string
   * @param paymentInstrumentId string
   * @param overwriteAmount number
   */
  @CorrelationContext()
  async processDirectSale(event: CloudEvent<MessagePublishedData<PublishMessageForDirectSaleProcessingParams>>) {
    const data = event.data.message.json;
    let foundTx = await this.txRepository.findOne({
      where: { id: data.txId },
      relations: ["paymentTx", "txApp"],
    });

    if (!foundTx) {
      throw errorBuilder.transaction.notFound(data.txId);
    }

    this.transactionFactoryService.prePaymentProcess(foundTx);

    const txDiscounts = await this.campaignService.getTxDiscounts(foundTx);

    if (txDiscounts.thirdParty || txDiscounts.dash) {
      const { updatedTx, isDiscountMatch } = await this.transactionFactoryService.calculateDiscountsAndUpdateTx(
        foundTx,
        txDiscounts,
      );
      foundTx = updatedTx;
      if (!isDiscountMatch) {
        await this.txTagRepository.addTagsToTx(foundTx, [
          TxTag.createTxTag(TxTagType.DISCOUNT_MISMATCH, foundTx.id, "SYSTEM", "Discount mismatch"),
        ]);
      }
    }

    const isCalcCorrect = this.transactionFactoryService.isTxCalculationCorrect(foundTx, true);
    this.logger.debug("isCalcCorrect", { tx: foundTx.id, isCalcCorrect, foundTx });
    if (!isCalcCorrect.result || foundTx.total === undefined) {
      await this.txTagRepository.addTagsToTx(foundTx, [
        TxTag.createTxTag(TxTagType.AMOUNT_MISMATCH, foundTx.id, "SYSTEM", isCalcCorrect.reason),
      ]);
      await this.transactionFactoryService.postPaymentProcess(foundTx, false);
      await this.campaignService.resetOrFailDiscounts(txDiscounts);
      throw errorBuilder.transaction.incorrectCalculation(foundTx.id);
    }

    // Special case: if transaction is 0 and has discount skip capture
    if (foundTx.total == 0 && foundTx.discount) {
      await this.processNoPaymentNecessary(foundTx, txDiscounts);
      return;
    }

    let soldPaymentTx;
    try {
      soldPaymentTx = await this.paymentService.processSale(foundTx, data.paymentInstrumentId, data.overwriteAmount);
    } catch (error: any) {
      await this.txTagRepository.addTagsToTx(foundTx, [
        TxTag.createTxTag(TxTagType.UNABLE_TO_SELL, foundTx.id, "SYSTEM", error?.message ?? "UNKNOWN"),
      ]);
      await this.transactionFactoryService.postPaymentProcess(foundTx, false);
      await this.campaignService.resetOrFailDiscounts(txDiscounts);
      throw error;
    }
    if (soldPaymentTx.status === PaymentInformationStatus.SUCCESS) {
      const paymentTxsToVoid = await this.paymentService.findAllPreviousAuthAndSaleWithoutCaptureAndVoid(
        foundTx.paymentTx,
        true,
      );

      if (paymentTxsToVoid?.length > 0) {
        await this.pubsubService.publishMessageForVoidProcessing({ tx: foundTx, paymentTxs: paymentTxsToVoid });
      }

      await this.transactionFactoryService.postPaymentProcess(foundTx, true);
      await this.campaignService.redeemDiscounts(txDiscounts);
    }
    return soldPaymentTx;
  }

  /**
   * process no payment necessary
   */
  async processNoPaymentNecessary(tx: Tx, txDiscounts: TxDiscounts) {
    await this.transactionFactoryService.prePaymentProcess(tx);
    await this.transactionFactoryService.postPaymentProcess(tx, true);
    await this.campaignService.redeemDiscounts(txDiscounts);
    // Add tx tag for no payment necessary
    await this.txTagRepository.addTagsToTx(tx, [
      TxTag.createTxTag(TxTagType.NO_PAYMENT_NEEDED, tx.id, "SYSTEM", "No payment necessary"),
    ]);
  }
}

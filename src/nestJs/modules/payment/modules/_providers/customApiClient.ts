// eslint-disable-next-line @typescript-eslint/no-var-requires
const { ApiClient } = require("cybersource-rest-client");

export class CustomApiClient extends ApiClient {
  async callApi(
    path: string,
    httpMethod: string,
    pathParams: any,
    queryParams: any,
    headerParams: any,
    formParams: any,
    bodyParam: any,
    authNames: any,
    contentTypes: any,
    accepts: any,
    returnType: any,
    callback: any,
  ) {
    const response = await super.callApi(
      path,
      httpMethod,
      pathParams,
      queryParams,
      headerParams,
      formParams,
      bodyParam,
      authNames,
      contentTypes,
      accepts,
      returnType,
      callback,
    );

    // Log the headers and body after the request is sent
    console.log("[CustomApiClient] callApi Parameters:", {
      path,
      httpMethod,
      pathParams,
      queryParams,
      headerParams,
      formParams,
      bodyParam: JSON.stringify(bodyParam, null, 2),
      authNames,
      contentTypes,
      accepts,
      returnType,
    });

    return response;
  }
}

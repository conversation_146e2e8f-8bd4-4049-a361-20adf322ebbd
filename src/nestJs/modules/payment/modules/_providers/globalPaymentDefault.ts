import { ConfigService } from "@nestjs/config";
import cybersourceRest<PERSON><PERSON> from "cybersource-rest-client";

import { CustomApiClient } from "./customApiClient";

/**
 * GlobalPaymentDefault Class
 */
export default class GlobalPaymentDefault {
  apiClient: cybersourceRestApi.ApiClient;
  config: cybersourceRestApi.ApiConfig;

  constructor(readonly configService: ConfigService) {
    this.configService = configService;
    // this.apiClient = new cybersourceRestApi.ApiClient();
    this.apiClient = new CustomApiClient();
    const AuthenticationType = "http_signature";
    const RunEnvironment = `${this.configService.getOrThrow("GLOBAL_PAYMENT_ENV")}.cybersource.com`;
    const MerchantId = this.configService.getOrThrow("GLOBAL_PAYMENT_MERCHANT_ID");

    // http_signature parameters
    const MerchantKeyId = this.configService.getOrThrow("GLOBAL_PAYMENT_MERCHANT_KEY_ID");
    const MerchantSecretKey = this.configService.getOrThrow("GLOBAL_PAYMENT_MERCHANT_KEY_SECRET");

    // jwt parameters
    const KeysDirectory = "Resource";
    const KeyFileName = this.configService.getOrThrow("GLOBAL_PAYMENT_KEY_FILENAME");
    const KeyAlias = this.configService.getOrThrow("GLOBAL_PAYMENT_KEY_FILENAME");
    const KeyPass = this.configService.getOrThrow("GLOBAL_PAYMENT_KEY_FILENAME");

    //meta key parameters
    const UseMetaKey = false;
    const PortfolioID = "";

    // logging parameters
    const EnableLog = true;
    const LogFileName = "cybs";
    const LogDirectory = "log";
    const LogfileMaxSize = "5242880"; //10 MB In Bytes
    const EnableMasking = true;

    /*
    PEM Key file path for decoding JWE Response Enter the folder path where the .pem file is located.
    It is optional property, require adding only during JWE decryption.
    */
    const PemFileDirectory = "Resource/NetworkTokenCert.pem";
    this.config = {
      authenticationType: AuthenticationType,
      runEnvironment: RunEnvironment,

      merchantID: MerchantId,
      merchantKeyId: MerchantKeyId,
      merchantsecretKey: MerchantSecretKey,

      keyAlias: KeyAlias,
      keyPass: KeyPass,
      keyFileName: KeyFileName,
      keysDirectory: KeysDirectory,

      useMetaKey: UseMetaKey,
      portfolioID: PortfolioID,
      pemFileDirectory: PemFileDirectory,

      logConfiguration: {
        enableLog: EnableLog,
        logFileName: LogFileName,
        logDirectory: LogDirectory,
        logFileMaxSize: LogfileMaxSize,
        loggingLevel: "debug",
        enableMasking: EnableMasking,
      },
    };
  }
}

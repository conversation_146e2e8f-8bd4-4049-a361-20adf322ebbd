import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import cybersourceRestApi from "cybersource-rest-client";

import PaymentInstrument from "../../../../../database/entities/paymentInstrument.entity";
import PaymentTx from "../../../../../database/entities/paymentTx.entity";
import Tx from "../../../../../database/entities/tx.entity";
import User from "../../../../../database/entities/user.entity";
import {
  CreatePaymentInstrumentBody,
  UpdatePaymentInstrumentBody,
} from "../../../../../me/modules/mePaymentInstrument/mePaymentInstrument.dto";
import { errorBuilder } from "../../../../../utils/utils/error.utils";
import {
  CheckPayerAuthEnrollmentResponse,
  PaymentInstrumentState,
  PaymentInstrumentType,
  ValidateAuthenticationResultResponse,
} from "../../dto/paymentInstrument.dto";
import { ThreeDSecureOptions } from "../globalPayment/dto/globalPayment.dto";
import PaymentInstrumentInterface from "../paymentInstrumentInterface";

/**
 * Manual service
 */
@Injectable()
export class ManualService implements PaymentInstrumentInterface {
  configService: ConfigService<Record<string, unknown>, false>;

  async createPaymentInstrumentIdentifier(content: CreatePaymentInstrumentBody) {
    throw errorBuilder.factory.notImplemented("ManualService.createPaymentInstrument");
    return {
      instrumentIdentifier: "manual",
      state: PaymentInstrumentState.INACTIVE,
      raw: {} as cybersourceRestApi.CreatePaymentInstrumentIdentifierResponse,
    };
  }

  async createPaymentInstrument(
    paymentInstrument: { cardType: PaymentInstrumentType; instrumentIdentifier: string },
    content: CreatePaymentInstrumentBody | UpdatePaymentInstrumentBody,
    user: User,
  ) {
    throw errorBuilder.factory.notImplemented("ManualService.createPaymentInstrument");
    return {
      token: "manual",
      state: PaymentInstrumentState.INACTIVE,
      raw: {} as cybersourceRestApi.CreatePaymentInstrumentResponse,
    };
  }

  async deletePaymentInstrument(paymentInstrument: PaymentInstrument, user: User): Promise<PaymentInstrument> {
    throw errorBuilder.factory.notImplemented("ManualService.deletePaymentInstrument");
    return paymentInstrument;
  }

  async createNewPaymentInstrument(content: CreatePaymentInstrumentBody, user: User): Promise<PaymentInstrument> {
    throw errorBuilder.factory.notImplemented("ManualService.createPaymentInstrument");
  }

  async updateNewPaymentInstrument(
    paymentInstrumentId: string,
    content: UpdatePaymentInstrumentBody,
    user: User,
  ): Promise<PaymentInstrument> {
    throw errorBuilder.factory.notImplemented("ManualService.updateNewPaymentInstrument");
  }

  async initiate3DSecure(content: PaymentInstrument) {
    throw errorBuilder.factory.notImplemented("ManualService.initiate3DSecure");
    return {
      accessToken: "",
      referenceId: "",
      deviceDataCollectionUrl: "",
      status: "",
    };
  }

  async createPayment(tx: Tx, paymentInstrument: PaymentInstrument) {
    throw errorBuilder.factory.notImplemented("ManualService.createSale");
    return new PaymentTx();
  }

  async createPaymentWith3DS(tx: Tx, paymentInstrument: PaymentInstrument, threeDSecureOptions: ThreeDSecureOptions) {
    throw errorBuilder.factory.notImplemented("ManualService.createPaymentWith3DS");
    return {
      id: "",
      accessToken: "",
      stepUpUrl: "",
      status: "",
      raw: {},
    };
  }

  async validateAuthenticationResults(content: PaymentInstrument): Promise<ValidateAuthenticationResultResponse> {
    throw errorBuilder.factory.notImplemented("ManualService.validateAuthenticationResults");
    return {
      status: PaymentInstrumentState.VERIFICATION_FAILED,
      raw: {},
    };
  }

  async createAuthWithPayerAuthValidation(tx: Tx, content: PaymentInstrument): Promise<PaymentTx> {
    throw errorBuilder.factory.notImplemented("ManualService.createAuthWithPayerAuthValidation");
    return new PaymentTx();
  }

  async checkPayerAuthEnrollment(content: PaymentInstrument, user: User): Promise<CheckPayerAuthEnrollmentResponse> {
    throw errorBuilder.factory.notImplemented("ManualService.checkPayerAuthEnrollment");
    return {
      isPayerAuthEnroled: false,
      raw: {},
    };
  }
}

import { Module, forwardRef } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { AppDatabaseModule } from "@nest/modules/appDatabase/appDatabase.module";
import { PaymentInstrumentRepository } from "@nest/modules/database/repositories/paymentInstument.repository";

import { GlobalPaymentController } from "./globalPayment.controller";
import { GlobalPaymentService } from "./globalPayment.service";
import { PaymentTxRepository } from "../../../../../database/repositories/paymentTx.repository";
import LoggerServiceAdapter from "../../../../../utils/logger/logger.service";
import { PaymentInstrumentModule } from "../../paymentInstrument.module";
import { KrakenModule } from "../kraken/kraken.module";

/**
 * GlobalPayment module
 */
@Module({
  providers: [GlobalPaymentService, GlobalPaymentController, PaymentTxRepository, PaymentInstrumentRepository],
  controllers: [GlobalPaymentController],
  imports: [
    ConfigModule,
    forwardRef(() => PaymentInstrumentModule),
    LoggerServiceAdapter,
    KrakenModule,
    AppDatabaseModule,
  ],
  exports: [GlobalPaymentService, GlobalPaymentController],
})
export class GlobalPaymentModule {}

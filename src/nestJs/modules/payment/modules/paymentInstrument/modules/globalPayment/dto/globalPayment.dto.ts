import { PaymentInstrumentState } from "../../../dto/paymentInstrument.dto";

export class GlobalPaymentNotificationBody {
  MD: string;
  TransactionId: string;
}

export const payerAuthenticationStatusMapping: Record<
  string,
  | PaymentInstrumentState.VERIFICATION_FAILED
  | PaymentInstrumentState.VERIFICATION_PENDING
  | PaymentInstrumentState.VERIFIED
> = {
  AUTHENTICATION_FAILED: PaymentInstrumentState.VERIFICATION_FAILED,
  PENDING_AUTHENTICATION: PaymentInstrumentState.VERIFICATION_PENDING,
  AUTHENTICATION_SUCCESSFUL: PaymentInstrumentState.VERIFIED,
};

export type ThreeDSecureOptions = {
  sessionId: string;
  ipAddress: string;
  httpBrowserScreenHeight: string;
  httpBrowserScreenWidth: string;
};

export const PAYMENT_MERCHANT_INFORMATION_MERCHANT_DESCRIPTOR_AUTH: string = "DASH *PENDING*";

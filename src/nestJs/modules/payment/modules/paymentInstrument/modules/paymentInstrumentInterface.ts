import { ConfigService } from "@nestjs/config";

import { ThreeDSecureOptions } from "./globalPayment/dto/globalPayment.dto";
import PaymentInstrument from "../../../../database/entities/paymentInstrument.entity";
import PaymentTx from "../../../../database/entities/paymentTx.entity";
import Tx from "../../../../database/entities/tx.entity";
import User from "../../../../database/entities/user.entity";
import {
  CreatePaymentInstrumentBody,
  UpdatePaymentInstrumentBody,
} from "../../../../me/modules/mePaymentInstrument/mePaymentInstrument.dto";
import {
  PaymentInstrumentState,
  Initiate3DSecureResponse,
  CreatePaymentResponse,
  ValidateAuthenticationResultResponse,
  CheckPayerAuthEnrollmentResponse,
  PaymentInstrumentType,
} from "../dto/paymentInstrument.dto";

/**
 * PaymentInstrument Interface
 */
export default interface PaymentInstrumentInterface {
  configService: ConfigService;

  readonly createPaymentInstrumentIdentifier: (
    content: CreatePaymentInstrumentBody,
  ) => Promise<{ instrumentIdentifier: string; state: PaymentInstrumentState }>;

  readonly deletePaymentInstrument: (paymentInstrument: PaymentInstrument, user: User) => Promise<PaymentInstrument>;

  readonly createPaymentInstrument: (
    paymentInstrument: { cardType: PaymentInstrumentType; instrumentIdentifier: string },
    content: CreatePaymentInstrumentBody | UpdatePaymentInstrumentBody,
    user: User,
  ) => Promise<{ token: string; state: PaymentInstrumentState }>;

  readonly createNewPaymentInstrument: (content: CreatePaymentInstrumentBody, user: User) => Promise<PaymentInstrument>;
  readonly updateNewPaymentInstrument: (
    paymentInstrumentId: string,
    content: UpdatePaymentInstrumentBody,
    user: User,
  ) => Promise<PaymentInstrument>;

  readonly initiate3DSecure: (content: PaymentInstrument) => Promise<Initiate3DSecureResponse>;
  readonly createPayment: (tx: Tx, paymentInstrument: PaymentInstrument) => Promise<PaymentTx>;
  readonly createPaymentWith3DS: (
    tx: Tx,
    paymentInstrument: PaymentInstrument,
    threeDSecureOptions: ThreeDSecureOptions,
    isSale?: boolean,
  ) => Promise<CreatePaymentResponse>;
  readonly validateAuthenticationResults: (content: PaymentInstrument) => Promise<ValidateAuthenticationResultResponse>;
  readonly createAuthWithPayerAuthValidation: (tx: Tx, content: PaymentInstrument) => Promise<PaymentTx>;

  readonly checkPayerAuthEnrollment: (
    paymentInstrument: PaymentInstrument,
    user: User,
  ) => Promise<CheckPayerAuthEnrollmentResponse>;
}

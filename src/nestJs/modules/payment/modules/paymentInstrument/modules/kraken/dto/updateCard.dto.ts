import { ApiProperty } from "@nestjs/swagger";
import Jo<PERSON> from "joi";

import { CardState, CardType, cardStateSchema, cardTypeSchema } from "./card.dto";

export class UpdateCardParams {
  @ApiProperty({ example: "5a21d2ba-25ad-44ed-82b6-e941ba628356" })
  token: string;
}

export class UpdateCardBody {
  @ApiProperty({ example: "****************" })
  cardNumber?: string;

  @ApiProperty({ example: "31" })
  expirationYear?: string;

  @ApiProperty({ example: "01" })
  expirationMonth?: string;

  @ApiProperty({ example: "Goku" })
  cardHolderName?: string;

  @ApiProperty({ example: "777" })
  cvv?: string;

  @ApiProperty({ example: CardType.VISA })
  type?: CardType;

  @ApiProperty({ example: CardState.NOT_VERIFIED })
  state?: CardState;
}

export const updateCardParamsSchema = Joi.object<UpdateCardParams>({
  token: Joi.string().required(),
});

export const updateCardBodySchema = Joi.object<UpdateCardBody>({
  cardNumber: Joi.string(),
  expirationYear: Joi.string(),
  expirationMonth: Joi.string(),
  cardHolderName: Joi.string(),
  cvv: Joi.string(),
  type: cardTypeSchema,
  state: cardStateSchema,
});

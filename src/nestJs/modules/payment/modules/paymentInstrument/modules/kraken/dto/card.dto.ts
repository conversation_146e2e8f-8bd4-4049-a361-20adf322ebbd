import Joi from "joi";

import { PaymentInstrumentState } from "../../../dto/paymentInstrument.dto";

export enum CardType {
  VISA = "VISA",
  MASTERCARD = "MASTERCARD",
}
export const cardTypeSchema = Joi.string().valid(...Object.values(CardType));

export enum CardState {
  NOT_VERIFIED = "NOT_VERIFIED",
  VERIFIED = "VERIFIED",
}
export const cardStateSchema = Joi.string().valid(...Object.values(CardState));

export type KrakenCardPublic = { token: string; state: PaymentInstrumentState };

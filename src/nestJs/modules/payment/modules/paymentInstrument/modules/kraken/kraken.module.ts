import { Module, forwardRef } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { PaymentInstrumentRepository } from "@nest/modules/database/repositories/paymentInstument.repository";

import { KrakenApi } from "./kraken.api";
import { KrakenService } from "./kraken.service";
import { PaymentTxRepository } from "../../../../../database/repositories/paymentTx.repository";
import LoggerServiceAdapter from "../../../../../utils/logger/logger.service";
import { PaymentInstrumentModule } from "../../paymentInstrument.module";

/**
 * Kraken module
 */
@Module({
  providers: [KrakenService, KrakenApi, PaymentTxRepository, PaymentInstrumentRepository],
  imports: [ConfigModule, forwardRef(() => PaymentInstrumentModule), LoggerServiceAdapter],
  exports: [KrakenService],
})
export class KrakenModule {}

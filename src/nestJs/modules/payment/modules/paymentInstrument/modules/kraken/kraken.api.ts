import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import axios from "axios";

import { errorBuilder, isKrakenError } from "@nest/modules/utils/utils/error.utils";

import { CaptureBody } from "./dto/capture.dto";
import { KrakenCardPublic } from "./dto/card.dto";
import { DeleteCardParams } from "./dto/deleteCard.dto";
import { PaymentBody } from "./dto/payment.dto";
import { RefundBody } from "./dto/refund.dto";
import { SaveCardBody } from "./dto/saveCard.dto";
import { KrakenTransactionPublic } from "./dto/transaction.dto";
import { UpdateCardBody, UpdateCardParams } from "./dto/updateCard.dto";
import { ValidateAuthenticationResultsBody } from "./dto/validateAuthenticationResults.dto";

@Injectable()
export class KrakenApi {
  krakenApiUrl: string;
  retry = 3;
  headers = {};

  /**
   * Creates an instance of KrakenApi.
   * Should only be used in krakenservice.
   * @param configService - Provides application configuration.
   */
  constructor(private readonly configService: ConfigService) {
    this.krakenApiUrl = this.configService.getOrThrow("KRAKEN_API_URL");
    this.headers = {
      "Content-Type": "application/json",
      "x-api-key": this.configService.getOrThrow("KRAKEN_API_KEY"),
    };
  }

  /**
   * Handles errors from Kraken.
   * Should only be used in krakenservice.
   * @param error - Error object from the HTTP request.
   * @throws An error formatted by errorBuilder.kraken.
   */
  private krakenErrorHandler(error: any): never {
    if (isKrakenError(error)) {
      throw errorBuilder.kraken.api(error);
    }
    throw errorBuilder.kraken.unexpected(error);
  }

  /**
   * Saves a new card to the Kraken API.
   * Should only be used in krakenservice.
   * @param card - The card details to save.
   * @returns The saved card details.
   */
  saveCard(card: SaveCardBody) {
    return axios
      .post<KrakenCardPublic>(`${this.krakenApiUrl}/card`, card, {
        headers: this.headers,
      })
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Updates an existing card in the Kraken API.
   * Should only be used in krakenservice.
   * @param params - The parameters containing the card token.
   * @param card - The updated card details.
   * @returns The updated card details.
   */
  updateCard(params: UpdateCardParams, card: UpdateCardBody) {
    return axios
      .patch<KrakenCardPublic>(`${this.krakenApiUrl}/card/${params.token}`, card, {
        headers: this.headers,
      })
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Deletes a card from the Kraken API.
   * Should only be used in krakenservice.
   * @param params - The parameters containing the card token.
   * @returns The deleted card details.
   */
  deleteCard(params: DeleteCardParams) {
    return axios
      .delete<KrakenCardPublic>(`${this.krakenApiUrl}/card/${params.token}`, {
        headers: this.headers,
      })
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Processes a sale transaction through the Kraken API.
   * Should only be used in krakenservice.
   * @param body - The sale transaction details.
   * @returns The transaction details.
   */
  public async sale(body: PaymentBody) {
    return axios
      .post<KrakenTransactionPublic>(`${this.krakenApiUrl}/payment/sale`, body, {
        headers: this.headers,
      })
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Authorizes a payment through the Kraken API.
   * Should only be used in krakenservice.
   * @param body - The authorization details.
   * @returns The transaction details.
   */
  public async auth(body: PaymentBody) {
    return axios
      .post<KrakenTransactionPublic>(`${this.krakenApiUrl}/payment/auth`, body, {
        headers: this.headers,
      })
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Captures a previously authorized transaction through the Kraken API.
   * Should only be used in krakenservice.
   * @param transactionId - The ID of the transaction to capture.
   * @param body - The capture details.
   * @returns The transaction details.
   */
  public async capture(transactionId: string, body: CaptureBody) {
    return axios
      .post<KrakenTransactionPublic>(`${this.krakenApiUrl}/payment/${transactionId}/capture`, body, {
        headers: this.headers,
      })
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Enquires about a transaction through the Kraken API.
   * Should only be used in krakenservice.
   * @param transactionId - The ID of the transaction to enquire about.
   * @returns The transaction details.
   */
  public async enquire(transactionId: string) {
    return axios
      .get<KrakenTransactionPublic>(`${this.krakenApiUrl}/payment/${transactionId}/enquire`, {
        headers: this.headers,
      })
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Searches for a transaction through the Kraken API.
   * @param transactionId - The ID of the transaction to search for.
   * @returns The transaction details.
   */
  public async search(transactionId: string) {
    return axios
      .get<KrakenTransactionPublic>(`${this.krakenApiUrl}/payment/${transactionId}/search`, {
        headers: this.headers,
      })
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Refunds a transaction through the Kraken API.
   * Should only be used in krakenservice.
   * @param transactionId - The ID of the transaction to refund.
   * @param body - The refund details.
   * @returns The transaction details.
   */
  public async refund(transactionId: string, body: RefundBody) {
    return axios
      .post<KrakenTransactionPublic>(`${this.krakenApiUrl}/payment/${transactionId}/refund/`, body, {
        headers: this.headers,
      })
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Voids a transaction through the Kraken API.
   * Should only be used in krakenservice.
   * @param transactionId - The ID of the transaction to void.
   * @returns The transaction details.
   */
  public async void(transactionId: string) {
    return axios
      .post<KrakenTransactionPublic>(
        `${this.krakenApiUrl}/payment/${transactionId}/void`,
        { reason: "voiding" },
        { headers: this.headers },
      )
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Sets up payer authentication.
   * Should only be used in krakenservice.
   * @param cardToken - The card token for setting up payer auth.
   * @returns The transaction details for payer auth setup.
   */
  public async payerAuthSetup(cardToken: string) {
    return axios
      .post<KrakenTransactionPublic>(
        `${this.krakenApiUrl}/card/${cardToken}/payer-auth-setup`,
        {},
        { headers: this.headers },
      )
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Checks payer authentication enrolment.
   * Should only be used in krakenservice.
   * @param cardToken - The card token for checking payer auth enrolment.
   * @returns The transaction details for payer auth enrolment check.
   */
  public async checkPayerAuthEnrolment(cardToken: string) {
    return axios
      .post<KrakenTransactionPublic>(
        `${this.krakenApiUrl}/card/${cardToken}/check-payer-auth-enrolment`,
        {},
        { headers: this.headers },
      )
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }

  /**
   * Validates authentication results for a transaction.
   * Should only be used in krakenservice.
   * @param transactionId - The identifier of the transaction.
   * @returns The transaction details for authentication results validation.
   */
  public async validateAuthenticationResults(transactionId: string, body: ValidateAuthenticationResultsBody) {
    return axios
      .post<KrakenTransactionPublic>(
        `${this.krakenApiUrl}/payment/${transactionId}/validate-authentication-results`,
        body,
        { headers: this.headers },
      )
      .then((response) => response.data)
      .catch(this.krakenErrorHandler);
  }
}

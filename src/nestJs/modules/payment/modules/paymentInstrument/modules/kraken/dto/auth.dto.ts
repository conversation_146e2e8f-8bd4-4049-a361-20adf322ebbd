import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export class AuthBody {
  @ApiProperty({ example: "YmZlNTMyZTMtMjdkNi00Y2ZlLWI5YTAtZDlhNzYzZDI2YmQ1" })
  cardToken: string;

  @ApiProperty({ example: 15 })
  amount: number;
}

export const authBodySchema = Joi.object<AuthBody>({
  cardToken: Joi.string().required(),
  amount: Joi.number().precision(1).required(),
});

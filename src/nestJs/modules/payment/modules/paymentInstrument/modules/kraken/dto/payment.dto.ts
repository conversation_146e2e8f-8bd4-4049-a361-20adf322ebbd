import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export class PaymentUnauthenticatedBody {
  @ApiProperty({ example: "YmZlNTMyZTMtMjdkNi00Y2ZlLWI5YTAtZDlhNzYzZDI2YmQ1" })
  cardToken: string;

  @ApiProperty({ example: 15 })
  amount: number;
}

export const paymentUnauthenticatedBodySchema = Joi.object<PaymentUnauthenticatedBody>({
  cardToken: Joi.string().required(),
  amount: Joi.number().required(),
});

export class PaymentValidateConsumerAuthenticationBody {
  @ApiProperty({ example: "YmZlNTMyZTMtMjdkNi00Y2ZlLWI5YTAtZDlhNzYzZDI2YmQ1" })
  cardToken: string;

  @ApiProperty({ example: 15 })
  amount: number;

  @ApiProperty({ example: "AA" })
  authenticationTransactionId: string;
}

export const paymentValidateConsumerAuthenticationBodySchema = Joi.object<PaymentValidateConsumerAuthenticationBody>({
  cardToken: Joi.string().required(),
  amount: Joi.number().required(),
  authenticationTransactionId: Joi.string().required(),
});

export class PaymentConsumerAuthenticationDeviceInformationBody {
  @ApiProperty({ example: "12.345.678.90" })
  ipAddress: string;

  @ApiProperty({ example: "850" })
  httpBrowserScreenHeight: string;

  @ApiProperty({ example: "500" })
  httpBrowserScreenWidth: string;
}

const paymentConsumerAuthenticationDeviceInformationBodySchema =
  Joi.object<PaymentConsumerAuthenticationDeviceInformationBody>({
    ipAddress: Joi.string().required(),
    httpBrowserScreenHeight: Joi.string().required(),
    httpBrowserScreenWidth: Joi.string().required(),
  });

export class PaymentConsumerAuthenticationBody {
  @ApiProperty({ example: "YmZlNTMyZTMtMjdkNi00Y2ZlLWI5YTAtZDlhNzYzZDI2YmQ1" })
  cardToken: string;

  @ApiProperty({ example: 15 })
  amount: number;

  @ApiProperty({ example: "2cb85048-a586-44b3-b3de-31f5b47ac584" })
  sessionId: string;

  @ApiProperty({
    example: "http://127.0.0.1:5001/dash-dev-81bb1/asia-east2/api/global-payments/3d-secure/notification",
  })
  returnUrl: string;

  @ApiProperty({ type: PaymentConsumerAuthenticationDeviceInformationBody })
  deviceInformation: PaymentConsumerAuthenticationDeviceInformationBody;
}

export const paymentConsumerAuthenticationBodySchema = Joi.object<PaymentConsumerAuthenticationBody>({
  cardToken: Joi.string().required(),
  amount: Joi.number().required(),
  sessionId: Joi.string().required(),
  returnUrl: Joi.string().required(),
  deviceInformation: paymentConsumerAuthenticationDeviceInformationBodySchema.required(),
});

export type PaymentBody =
  | PaymentUnauthenticatedBody
  | PaymentValidateConsumerAuthenticationBody
  | PaymentConsumerAuthenticationBody;

export const paymentBodyTypeSchema = Joi.alternatives().try(
  paymentUnauthenticatedBodySchema.required(),
  paymentValidateConsumerAuthenticationBodySchema.required(),
  paymentConsumerAuthenticationBodySchema.required(),
);

export const isPaymentUnauthenticated = (content: PaymentBody): content is PaymentUnauthenticatedBody =>
  !Object.prototype.hasOwnProperty.call(content, "authenticationTransactionId") &&
  !Object.prototype.hasOwnProperty.call(content, "sessionId");

export const isPaymentValidateConsumerAuthentication = (
  content: PaymentBody,
): content is PaymentValidateConsumerAuthenticationBody =>
  Object.prototype.hasOwnProperty.call(content, "authenticationTransactionId");

export const isPaymentConsumerAuthentication = (content: PaymentBody): content is PaymentConsumerAuthenticationBody =>
  Object.prototype.hasOwnProperty.call(content, "sessionId");

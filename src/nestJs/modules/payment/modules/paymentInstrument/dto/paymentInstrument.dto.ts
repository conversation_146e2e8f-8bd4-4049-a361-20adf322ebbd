import Joi from "joi";

export enum PaymentInstrumentState {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE", // SUSPENDED or DELETED. See https://developer.cybersource.com/docs/cybs/en-us/tms/developer/ctv/so/tms/tms-overview/tms-lcm.html
  VERIFICATION_FAILED = "VERIFICATION_FAILED",
  VERIFICATION_PENDING = "VERIFICATION_PENDING",
  VERIFIED = "VERIFIED",
}

export enum PaymentInstrumentType {
  VISA = "VISA",
  MASTERCARD = "MASTERCARD",
}

export const paymentInstrumentTypeSchema = Joi.string().valid(...Object.values(PaymentInstrumentType));

export interface CreatePaymentInstrumentIdentifierResponse {
  instrumentIdentifier: string;
  state: PaymentInstrumentState;
  raw: any;
}

export interface CreatePaymentInstrumentResponse {
  token: string;
  state: PaymentInstrumentState;
  raw: any;
}

export interface Initiate3DSecureResponse {
  accessToken: string;
  referenceId: string;
  deviceDataCollectionUrl: string;
  status: string;
}

export interface CreatePaymentResponse {
  id: string;
  accessToken: string;
  stepUpUrl: string;
  status: string;
  raw: any;
}

export interface ValidateAuthenticationResultResponse {
  status:
    | PaymentInstrumentState.VERIFICATION_PENDING
    | PaymentInstrumentState.VERIFIED
    | PaymentInstrumentState.VERIFICATION_FAILED;
  raw: any;
}

export class getPaymentInstrumentBody {
  instrumentIdentifier: string;
}

export class CheckPayerAuthEnrollmentResponse {
  isPayerAuthEnroled: boolean;
  raw: any;
}

import { Module, forwardRef } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { GlobalPaymentModule } from "./modules/globalPayment/globalPayment.module";
import { KrakenModule } from "./modules/kraken/kraken.module";
import { PaymentInstrumentService } from "./paymentInstrument.service";
import { AppDatabaseModule } from "../../../appDatabase/appDatabase.module";
import { TxAppRepository } from "../../../database/repositories/app.repository";
import { PaymentInstrumentRepository } from "../../../database/repositories/paymentInstument.repository";
import { PaymentTxRepository } from "../../../database/repositories/paymentTx.repository";
import { TxRepository } from "../../../database/repositories/tx.repository";
import { UserRepository } from "../../../database/repositories/user.repository";

/**
 * PaymentInstrument module
 */
@Module({
  providers: [
    PaymentInstrumentService,
    PaymentInstrumentRepository,
    UserRepository,
    AppDatabaseModule,
    TxAppRepository,
    TxRepository,
    PaymentTxRepository,
  ],
  imports: [forwardRef(() => GlobalPaymentModule), AppDatabaseModule, KrakenModule, ConfigModule],
  controllers: [],
  exports: [PaymentInstrumentService],
})
export class PaymentInstrumentModule {}

import { ApiProperty } from "@nestjs/swagger";
import <PERSON><PERSON> from "joi";

export class PaymentSaleBodyDto {
  @ApiProperty({ required: true })
  txId: string;

  @ApiProperty({ required: true })
  paymentInstrumentId: string;

  @ApiProperty({ required: false })
  amount?: number;
}

export const paymentSaleRequestSchema = Joi.object({
  txId: Joi.string().uuid().required(),
  paymentInstrumentId: Joi.string().uuid().required(),
  amount: Joi.number().precision(1).optional(),
});

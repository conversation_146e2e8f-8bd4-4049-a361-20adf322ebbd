import Joi from "joi";

/**
 * Payment Information Status enum
 */
export enum PaymentInformationStatus {
  SUCCESS = "SUCCESS",
  FAILURE = "FAILURE",
  REQUESTED = "REQUESTED",
  REQUEST_TIMEOUT = "REQUEST_TIMEOUT",
  PENDING = "PENDING",
  PROCESSING = "PROCESSING",
}

/**
 * Payment Information Status schema
 */
export const paymentInformationStatusSchema = Joi.string<PaymentInformationStatus>().valid(
  ...Object.values(PaymentInformationStatus),
);

import { ApiPropertyOptional } from "@nestjs/swagger";
import Jo<PERSON> from "joi";

import { PaymentGatewayResponse } from "./paymentGatewayResponses.model";
import { paymentGatewayTypesSchema } from "./paymentGatewayTypes.dto";
import { paymentInformationStatusSchema } from "./paymentInformationStatus.dto";
import { paymentInformationTypeSchema } from "./paymentInformationType.dto";
import PaymentTx from "../../database/entities/paymentTx.entity";
import { txSchema } from "../../transaction/dto/tx.dto";

export const paymentTxSchema: Joi.Schema<PaymentTx> = Joi.object<PaymentTx>({
  id: Joi.string().required(),
  amount: Joi.number().required(),
  gatewayTransactionId: Joi.string().required(),
  cardNumber: Joi.string().optional(),
  paymentMethod: Joi.string().optional(),
  tx: txSchema.optional(),
  gateway: paymentGatewayTypesSchema.optional(),
  gatewayResponse: Joi.object<PaymentGatewayResponse>().optional(),
  status: paymentInformationStatusSchema.optional(),
  type: paymentInformationTypeSchema.optional(),
  createdAt: Joi.date().optional(),
  requestedBy: Joi.string().optional(),
});

export type PaymentTxReceipt = {
  id: string;
  amount: number;
  gatewayTransactionId: string;
};

export const paymentTxReceiptSchema: Joi.Schema<PaymentTxReceipt> = Joi.object<PaymentTxReceipt>({
  id: Joi.string().required(),
  amount: Joi.number().precision(1).required(),
  gatewayTransactionId: Joi.string().required(),
}).unknown();

export class GetPaymentQuery {
  @ApiPropertyOptional()
  raw?: boolean;

  @ApiPropertyOptional()
  secondsTimeout?: number;
}

export const getPaymentQuerySchema = Joi.object({
  raw: Joi.boolean().optional(),
  secondsTimeout: Joi.number().positive().integer().optional(),
});

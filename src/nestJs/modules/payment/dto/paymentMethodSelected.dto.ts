import { FieldValue } from "firebase-admin/firestore";
import <PERSON><PERSON> from "joi";

export enum PaymentMethodSelected {
  VISA = "VISA",
  ALIPAY = "ALIPAY",
  CUP = "CUP",
  MASTERCARD = "MASTERCARD",
  WECHAT = "WECHAT",
}

export const paymentMethodSelectedSchema = Joi.string<PaymentMethodSelected>().valid(
  ...Object.values(PaymentMethodSelected),
);

export function isPaymentMethodSelected(value: PaymentMethodSelected | FieldValue): value is PaymentMethodSelected {
  return Object.values(PaymentMethodSelected).includes(value as PaymentMethodSelected);
}

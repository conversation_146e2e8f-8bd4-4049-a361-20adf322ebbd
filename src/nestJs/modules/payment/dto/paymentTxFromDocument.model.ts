import { PaymentGatewayResponse } from "./paymentGatewayResponses.model";
import { PaymentGatewayTypes } from "./paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "./paymentInformationStatus.dto";
import { PaymentInformationType } from "./paymentInformationType.dto";

/**
 * @typedef PaymentTxFromDocument PaymentTx from document
 */
type PaymentTxFromDocument = {
  id: string;
  gateway: PaymentGatewayTypes;
  createdAt: Date;
  type: PaymentInformationType;
  gatewayResponse?: PaymentGatewayResponse;
  status?: PaymentInformationStatus;
  amount?: number;
  gatewayTransactionId?: string;
  cardNumber?: string;
  paymentMethod?: string;
};

export default PaymentTxFromDocument;

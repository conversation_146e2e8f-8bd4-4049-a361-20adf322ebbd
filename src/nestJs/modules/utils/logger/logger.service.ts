import { Inject } from "@nestjs/common";

import ClsContextStorageService from "../context/clsContextStorage.service";
import loggerUtils from "../utils/logger.utils";

export default class LoggerServiceAdapter {
  public constructor(@Inject(ClsContextStorageService) private contextStorageService: ClsContextStorageService) {}

  public log(message: any, keys?: Record<string, any>) {
    return loggerUtils.debug(message, this.getMessageData(keys));
  }

  public error(message: any, keys?: Record<string, any>, error?: Error) {
    let errorToLog = new Error(message);
    if (error instanceof Error) {
      errorToLog = error;
    }
    return loggerUtils.error(message, this.getMessageData(keys), errorToLog);
  }

  public warn(message: any, keys?: Record<string, any>) {
    return loggerUtils.warn(message, this.getMessageData(keys));
  }

  public info(message: any, keys?: Record<string, any>) {
    return loggerUtils.info(message, this.getMessageData(keys));
  }

  public debug(message: any, keys?: Record<string, any>) {
    return loggerUtils.debug(message, this.getMessageData(keys));
  }

  public verbose(message: any, keys?: Record<string, any>) {
    return loggerUtils.debug(message, this.getMessageData(keys));
  }

  public getContextId(): string {
    return this.contextStorageService.getContextId();
  }

  public getContext() {
    return this.contextStorageService.getContext();
  }

  private getMessageData(keys?: Record<string, any>) {
    return {
      ...keys,
      ...this.contextStorageService.getContext(),
    };
  }
}

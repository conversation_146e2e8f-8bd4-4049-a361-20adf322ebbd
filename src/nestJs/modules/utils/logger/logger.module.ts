import { DynamicModule, Module } from "@nestjs/common";

import LoggerServiceAdapter from "./logger.service";

/**
 * Logger module
 */
@Module({
  providers: [LoggerServiceAdapter],
  exports: [LoggerServiceAdapter],
})
export class LoggerModule {
  /**
   * Logger module for root
   * @returns DynamicModule
   */
  static forRoot(): DynamicModule {
    return {
      global: true,
      module: LoggerModule,
      providers: [LoggerServiceAdapter],
      exports: [LoggerServiceAdapter],
    };
  }
}

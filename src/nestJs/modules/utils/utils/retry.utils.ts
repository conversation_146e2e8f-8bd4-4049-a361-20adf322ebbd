const retryFct = async <ResultT>(
  promise: () => Promise<ResultT>,
  secondsTimeout?: number,
  checkContinue?: (result: ResultT, count: number) => boolean,
  count = 0,
): Promise<{ result: ResultT; elapsed: number }> => {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(
      async () => {
        clearTimeout(timeout);
        try {
          const result = await promise();
          if (checkContinue && (await checkContinue(result, count)) && count < (secondsTimeout ?? 0)) {
            return resolve(await retryFct(promise, secondsTimeout, checkContinue, count + 1));
          }
          return resolve({ result, elapsed: count });
        } catch (error) {
          if (count >= (secondsTimeout ?? 0)) {
            return reject(error);
          }
          try {
            return resolve(await retryFct(promise, secondsTimeout, checkContinue, count + 1));
          } catch (error) {
            return reject(error);
          }
        }
      },
      count ? 1000 : 0,
    );
  });
};

const retryUtils = {
  retryFct,
};

export default retryUtils;

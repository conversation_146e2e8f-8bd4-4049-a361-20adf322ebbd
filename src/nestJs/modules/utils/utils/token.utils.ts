import crypto from "crypto";

/**Util class for generating random tokens */
export class TokenUtils {
  static readonly DEFAULT_TOKEN_LENGTH = 32;

  static generateHexToken(length: number = TokenUtils.DEFAULT_TOKEN_LENGTH): string {
    return crypto.randomBytes(length).toString("hex");
  }

  static generateTokenWithIdentifier(identifier: string, length: number = TokenUtils.DEFAULT_TOKEN_LENGTH): string {
    const hexToken = this.generateHexToken(length);
    return `${identifier}_${hexToken}`;
  }
}

import { compareVersions } from "compare-versions";

export const isGreaterOrEqualThan = (version: string, targetVersion: string): boolean => {
  let version1 = version;
  let version2 = targetVersion;

  if (version.includes("(")) {
    version1 = version.split("(")[0];
  }
  if (targetVersion.includes("(")) {
    version2 = targetVersion.split("(")[0];
  }

  return compareVersions(version1, version2) >= 0;
};

const versionUtils = {
  isGreaterOrEqualThan,
};

export default versionUtils;

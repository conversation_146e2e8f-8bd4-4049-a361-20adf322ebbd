import { LanguageOption } from "../../validation/dto/language.dto";

/**
 * languageUtils
 */
const languageUtils = {
  /**
   * getLanguangeType
   * @param languange string
   * @returns LanguageOption
   */
  getLanguageType(language?: string): LanguageOption {
    return language ? languageMap[language] ?? LanguageOption.ZHHK : LanguageOption.ZHHK;
  },
};

const languageMap: { [key: string]: LanguageOption } = {
  ["en"]: LanguageOption.EN,
  ["zh-hk"]: LanguageOption.ZHHK,
};

export default languageUtils;

import { Brackets, ObjectLiteral, Repository, SelectQueryBuilder } from "typeorm";

import { DirectionType } from "../../validation/dto/listingSchema.dto";

/**
 * apiUtils
 */
const apiUtils = {
  /**
   * getList
   * @param repository
   * @param alias string
   * @param where string | Brackets | ((qb: SelectQueryBuilder<any>) => string) | ObjectLiteral | ObjectLiteral[]
   * @param paginateOption PaginationOption    --limit, offset, sort, orderBy
   * @param joinAndSelectOptions JoinAndSelectOption[]    --property, alias, condition
   * @param withDeleted boolean
   * @returns list and a count of the list
   */
  async paginateRaw<T extends ObjectLiteral>(
    repository: Repository<T>,
    alias: string,
    where: string | Brackets | ((qb: SelectQueryBuilder<any>) => string) | ObjectLiteral | ObjectLiteral[],
    paginateOption: PaginationOption,
    joinAndSelectOptions: JoinAndSelectOption[] = [],
    joinAndSelectOptionsWithSubQueryFactory: JoinAndSelectOptionWithSubQueryFactory[] = [],
    groupBy: string[] = [],
    selects: string[] = [],
    withDeleted = false,
  ): Promise<[T[], number]> {
    const { sort = `${alias}.createdAt`, orderBy = DirectionType.DESC, limit = 50, offset = 0 } = paginateOption;
    const queryBuilder = repository.createQueryBuilder(alias);

    if (withDeleted) {
      queryBuilder.withDeleted();
    }

    joinAndSelectOptions.forEach((item) => {
      queryBuilder.leftJoinAndSelect(item.property, item.alias);
    });
    joinAndSelectOptionsWithSubQueryFactory.forEach((item) => {
      queryBuilder.leftJoinAndSelect(item.subQueryFactory, item.alias, item.condition);
    });
    groupBy.forEach((item) => {
      queryBuilder.addGroupBy(item);
    });
    if (selects.length > 0) {
      queryBuilder.select(selects);
    }
    const rows = await queryBuilder
      .where(where)
      .orderBy(sort, orderBy, "NULLS LAST")
      .limit(limit)
      .offset(offset * limit)
      .getRawMany();

    const countQueryBuilder = repository.createQueryBuilder(alias).where(where);
    const count = await countQueryBuilder.getCount();
    return [rows, count];
  },
};

export type PaginationOption = {
  sort?: string;
  orderBy?: DirectionType;
  limit?: number;
  offset?: number;
};

export type JoinAndSelectOption = {
  property: string;
  alias: string;
  condition?: string;
};

export type JoinAndSelectOptionWithSubQueryFactory = {
  subQueryFactory: (qb: SelectQueryBuilder<any>) => SelectQueryBuilder<any>;
  alias: string;
  condition?: string;
};

export default apiUtils;

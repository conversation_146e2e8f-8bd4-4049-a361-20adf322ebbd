import { DashError } from "./error.utils";

/**
 * build a html string from a list of errors in a pre tag
 * the pre tag title would have the error message
 * the pre tag content would have the error stack
 * You can expand or collapse the error stack
 * @param errors DashError[]
 */
export const buildErrorHtml = (errors: DashError[]): string => {
  const errorHtml = errors
    .map((error) => {
      return `<details><summary>${error.code} - ${error.message}</summary><pre>${JSON.stringify(
        error,
        null,
        2,
      )}</pre></details>`;
    })
    .join("");

  return `<div>${errorHtml}</div>`;
};

export const apiTags = {
  admin: ["Admin"],
  auth: ["Auth"],
  me: ["App User"],
  merchant: ["Merchant"],
  driver: ["Merchant - Driver"],
  meter: ["Meter"],
  public: ["Public"],
  secondRow: ["Second Row"],
  system: ["System"],
  cloudTask: ["Cloud Task"],
  geospatial: ["Geospatial"],
  teamsNotification: ["Teams Notification"],
};

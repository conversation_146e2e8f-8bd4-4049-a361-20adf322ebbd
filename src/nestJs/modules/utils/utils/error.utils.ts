import { Arguments<PERSON>ost, ExceptionFilter, HttpStatus } from "@nestjs/common";
import { HttpException, HttpExceptionOptions } from "@nestjs/common/exceptions/http.exception";
import { captureException } from "@sentry/nestjs";
import { CaptureContext } from "@sentry/types";
import { Response } from "express";
import { FirebaseAuthError } from "firebase-admin/auth";

import { NotificationTaskStatus, NotificationTaskType } from "@nest/modules/database/entities/notificationTask.entity";
import { DiscountState } from "@nest/modules/discount/dto/discount.dto";
import { WebhookEventType } from "@nest/modules/pubsub/dto/PublishMessageForWebhookProcessing.dto";

import infoJson from "../../../../static/info.json";
import { BankNames } from "../../bank/dto/bankName.dto";
import { PaymentChannelType } from "../../campaign/dto/campaign.dto";
import PaymentTx from "../../database/entities/paymentTx.entity";
import { NotificationTriggerEventType } from "../../fcm/types";
import { ComputeRoutesParams } from "../../location/dto/location.dto";
import { PaymentGatewayResponse } from "../../payment/dto/paymentGatewayResponses.model";
import { PaymentGatewayTypes } from "../../payment/dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../payment/dto/paymentInformationType.dto";
import { TxTypes } from "../../transaction/dto/txType.dto";
import LoggerServiceAdapter from "../logger/logger.service";

type DashHttpExceptionOptions = HttpExceptionOptions & { custom?: Record<string, any> };

export class DashError extends HttpException {
  public custom?: Record<string, any>;

  constructor(public readonly code: DashErrorCodes, status: HttpStatus, options?: DashHttpExceptionOptions) {
    super({ code, description: options?.description }, status, options);
    this.message = options?.description ?? code;
    this.custom = options?.custom;
  }
}

export enum DashErrorCodes {
  UNEXPECTED = "UN_001",
  STORAGE__FILE_PATH_NOT_FOUND = "ST_001",
  STORAGE__INVALID_CONTENT = "ST_002",
  STORAGE__FILE_NOT_FOUND = "ST_003",
  MESSAGE__FACTORY_NOT_IMPLEMENTED = "MS_001",
  MESSAGE__FACTORY_WRONG_IMPLEMENTATION = "MS_010",
  MESSAGE__SMS__RECIPIENT_REQUIRED = "MS_002",
  MESSAGE__SMS__TEMPLATE_NOT_IMPLEMENTED = "MS_003",
  MESSAGE__SMS__TWILIO_CREATE = "MS_004",
  MESSAGE__WHATSAPP__RECIPIENT_REQUIRED = "MS_005",
  MESSAGE__WHATSAPP__TEMPLATE_NOT_IMPLEMENTED = "MS_006",
  MESSAGE__WHATSAPP__API_FAILURE = "MS_007",
  MESSAGE__WHATSAPP__INVALID_NUMBER = "MS_008",
  MESSAGE__WHATSAPP__MAX_ATTEMPT_CALLED = "MS_009",
  MESSAGE__WHATSAPP__TEMPLATE_NOT_ENABLED = "MS_011",
  BANK__FACTORY_BANK_NAME_REQUIRED = "BK_001",
  BANK__FACTORY_NOT_IMPLEMENTED = "BK_002",
  PAYOUT__NO_CONTENT_IN_BANK_FILE = "PY_001",
  PAYOUT__NOT_FOUND = "PY_002",
  PAYOUT__MERCHANT__NOT_FOUND = "PY_003",
  PAYMENT__FACTORY_NOT_IMPLEMENTED = "PM_001",
  PAYMENT__FACTORY_GATEWAY_REQUIRED = "PM_005",
  PAYMENT__SOEPAY__INVALID_RESPONSE = "PM_002",
  PAYMENT__SOEPAY__API_FAILURE = "PM_003",
  PAYMENT__SOEPAY__MAX_ATTEMPT_CALLED = "PM_004",
  PAYMENT__SOEPAY__VOID_FAILED = "PM_006",
  PAYMENT__TX__NOT_FOUND = "PM_007",
  PAYMENT__TX__INVALID_TYPE_OR_STATUS = "PM_008",
  PAYMENT__TX__AMOUNT_EXCEEDS_AUTH = "PM_009",
  PAYMENT__INSTRUMENT__COULD_NOT_SAVE = "PM_013",
  PAYMENT__INSTRUMENT__NOT_FOUND = "PM_014",
  PAYMENT__INSTRUMENT__3DS__NOT_COMPATIBLE = "PM_015",
  PAYMENT__INSTRUMENT__IP_ADDRESS_MISSING = "PM_016",
  PAYMENT__INSTRUMENT__COULD_NOT_DELETE = "PM_017",
  PAYMENT__INSTRUMENT__COULD_NOT_DELETE_WITH_INGOING_TX = "PM_026",
  PAYMENT__ALREADY_SOLD = "PM_018",
  PAYMENT__UNABLE_TO_SELL = "PM_019",
  PAYMENT__AUTH_FAILED = "PM_020",
  PAYMENT__CAPTURE_FAILED = "PM_021",
  PAYMENT__VOID_FAILED = "PM_022",
  PAYMENT__REFUND_FAILED = "PM_023",
  PAYMENT__ENQUIRY_FAILED = "PM_024",
  PAYMENT__SEARCH_FAILED = "PM_025",
  TRANSACTION__NOT_FOUND = "TX_001",
  TRANSACTION__INCORRECT_CALCUATION = "TX_002",
  TRANSACTION__FACTORY_TX_REQUIRED = "TX_003",
  TRANSACTION__FACTORY_NOT_IMPLEMENTED = "TX_004",
  TRANSACTION__FACTORY_WRONG_IMPLEMENT = "TX_010",
  TRANSACTION__TRIP__METER_CHANGE_NO_DATA = "TX_005",
  TRANSACTION__TRIP__CREATED_NO_DATA = "TX_006",
  TRANSACTION__TRIP__INVALID_TOTAL = "TX_007",
  TRANSACTION__TAG__NOT__FOUND = "TX_008",
  TRANSACTION__TRIP__NOT_ENDED = "TX_009",
  TRANSACTION__MERCHANT__NOT_FOUND = "TX_011",
  TRANSACTION__ALREADY_UNLOCKED = "TX_012",
  TRANSACTION__TRIP__MISSING_LICENCE_PLATE = "TX_013",
  TRANSACTION__LOCKED_BY_OTHER_USER = "TX_014",
  TRANSACTION__ALREADY_LOCKED = "TX_015",
  TRANSACTION__PARENT_TX_NOT_FOUND = "TX_016",
  TRANSACTION__TOTAL_AMOUNT_MISSING = "TX_017",
  TRANSACTION__TRIP__ALREADY_END = "TX_018",
  TRANSACTION__TRIP__NOT_PAIRED_WITH_USER = "TX_019",
  TRANSACTION__EVENT__TYPE_MISMATCH = "TX_020",
  TRANSACTION__EVENT__TYPE_NOT_SUPPORTED = "TX_021",
  TRANSACTION__EVENT__SUCCESS_AUTH_NOT_FOUND = "TX_022",
  TRANSACTION__PAYMENT_INSTRUMENT_NOT_FOUND = "TX_023",
  TRANSACTION__NO_SALE = "TX_024",
  TRANSACTION__ADJUSTMENT_NOT_SUPPORTED = "TX_025",
  MERCHANT__DRIVER__CREATED_NO_DATA = "MR_001",
  MERCHANT__DRIVER__UPDATED_NO_DATA = "MR_002",
  MERCHANT__DRIVER__TRIP_CHANGED_NO_DATA = "MR_003",
  MERCHANT__DRIVER__SESSION_NOT_FOUND_OR_NO_DATA = "MR_004",
  MERCHANT__DRIVER__NOT_FOUND = "MR_005",
  MERCHANT__NOT_FOUND = "MR_006",
  MERCHANT__DRIVER__REFERRAL_CODE_GENERATION_LIMIT_EXCEEDED = "MR_007",
  MERCHANT__DRIVER__REFERRAL_CODE_NOT_FOUND = "MR_008",
  MERCHANT__DRIVER__REFERRAL_CODE_INVALID = "MR_009",
  MERCHANT__DRIVER__REFERRAL_CODE_GENERATION_FAILED = "MR_010",
  VALIDATION__FAILED = "VL_001",
  METER__TRIP_NOT_FOUND = "MT_001",
  METER__TRIP_CHECK_TIME_OUT = "MT_002",
  METER__TRIP_UPDATE_FAILED = "MT_003",
  METER__NOT_FOUND = "MT_004",
  METER__ALREADY_EXIST = "MT_005",
  METER__TRIP_UPDATE_UNAUTHORIZED = "MT_006",
  METER__TRIP_ALREADY_ENDED = "MT_007",
  METER__FLEET_NOT_FOUND = "MT_008",
  LINK__NOT_FOUND = "LN_001",
  LINK__FAILED_TO_CREATE = "LN_002",
  LINK__EXPIRED = "LN_003",
  GLOBAL__REQUIRED_PARAM = "GB_001",
  GLOBAL__INVALID_PARAM = "GB_002",
  GLOBAL__UNKNOWN = "GB_003",
  GLOBAL__UNPROCESSABLE_ENTITY = "GB_004",
  GLOBAL__RESOURCE_ALREADY_EXISTS = "GB_005",
  TRIP__NOT_FOUND = "TP_001",
  AUTH__TOKEN_NOT_SET = "AU_001",
  AUTH__TOKEN_INVALID_ROLE = "AU_002",
  AUTH__ERROR = "AU_003",
  AUTH__TOKEN_EXPIRED = "AU_004",
  AUTH__INVALID_CREDENTIALS = "AU_005",
  AUTH__USER_DISABLED = "AU_006",
  AUTH__AUTH_ATTEMPTS_EXCEEDED = "AU_007",
  AUTH__MISSING_EMAIL = "AU_008",
  AUTH__MFA_ERROR = "AU_009",
  AUTH__RECAPTCHA_ERROR = "AU_010",
  AUTH__INVALID_RESET_TOKEN_ERROR = "AU_011",
  AUTH__INADEQUATE_PASSWORD = "AU_012",
  AUTH__PASSWORD_USED_IN_LAST_YEAR = "AU_013",
  AUTH__INVALID_DEVICE_JWT = "AU_014",
  AUTH__EMAIL_ALREADY_EXIST = "AU_015",
  AUTH__USER_NOT_FOUND = "AU_016",
  AUTH__FLEET_ID_REQUIRED = "AU_017",
  TX_APP__NAME_REQUIRED = "AP_001",
  TX_APP__NOT_FOUND = "AP_002",
  TX_APP__REQUIRED = "AP_003",
  USER__APP__USER_NOT_FOUND_IN_FIRESTORE = "US_001",
  USER__APP__USER_MISSING = "US_002",
  USER__APP__USER_UPDATED_NO_DATA = "US_003",
  USER__APP__USER_NOT_FOUND_IN_SQL_DB = "US_004",
  USER__APP__USER_NOT_FOUND_IN_FIREBASEAUTH = "US_005",
  USER__APP__USER_NOT_FOUND_PHONE_NUMBER_IN_FIREBASEAUTH = "US_006",
  USER__APP__USER_CREATE_PIN_FAILED_WITH_EXISTING_PIN = "US_009",
  USER__APP__USER_VERIFY_PIN_NOT_FOUND_IN_REQUEST = "US_012",
  USER__APP__USER_VERIFY_PIN_UNKNOWN = "US_013",
  USER__APP__USER_VERIFY_PIN_FAILED_WITHOUT_EXISTING_PIN = "US_014",
  USER__APP__USER_UPDATED_PIN_FAILED_WITH_WRONG_OLD_PIN = "US_015",
  USER__APP__USER_BLOACKED_FOR_EXCEEDING_PIN_ERROR_COUNT = "US_016",
  USER__APP__USER_INVALID_TYPE = "US_017",
  USER__APP__USER_VERIFY_PIN_FAILED_NO_PRIVATE_KEY = "US_010",
  USER__APP__USER_VERIFY_PIN_FAILED_NO_SALT = "US_011",
  USER__APP__USER_NOT_FOUND_IN_SQL_DB_WITH_ANONYMOUS_PHONENUMBER = "US_018",
  USER__APP__USER_ALREADY_REGISTERED_SHOULD_GO_TO_APP = "US_019",
  USER__APP__REFERRAL_CODE_GENERATION_LIMIT_EXCEEDED = "US_020",
  USER__APP__REFERRAL_CODE_NOT_FOUND = "US_021",
  USER__APP__REFERRAL_CODE_INVALID = "US_022",
  USER__APP__REFERRAL_CODE_GENERATION_FAILED = "US_023",
  USER__APP__USER_PHONE_NUMBER_NOT_FOUND_IN_SQL_DB = "US_024",
  INCENTIVE__CAMPAIGN__NOT_FOUND = "IC_001",
  INCENTIVE__CAMPAIGN__INVALID_DATE = "IC_002",
  INCENTIVE__DISCOUNT__INVALID_CAMPAIGN_DATE = "IC_003",
  INCENTIVE__DISCOUNT__INVALID_CAMPAIGN_PAYMENT_CHANNEL = "IC_004",
  INCENTIVE__DISCOUNT__INVALID_TX_TYPE = "IC_005",
  INCENTIVE__DISCOUNT__TRIP_ALREADY_END = "IC_006",
  INCENTIVE__DISCOUNT__INVALID_CAMPAIGN_NO_DISCOUNT_RULES = "IC_007",
  INCENTIVE__DISCOUNT__MAX_USAGE_PER_USER_REACHED = "IC_008",
  INCENTIVE__DISCOUNT__NOT_FOUND = "IC_009",
  INCENTIVE__DISCOUNT__ALREADY_REQUESTED_BY_OTHER_USER = "IC_010",
  INCENTIVE__DISCOUNT__USER_NOT_FOUND = "IC_011",
  INCENTIVE__DISCOUNT__REWARD_CODE_NOT_FOUND = "IC_012",
  INCENTIVE__DISCOUNT__REWARD_CODE_ALREADY_CLAIMED = "IC_013",
  INCENTIVE__DISCOUNT__REWARD_CODE_EXPIRED = "IC_014",
  INCENTIVE__DISCOUNT__REWARD_CODE_LIMIT_EXCEEDED = "IC_015",
  INCENTIVE__DISCOUNT__AMOUNT_EXCEEDED = "IC_016",
  INCENTIVE__DISCOUNT__USER_MISMATCH = "IC_017",
  INCENTIVE__DISCOUNT__ALREADY_REDEEMED = "IC_018",
  INCENTIVE__DISCOUNT__NOT_APPLIED = "IC_019",
  INCENTIVE__DISCOUNT__NOT_CREATED = "IC_020",
  INCENTIVE__CAMPAIGN__NOT_YET_STARTED = "IC_021",
  INCENTIVE__CAMPAIGN__EXPIRED = "IC_022",
  INCENTIVE__DISCOUNT__CANNOT_TRANSITION_STATE = "IC_023",
  FACTORY__NOT_IMPLEMENTED = "FA_001",
  GLOBAL_PAYMENT__CUSTOM = "GP_001",
  FIRESTORE_NOT_FOUND = "FS_001",
  FIRESTORE_UNABLE_TO_CREATE = "FS_002",
  FIRESTORE_ALREADY_EXISTS = "FS_003",
  QR_CODE__NOT_FOUND = "QR_001",
  QR_CODE__ALREADY_PAIR = "QR_002",
  QR_CODE__FACTORY_NOT_IMPLEMENTED = "QR_003",
  INFRASTRUCTURE__SECRET_NOT_FOUND = "IN_001",
  LOCATION__REVERSE_GEOCODE__INVALID_PARAMS = "LO_001",
  LOCATION__REVERSE_GEOCODE__DEPENDENCY_FAIL = "LO_002",
  LOCATION__PLACE_AUTOCOMPLETE__DEPENDENCY_FAIL = "LO_003",
  LOCATION__PLACE_DETAILS__DEPENDENCY_FAIL = "LO_004",
  LOCATION__COMPUTE_ROUTES__DEPENDENCY_FAIL = "LO_005",
  LOCATION__COMPUTE_ROUTES__NO_ROUTES_FOUND = "LO_006",
  AXIOS_DEPENDENCY__FAILED = "AD_001",
  FCM_PUSH_NOTIFICATION__TEMPLATES_COLLECTION_NOT_FOUND_IN_FIRESTORE = "FN_001",
  FCM_PUSH_NOTIFICATION__TEMPLATES_DATA_NOT_FOUND_IN_FIRESTORE = "FN_002",
  FCM_PUSH_NOTIFICATION__MISSING_NOTIFICATION_TEMPLATE = "FN_003",
  PAYMENT__KRAKEN__API_FAILURE = "KR_001",
  PAYMENT__KRAKEN__UNEXPECTED_FAILURE = "KR_002",
  ENCRYPTION__KEYRING_NOT_FOUND = "EN_001",
  ENCRYPTION__KEYRING_NOT_CREATED = "EN_002",
  ENCRYPTION__CRYPTOKEY_NOT_FOUND = "EN_003",
  ENCRYPTION__CRYPTOKEY_NOT_CREATED = "EN_004",
  ENCRYPTION__DECRYPTION_FAILED = "EN_005",
  ENCRYPTION__ENCRYPTION_FAILED = "EN_006",
  WEBHOOK__FAILED_TO_SEND = "WH_001",
  WEBHOOK__NOT_FOUND = "WH_002",
  WEBHOOK__SIGNATURE_SECRET_NOT_FOUND = "WH_003",
  WEBHOOK__INVALID_SIGNATURE_KEY_ID = "WH_004",
  WEBHOOK__NOT_IMPLEMENTED = "WH_005",
  REPORT__QUERY_METADATA_NOT_IMPLEMENTED_YET = "RP_001",
  REPORT__NO_REPORT_FILE_IN_CLOUD_STORAGE = "RP_002",
  CONFIG__WATI_TEMPLATES_NOT_FOUND = "CF_001",
  HAILING__INVALID_BOOST_AMOUNT = "HL_001",
  HAILING__BOOST_LIMIT_EXCEEDED = "HL_002",
  HAILING__NOT_UPDATABLE = "HL_003",
  NOTIFICATION__NOT_IMPLEMENTED = "NF_001",
  NOTIFICATION__USER_NOT_FOUND = "NF_002",
  NOTIFICATION__USERS_NOT_FOUND = "NF_003",
  NOTIFICATION__TASK_NOT_FOUND = "NF_004",
  NOTIFICATION__TASK_INVALID_START_STATUS = "NF_005",
  NOTIFICATION__NO_VALID_PHONE_NUMBERS = "NF_006",
  NOTIFICATION__ERROR_SENDING = "NF_007",
  NOTIFICATION__FAILED_TO_CREATE_TASK = "NF_008",
  NOTIFICATION__TASK_INVALID_STATE = "NF_009",
  NOTIFICATION__CANNOT_UPDATE_TASK_WITH_STATUS = "NF_010",
  NOTIFICATION__INVALID_PHONE_NUMBER = "NF_011",
  NOTIFICATION__INVALID_USER_TYPE = "NF_012",
  FLEET_TAXI__NO_FLEET_QUOTE_FOUND = "FT_001",
  FLEET_TAXI__INVALID_FLEET_ORDER_REQUEST = "FT_002",
  FLEET_TAXI__NO_FLEET_ORDER_FOUND = "FT_003",
  FLEET_TAXI__NO_UPDATE_FLEET_ORDER_DELEGATEE = "FT_004",
  FLEET_TAXI__NO_DRIVER_TYPE_FOR_PARTNER_KEY = "FT_005",
  FLEET_TAXI__NO_FLEET_PARTNER_FOUND = "FT_006",
  FLEET_TAXI__NO_METER_FOUND = "FT_007",
  FLEET_TAXI__NO_CANCEL_FLEET_ORDER_DELEGATEE = "FT_008",
  FLEET_TAXI__SYNCAB_API_ERROR = "FT_009",
  FLEET_TAXI__NO_FLEET_VEHICLE_TYPE_FOUND = "FT_010",
  GEOSPATIAL__INITIALIZE_FAILED = "GS_001",
  GEOSPATIAL__PARSE_POLYGON_FAILED = "GS_002",
  FLEET_TAXI__NO_DRIVER_PHONE_NUMBER = "FT_011",
  FLEET_TAXI__CREATE_FLEET_ORDER_FAILURE = "FT_012",
}

export type KrakenError = {
  status: number;
  description: string;
  timeStamp: string;
  code?: string;
  custom?: Record<string, any>;
};

export const isKrakenError = (error: any): error is KrakenError => {
  return error?.status && error?.description && error?.timeStamp;
};

export const errorBuilder = {
  kraken: {
    api: (error: KrakenError) =>
      new DashError(DashErrorCodes.PAYMENT__KRAKEN__API_FAILURE, error.status ?? HttpStatus.BAD_REQUEST, {
        description: error.description,
        custom: {
          ...error.custom,
          krakenCode: error.code,
        },
      }),
    unexpected: (error: any) =>
      new DashError(
        DashErrorCodes.PAYMENT__KRAKEN__UNEXPECTED_FAILURE,
        error.status ?? HttpStatus.INTERNAL_SERVER_ERROR,
        {
          cause: error,
          description: error.message,
        },
      ),
  },
  dependency: {
    failed: (error: any, description?: string) =>
      new DashError(DashErrorCodes.AXIOS_DEPENDENCY__FAILED, error?.response?.status ?? HttpStatus.BAD_REQUEST, {
        description: description ?? error.message ?? "Dependency failed",
      }),
  },
  location: {
    reverseGeocode: {
      invalidParams: () =>
        new DashError(DashErrorCodes.LOCATION__REVERSE_GEOCODE__INVALID_PARAMS, HttpStatus.BAD_REQUEST, {
          description: "Invalid reverse geocode params",
        }),
      dependencyFailed: (errorData: Record<string, any>, description?: string) =>
        new DashError(DashErrorCodes.LOCATION__REVERSE_GEOCODE__DEPENDENCY_FAIL, HttpStatus.BAD_REQUEST, {
          description: description ?? "Dependency failed",
          custom: { data: errorData },
        }),
    },
    computeRoutes: {
      dependencyFailed: (errorData: Record<string, any>, description?: string) =>
        new DashError(DashErrorCodes.LOCATION__COMPUTE_ROUTES__DEPENDENCY_FAIL, HttpStatus.BAD_REQUEST, {
          description: description ?? "Dependency failed",
          custom: { data: errorData },
        }),
      noRoutesFound: (data: ComputeRoutesParams) =>
        new DashError(DashErrorCodes.LOCATION__COMPUTE_ROUTES__NO_ROUTES_FOUND, HttpStatus.BAD_REQUEST, {
          description: "No routes found",
          custom: { data },
        }),
    },
    placeAutocomplete: {
      dependencyFailed: (errorData: Record<string, any>, description?: string) =>
        new DashError(DashErrorCodes.LOCATION__PLACE_AUTOCOMPLETE__DEPENDENCY_FAIL, HttpStatus.BAD_REQUEST, {
          description: description ?? "Dependency failed",
          custom: { data: errorData },
        }),
    },
    placeDetails: {
      dependencyFailed: (errorData: Record<string, any>, description?: string) =>
        new DashError(DashErrorCodes.LOCATION__PLACE_DETAILS__DEPENDENCY_FAIL, HttpStatus.BAD_REQUEST, {
          description: description ?? "Dependency failed",
          custom: { data: errorData },
        }),
    },
  },
  config: {
    watiTemplatesNotFound: () =>
      new DashError(DashErrorCodes.CONFIG__WATI_TEMPLATES_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: "Wati templates not found",
      }),
  },
  qrcode: {
    notFound: (qrId: string) =>
      new DashError(DashErrorCodes.QR_CODE__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `QR: ${qrId} not found`,
      }),
    alreadyPaired: (qrId: string) =>
      new DashError(DashErrorCodes.QR_CODE__ALREADY_PAIR, HttpStatus.CONFLICT, {
        description: `QR: ${qrId} is already paired`,
      }),
    factoryNotImplemented: (factoryName: string) =>
      new DashError(DashErrorCodes.QR_CODE__FACTORY_NOT_IMPLEMENTED, HttpStatus.NOT_IMPLEMENTED, {
        description: `QR Code factory: ${factoryName} is not implemented`,
      }),
  },
  globalPayment: {
    custom: (status: HttpStatus, options?: DashHttpExceptionOptions) =>
      new DashError(DashErrorCodes.GLOBAL_PAYMENT__CUSTOM, status, options),
  },
  factory: {
    notImplemented: (location: string) =>
      new DashError(DashErrorCodes.FACTORY__NOT_IMPLEMENTED, HttpStatus.NOT_IMPLEMENTED, {
        description: `Not implemented in ${location}`,
      }),
  },
  txApp: {
    txAppNameRequired: () =>
      new DashError(DashErrorCodes.TX_APP__NAME_REQUIRED, HttpStatus.BAD_REQUEST, {
        description: "tXApp name is required",
      }),
    txAppRequired: () =>
      new DashError(DashErrorCodes.TX_APP__REQUIRED, HttpStatus.BAD_REQUEST, {
        description: "tXApp is required",
      }),
    notFound: (txAppName: string) =>
      new DashError(DashErrorCodes.TX_APP__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `tXApp not found for txAppName: ${txAppName}`,
      }),
  },
  bank: {
    factoryBankNameRequired: () =>
      new DashError(DashErrorCodes.BANK__FACTORY_BANK_NAME_REQUIRED, HttpStatus.BAD_REQUEST, {
        description: "Bank name is required",
      }),
    factoryNotImplemented: (bankName: BankNames) =>
      new DashError(DashErrorCodes.BANK__FACTORY_NOT_IMPLEMENTED, HttpStatus.NOT_IMPLEMENTED, {
        description: `Bank name: ${bankName} is not implemented`,
      }),
  },
  global: {
    unexpected: (error: any) =>
      new DashError(DashErrorCodes.UNEXPECTED, HttpStatus.INTERNAL_SERVER_ERROR, {
        cause: error,
        description: error.message,
      }),
    requiredParam: (param: string) =>
      new DashError(DashErrorCodes.GLOBAL__REQUIRED_PARAM, HttpStatus.BAD_REQUEST, {
        description: `${param} is required`,
      }),
    invalidParam: (param: string) =>
      new DashError(DashErrorCodes.GLOBAL__INVALID_PARAM, HttpStatus.BAD_REQUEST, {
        description: `${param} is invalid`,
      }),
    unknown: (error: any) =>
      new DashError(DashErrorCodes.GLOBAL__UNKNOWN, HttpStatus.INTERNAL_SERVER_ERROR, {
        cause: error,
        description: error.message,
      }),
    unprocessableEntity: (message: string, details?: Record<string, any>) => {
      return new DashError(DashErrorCodes.GLOBAL__UNPROCESSABLE_ENTITY, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: message,
        custom: details,
      });
    },
    resourceAlreadyExists: (message: string, details?: Record<string, any>) => {
      return new DashError(DashErrorCodes.GLOBAL__RESOURCE_ALREADY_EXISTS, HttpStatus.CONFLICT, {
        description: message,
        custom: details,
      });
    },
    invalidColumns: (colume: string, invalidColumns: string[]) =>
      new DashError(DashErrorCodes.GLOBAL__INVALID_PARAM, HttpStatus.BAD_REQUEST, {
        description: `The columns of '${invalidColumns}' are invalid, as it includes '${colume}', which is not allowed.`,
      }),
  },
  merchant: {
    notFound: (id: string) =>
      new DashError(DashErrorCodes.MERCHANT__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Merchant not found for merchantId: ${id}`,
      }),
    driver: {
      notFound: (phoneNumber?: string) =>
        new DashError(DashErrorCodes.MERCHANT__DRIVER__NOT_FOUND, HttpStatus.NOT_FOUND, {
          description: `Driver not found for driverId: ${phoneNumber}`,
        }),
      createdNoData: () =>
        new DashError(DashErrorCodes.MERCHANT__DRIVER__CREATED_NO_DATA, HttpStatus.BAD_REQUEST, {
          description: "No driver data",
        }),
      updatedNoData: () =>
        new DashError(DashErrorCodes.MERCHANT__DRIVER__UPDATED_NO_DATA, HttpStatus.BAD_REQUEST, {
          description: "No driver data",
        }),
      referralCodeGenerationLimitExceeded: (driverId: string) =>
        new DashError(
          DashErrorCodes.MERCHANT__DRIVER__REFERRAL_CODE_GENERATION_LIMIT_EXCEEDED,
          HttpStatus.BAD_REQUEST,
          {
            description: `Referral code generation limit exceeded for driver: ${driverId}`,
          },
        ),
      referralCodeNotFound: (referralCode: string) =>
        new DashError(DashErrorCodes.MERCHANT__DRIVER__REFERRAL_CODE_NOT_FOUND, HttpStatus.NOT_FOUND, {
          description: `Referral code: ${referralCode} not found`,
        }),
      referralCodeInvalid: (referralCode: string) =>
        new DashError(DashErrorCodes.MERCHANT__DRIVER__REFERRAL_CODE_INVALID, HttpStatus.BAD_REQUEST, {
          description: `Referral code: ${referralCode} is invalid`,
        }),
      referralCodeGenerationFailed: (driverId: string) =>
        new DashError(DashErrorCodes.MERCHANT__DRIVER__REFERRAL_CODE_GENERATION_FAILED, HttpStatus.BAD_REQUEST, {
          description: `Referral code generation failed for driver: ${driverId}`,
        }),
      trip: {
        tripChangedNoData: (driverId: string, sessionId: string, tripId: string) =>
          new DashError(DashErrorCodes.MERCHANT__DRIVER__TRIP_CHANGED_NO_DATA, HttpStatus.NOT_FOUND, {
            description: `No trip change data found for driver: ${driverId}, session: ${sessionId}, trip: ${tripId}`,
          }),
      },
      session: {
        sessionNotFoundOrNoData: (driverId: string, sessionId?: string) =>
          new DashError(DashErrorCodes.MERCHANT__DRIVER__SESSION_NOT_FOUND_OR_NO_DATA, HttpStatus.NOT_FOUND, {
            description: `Session not found or no data for driver: ${driverId} and session: ${sessionId}`,
          }),
      },
    },
  },
  meter: {
    tripNotFound: (meterId: string, tripId: string) =>
      new DashError(DashErrorCodes.METER__TRIP_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `No tripId: ${tripId} found for meter: ${meterId}`,
      }),
    tripCheckTimeOut: (meterId: string, tripId: string) =>
      new DashError(DashErrorCodes.METER__TRIP_CHECK_TIME_OUT, HttpStatus.REQUEST_TIMEOUT, {
        description: `Check Time Out for meter: ${meterId}, tripId: ${tripId}`,
      }),
    tripUpdateFailed: (meterId: string, tripId: string) =>
      new DashError(DashErrorCodes.METER__TRIP_UPDATE_FAILED, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: `Update Failed for meter: ${meterId}, tripId: ${tripId}`,
      }),
    notFound: (meterId: string) =>
      new DashError(DashErrorCodes.METER__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Meter not found for meterId: ${meterId}`,
      }),
    alreadyExist: (meterId: string) =>
      new DashError(DashErrorCodes.METER__ALREADY_EXIST, HttpStatus.CONFLICT, {
        description: `Meter already exist for meterId: ${meterId}`,
      }),
    tripUpdateUnauthorized: (meterId: string, tripId: string) =>
      new DashError(DashErrorCodes.METER__TRIP_UPDATE_UNAUTHORIZED, HttpStatus.UNAUTHORIZED, {
        description: `Unauthorized to update trip for meter: ${meterId}, tripId: ${tripId}`,
      }),
    tripAlreadyEnded: (meterId: string, tripId: string) =>
      new DashError(DashErrorCodes.METER__TRIP_ALREADY_ENDED, HttpStatus.BAD_REQUEST, {
        description: `Trip already ended for meter: ${meterId}, tripId: ${tripId}`,
      }),
    meterFleetNotFound: (meterId: string) =>
      new DashError(DashErrorCodes.METER__FLEET_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Fleet not found for meter: ${meterId}`,
      }),
  },
  trip: {
    notFound: (tripId: string) =>
      new DashError(DashErrorCodes.TRIP__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Trip not found for tripId: ${tripId} in FireStore`,
      }),
  },
  validation: {
    failed: (messages: string, details?: Record<string, any>) =>
      new DashError(DashErrorCodes.VALIDATION__FAILED, HttpStatus.BAD_REQUEST, {
        description: `Validation failed: ${messages}`,
        custom: details,
      }),
  },
  transaction: {
    noSale: (txId: string) =>
      new DashError(DashErrorCodes.TRANSACTION__NO_SALE, HttpStatus.BAD_REQUEST, {
        description: `No sale for Tx: ${txId}`,
      }),
    noPaymentInstrumentFound: () =>
      new DashError(DashErrorCodes.TRANSACTION__PAYMENT_INSTRUMENT_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: "Payment Instrument not found",
      }),
    successAuthNotFound: (txId: string) =>
      new DashError(DashErrorCodes.TRANSACTION__EVENT__SUCCESS_AUTH_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Success Auth not found for Tx: ${txId}`,
      }),
    eventTypeNotSupported: (txId: string, eventType: string) =>
      new DashError(DashErrorCodes.TRANSACTION__EVENT__TYPE_NOT_SUPPORTED, HttpStatus.BAD_REQUEST, {
        description: `Tx: ${txId} event type: ${eventType} is not supported`,
      }),
    eventTypeMismatch: (txId: string, expectedType: TxTypes, foundTxType: TxTypes) =>
      new DashError(DashErrorCodes.TRANSACTION__EVENT__TYPE_MISMATCH, HttpStatus.BAD_REQUEST, {
        description: `Tx: ${txId} expected type: ${expectedType} but found type: ${foundTxType}`,
      }),
    lockedByOtherUser: (txId: string) =>
      new DashError(DashErrorCodes.TRANSACTION__LOCKED_BY_OTHER_USER, HttpStatus.CONFLICT, {
        description: `Tx: ${txId} is locked by other user`,
      }),
    alreadyUnlocked: (txId: string) =>
      new DashError(DashErrorCodes.TRANSACTION__ALREADY_UNLOCKED, HttpStatus.CONFLICT, {
        description: `Tx is already unlocked, txId: ${txId}`,
      }),
    alreadyLocked: (txId: string) =>
      new DashError(DashErrorCodes.TRANSACTION__ALREADY_LOCKED, HttpStatus.CONFLICT, {
        description: `Tx is already locked, txId: ${txId}`,
      }),
    factoryTxRequired: () =>
      new DashError(DashErrorCodes.TRANSACTION__FACTORY_TX_REQUIRED, HttpStatus.BAD_REQUEST, {
        description: "tx is required",
      }),
    factoryNotImplemented: (txType: TxTypes) =>
      new DashError(DashErrorCodes.TRANSACTION__FACTORY_NOT_IMPLEMENTED, HttpStatus.NOT_IMPLEMENTED, {
        description: `tx type: ${txType} is not implemented`,
      }),
    wrongImplement: (txType: TxTypes, location: string) =>
      new DashError(DashErrorCodes.TRANSACTION__FACTORY_WRONG_IMPLEMENT, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: `wrong tx type: ${txType} in ${location}`,
      }),
    notFound: (txId: string, custom?: Record<string, string>) =>
      new DashError(DashErrorCodes.TRANSACTION__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Tx '${txId}' not found`,
        custom,
      }),
    incorrectCalculation: (txId: string) =>
      new DashError(DashErrorCodes.TRANSACTION__INCORRECT_CALCUATION, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: `Tx '${txId}' incorrect calculation`,
      }),
    merchantNotFound: (phoneNumber: string) =>
      new DashError(DashErrorCodes.TRANSACTION__MERCHANT__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Merchant '${phoneNumber}' not found`,
      }),
    totalAmountMissing: (txId: string) =>
      new DashError(DashErrorCodes.TRANSACTION__TOTAL_AMOUNT_MISSING, HttpStatus.NOT_FOUND, {
        description: `Tx '${txId}' total is missing`,
      }),
    parentTxNotFound: (txId: string) =>
      new DashError(DashErrorCodes.TRANSACTION__PARENT_TX_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Parent Tx of '${txId}' not found`,
      }),
    adjustmentNotSupported: (txId: string, params: unknown) =>
      new DashError(DashErrorCodes.TRANSACTION__ADJUSTMENT_NOT_SUPPORTED, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: `Adjustment type to txId(${txId}) not supported: ${JSON.stringify(params)}`,
        custom: { txId, params },
      }),
    trip: {
      meterChangeNoData: () =>
        new DashError(DashErrorCodes.TRANSACTION__TRIP__METER_CHANGE_NO_DATA, HttpStatus.BAD_REQUEST, {
          description: "No meter change data",
        }),
      createdNoData: () =>
        new DashError(DashErrorCodes.TRANSACTION__TRIP__CREATED_NO_DATA, HttpStatus.BAD_REQUEST, {
          description: "No trip data",
        }),
      invalidTotal: ({ txId, total }: { txId: string; total: number }) =>
        new DashError(DashErrorCodes.TRANSACTION__TRIP__INVALID_TOTAL, HttpStatus.BAD_REQUEST, {
          description: `Invalid total: ${total}`,
          custom: { txId, total },
        }),
      notEnded: (txId: string) =>
        new DashError(DashErrorCodes.TRANSACTION__TRIP__NOT_ENDED, HttpStatus.BAD_REQUEST, {
          description: `Trip is not ended, txId: ${txId}`,
        }),
      missingLicencePlate: (txId: string) =>
        new DashError(DashErrorCodes.TRANSACTION__TRIP__MISSING_LICENCE_PLATE, HttpStatus.UNPROCESSABLE_ENTITY, {
          description: `Licence Plate is missing, txId: ${txId}`,
        }),
      tripAlreadyEnded: (txId: string) =>
        new DashError(DashErrorCodes.TRANSACTION__TRIP__ALREADY_END, HttpStatus.BAD_REQUEST, {
          description: `Trip is already ended, txId: ${txId}`,
        }),
      tripNotPairedWithUser: (txId: string, appDatabaseId: string) =>
        new DashError(DashErrorCodes.TRANSACTION__TRIP__NOT_PAIRED_WITH_USER, HttpStatus.BAD_REQUEST, {
          description: `Trip : ${txId} is not paired with user: ${appDatabaseId}`,
        }),
    },
    tag: {
      notFoundOrRemoved: (txId: string, ids: number[]) =>
        new DashError(DashErrorCodes.TRANSACTION__TAG__NOT__FOUND, HttpStatus.NOT_FOUND, {
          description: `Tags: ${ids} not found or have been removed already, for Tx: ${txId}`,
        }),
    },
  },
  payout: {
    noContentInBankFile: (bankFile: string, custom?: Record<string, string>) =>
      new DashError(DashErrorCodes.PAYOUT__NO_CONTENT_IN_BANK_FILE, HttpStatus.BAD_REQUEST, {
        description: `No content in bank file ${bankFile}`,
        custom,
      }),
    notFound: (bankFile: string, custom?: Record<string, string>) =>
      new DashError(DashErrorCodes.PAYOUT__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `No payout found for bank file ${bankFile}`,
        custom,
      }),
    payoutMerchantNotFound: (merchantId: string) =>
      new DashError(DashErrorCodes.PAYOUT__MERCHANT__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Merchant not found for merchantId: ${merchantId}`,
      }),
  },
  payment: {
    captureFailed: (paymentTxId: string, error: any) =>
      new DashError(DashErrorCodes.PAYMENT__CAPTURE_FAILED, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: "Payment capture failed for paymentTx",
        custom: { paymentTxId },
        cause: error,
      }),
    voidFailed: (paymentTxId: string, error: any) =>
      new DashError(DashErrorCodes.PAYMENT__VOID_FAILED, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: "Payment void failed for paymentTx",
        custom: { paymentTxId },
        cause: error,
      }),
    refundFailed: (paymentTxId: string, error: any) =>
      new DashError(DashErrorCodes.PAYMENT__REFUND_FAILED, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: "Payment refund failed for paymentTx",
        custom: { paymentTxId },
        cause: error,
      }),
    enquiryFailed: (paymentTxId: string, error: any) =>
      new DashError(DashErrorCodes.PAYMENT__ENQUIRY_FAILED, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: "Payment enquiry failed for paymentTx",
        custom: { paymentTxId },
        cause: error,
      }),
    searchFailed: (paymentTxId: string, error: any) =>
      new DashError(DashErrorCodes.PAYMENT__SEARCH_FAILED, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: "Payment search failed for paymentTx",
        custom: { paymentTxId },
        cause: error,
      }),
    authFailed: (gatewayResponse: PaymentGatewayResponse | undefined) =>
      new DashError(DashErrorCodes.PAYMENT__AUTH_FAILED, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: "Payment auth failed",
        custom: gatewayResponse,
      }),
    instrument: {
      couldNotSave: (paymentInstrumentId: string, error: any) =>
        new DashError(DashErrorCodes.PAYMENT__INSTRUMENT__COULD_NOT_SAVE, HttpStatus.INTERNAL_SERVER_ERROR, {
          description: `Could not save payment instrument: ${paymentInstrumentId}`,
          cause: error,
        }),
      couldNotDelete: (paymentInstrumentId: string, error: any) =>
        new DashError(DashErrorCodes.PAYMENT__INSTRUMENT__COULD_NOT_DELETE, HttpStatus.INTERNAL_SERVER_ERROR, {
          description: `Could not delete payment instrument: ${paymentInstrumentId}`,
          cause: error,
        }),
      couldNotDeleteWithOngoingTx: (paymentInstrumentId: string) =>
        new DashError(DashErrorCodes.PAYMENT__INSTRUMENT__COULD_NOT_DELETE_WITH_INGOING_TX, HttpStatus.BAD_REQUEST, {
          description: `Could not delete payment instrument: ${paymentInstrumentId} with ongoing transactions`,
        }),
      notFound: (paymentInstrumentId: string) =>
        new DashError(DashErrorCodes.PAYMENT__INSTRUMENT__NOT_FOUND, HttpStatus.NOT_FOUND, {
          description: `Payment instrument: ${paymentInstrumentId} not found`,
        }),
      ipAddressMissing: (paymentInstrumentId: string) =>
        new DashError(DashErrorCodes.PAYMENT__INSTRUMENT__IP_ADDRESS_MISSING, HttpStatus.BAD_REQUEST, {
          description: `Payment instrument: ${paymentInstrumentId} is missing IP address`,
        }),
      threeDSecure: {
        notCompatible: (paymentInstrumentId: string, status: string) =>
          new DashError(DashErrorCodes.PAYMENT__INSTRUMENT__3DS__NOT_COMPATIBLE, HttpStatus.BAD_REQUEST, {
            description: `Payment instrument: ${paymentInstrumentId} is not 3D Secure compatible, status: ${status}`,
          }),
      },
    },
    factoryNotImplemented: (paymentGatewayTypes: PaymentGatewayTypes) =>
      new DashError(DashErrorCodes.PAYMENT__FACTORY_NOT_IMPLEMENTED, HttpStatus.NOT_IMPLEMENTED, {
        description: `Payment gateway type: ${paymentGatewayTypes} is not implemented`,
      }),
    factoryPaymentGatewayRequired: () =>
      new DashError(DashErrorCodes.PAYMENT__FACTORY_GATEWAY_REQUIRED, HttpStatus.BAD_REQUEST, {
        description: "Payment gateway type is required",
      }),
    soePay: {
      invalidResponse: (document: any) =>
        new DashError(DashErrorCodes.PAYMENT__SOEPAY__INVALID_RESPONSE, HttpStatus.BAD_REQUEST, {
          description: document,
        }),
      apiFailure: (error: any) =>
        new DashError(DashErrorCodes.PAYMENT__SOEPAY__API_FAILURE, HttpStatus.BAD_REQUEST, {
          description: error.message,
          cause: error,
        }),
      maxAttemptCalled: (status: HttpStatus) =>
        new DashError(DashErrorCodes.PAYMENT__SOEPAY__MAX_ATTEMPT_CALLED, HttpStatus.BAD_REQUEST, {
          description: `Max attempt called, status: ${status}`,
        }),
      voidFailed: (length: number, reasons: string) =>
        new DashError(DashErrorCodes.PAYMENT__SOEPAY__VOID_FAILED, HttpStatus.BAD_REQUEST, {
          description: `Failed to void ${length} payment transaction(s). Reasons: ${reasons}`,
        }),
    },
    paymentTxNotFound: (paymentTxId: string) =>
      new DashError(DashErrorCodes.PAYMENT__TX__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Payment_Tx not found: ${paymentTxId}`,
      }),
    paymentTxWrongTypeOrStatus: (
      paymentTx: PaymentTx,
      expectedTypes: PaymentInformationType[],
      expectedStatus: PaymentInformationStatus,
    ) =>
      new DashError(DashErrorCodes.PAYMENT__TX__INVALID_TYPE_OR_STATUS, HttpStatus.FORBIDDEN, {
        description: `Wrong payment_tx type: ${paymentTx.type} or status: ${paymentTx.status} for paymentTx: ${paymentTx.id}, expected type: ${expectedTypes} and status: ${expectedStatus}`,
      }),
    amountExceedsAuth: (paymentTx: PaymentTx, amount: number) =>
      new DashError(DashErrorCodes.PAYMENT__TX__AMOUNT_EXCEEDS_AUTH, HttpStatus.FORBIDDEN, {
        description: `The Capture Amount: ${amount} exceeds auth: ${paymentTx.amount} for paymentTx: ${paymentTx.id}`,
      }),
    alreadySold: (txId: string, paymentInstrumentId: string) =>
      new DashError(DashErrorCodes.PAYMENT__ALREADY_SOLD, HttpStatus.CONFLICT, {
        description: `The payment has already been processed for tx: ${txId} and payment instrument: ${paymentInstrumentId}`,
      }),
    unableToSell: (txId: string, paymentInstrumentId: string) =>
      new DashError(DashErrorCodes.PAYMENT__UNABLE_TO_SELL, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: `The payment has failed for tx: ${txId} and payment instrument: ${paymentInstrumentId}`,
      }),
  },
  storage: {
    noFilePath: () =>
      new DashError(DashErrorCodes.STORAGE__FILE_PATH_NOT_FOUND, HttpStatus.BAD_REQUEST, {
        description: "No file path provided",
      }),
    invalidContentType: () =>
      new DashError(DashErrorCodes.STORAGE__INVALID_CONTENT, HttpStatus.UNSUPPORTED_MEDIA_TYPE, {
        description: "Invalid content type",
      }),
    fileNotFound: (filePath: string) =>
      new DashError(DashErrorCodes.STORAGE__FILE_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `File not found: ${filePath}`,
      }),
  },
  message: {
    factoryWrongImplementation: (custom?: any) =>
      new DashError(DashErrorCodes.MESSAGE__FACTORY_WRONG_IMPLEMENTATION, HttpStatus.INTERNAL_SERVER_ERROR, { custom }),
    factoryNotImplemented: () =>
      new DashError(DashErrorCodes.MESSAGE__FACTORY_NOT_IMPLEMENTED, HttpStatus.NOT_IMPLEMENTED),
    sms: {
      recipientRequired: (tranId: string) =>
        new DashError(DashErrorCodes.MESSAGE__SMS__RECIPIENT_REQUIRED, HttpStatus.BAD_REQUEST, {
          description: `Recipient is required, transactionId: ${tranId}`,
        }),
      templateNotImplemented: (message?: Record<string, any>) =>
        new DashError(DashErrorCodes.MESSAGE__SMS__TEMPLATE_NOT_IMPLEMENTED, HttpStatus.NOT_IMPLEMENTED, {
          description: "template is not implemented",
          custom: message,
        }),
      twilioCreate: (error: any) =>
        new DashError(DashErrorCodes.MESSAGE__SMS__TWILIO_CREATE, HttpStatus.BAD_REQUEST, {
          description: error.message,
          cause: error,
        }),
    },
    whatsapp: {
      recipientRequired: (tranId: string) =>
        new DashError(DashErrorCodes.MESSAGE__WHATSAPP__RECIPIENT_REQUIRED, HttpStatus.BAD_REQUEST, {
          description: `Recipient is required, transactionId: ${tranId}`,
        }),
      templateNotImplemented: (message?: Record<string, any>) =>
        new DashError(DashErrorCodes.MESSAGE__WHATSAPP__TEMPLATE_NOT_IMPLEMENTED, HttpStatus.NOT_IMPLEMENTED, {
          description: "template is not implemented",
          custom: message,
        }),
      apiFailure: (error: any) =>
        new DashError(DashErrorCodes.MESSAGE__WHATSAPP__API_FAILURE, HttpStatus.BAD_REQUEST, {
          description: error.message,
          cause: error,
        }),
      invalidNumber: (number: string) =>
        new DashError(DashErrorCodes.MESSAGE__WHATSAPP__INVALID_NUMBER, HttpStatus.BAD_REQUEST, {
          description: `Invalid number: ${number}`,
        }),
      maxAttemptCalled: (status: HttpStatus) =>
        new DashError(DashErrorCodes.MESSAGE__WHATSAPP__MAX_ATTEMPT_CALLED, HttpStatus.BAD_REQUEST, {
          description: `Max attempt called, status: ${status}`,
        }),
      templateNotEnabled: (templateName: string) =>
        new DashError(DashErrorCodes.MESSAGE__WHATSAPP__TEMPLATE_NOT_ENABLED, HttpStatus.BAD_REQUEST, {
          description: `Template: ${templateName} is not enabled`,
        }),
    },
  },
  auth: {
    tokenNotSet: () =>
      new DashError(DashErrorCodes.AUTH__TOKEN_NOT_SET, HttpStatus.UNAUTHORIZED, {
        description: "Unauthorized, please set authorization header",
      }),
    tokenInvalidRole: () =>
      new DashError(DashErrorCodes.AUTH__TOKEN_INVALID_ROLE, HttpStatus.UNAUTHORIZED, {
        description: "Unauthorized, please check the role",
      }),
    firebaseAuthError: (error: FirebaseAuthError) =>
      new DashError(DashErrorCodes.AUTH__ERROR, HttpStatus.UNAUTHORIZED, {
        description: error.message,
        cause: error,
      }),
    firebaseTokenExpired: (error: FirebaseAuthError) =>
      new DashError(DashErrorCodes.AUTH__TOKEN_EXPIRED, HttpStatus.UNAUTHORIZED, {
        description: error.message,
        cause: error,
      }),
    invalidCredentials: () =>
      new DashError(DashErrorCodes.AUTH__INVALID_CREDENTIALS, HttpStatus.UNAUTHORIZED, {
        description: "Invalid credentials",
      }),
    userDisabled: () =>
      new DashError(DashErrorCodes.AUTH__USER_DISABLED, HttpStatus.UNAUTHORIZED, {
        description: "User is disabled",
      }),
    unknown: (error: any) =>
      new DashError(DashErrorCodes.GLOBAL__UNKNOWN, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: error.message,
        cause: error,
      }),
    authAttemptsExceeded: () =>
      new DashError(DashErrorCodes.AUTH__AUTH_ATTEMPTS_EXCEEDED, HttpStatus.TOO_MANY_REQUESTS, {
        description: "Auth attempts exceeded",
      }),
    missingEmail: () =>
      new DashError(DashErrorCodes.AUTH__MISSING_EMAIL, HttpStatus.UNAUTHORIZED, {
        description: "Auth credentials are missing an associated email",
      }),
    mfaError: (error: any) =>
      new DashError(DashErrorCodes.AUTH__MFA_ERROR, HttpStatus.UNAUTHORIZED, {
        description: error.message,
        cause: error,
      }),
    recaptchaError: (error: any) =>
      new DashError(DashErrorCodes.AUTH__RECAPTCHA_ERROR, HttpStatus.UNAUTHORIZED, {
        description: error.message,
        cause: error,
      }),
    invalidResetToken: () =>
      new DashError(DashErrorCodes.AUTH__INVALID_RESET_TOKEN_ERROR, HttpStatus.BAD_REQUEST, {
        description: "Invalid reset token",
      }),
    inadequatePassword: () =>
      new DashError(DashErrorCodes.AUTH__INADEQUATE_PASSWORD, HttpStatus.BAD_REQUEST, {
        description: "Password must have at least 12 characters including at least one special character.",
      }),
    passwordUsedInLastYear: () =>
      new DashError(DashErrorCodes.AUTH__PASSWORD_USED_IN_LAST_YEAR, HttpStatus.BAD_REQUEST, {
        description: "Password cannot have been used in the last year.",
      }),
    invalidDeviceJwt: () =>
      new DashError(DashErrorCodes.AUTH__INVALID_DEVICE_JWT, HttpStatus.UNAUTHORIZED, {
        description: "Invalid device jwt",
      }),
    emailAlreadyExists: (email: string) =>
      new DashError(DashErrorCodes.AUTH__EMAIL_ALREADY_EXIST, HttpStatus.TOO_MANY_REQUESTS, {
        description: `Email: ${email} already exists`,
      }),
    userNotFound: (email: string) =>
      new DashError(DashErrorCodes.AUTH__USER_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `User with email: ${email} not found in auths collection in firestore`,
      }),
    fleetIdRequired: () =>
      new DashError(DashErrorCodes.AUTH__FLEET_ID_REQUIRED, HttpStatus.BAD_REQUEST, {
        description: "Fleet Id is required for fleet manager",
      }),
  },
  user: {
    notFoundInFirestore: (appDatabaseId: string) =>
      new DashError(DashErrorCodes.USER__APP__USER_NOT_FOUND_IN_FIRESTORE, HttpStatus.NOT_FOUND, {
        description: `App User: ${appDatabaseId} not found in Firestore`,
      }),
    notFoundInSql: (appDatabaseId: string) =>
      new DashError(DashErrorCodes.USER__APP__USER_NOT_FOUND_IN_SQL_DB, HttpStatus.NOT_FOUND, {
        description: `App User: ${appDatabaseId} not found in SQL`,
      }),
    notFoundPhoneNumberInSql: (phoneNumber: string) =>
      new DashError(DashErrorCodes.USER__APP__USER_PHONE_NUMBER_NOT_FOUND_IN_SQL_DB, HttpStatus.NOT_FOUND, {
        description: `App User: phone number ${phoneNumber} not found in SQL`,
      }),
    notFoundAnonymousUserByPhoneNumberInSql: (phoneNumber: string) =>
      new DashError(
        DashErrorCodes.USER__APP__USER_NOT_FOUND_IN_SQL_DB_WITH_ANONYMOUS_PHONENUMBER,
        HttpStatus.NOT_FOUND,
        {
          description: `2nd Row User: ${phoneNumber} not found in SQL`,
        },
      ),
    notFoundInFirebaseAuth: (appDatabaseId: string) =>
      new DashError(DashErrorCodes.USER__APP__USER_NOT_FOUND_IN_FIREBASEAUTH, HttpStatus.NOT_FOUND, {
        description: `App User: ${appDatabaseId} not found in Firebase Auth`,
      }),
    updatedNoData: (appDatabaseId: string) =>
      new DashError(DashErrorCodes.USER__APP__USER_UPDATED_NO_DATA, HttpStatus.BAD_REQUEST, {
        description: `No user data update for id: ${appDatabaseId}`,
      }),
    missing: () =>
      new DashError(DashErrorCodes.USER__APP__USER_MISSING, HttpStatus.BAD_REQUEST, {
        description: "App User is missing",
      }),
    notPhoneNumberInFirebaseAuth: (appDatabaseId: string) =>
      new DashError(DashErrorCodes.USER__APP__USER_NOT_FOUND_PHONE_NUMBER_IN_FIREBASEAUTH, HttpStatus.NOT_FOUND, {
        description: `No Phone Number found in Firebase Auth for user: ${appDatabaseId}`,
      }),
    invalidUserType: (appDatabaseId: string) =>
      new DashError(DashErrorCodes.USER__APP__USER_INVALID_TYPE, HttpStatus.BAD_REQUEST, {
        description: `Invalid type for user: ${appDatabaseId}`,
      }),
    alreadyRegisteredShouldGoToApp: (phoneNumer: string) =>
      new DashError(DashErrorCodes.USER__APP__USER_ALREADY_REGISTERED_SHOULD_GO_TO_APP, HttpStatus.BAD_REQUEST, {
        description: `User with phone number: ${phoneNumer} already registered, should go to App`,
      }),
    referralCodeGenerationLimitExceeded: (appDatabaseId: string) =>
      new DashError(DashErrorCodes.USER__APP__REFERRAL_CODE_GENERATION_LIMIT_EXCEEDED, HttpStatus.BAD_REQUEST, {
        description: `Referral code generation limit exceeded for user: ${appDatabaseId}`,
      }),
    referralCodeNotFound: (referralCode: string) =>
      new DashError(DashErrorCodes.USER__APP__REFERRAL_CODE_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Referral code: ${referralCode} not found`,
      }),
    referralCodeInvalid: (referralCode: string) =>
      new DashError(DashErrorCodes.USER__APP__REFERRAL_CODE_INVALID, HttpStatus.BAD_REQUEST, {
        description: `Referral code: ${referralCode} is invalid`,
      }),
    referralCodeGenerationFailed: (appDatabaseId: string) =>
      new DashError(DashErrorCodes.USER__APP__REFERRAL_CODE_GENERATION_FAILED, HttpStatus.BAD_REQUEST, {
        description: `Referral code generation failed for user: ${appDatabaseId}`,
      }),
    pin: {
      createPinFailedWithExistingPin: (userId: string) =>
        new DashError(
          DashErrorCodes.USER__APP__USER_CREATE_PIN_FAILED_WITH_EXISTING_PIN,
          HttpStatus.INTERNAL_SERVER_ERROR,
          {
            description: `Create pin for user: ${userId} Failed`,
          },
        ),
      verifyPinFailedWithoutExistingPin: (userId: string) =>
        new DashError(
          DashErrorCodes.USER__APP__USER_VERIFY_PIN_FAILED_WITHOUT_EXISTING_PIN,
          HttpStatus.INTERNAL_SERVER_ERROR,
          {
            description: `Verify pin for user: ${userId} Failed`,
          },
        ),
      userBlockedForExceedingPinErrorCount: (userId: string) =>
        new DashError(DashErrorCodes.USER__APP__USER_BLOACKED_FOR_EXCEEDING_PIN_ERROR_COUNT, HttpStatus.FORBIDDEN, {
          description: `User: ${userId} is blocked for exceeding pin error count`,
        }),
      updatePinFailedWithWrongOldPin: (userId: string) =>
        new DashError(
          DashErrorCodes.USER__APP__USER_UPDATED_PIN_FAILED_WITH_WRONG_OLD_PIN,
          HttpStatus.INTERNAL_SERVER_ERROR,
          {
            description: `Update pin for user: ${userId} Failed`,
          },
        ),
      verifyFailedWithPinNotFoundInRequest: () =>
        new DashError(DashErrorCodes.USER__APP__USER_VERIFY_PIN_NOT_FOUND_IN_REQUEST, HttpStatus.NOT_FOUND, {
          description: "Verify failed: pin not found",
        }),
      verifyFailedWithPinUnkown: () =>
        new DashError(DashErrorCodes.USER__APP__USER_VERIFY_PIN_UNKNOWN, HttpStatus.NOT_FOUND, {
          description: "Verify failed",
        }),
    },
  },
  incentive: {
    campaign: {
      notFound: (campaignId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__CAMPAIGN__NOT_FOUND, HttpStatus.NOT_FOUND, {
          description: `Campaign not found for id: ${campaignId}`,
        }),
      invalidDate: (campaignId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__CAMPAIGN__INVALID_DATE, HttpStatus.BAD_REQUEST, {
          description: `Invalid date for campaign: ${campaignId}`,
        }),
      notYetStarted: (campaignId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__CAMPAIGN__NOT_YET_STARTED, HttpStatus.UNPROCESSABLE_ENTITY, {
          description: `Campaign not yet started: ${campaignId}`,
        }),
      alreadyEnded: (campaignId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__CAMPAIGN__EXPIRED, HttpStatus.UNPROCESSABLE_ENTITY, {
          description: `Campaign already ended: ${campaignId}`,
        }),
    },
    discount: {
      cannotTransitionState: (id: string, stateFrom: DiscountState, stateTo: DiscountState) => {
        return new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__CANNOT_TRANSITION_STATE, HttpStatus.BAD_REQUEST, {
          description: `Discount (${id}) cannot transition from state: ${stateFrom} to state: ${stateTo}`,
        });
      },
      unableToCreate: () =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__NOT_CREATED, HttpStatus.INTERNAL_SERVER_ERROR, {
          description: "Discount not created. Please try again",
        }),
      valueExceeded: (discountId: string, discountValue: number) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__AMOUNT_EXCEEDED, HttpStatus.UNPROCESSABLE_ENTITY, {
          description: `Redeem value exceeded for discountId: ${discountId}, max is ${discountValue}`,
        }),
      userMismatch: (
        discountId: string,
        txId: string,
        discountUserId: string | undefined,
        txUserId: string | undefined,
      ) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__USER_MISMATCH, HttpStatus.UNPROCESSABLE_ENTITY, {
          description: `User mismatch for discountId: ${discountId}, txId: ${txId}, discountUserId: ${discountUserId}, txUserId: ${txUserId}`,
        }),
      alreadyRedeemed: (discountId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__ALREADY_REDEEMED, HttpStatus.UNPROCESSABLE_ENTITY, {
          description: `Discount already redeemed for discountId: ${discountId}`,
        }),
      notApplied: (discountId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__NOT_APPLIED, HttpStatus.UNPROCESSABLE_ENTITY, {
          description: `Discount was not applied for discountId: ${discountId}. Check your details and try again.`,
        }),
      invalidCampaignDate: (campaignId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__INVALID_CAMPAIGN_DATE, HttpStatus.BAD_REQUEST, {
          description: `Can't request discount for campaign: ${campaignId} as it's not active, please check the date`,
        }),
      invalidCampaignPaymentChannel: (campaignId: string, wanted: PaymentChannelType) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__INVALID_CAMPAIGN_PAYMENT_CHANNEL, HttpStatus.BAD_REQUEST, {
          description: `Can't request discount for campaign: ${campaignId}, payment channel is not ${wanted}`,
        }),
      notFoundDiscountRulesForCampaign: (campaignId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__INVALID_CAMPAIGN_NO_DISCOUNT_RULES, HttpStatus.BAD_REQUEST, {
          description: `Can't request discount for campaign: ${campaignId}, no discount rules found`,
        }),
      invalidTxType: (txId: string, txType: TxTypes) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__INVALID_TX_TYPE, HttpStatus.BAD_REQUEST, {
          description: `Can't request discount for trip: ${txId}, txType is not: ${txType}`,
        }),
      tripAlreadyEnded: (txId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__TRIP_ALREADY_END, HttpStatus.BAD_REQUEST, {
          description: `Can't request discount for trip: ${txId}, trip is already ended`,
        }),
      maxUsagePerUserReached: (campaignId: string, phoneNumber: string, appDatabaseId?: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__MAX_USAGE_PER_USER_REACHED, HttpStatus.BAD_REQUEST, {
          description: `Max usage per user reached for campaign: ${campaignId}, phoneNumber: ${phoneNumber}, appDatabaseId: ${appDatabaseId}`,
        }),
      notFound: (discountId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__NOT_FOUND, HttpStatus.NOT_FOUND, {
          description: `Discount not found for id: ${discountId}`,
        }),
      tripAlreadyClaimedByOtherUser: (tripId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__ALREADY_REQUESTED_BY_OTHER_USER, HttpStatus.BAD_REQUEST, {
          description: `Trip: ${tripId} already claimed by other user`,
        }),
      userNotFound: (campaignId: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__USER_NOT_FOUND, HttpStatus.NOT_FOUND, {
          description: `User not found for campaign: ${campaignId}`,
        }),
      rewardCodeNotFound: (rewardCode: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__REWARD_CODE_NOT_FOUND, HttpStatus.NOT_FOUND, {
          description: `Reward code not found: ${rewardCode}`,
        }),
      rewardCodeAlreadyClaimed: (rewardCode: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__REWARD_CODE_ALREADY_CLAIMED, HttpStatus.BAD_REQUEST, {
          description: `Reward code already claimed: ${rewardCode}`,
        }),
      rewardCodeExpired: (rewardCode: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__REWARD_CODE_EXPIRED, HttpStatus.BAD_REQUEST, {
          description: `Reward code expired: ${rewardCode}`,
        }),
      rewardCodeLimitExceeded: (rewardCode: string) =>
        new DashError(DashErrorCodes.INCENTIVE__DISCOUNT__REWARD_CODE_LIMIT_EXCEEDED, HttpStatus.BAD_REQUEST, {
          description: `Reward code limit exceeded: ${rewardCode}`,
        }),
    },
  },
  appDatabase: {
    notFoundInFirestore: (path: string, documentId: string) =>
      new DashError(DashErrorCodes.FIRESTORE_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `${documentId} is not found in Firestore in ${path} collection`,
      }),
    unableToCreateDocument: (path: string) =>
      new DashError(DashErrorCodes.FIRESTORE_UNABLE_TO_CREATE, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: `unable to create document in Firestore in ${path} collection`,
      }),
    documentAlreadyExists: (path: string, documentId: string) =>
      new DashError(DashErrorCodes.FIRESTORE_ALREADY_EXISTS, HttpStatus.CONFLICT, {
        description: `${documentId} already exists in Firestore in ${path} collection`,
      }),
  },
  infrastructure: {
    secretNotFound: (secretName: string) =>
      new DashError(DashErrorCodes.INFRASTRUCTURE__SECRET_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Secret: ${secretName} not found`,
      }),
  },
  pushNotification: {
    templatesCollectionNotFoundInFirestore: () =>
      new DashError(
        DashErrorCodes.FCM_PUSH_NOTIFICATION__TEMPLATES_COLLECTION_NOT_FOUND_IN_FIRESTORE,
        HttpStatus.NOT_FOUND,
        {
          description: "notification_templates collection not found in firestore",
        },
      ),
    templatesNoDataInFirestore: () =>
      new DashError(DashErrorCodes.FCM_PUSH_NOTIFICATION__TEMPLATES_DATA_NOT_FOUND_IN_FIRESTORE, HttpStatus.NOT_FOUND, {
        description: "notification_templates data not found in firestore",
      }),
    templateMissing: (templateEventName: NotificationTriggerEventType) =>
      new DashError(DashErrorCodes.FCM_PUSH_NOTIFICATION__MISSING_NOTIFICATION_TEMPLATE, HttpStatus.NOT_FOUND, {
        description: `Notification template: ${templateEventName} is missing`,
      }),
    wrongTemplate: (templateEventName: NotificationTriggerEventType) =>
      new DashError(DashErrorCodes.FCM_PUSH_NOTIFICATION__MISSING_NOTIFICATION_TEMPLATE, HttpStatus.NOT_FOUND, {
        description: `Notification template: ${templateEventName} is invalid`,
      }),
  },
  link: {
    notFound: () =>
      new DashError(DashErrorCodes.LINK__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: "Link not found",
      }),
    failedToCreateLink: () =>
      new DashError(DashErrorCodes.LINK__FAILED_TO_CREATE, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: "Failed to create link",
      }),
    expired: () =>
      new DashError(DashErrorCodes.LINK__EXPIRED, HttpStatus.BAD_REQUEST, {
        description: "Link expired",
      }),
  },
  kmsEncryption: {
    keyRingNotFound: (keyRingId: string) =>
      new DashError(DashErrorCodes.ENCRYPTION__KEYRING_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Key ring not found: ${keyRingId}`,
        custom: { keyRingId },
      }),
    keyRingNotCreated: (keyRingId: string) =>
      new DashError(DashErrorCodes.ENCRYPTION__KEYRING_NOT_CREATED, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: `Key ring not created: ${keyRingId}`,
        custom: { keyRingId },
      }),
    cryptoKeyNotFound: (cryptoKeyId: string) =>
      new DashError(DashErrorCodes.ENCRYPTION__CRYPTOKEY_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Crypto key not found: ${cryptoKeyId}`,
        custom: { cryptoKeyId },
      }),
    cryptoKeyNotCreated: (cryptoKeyId: string) =>
      new DashError(DashErrorCodes.ENCRYPTION__CRYPTOKEY_NOT_CREATED, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: `Crypto key not created: ${cryptoKeyId}`,
        custom: { cryptoKeyId },
      }),
    decryptionFailed: (cipherText: string | Uint8Array) =>
      new DashError(DashErrorCodes.ENCRYPTION__DECRYPTION_FAILED, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: `Decryption failed: ${cipherText}`,
        custom: { cipherText },
      }),
    encryptionFailed: () =>
      new DashError(DashErrorCodes.ENCRYPTION__ENCRYPTION_FAILED, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: "Encryption failed",
      }),
  },
  webhook: {
    failedToSend: (error: any) =>
      new DashError(DashErrorCodes.WEBHOOK__FAILED_TO_SEND, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: "Failed to send webhook",
        cause: error,
      }),
    notFound: (webhookId: string) =>
      new DashError(DashErrorCodes.WEBHOOK__NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Webhook not found for webhookId: ${webhookId}`,
      }),
    signatureSecretNotFound: (signatureSecretId: string) =>
      new DashError(DashErrorCodes.WEBHOOK__SIGNATURE_SECRET_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Signature secret not found for signatureSecretId: ${signatureSecretId}`,
      }),
    invalidSignatureKeyId: (signatureKeyId: string) =>
      new DashError(DashErrorCodes.WEBHOOK__INVALID_SIGNATURE_KEY_ID, HttpStatus.BAD_REQUEST, {
        description: `Invalid signature key id: ${signatureKeyId}`,
      }),
    notImplemented: (event: WebhookEventType) =>
      new DashError(DashErrorCodes.WEBHOOK__NOT_IMPLEMENTED, HttpStatus.NOT_IMPLEMENTED, {
        description: `Webhook event: ${event} is not implemented`,
      }),
  },
  report: {
    queryMetadataNotImplementedYet: (name: string) =>
      new DashError(DashErrorCodes.REPORT__QUERY_METADATA_NOT_IMPLEMENTED_YET, HttpStatus.NOT_IMPLEMENTED, {
        description: `For report ${name}, Query metadata not implemented yet, please set queryString`,
      }),
    noReportFileInCloudStorage: (reportName: string) =>
      new DashError(DashErrorCodes.REPORT__NO_REPORT_FILE_IN_CLOUD_STORAGE, HttpStatus.NOT_FOUND, {
        description: `No report file found in cloud storage for report: ${reportName}`,
      }),
  },
  hailing: {
    invalidBoostAmount: (message: string) =>
      new DashError(DashErrorCodes.HAILING__INVALID_BOOST_AMOUNT, HttpStatus.BAD_REQUEST, {
        description: message,
      }),
    boostLimitExceeded: (currentBoost: number, maxLimit: number) =>
      new DashError(DashErrorCodes.HAILING__BOOST_LIMIT_EXCEEDED, HttpStatus.BAD_REQUEST, {
        description: `Boost limit exceeded. Current: $${currentBoost.toFixed(2)}, Max allowed: $${maxLimit.toFixed(2)}`,
        custom: { currentBoost, maxLimit },
      }),
    hailNotUpdatable: (hailStatus: string, hailId: string) =>
      new DashError(DashErrorCodes.HAILING__NOT_UPDATABLE, HttpStatus.BAD_REQUEST, {
        description: `Hail not updatable with status ${hailStatus}`,
        custom: { hailId, hailStatus },
      }),
  },
  notification: {
    notImplemented: (type: NotificationTaskType) =>
      new DashError(DashErrorCodes.NOTIFICATION__NOT_IMPLEMENTED, HttpStatus.NOT_IMPLEMENTED, {
        description: `Notification type: ${type} is not implemented`,
      }),
    userNotFound: (userId: string) =>
      new DashError(DashErrorCodes.NOTIFICATION__USER_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `User not found: ${userId}`,
        custom: { userId },
      }),
    noUsersFound: (phoneNumbers?: string[]) =>
      new DashError(DashErrorCodes.NOTIFICATION__USERS_NOT_FOUND, HttpStatus.BAD_REQUEST, {
        description: "No users found for the provided phone numbers",
        custom: { phoneNumbers },
      }),
    taskNotFound: (taskId: string) =>
      new DashError(DashErrorCodes.NOTIFICATION__TASK_NOT_FOUND, HttpStatus.NOT_FOUND, {
        description: `Notification Task not found: ${taskId}`,
        custom: { taskId },
      }),
    failedToCreateTask: (error: any) =>
      new DashError(DashErrorCodes.NOTIFICATION__FAILED_TO_CREATE_TASK, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: "Failed to create notification task",
        cause: error,
      }),
    taskInvalidState: (taskId: string, currentState: NotificationTaskStatus, newState: NotificationTaskStatus) =>
      new DashError(DashErrorCodes.NOTIFICATION__TASK_INVALID_STATE, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: `Invalid state transition for Task ${taskId}: ${currentState} -> ${newState}`,
        custom: { taskId, currentState, newState },
      }),
    cannotUpdateTaskWithStatus: (taskId: string, status: NotificationTaskStatus) =>
      new DashError(DashErrorCodes.NOTIFICATION__CANNOT_UPDATE_TASK_WITH_STATUS, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: `Task ${taskId} with status '${status}' can no longer be updated`,
        custom: { taskId, status },
      }),
    noValidPhoneNumbers: () =>
      new DashError(DashErrorCodes.NOTIFICATION__NO_VALID_PHONE_NUMBERS, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: "No valid phone numbers provided for notifications",
      }),
    invalidPhoneNumbers: (validationError: Error) =>
      new DashError(DashErrorCodes.NOTIFICATION__INVALID_PHONE_NUMBER, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: `Invalid phone numbers provided: ${validationError.message}`,
        cause: validationError,
      }),
    errorSending: () =>
      new DashError(DashErrorCodes.NOTIFICATION__ERROR_SENDING, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: "Error sending notification",
      }),
    invalidUserType: (userType: unknown) =>
      new DashError(DashErrorCodes.NOTIFICATION__INVALID_USER_TYPE, HttpStatus.UNPROCESSABLE_ENTITY, {
        description: `Invalid user type: ${userType}`,
      }),
  },
  fleetTaxi: {
    noFleetQuoteFound: () =>
      new DashError(DashErrorCodes.FLEET_TAXI__NO_FLEET_QUOTE_FOUND, HttpStatus.BAD_REQUEST, {
        description: "No fleet quote found",
      }),
    invalidFleetOrderRequest: () =>
      new DashError(DashErrorCodes.FLEET_TAXI__INVALID_FLEET_ORDER_REQUEST, HttpStatus.BAD_REQUEST, {
        description: "Invalid fleet order request",
      }),
    noFleetOrderFound: () =>
      new DashError(DashErrorCodes.FLEET_TAXI__NO_FLEET_ORDER_FOUND, HttpStatus.BAD_REQUEST, {
        description: "No fleet order found",
      }),
    noUpdateFleetOrderDelegatee: () =>
      new DashError(DashErrorCodes.FLEET_TAXI__NO_UPDATE_FLEET_ORDER_DELEGATEE, HttpStatus.BAD_REQUEST, {
        description: "No update fleet order delegatee found",
      }),
    noDriverTypeForPartnerKey: () =>
      new DashError(DashErrorCodes.FLEET_TAXI__NO_DRIVER_TYPE_FOR_PARTNER_KEY, HttpStatus.BAD_REQUEST, {
        description: "No driver type found for partner key",
      }),
    noFleetPartnerFound: () =>
      new DashError(DashErrorCodes.FLEET_TAXI__NO_FLEET_PARTNER_FOUND, HttpStatus.BAD_REQUEST, {
        description: "No fleet partner found",
      }),
    noMeterFound: () =>
      new DashError(DashErrorCodes.FLEET_TAXI__NO_METER_FOUND, HttpStatus.BAD_REQUEST, {
        description: "No meter found for fleet partner",
      }),
    noCancelFleetOrderDelegatee: () =>
      new DashError(DashErrorCodes.FLEET_TAXI__NO_CANCEL_FLEET_ORDER_DELEGATEE, HttpStatus.BAD_REQUEST, {
        description: "No cancel fleet order delegatee found",
      }),
    syncabCreateOrderApiError: (error: any) =>
      new DashError(DashErrorCodes.FLEET_TAXI__SYNCAB_API_ERROR, HttpStatus.BAD_REQUEST, {
        description: "Syncab API error on create order",
        cause: error,
      }),
    syncablQuoteApiError: (error: any) =>
      new DashError(DashErrorCodes.FLEET_TAXI__SYNCAB_API_ERROR, HttpStatus.BAD_REQUEST, {
        description: "Syncab API error on quote",
        cause: error,
      }),
    noFleetVehicleTypeFound: (fleetVehicleType: string) =>
      new DashError(DashErrorCodes.FLEET_TAXI__NO_FLEET_VEHICLE_TYPE_FOUND, HttpStatus.BAD_REQUEST, {
        description: `No fleet vehicle type found for fleetVehicleType: ${fleetVehicleType}`,
      }),
    noDriverPhoneNumber: () =>
      new DashError(DashErrorCodes.FLEET_TAXI__NO_DRIVER_PHONE_NUMBER, HttpStatus.BAD_REQUEST, {
        description: "No driver phone number found",
      }),
    createFleetOrderFailure: (error: any) =>
      new DashError(DashErrorCodes.FLEET_TAXI__CREATE_FLEET_ORDER_FAILURE, HttpStatus.BAD_REQUEST, {
        description: "Failed to create fleet order",
        cause: error,
      }),
  },
  geospatial: {
    kmlFileDownloadFailed: (fileName: string, bucketName: string, error: any) =>
      new DashError(DashErrorCodes.GEOSPATIAL__INITIALIZE_FAILED, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: `Failed to download file: ${fileName} from bucket: ${bucketName}`,
        cause: error,
      }),
    kmlFileIsEmpty: (fileName: string) =>
      new DashError(DashErrorCodes.GEOSPATIAL__INITIALIZE_FAILED, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: `KML file ${fileName} is empty or not found.`,
      }),
    kmlFileConversionError: (fileName: string, error: any) =>
      new DashError(DashErrorCodes.GEOSPATIAL__INITIALIZE_FAILED, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: `Failed to convert KML file: ${fileName}`,
        cause: error,
      }),
    kmlFileContentNotString: (fileName: string) =>
      new DashError(DashErrorCodes.GEOSPATIAL__INITIALIZE_FAILED, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: `FKML file content is not a string.: ${fileName}`,
      }),
    parsePolygonFailed: (value: string, error: any) =>
      new DashError(DashErrorCodes.GEOSPATIAL__PARSE_POLYGON_FAILED, HttpStatus.INTERNAL_SERVER_ERROR, {
        description: `Failed to parse polygon with value: ${value}`,
        cause: error,
      }),
  },
};

type ErrorResponseBody = {
  status: HttpStatus;
  code: DashErrorCodes;
  timestamp: string;
  description?: string;
};

export class DashExceptionFilter implements ExceptionFilter {
  constructor(private logger: LoggerServiceAdapter) {}

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    // const request = ctx.getRequest<Request>();
    const status = exception.getStatus?.() ?? HttpStatus.INTERNAL_SERVER_ERROR;
    const errorResponse = exception.getResponse?.() ?? { description: exception.message };

    /**
     * ERROR BUILDER
     */
    const responseBody: ErrorResponseBody = {
      ...(exception?.custom ? exception.custom : {}),
      ...errorResponse,
      status,
      timestamp: new Date().toISOString(),
    };

    this.logger.error("Api Error", responseBody, exception as Error);

    /**
     * SENTRY
     */
    const sentryContext: CaptureContext = {
      contexts: {
        app: {
          app_version: infoJson.commit_hash,
        },
      },
    };
    // if (request.user) {
    //   sentryContext.user = {};
    //   if (request.user?.email) sentryContext.user.email = request.user.email;
    //   if (request.user?.id) sentryContext.user.id = request.user.id;
    //   if (request.user?.phone_number) sentryContext.user.phoneNumber = request.user?.phone_number;
    // }

    captureException(exception, sentryContext);

    response.status(status).json(responseBody);
  }
}

export const listAuthErrors = () => [
  errorBuilder.auth.tokenNotSet(),
  errorBuilder.auth.tokenInvalidRole(),
  errorBuilder.auth.firebaseTokenExpired(new FirebaseAuthError("firebase token expired")),
  errorBuilder.auth.firebaseAuthError(new FirebaseAuthError("auth/invalid-credential")),
];

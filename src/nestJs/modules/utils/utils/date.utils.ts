import { DocumentData, DocumentReference, Timestamp } from "firebase-admin/firestore";
import moment from "moment";

import { errorBuilder } from "./error.utils";

const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;

function isFirestoreSentinel(val: any): boolean {
  return (
    typeof val === "object" && val !== null && typeof val._methodName === "string" && Object.keys(val).length === 0
  );
}

/**
 * dateUtils
 */
const dateUtils = {
  /**
   * objectDatesToTimestamps
   * @param obj Partial<T>
   * @returns DocumentData
   */
  objectDatesToTimestamps<T>(obj: Partial<T>): DocumentData {
    for (const [key, value] of Object.entries(obj)) {
      if (value instanceof Date) {
        obj[key as keyof T] = Timestamp.fromDate(value) as unknown as T[keyof T];
      } else if (Array.isArray(value)) {
        obj[key as keyof T] = value.map((item) => {
          if (typeof item === "object" && item !== null && !(value instanceof DocumentReference)) {
            return this.objectDatesToTimestamps(item);
          }
          return item;
        }) as unknown as T[keyof T];
      } else if (typeof value === "object" && value !== null && !(value instanceof DocumentReference)) {
        obj[key as keyof T] = this.objectDatesToTimestamps(value) as unknown as T[keyof T];
      }
    }
    return obj as DocumentData;
  },
  /**
   * objectTimestampsToDates
   * @param obj DocumentData
   * @returns T
   */
  objectTimestampsToDates<T>(obj: DocumentData): T {
    for (const [key, value] of Object.entries(obj)) {
      if (value instanceof Timestamp) {
        obj[key] = value.toDate();
      } else if (Array.isArray(value)) {
        obj[key] = value.map((item) => {
          if (typeof item === "object" && item !== null && !(value instanceof DocumentReference)) {
            return this.objectTimestampsToDates(item);
          }
          return item;
        });
      } else if (typeof value === "object" && value !== null && !(value instanceof DocumentReference)) {
        obj[key] = this.objectTimestampsToDates(value);
      }
    }
    return obj as T;
  },
  /**
   *parseDuration
   * @param durationString string
   * @returns moment.Duration
   */
  parseDuration(durationString: string): moment.Duration {
    const durationRegex = /(-?\d+)(\s*)([a-z]+)\b/gi;
    let match = null;
    const duration = moment.duration();

    while ((match = durationRegex.exec(durationString)) !== null) {
      const value = parseInt(match[1]);
      const unit = match[3].toLowerCase();
      switch (unit) {
        case "years":
        case "year":
        case "yrs":
        case "yr":
          duration.add(value, "years");
          break;
        case "months":
        case "month":
        case "mos":
        case "mo":
          duration.add(value, "months");
          break;
        case "weeks":
        case "week":
        case "wks":
        case "wk":
          duration.add(value, "weeks");
          break;
        case "days":
        case "day":
        case "d":
          duration.add(value, "days");
          break;
        case "hours":
        case "hour":
        case "hrs":
        case "hr":
        case "h":
          duration.add(value, "hours");
          break;
        case "minutes":
        case "minute":
        case "mins":
        case "min":
        case "m":
          duration.add(value, "minutes");
          break;
        case "seconds":
        case "second":
        case "secs":
        case "sec":
        case "s":
          duration.add(value, "seconds");
          break;
        default:
          throw errorBuilder.global.invalidParam(`duration unit: ${unit}`);
      }
    }
    return duration;
  },
  convertIsoStringsToDates(obj: any): any {
    if (isFirestoreSentinel(obj)) {
      return obj;
    }
    if (typeof obj === "object" && obj !== null && Object.keys(obj).length === 0) {
      return obj;
    }
    if (typeof obj === "string" && isoDateRegex.test(obj)) return new Date(obj);
    if (Array.isArray(obj)) return obj.map(dateUtils.convertIsoStringsToDates);
    if (obj && typeof obj === "object")
      return Object.fromEntries(Object.entries(obj).map(([k, v]) => [k, dateUtils.convertIsoStringsToDates(v)]));
    return obj;
  },
};

export default dateUtils;

import * as fs from "fs";

import * as archiver from "archiver";
import archiverZipEncrypted from "archiver-zip-encrypted";

export class ZipUtils {
  static async zipFile(
    inputFilePath: string,
    outputFilePath: string,
    options?: archiver.ArchiverOptions,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(outputFilePath);
      if (!archiver.isRegisteredFormat("zip-encrypted")) {
        archiver.registerFormat("zip-encrypted", archiverZipEncrypted);
      }
      const archive = archiver.create("zip-encrypted", {
        zlib: { level: 9 },
        encryptionMethod: options?.password ? "aes256" : undefined,
        ...options,
      });

      output.on("close", () => {
        resolve();
      });

      archive.on("warning", (err) => {
        if (err.code !== "ENOENT") {
          reject(err);
        }
      });

      archive.on("error", (err) => {
        reject(err);
      });

      archive.pipe(output);
      archive.file(inputFilePath, { name: inputFilePath.split("/").pop() ?? inputFilePath });
      archive.finalize();
    });
  }
}

import * as crypto from "crypto";

/**
 * Utility class for generating secret tokens, checksums, and validating them.
 */
export class ChecksumUtils {
  /**
   * Generates secret token that can be used for signing payloads.
   */
  static generateSecretToken(length: number = 32): string {
    return crypto.randomBytes(length).toString("hex");
  }

  /**
   * Generates checksum for payload using secret.
   */
  static generateChecksum(secret: string, payload: string): string {
    return crypto.createHmac("sha256", secret).update(payload).digest("hex");
  }

  /**
   * Validates if the received payload has a valid signature when compared to the received checksum.
   */
  static isSignatureValid(receivedPayload: string, receivedChecksum: string, secret: string): boolean {
    const generatedChecksum = crypto.createHmac("sha256", secret).update(receivedPayload).digest("hex");
    return crypto.timingSafeEqual(Buffer.from(generatedChecksum), Buffer.from(receivedChecksum));
  }
}

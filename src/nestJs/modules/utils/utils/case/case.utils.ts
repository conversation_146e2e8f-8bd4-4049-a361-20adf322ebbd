import _ from "lodash";

/**
 * casesUtils
 */
const casesUtils = {
  // Define keys to preserve during transformation
  EXCEPTIONS: ["i18n", "l10n", "API", "URL"], // Add more as needed

  /**
   * Check if a key should be preserved
   * @param key string
   * @returns boolean
   */
  shouldPreserve(key: string): boolean {
    return this.EXCEPTIONS.includes(key);
  },

  // Define locale patterns
  LOCALE_SNAKE_REGEX: /^[a-z]{2}_[a-z]{2}$/, // e.g., zh_hk
  LOCALE_CAMEL_REGEX: /^[a-z]{2}[A-Z]{2}$/, // e.g., zhHK

  /**
   * Check if a key is a locale in snake_case
   * @param key string
   * @returns boolean
   */
  isLocaleSnakeCase(key: string): boolean {
    return this.LOCALE_SNAKE_REGEX.test(key);
  },

  /**
   * Check if a key is a locale in camelCase
   * @param key string
   * @returns boolean
   */
  isLocaleCamelCase(key: string): boolean {
    return this.LOCALE_CAMEL_REGEX.test(key);
  },

  /**
   * Custom snake_case transformer for locale keys
   * @param key string
   * @returns string
   */
  customSnakeCase(key: string): string {
    // If key is in camelCase locale, convert to snake_case
    if (this.isLocaleCamelCase(key)) {
      // Insert underscore before uppercase letters and convert to lowercase
      return key.replace(/([a-z])([A-Z])/g, "$1_$2").toLowerCase();
    }
    // Otherwise, use lodash's snakeCase
    return _.snakeCase(key);
  },

  /**
   * Custom camelCase transformer for locale keys
   * @param key string
   * @returns string
   */
  customCamelCase(key: string): string {
    // If key is in snake_case locale, convert to camelCase
    if (this.isLocaleSnakeCase(key)) {
      // Split by underscore, capitalize parts after the first, and join
      return key
        .split("_")
        .map((part, index) => {
          if (index === 0) return part.toLowerCase();
          return part.toUpperCase();
        })
        .join("");
    }
    // Otherwise, use lodash's camelCase
    return _.camelCase(key);
  },

  /**
   * transform the input to snake case
   * @param obj any
   * @returns object
   */
  snakeKeys(obj: any): object {
    if (Array.isArray(obj)) {
      return obj.map((v) => this.snakeKeys(v));
    } else if (obj != null && obj.constructor === Object) {
      return Object.keys(obj).reduce((result, key) => {
        const newKey = this.shouldPreserve(key)
          ? key
          : this.isLocaleCamelCase(key)
          ? this.customSnakeCase(key)
          : _.snakeCase(key);
        return {
          ...result,
          [newKey]: this.snakeKeys(obj[key]),
        };
      }, {});
    }
    return obj;
  },
  /**
   * transform the input to camel case
   * @param obj any
   * @returns object
   */
  camelizeKeys(obj: any): object {
    if (Array.isArray(obj)) {
      return obj.map((v) => this.camelizeKeys(v));
    } else if (obj != null && obj.constructor === Object) {
      return Object.keys(obj).reduce((result, key) => {
        const newKey = this.shouldPreserve(key)
          ? key
          : this.isLocaleSnakeCase(key)
          ? this.customCamelCase(String(key))
          : _.camelCase(key);
        return {
          ...result,
          [newKey]: this.camelizeKeys(obj[key]),
        };
      }, {});
    }
    return obj;
  },
};

export default casesUtils;

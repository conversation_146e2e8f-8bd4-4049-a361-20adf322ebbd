import { Injectable } from "@nestjs/common";

import apiUtils from "./utils/api.utils";
import casesUtils from "./utils/case/case.utils";
import dateUtils from "./utils/date.utils";
import firebaseUtils from "./utils/firebase.utils";
import languageUtils from "./utils/language.utils";
import numberUtils from "./utils/number.utils";
import retryUtils from "./utils/retry.utils";
import stringUtils from "./utils/string.utils";
import versionUtils from "./utils/version.utils";

/**
 * Utils service
 */
@Injectable()
export class UtilsService {
  public readonly date = dateUtils;
  public readonly case = casesUtils;
  public readonly firebase = firebaseUtils;
  public readonly language = languageUtils;
  public readonly number = numberUtils;
  public readonly version = versionUtils;
  public readonly retry = retryUtils;
  public readonly string = stringUtils;

  public readonly api = apiUtils;

  public static readonly requestedByUnknown = "Unknown";
  constructor() {}
}

import { DynamicModule, Module } from "@nestjs/common";

import { UtilsService } from "./utils.service";

/**
 * Utils module
 */
@Module({
  providers: [UtilsService],
  exports: [UtilsService],
})
export class UtilsModule {
  /**
   * Utils module for root
   * @returns DynamicModule
   */
  static forRoot(): DynamicModule {
    return {
      global: true,
      module: UtilsModule,
      providers: [UtilsService],
      exports: [UtilsService],
    };
  }
}

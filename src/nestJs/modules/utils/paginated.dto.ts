import { ApiProperty } from "@nestjs/swagger";

export class PaginationInfo {
  @ApiProperty({
    description: "Total count of results",
    example: 10,
  })
  count: number;

  @ApiProperty({
    description: "Number of results to skip",
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: "Number of results to take",
    example: 10,
  })
  pageSize: number;
}

export class PaginatedResponseDto<T> {
  data: T[];

  @ApiProperty()
  pagination: PaginationInfo;
}

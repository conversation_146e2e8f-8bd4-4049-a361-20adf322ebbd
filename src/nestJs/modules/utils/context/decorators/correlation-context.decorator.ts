import { randomUUID } from "crypto";

import { CLS_ID, UseCls } from "nestjs-cls";

export function CorrelationContext(): MethodDecorator {
  return function (target: unknown, propertyKey: string | symbol, descriptor: TypedPropertyDescriptor<any>) {
    UseCls({
      setup(cls, event) {
        const data = event?.data?.message?.json;
        const correlationId = data?.correlationId ?? randomUUID();
        if (data?.correlationId) {
          delete data.correlationId;
        }
        cls.set(CLS_ID, correlationId);
        return correlationId;
      },
    })(target, propertyKey, descriptor);
  };
}

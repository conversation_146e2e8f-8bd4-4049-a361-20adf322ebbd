import { randomUUID } from "crypto";

import { Global, Module } from "@nestjs/common";
import { Request } from "express";
import { ClsModule } from "nestjs-cls";

import ClsContextStorageService from "./clsContextStorage.service";

@Global()
@Module({
  imports: [
    ClsModule.forRoot({
      global: true,
      middleware: {
        mount: true,
        generateId: true,
        idGenerator: (req: Request): string => (req.headers["x-correlation-id"] ?? randomUUID()).toString(),
      },
    }),
  ],
  controllers: [],
  providers: [ClsContextStorageService],
  exports: [ClsContextStorageService],
})
export class ContextModule {}

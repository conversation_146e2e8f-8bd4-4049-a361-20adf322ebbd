import { randomUUID } from "crypto";

import { Injectable } from "@nestjs/common";
import { CLS_ID, ClsService } from "nestjs-cls";

@Injectable()
export default class ClsContextStorageService {
  constructor(private readonly cls: ClsService) {}

  public get<T>(key: string): T | undefined {
    return this.cls.get(key);
  }

  public setContextId(id: string) {
    this.cls.set(CLS_ID, id);
  }

  public setUserId(userId: string) {
    this.cls.set("userId", userId);
  }

  public getContextId(): string {
    if (!this.cls.getId()) {
      const id = randomUUID();
      this.cls.set(CLS_ID, id);
      return id;
    }
    return this.cls.getId();
  }

  public getContext() {
    return {
      correlationId: this.cls.getId(),
      userId: this.cls.get("userId"),
    };
  }

  public set<T>(key: string, value: T): void {
    this.cls.set(key, value);
  }
}

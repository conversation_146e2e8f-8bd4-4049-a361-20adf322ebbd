import { PubSub } from "@google-cloud/pubsub";
import { CloudEvent } from "firebase-functions/v2";
import { MessagePublishedData, PubSubOptions, onMessagePublished } from "firebase-functions/v2/pubsub";

import { TopicNamesType } from "../../pubsub/dto/topicName.dto";
import { DashError, errorBuilder } from "../utils/error.utils";

const pubSubClient = new PubSub();

export type DeadLetterQueueMessage = {
  data: any;
  from: string;
  topic: TopicNamesType;
  error: DashError;
};

export class CustomFunction {
  static onMessagePublished<T, R>(
    options: Omit<PubSubOptions, "retry"> & {
      maxDeliveryAttempts?: number;
      topic: TopicNamesType;
      cloudRunName: string;
    },
    handler: (event: CloudEvent<MessagePublishedData<T>>) => R,
  ) {
    return onMessagePublished(options, async (event) => {
      const data = event.data.message.json;
      let retryCount = data.retryCount ?? 0;

      delete event.data.message.json.retryCount;

      try {
        return await handler(event);
      } catch (error: any) {
        if (options.maxDeliveryAttempts) {
          if (retryCount < options.maxDeliveryAttempts) {
            retryCount += 1;
            return pubSubClient.topic(options.topic).publishMessage({
              data: Buffer.from(JSON.stringify({ ...data, retryCount })),
            });
          } else {
            let errorToSend = error;
            if (error instanceof Error) {
              errorToSend = errorBuilder.global.unexpected(error);
            }
            const message: DeadLetterQueueMessage = {
              data,
              from: options.cloudRunName,
              topic: options.topic,
              error: errorToSend,
            };
            pubSubClient.topic(TopicNamesType.DEAD_LETTER_QUEUE).publishMessage({
              data: Buffer.from(JSON.stringify(message)),
            });
            throw error;
          }
        }
        throw error;
      }
    });
  }
}

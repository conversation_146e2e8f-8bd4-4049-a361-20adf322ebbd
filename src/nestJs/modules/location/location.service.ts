import {
  AddressType,
  Client,
  GeocodeResult,
  GeocodingAddressComponentType,
  Language,
  PlaceAutocompleteRequest,
  ReverseGeocodeRequest,
  ReverseGeocodeResponse,
} from "@googlemaps/google-maps-services-js";
import { v1 } from "@googlemaps/places";
import { google } from "@googlemaps/places/build/protos/protos";
import { v2 } from "@googlemaps/routing";
import { Inject, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import memoizee from "memoizee";

import {
  ComputeRoutesParams,
  ComputeRoutesResponse,
  isLocationPlaceAutocompleteBiasParams,
  LocationLanguage,
  LocationPlaceAutocompleteParams,
  locationPlaceAutocompleteParamsSchema,
  LocationPlaceAutocompleteResponse,
  LocationPlaceDetailsParams,
  locationPlaceDetailsParamsSchema,
  LocationPlaceDetailsResponse,
  LocationReverseGeocodeResponse,
  LocationReverseGeocodeV2Response,
  ReverseGeocodeParams,
} from "./dto/location.dto";
import { errorBuilder } from "../utils/utils/error.utils";
import { ValidationService } from "../validation/validation.service";
import { GeospatialService } from "./geospatial/geospatial.service";
import LoggerServiceAdapter from "../utils/logger/logger.service";
import { PickupPoint, PickupPointPolygon } from "./geospatial/models/pickup_point_data";
import { ShenzhenBayUtils } from "./geospatial/utils/shenzhen_bay.utils";

const client = new Client();
const placesClient = new v1.PlacesClient({});
const routesClient = new v2.RoutesClient({});

@Injectable()
export class LocationService {
  constructor(
    private readonly configService: ConfigService,
    private readonly geospatialService: GeospatialService,
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
  ) {}

  formatAddress(address: string | null | undefined, defaultAddress: string = ""): string {
    const regex = /(Hong Kong, )|(, Hong Kong)/gi;
    const formated = address?.replace(regex, "").trim();
    const defaultAdd = defaultAddress?.replace(regex, "").trim();
    return formated ?? defaultAdd;
  }

  async placeAutocomplete(params: LocationPlaceAutocompleteParams): Promise<LocationPlaceAutocompleteResponse> {
    const [validatedEntry] = ValidationService.validate<[LocationPlaceAutocompleteParams]>([
      {
        schema: locationPlaceAutocompleteParamsSchema,
        value: params,
      },
    ]);

    const data: PlaceAutocompleteRequest = {
      params: {
        key: this.configService.getOrThrow("GOOGLE_PLACES_KEY"),
        input: validatedEntry.query,
        language: validatedEntry.language,
        components: ["country:hk"],
        ...(isLocationPlaceAutocompleteBiasParams(validatedEntry)
          ? {
              location: {
                lat: validatedEntry.lat,
                lng: validatedEntry.lng,
              },
              radius: validatedEntry.radius ? validatedEntry.radius : 1000,
            }
          : {}),
      },
    };

    return client
      .placeAutocomplete(data)
      .then((result): LocationPlaceAutocompleteResponse => {
        let results = result.data.predictions.map((prediction) => ({
          placeId: prediction.place_id,
          mainText: this.formatAddress(prediction.structured_formatting.main_text),
          secondaryText: this.formatAddress(prediction.structured_formatting.secondary_text),
        }));

        results = ShenzhenBayUtils.autocompleteResultProxy(results, validatedEntry.query);

        return results;
      })
      .catch((e) => {
        throw errorBuilder.location.placeAutocomplete.dependencyFailed(e.response.data, e.response.data.error_message);
      });
  }

  async getPlaceDetails(
    params: LocationPlaceDetailsParams,
    sessionToken?: string,
  ): Promise<LocationPlaceDetailsResponse> {
    if (ShenzhenBayUtils.isShenzhenBayPlace(params.placeId)) {
      return ShenzhenBayUtils.getShenzhenBayPlaceDetails(params.language);
    }
    return this.placeDetails(params, sessionToken);
  }

  readonly placeDetails = memoizee(this.placeDetailsRaw, {
    promise: true,
    maxAge: 1000 * 60 * 60 * 24, // 24 hours
  });

  async placeDetailsRaw(
    params: LocationPlaceDetailsParams,
    sessionToken?: string,
  ): Promise<LocationPlaceDetailsResponse> {
    const [validatedEntry] = ValidationService.validate<[LocationPlaceDetailsParams]>([
      {
        schema: locationPlaceDetailsParamsSchema,
        value: {
          ...params,
          language: params.language === LocationLanguage.ZHHK ? LocationLanguage.ZH_HK : params.language,
        },
      },
    ]);

    const data: google.maps.places.v1.IGetPlaceRequest = {
      name: `places/${validatedEntry.placeId}`,
      languageCode: validatedEntry.language,
      sessionToken,
    };

    return placesClient
      .getPlace(data, {
        otherArgs: {
          headers: {
            "X-Goog-FieldMask": "id,displayName,location,formattedAddress,addressComponents",
          },
        },
      })
      .then(async (result): Promise<LocationPlaceDetailsResponse> => {
        const addressComponentTypesList: (AddressType | GeocodingAddressComponentType)[][] = [
          [AddressType.premise],
          validatedEntry.language === LocationLanguage.EN
            ? [GeocodingAddressComponentType.street_number, AddressType.route]
            : [AddressType.route, GeocodingAddressComponentType.street_number],
          [GeocodingAddressComponentType.point_of_interest],
          [GeocodingAddressComponentType.establishment],
          [AddressType.neighborhood],
        ];
        let foundDisplayName: string | undefined;
        if (result[0]?.displayName?.text) {
          foundDisplayName = this.formatAddress(result[0].displayName.text);
        } else {
          addressComponentTypesList.forEach((addressComponentTypes) => {
            if (foundDisplayName) {
              return;
            }
            if (
              addressComponentTypes.every((type) =>
                result[0]?.addressComponents?.some((addressComponent) => addressComponent?.types?.includes(type)),
              )
            ) {
              foundDisplayName = this.formatAddress(
                addressComponentTypes
                  .map((type) => {
                    const longText = result[0]?.addressComponents?.find((addressComponent) =>
                      addressComponent?.types?.includes(type),
                    )?.longText;
                    if (
                      type === GeocodingAddressComponentType.street_number &&
                      validatedEntry.language !== LocationLanguage.EN &&
                      !longText?.includes("號")
                    ) {
                      return `${longText}號`;
                    }
                    return longText;
                  })
                  .join(validatedEntry.language === LocationLanguage.EN ? " " : ""),
              );
            }
          });
        }
        let pickupPointPolygon: PickupPointPolygon | undefined;
        try {
          pickupPointPolygon = await this.getPickupPointPolygon(
            result[0].location?.latitude ?? 0,
            result[0].location?.longitude ?? 0,
          );
        } catch (error) {
          this.logger.error(
            `Place details: error finding polygon for coordinates [${result[0].location?.longitude}, ${result[0].location?.latitude}]`,
            {
              coordinates: [result[0].location?.longitude, result[0].location?.latitude],
              error,
            },
          );
        }

        return {
          displayName: foundDisplayName ?? result[0].formattedAddress,
          formattedAddress: this.formatAddress(result[0].formattedAddress),
          placeId: validatedEntry.placeId,
          lat: result[0].location?.latitude,
          lng: result[0].location?.longitude,
          pickupPointPolygon: pickupPointPolygon,
        };
      })
      .catch((e) => {
        throw errorBuilder.location.placeDetails.dependencyFailed(e.statusDetails);
      });
  }

  getReverseGeocodeAddress(
    geocodeResult: GeocodeResult[],
    language: LocationLanguage,
  ): {
    displayName: string;
    formattedAddress: string;
    placeId: string;
  } {
    if (!geocodeResult || geocodeResult.length === 0) {
      return {
        displayName: "",
        formattedAddress: "",
        placeId: "",
      };
    }
    const addressTypesList: (AddressType | GeocodingAddressComponentType)[][] = [
      [AddressType.street_address],
      [AddressType.premise],
      [AddressType.point_of_interest],
    ];

    const addressComponentTypesList: (AddressType | GeocodingAddressComponentType)[][] = [
      [AddressType.premise],
      language === LocationLanguage.EN
        ? [GeocodingAddressComponentType.street_number, AddressType.route]
        : [AddressType.route, GeocodingAddressComponentType.street_number],
      [GeocodingAddressComponentType.point_of_interest],
      [GeocodingAddressComponentType.establishment],
      [AddressType.neighborhood],
    ];

    let found: { displayName: string; formattedAddress: string; placeId: string } | undefined;
    let foundGeocodeResult: GeocodeResult | undefined;

    addressTypeLoop: for (const addressType of addressTypesList) {
      for (const result of geocodeResult) {
        const hasType = result.types.some((type) => addressType.includes(type));
        const isNotPlusCode =
          result.address_components.length > 0 && !result.address_components[0].types.includes(AddressType.plus_code);
        if (hasType && isNotPlusCode) {
          foundGeocodeResult = result;
          break addressTypeLoop;
        }
      }
    }

    if (!foundGeocodeResult) {
      foundGeocodeResult =
        geocodeResult.find(
          (result) =>
            result.address_components.length > 0 && !result.address_components[0].types.includes(AddressType.plus_code),
        ) ?? geocodeResult[0];
    }

    addressComponentTypesList.forEach((addressComponentTypes) => {
      if (found) {
        return;
      }
      if (
        addressComponentTypes.every((type) =>
          foundGeocodeResult?.address_components.some((addressComponent) => addressComponent.types.includes(type)),
        )
      ) {
        {
          found = {
            displayName: this.formatAddress(
              addressComponentTypes
                .map((type) => {
                  const longName = foundGeocodeResult?.address_components.find((addressComponent) =>
                    addressComponent.types.includes(type),
                  )?.long_name;
                  if (
                    type === GeocodingAddressComponentType.street_number &&
                    language !== LocationLanguage.EN &&
                    !longName?.includes("號")
                  ) {
                    return `${longName}號`;
                  }
                  return longName;
                })
                .join(language === LocationLanguage.EN ? " " : ""),
            ),
            formattedAddress: this.formatAddress(foundGeocodeResult?.formatted_address),
            placeId: foundGeocodeResult?.place_id ?? "",
          };
        }
      }
    });

    return (
      found ?? {
        displayName: this.formatAddress(foundGeocodeResult.address_components[0].long_name),
        formattedAddress: this.formatAddress(foundGeocodeResult.formatted_address),
        placeId: foundGeocodeResult.place_id,
      }
    );
  }

  async reverseGeocode(params: ReverseGeocodeParams): Promise<LocationReverseGeocodeResponse> {
    const data: ReverseGeocodeRequest = {
      params: {
        key: this.configService.getOrThrow("GOOGLE_PLACES_KEY"),
        language: params.language as unknown as Language,
      },
    };

    data.params.latlng = {
      lat: params.lat,
      lng: params.lng,
    };
    return client
      .reverseGeocode(data)
      .then((result: ReverseGeocodeResponse): LocationReverseGeocodeResponse => {
        const address = this.getReverseGeocodeAddress(result.data.results, params.language);
        return {
          displayName: this.formatAddress(address.displayName),
          formattedAddress: this.formatAddress(address.formattedAddress),
          placeId: address.placeId,
        };
      })
      .catch((e) => {
        throw errorBuilder.location.reverseGeocode.dependencyFailed(e.response.data, e.response.data.error_message);
      });
  }

  // Same as reverseGeocode but will first check if the location is within a pickup point polygon
  // and return the pickup point polygon if it exists, otherwise it will return the reverse geocode result.
  async reverseGeocodeV2(params: ReverseGeocodeParams): Promise<LocationReverseGeocodeV2Response> {
    const data: ReverseGeocodeRequest = {
      params: {
        key: this.configService.getOrThrow("GOOGLE_PLACES_KEY"),
        language: params.language as unknown as Language,
      },
    };

    let pickupPointPolygon: PickupPointPolygon | undefined;
    data.params.latlng = {
      lat: params.lat,
      lng: params.lng,
    };
    try {
      pickupPointPolygon = await this.getPickupPointPolygon(params.lat, params.lng);

      if (!params.getIfPolygon && pickupPointPolygon) {
        return {
          pickupPointPolygon: pickupPointPolygon,
        };
      }
    } catch (error) {
      this.logger.error(`Reverse geocode: error finding polygon for coordinates [${params.lng}, ${params.lat}]`, {
        coordinates: [params.lng, params.lat],
        error,
      });
    }
    return client
      .reverseGeocode(data)
      .then((result: ReverseGeocodeResponse): LocationReverseGeocodeV2Response => {
        const address = this.getReverseGeocodeAddress(result.data.results, params.language);
        return {
          place: {
            displayName: this.formatAddress(address.displayName),
            formattedAddress: this.formatAddress(address.formattedAddress),
            placeId: address.placeId,
          },
          pickupPointPolygon: pickupPointPolygon,
        };
      })
      .catch((e) => {
        throw errorBuilder.location.reverseGeocode.dependencyFailed(e.response.data, e.response.data.error_message);
      });
  }

  async computeRoutes(
    data: ComputeRoutesParams,
  ): Promise<Omit<ComputeRoutesResponse, "destinationPlaceDetails" | "originPlaceDetails">> {
    let originPlaceId = data.originPlaceId;
    let destinationPlaceId = data.destinationPlaceId;
    let originIsInShenzhenBay = false;
    let destinationIsInShenzhenBay = false;
    // If either origin or destination is inside the Shenzhen Bay polygon, we should use the Shenzhen Bay Bridge for computing
    if (ShenzhenBayUtils.isShenzhenBayPlace(data.originPlaceId)) {
      originPlaceId = ShenzhenBayUtils.SHENZHEN_BAY_BRIDGE_PLACE_ID_FROM;
      originIsInShenzhenBay = true;
    }
    if (ShenzhenBayUtils.isShenzhenBayPlace(data.destinationPlaceId)) {
      destinationPlaceId = ShenzhenBayUtils.SHENZHEN_BAY_BRIDGE_PLACE_ID_TO;
      destinationIsInShenzhenBay = true;
    }

    return routesClient
      .computeRoutes(
        {
          origin: { placeId: originPlaceId },
          destination: { placeId: destinationPlaceId },
          languageCode: data.language,
          travelMode: "DRIVE",
          routingPreference: "TRAFFIC_AWARE",
          computeAlternativeRoutes: false,
          routeModifiers: {
            avoidTolls: false,
            avoidHighways: false,
            avoidFerries: false,
          },
          units: "METRIC",
        },
        {
          otherArgs: {
            headers: {
              "X-Goog-FieldMask":
                "routes.duration,routes.staticDuration,routes.distanceMeters,routes.polyline.encodedPolyline",
            },
          },
        },
      )
      .catch((e) => {
        throw errorBuilder.location.computeRoutes.dependencyFailed(e.response.data, e.response.data?.error_message);
      })
      .then((resultComputeRoutes) => {
        const firstRoute = resultComputeRoutes[0].routes?.[0];
        if (!firstRoute) {
          throw errorBuilder.location.computeRoutes.noRoutesFound(data);
        }

        let distanceMeters = firstRoute.distanceMeters ?? 0;
        let durationSeconds = parseInt(firstRoute.duration?.seconds?.toString() ?? "0", 10) ?? 0;
        let encodedPolyline = firstRoute.polyline?.encodedPolyline ?? "";
        if (originIsInShenzhenBay && !destinationIsInShenzhenBay) {
          distanceMeters += ShenzhenBayUtils.SHENZHEN_BAY_ADDITIONAL_DISTANCE_FROM;
          durationSeconds += ShenzhenBayUtils.SHENZHEN_BAY_ADDITIONAL_SECONDS_FROM;
          encodedPolyline = ShenzhenBayUtils.addToPolyline(encodedPolyline, true);
        } else if (!originIsInShenzhenBay && destinationIsInShenzhenBay) {
          distanceMeters += ShenzhenBayUtils.SHENZHEN_BAY_ADDITIONAL_DISTANCE_TO;
          durationSeconds += ShenzhenBayUtils.SHENZHEN_BAY_ADDITIONAL_SECONDS_TO;
          encodedPolyline = ShenzhenBayUtils.addToPolyline(encodedPolyline, false);
        }

        return {
          distanceMeters: distanceMeters,
          durationSeconds: durationSeconds,
          encodedPolyline: encodedPolyline,
        };
      });
  }

  async getMultiplePlaceDetails(
    placeIds: string[],
    language: LocationLanguage,
    sessionToken?: string,
  ): Promise<LocationPlaceDetailsResponse[]> {
    if (!placeIds || placeIds.length === 0) {
      return [];
    }
    const requests = placeIds.map((placeId) => {
      const params: LocationPlaceDetailsParams = {
        placeId,
        language,
      };
      return this.getPlaceDetails(params, sessionToken);
    });
    try {
      const results = await Promise.all(requests);
      return results;
    } catch (error) {
      console.error("Error fetching place details:", error);

      return [];
    }
  }

  async getPickupPointPolygon(lat: number, lng: number): Promise<PickupPointPolygon | undefined> {
    let pickupPointPolygon: PickupPointPolygon | undefined;
    const polygon = await this.geospatialService.findPolygonContainingPoint([lng, lat]);
    if (polygon) {
      let pickupPoints: PickupPoint[] | undefined;
      if (
        polygon.properties &&
        typeof polygon.properties === "object" &&
        "description" in polygon.properties &&
        polygon.properties.description
      ) {
        pickupPoints = this.geospatialService.parseAndConvertPolygonDescription(
          (polygon.properties as { description: { value: string } }).description?.value,
        );
      }

      pickupPointPolygon = new PickupPointPolygon({
        name:
          typeof polygon.properties === "object" && polygon.properties && "name" in polygon.properties
            ? (polygon.properties as { name?: string }).name || "Unknown Polygon"
            : "Unknown Polygon",
        coordinates: polygon.geometry.coordinates[0] as [number, number][],
        pickupPoints,
      });
    }

    return pickupPointPolygon;
  }
}

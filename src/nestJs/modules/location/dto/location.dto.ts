import Joi from "joi";

import { PickupPointPolygon } from "../geospatial/models/pickup_point_data";
/**Locales to be used when calling google map api only */
export enum LocationLanguage {
  EN = "en",
  ZH = "zh",
  ZH_TW = "zh-TW",
  ZH_CN = "zh-CN",
  ZH_HK = "zh-HK",
  ZH_HANT = "zh-Hant",

  ZHHK = "zhHK", // Alias for zh-HK, Mobile App uses zhHK
}

export const locationLanguageSchema = Joi.string().valid(...Object.values(LocationLanguage));

export enum LocalizedLanguage {
  EN = "en",
  ZHHK = "zhHK",
}

export const localizedLanguageSchema = Joi.string().valid(...Object.values(LocalizedLanguage));
/**
 * Location Place Autocomplete
 */

export type LocationPlaceAutocompleteRawParams = {
  sessionToken?: string;
  query: string;
  language: LocationLanguage;
};
export const isLocationPlaceAutocompleteRawParams = (
  content: LocationPlaceAutocompleteParams,
): content is LocationPlaceAutocompleteRawParams =>
  !Object.prototype.hasOwnProperty.call(content, "lng") && !Object.prototype.hasOwnProperty.call(content, "lat");

export type LocationPlaceAutocompleteBiasParams = {
  sessionToken?: string;
  query: string;
  language: LocationLanguage;
  lng: number;
  lat: number;
  radius?: number;
};
export const isLocationPlaceAutocompleteBiasParams = (
  content: LocationPlaceAutocompleteParams,
): content is LocationPlaceAutocompleteBiasParams =>
  Object.prototype.hasOwnProperty.call(content, "lng") && Object.prototype.hasOwnProperty.call(content, "lat");

export type LocationPlaceAutocompleteParams = LocationPlaceAutocompleteRawParams | LocationPlaceAutocompleteBiasParams;

export const locationPlaceAutocompleteParamsSchema = Joi.object<LocationPlaceAutocompleteParams>({
  sessionToken: Joi.string(),
  query: Joi.string().required(),
  language: locationLanguageSchema.required(),
  lng: Joi.number(),
  lat: Joi.number(),
  radius: Joi.number(),
}).and("lng", "lat");

export type LocationPlaceAutocompleteResponse = {
  placeId: string;
  mainText: string;
  secondaryText: string;
}[];

/**
 * Location Place Details
 */
export type LocationPlaceDetailsParams = {
  placeId: string;
  language: LocationLanguage;
};

export const locationPlaceDetailsParamsSchema = Joi.object<LocationPlaceDetailsParams>({
  placeId: Joi.string().required(),
  language: locationLanguageSchema.required(),
});

export type LocationPlaceDetailsResponse = {
  placeId: string;
  displayName?: string | null;
  formattedAddress?: string | null;
  lat?: number | null;
  lng?: number | null;
  pickupPointPolygon?: PickupPointPolygon;
};

/**
 * Reverse Geo Code
 */
export type ReverseGeocodeParams = {
  sessionToken?: string;
  language: LocationLanguage;
  lng: number;
  lat: number;
  getIfPolygon?: boolean;
};

export type LocationReverseGeocodeResponse = {
  placeId: string;
  formattedAddress: string;
  displayName: string;
};

export type LocationReverseGeocodeV2Response = {
  place?: {
    placeId: string;
    formattedAddress: string;
    displayName: string;
  };
  pickupPointPolygon?: PickupPointPolygon;
};

export type ComputeRoutesParams = {
  originPlaceId: string;
  destinationPlaceId: string;
  language: LocationLanguage;
};

export type ComputeRoutesResponse = {
  distanceMeters: number;
  durationSeconds: number;
  encodedPolyline?: string;
  originPlaceDetails: LocationPlaceDetailsResponse;
  destinationPlaceDetails: LocationPlaceDetailsResponse;
};

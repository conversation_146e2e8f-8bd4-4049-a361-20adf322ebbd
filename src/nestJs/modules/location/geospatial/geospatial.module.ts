import { MiddlewareConsumer, Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";

import { AdminAuthMiddleware } from "@nest/infrastructure/middlewares/adminAuth.middleware";

import { GeospatialController } from "./geospatial.controller";
import { GeospatialService } from "./geospatial.service";

@Module({
  imports: [ConfigModule],
  controllers: [GeospatialController],
  providers: [GeospatialService],
  exports: [GeospatialService],
})
export class GeospatialModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminAuthMiddleware).forRoutes("geospatial");
  }
}

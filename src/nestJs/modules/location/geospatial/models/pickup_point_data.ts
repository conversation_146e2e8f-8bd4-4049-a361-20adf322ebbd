import { LocalizedLanguage } from "../../dto/location.dto";

export class PickupPointPolygon {
  name: string;
  coordinates: [number, number][];
  pickupPoints?: PickupPoint[];

  constructor(data: Partial<PickupPointPolygon> = {}) {
    this.name = data.name || "";
    this.coordinates = data.coordinates || [];
    this.pickupPoints = data.pickupPoints || [];
  }
}

export class PickupPoint {
  i18n: {
    [key in LocalizedLanguage]?: { displayName?: string | null; formattedAddress?: string | null };
  };
  placeId: string;
  lat: number;
  lng: number;

  constructor(data: Partial<PickupPoint> = {}) {
    this.i18n = data.i18n || {};
    this.placeId = data.placeId || "";
    this.lat = data.lat || 0;
    this.lng = data.lng || 0;
  }
}

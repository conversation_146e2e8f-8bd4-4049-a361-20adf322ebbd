export class GooglePolylineUtils {
  /**
   * Encodes an array of coordinates into a Google Maps polyline string.
   * @param {Array} coordinates - Array of objects with `lat` and `lng` properties.
   * @returns {string} Encoded polyline string.
   */
  static encodePolyline(coordinates: { lat: number; lng: number }[]): string {
    let encoded = "";
    let prevLat = 0;
    let prevLng = 0;

    coordinates.forEach((point) => {
      const lat = Math.round(point.lat * 1e5);
      const lng = Math.round(point.lng * 1e5);

      const dLat = lat - prevLat;
      const dLng = lng - prevLng;

      encoded += GooglePolylineUtils.encodeSignedNumber(dLat) + GooglePolylineUtils.encodeSignedNumber(dLng);

      prevLat = lat;
      prevLng = lng;
    });

    return encoded;
  }

  /**
   * Encodes a signed number in the polyline format.
   * @param {number} num - The number to encode.
   * @returns {string} Encoded string.
   */
  static encodeSignedNumber(num: number): string {
    let sgnNum = num << 1;
    if (num < 0) {
      sgnNum = ~sgnNum;
    }
    return GooglePolylineUtils.encodeNumber(sgnNum);
  }

  /**
   * Encodes an unsigned number in the polyline format.
   * @param {number} num - The number to encode.
   * @returns {string} Encoded string.
   */
  static encodeNumber(num: number): string {
    let encodeString = "";
    while (num >= 0x20) {
      encodeString += String.fromCharCode((0x20 | (num & 0x1f)) + 63);
      num >>= 5;
    }
    encodeString += String.fromCharCode(num + 63);
    return encodeString;
  }

  /**
   * Decodes a Google Maps polyline string into an array of coordinates.
   * @param {string} encoded - The encoded polyline string.
   * @returns {Array} Array of objects with `lat` and `lng` properties.
   */
  static decodePolyline(encoded: string): { lat: number; lng: number }[] {
    const coordinates = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      const result = GooglePolylineUtils.decodeNumber(encoded, index);
      const dLat = result.value;
      index = result.index;
      lat += dLat;

      const resultLng = GooglePolylineUtils.decodeNumber(encoded, index);
      const dLng = resultLng.value;
      index = resultLng.index;
      lng += dLng;

      coordinates.push({
        lat: lat / 1e5,
        lng: lng / 1e5,
      });
    }

    return coordinates;
  }

  /**
   * Decodes a single number from the encoded polyline string.
   * @param {string} str - The encoded polyline string.
   * @param {number} startIndex - The current index in the string.
   * @returns {Object} An object containing the decoded value and the new index.
   */
  static decodeNumber(str: string, startIndex: number): { value: number; index: number } {
    let shift = 0;
    let result = 0;
    let index = startIndex;
    let byte = null;

    do {
      byte = str.charCodeAt(index++) - 63;
      result |= (byte & 0x1f) << shift;
      shift += 5;
    } while (byte >= 0x20);

    const delta = result & 1 ? ~(result >> 1) : result >> 1;
    return {
      value: delta,
      index: index,
    };
  }
}

import { <PERSON>, <PERSON> } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";

import { apiTags } from "@nest/modules/utils/utils/swagger.utils";

import { GeospatialService } from "./geospatial.service";

@ApiBearerAuth()
@Controller("geospatial")
@ApiTags(...apiTags.system)
export class GeospatialController {
  constructor(private readonly geospatialService: GeospatialService) {}

  @Patch("/reinitialize")
  @ApiOperation({
    summary: "Update pickup points from .kml file",
  })
  async reinitialize(): Promise<void> {
    return this.geospatialService.reinitialize();
  }
}

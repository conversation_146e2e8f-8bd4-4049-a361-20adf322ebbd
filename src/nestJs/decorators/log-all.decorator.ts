import { Inject } from "@nestjs/common";

import LoggerServiceAdapter from "@nest/modules/utils/logger/logger.service";

import { asyncLogDescriptorValue, getIsAsync, logDescriptorValue, LoggableMethod } from "./log.decorator";

export interface LogAllOptions {
  exclude?: string[];
}

export function LogAll(options: LogAllOptions = {}): ClassDecorator {
  const injectLogger = Inject(LoggerServiceAdapter);

  return (target: any) => {
    // Get all methods from the class prototype
    const prototype = target.prototype;
    const methodNames = Object.getOwnPropertyNames(prototype).filter((name) => {
      // Filter out constructor and non-function properties
      if (name === "constructor") return false;
      // Filter out excluded methods
      if (options.exclude?.includes(name)) return false;
      return typeof prototype[name] === "function";
    });

    // Inject logger into the class
    injectLogger(prototype, "logger");

    // Apply logging to each method
    methodNames.forEach((methodName) => {
      const descriptor = Object.getOwnPropertyDescriptor(prototype, methodName);
      if (!descriptor) return;

      const originalMethod = descriptor.value as LoggableMethod;
      // More robust async function detection
      const isAsync = getIsAsync(originalMethod);

      Object.defineProperty(prototype, methodName, {
        ...descriptor,
        value: isAsync
          ? asyncLogDescriptorValue(prototype, methodName, originalMethod)
          : logDescriptorValue(prototype, methodName, originalMethod),
      });
    });

    return target;
  };
}

/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpService } from "@nestjs/axios";
import { CallHandler, ExecutionContext, Inject, Injectable, NestInterceptor } from "@nestjs/common";
import { Request } from "express";
import { omit } from "lodash";
import { Observable } from "rxjs";

import ClsContextStorageService from "@nest/modules/utils/context/clsContextStorage.service";

import LoggerServiceAdapter from "../../modules/utils/logger/logger.service";

/**
 * Logger middleware
 */
@Injectable()
export class LoggerInterceptor implements NestInterceptor {
  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    @Inject(ClsContextStorageService) private contextStorageService: ClsContextStorageService,
    private httpService: HttpService,
  ) {}

  intercept(context: ExecutionContext, next: <PERSON><PERSON>and<PERSON>): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse();
    const { method, url, headers, body, query } = request;
    const ipData = headers["x-forwarded-for"] || request.socket.remoteAddress || "";
    const ip = Array.isArray(ipData) ? ipData[0] : ipData;

    const correlationId = this.logger.getContextId();
    if (request?.user?.uid) {
      this.contextStorageService.setUserId(request?.user?.uid);
    }

    this.logger.debug(`[${method}] ${url}`, {
      method,
      ip: (ip ?? "").split(",")[0],
      user: request?.user,
      userAgent: headers["user-agent"],
      acceptLanguage: headers["accept-language"],
      headers: omit(headers, ["authorization", "cookie", "set-cookie"]),
      body,
      query,
      url,
    });

    this.httpService.axiosRef.interceptors.request.use((config) => {
      config.headers["X-Correlation-ID"] = correlationId;
      return config;
    });

    // Add header to response
    response.setHeader("X-Correlation-ID", correlationId);

    // Make custom header visible to clients through CORS
    response.setHeader("Access-Control-Expose-Headers", "X-Correlation-ID");

    return next.handle();
  }
}

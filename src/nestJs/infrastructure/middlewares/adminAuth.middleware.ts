import { Inject, Injectable, NestMiddleware } from "@nestjs/common";

import { useAuth } from "./middleware.utils";
import LoggerServiceAdapter from "../../modules/utils/logger/logger.service";

@Injectable()
export class AdminAuthMiddleware implements NestMiddleware {
  constructor(@Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter) {}
  use = useAuth((decodedToken) => {
    const isAdmin = decodedToken && decodedToken.admin && decodedToken.email;
    if (isAdmin) {
      this.logger.info("Admin's Email: ", { email: decodedToken.email });
    }
    return isAdmin;
  });
}

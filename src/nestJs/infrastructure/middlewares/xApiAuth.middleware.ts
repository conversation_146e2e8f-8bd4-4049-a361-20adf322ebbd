import { Inject, Injectable, NestMiddleware } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { Request, Response, NextFunction } from "express";

import { errorBuilder } from "@nest/modules/utils/utils/error.utils";

import LoggerServiceAdapter from "../../modules/utils/logger/logger.service";

@Injectable()
export class XApiAuthMiddleware implements NestMiddleware {
  constructor(
    @Inject(LoggerServiceAdapter) private logger: LoggerServiceAdapter,
    private readonly configService: ConfigService,
  ) {}
  use(req: Request, res: Response, next: NextFunction) {
    const apiKey = req.headers["x-api-key"] as string;
    if (apiKey !== this.configService.getOrThrow("CLOUD_TASKS_API_KEY")) {
      this.logger.error("Invalid X-API-Key", { apiKey });
      throw errorBuilder.auth.unknown(new Error("Invalid X-API-Key"));
    }
    next();
  }
}

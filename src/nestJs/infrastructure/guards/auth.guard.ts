import { CanActivate, ExecutionContext, Injectable } from "@nestjs/common";
import { Request } from "express";
import { getAuth } from "firebase-admin/auth";

import { VerifyToken } from "../../modules/auth/types";
import { DashError, errorBuilder } from "../../modules/utils/utils/error.utils";
import firebaseUtils from "../../modules/utils/utils/firebase.utils";

@Injectable()
/**
 * AuthGuard is a guard that checks if the request has a valid token in the authorization header.
 */
export class AuthGuard implements CanActivate {
  /**
   * Verifies auth bearer token, adds user to request object and returns true if successful.
   */
  static async verifyAuth(request: Request, verifyToken?: VerifyToken): Promise<boolean> {
    const tokenPrefix = "Bearer ";
    const auth = request.headers.authorization as string | undefined;
    if (!auth || !auth.startsWith(tokenPrefix)) {
      throw errorBuilder.auth.tokenNotSet();
    }

    try {
      const idToken = auth.split(tokenPrefix)[1];
      const decodedToken = await getAuth().verifyIdToken(idToken);

      if (decodedToken && (!verifyToken || verifyToken(decodedToken))) {
        request.user = decodedToken;
        return true;
      } else {
        throw errorBuilder.auth.tokenInvalidRole();
      }
    } catch (err) {
      if (err instanceof DashError) {
        throw err;
      }
      if (firebaseUtils.isFirebaseTokenExpiredError(err)) {
        throw errorBuilder.auth.firebaseTokenExpired(err);
      }
      if (firebaseUtils.isFirebaseAuthError(err)) {
        throw errorBuilder.auth.firebaseAuthError(err);
      }
      throw errorBuilder.auth.unknown(err);
    }
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();

    return AuthGuard.verifyAuth(request);
  }
}

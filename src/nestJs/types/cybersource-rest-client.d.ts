declare module "cybersource-rest-client" {
  /**
   * -----------------------------------------------------------------
   * Responses
   * -----------------------------------------------------------------
   */
  class BaseResponse {
    bind: (
      instance:
        | InstrumentIdentifierApi
        | PaymentInstrumentApi
        | PayerAuthenticationApi
        | PaymentsApi
        | CaptureApi
        | VoidApi
        | TransactionDetailsApi
        | SearchTransactionsApi,
    ) => void;
  }
  export class PayerAuthSetupResponse extends BaseResponse {
    id: string;
    submitTimeUtc: string;
    status: string;
    consumerAuthenticationInformation: ConsumerAuthenticationInformation;
    clientReferenceInformation: ClientReferenceInformation;
  }

  export class CreatePaymentInstrumentIdentifierResponse extends BaseResponse {
    _links: Links;
    id: string;
    object: string;
    state: string;
    card: Card;
    metadata: Metadata;
  }

  export class SearchResponse extends BaseResponse {
    _links: Links;
    searchId: string;
    save: boolean;
    status: string;
    name: string;
    query: string;
    totalCount: number;
    _embedded: {
      transactionSummaries: any[];
    };
  }

  export class CreatePaymentInstrumentResponse extends BaseResponse {
    _links: Links;
    id: string;
    object: string;
    state: string;
    card: Card;
    billTo: BillTo;
    metadata: Metadata;
    _embedded: Embedded;
  }

  class AuthResponse extends BaseResponse {
    _links: Links;
    id: string;
    submitTimeUtc: string;
    status: string;
    reconciliationId: string;
    clientReferenceInformation: ClientReferenceInformation;
    processorInformation: ProcessorInformation;
    paymentAccountInformation: PaymentAccountInformation;
    paymentInformation: PaymentInformation;
    orderInformation: OrderInformation;
  }

  type GetTransactionResponse = any;

  interface OrderInformation {
    amountDetails: AmountDetails;
  }

  interface AmountDetails {
    totalAmount: string;
    authorizedAmount: string;
    currency: string;
  }

  interface PaymentInformation {
    card: Card;
    tokenizedCard: Card;
    paymentInstrument: PaymentInstrument;
    instrumentIdentifier: InstrumentIdentifier;
  }

  interface InstrumentIdentifier {
    id: string;
    state: string;
  }

  interface PaymentInstrument {
    id: string;
  }

  interface PaymentAccountInformation {
    card: CardAuth;
  }

  interface CardAuth {
    type: string;
  }

  interface ProcessorInformation {
    approvalCode: string;
    transactionId: string;
    networkTransactionId: string;
    responseCode: string;
    responseDetails: string;
    avs: Avs;
    merchantAdvice: Avs;
    consumerAuthenticationResponse: Avs;
    systemTraceAuditNumber: string;
    retrievalReferenceNumber: string;
  }

  interface Avs {
    code: string;
    codeRaw: string;
  }

  interface Links {
    self: Self;
  }

  interface Self {
    href: string;
    method: string;
  }

  export class ThreeDSResponse extends BaseResponse {
    id: string;
    submitTimeUtc: string;
    status: string;
    errorInformation: ErrorInformation;
    clientReferenceInformation: ClientReferenceInformation;
    paymentInformation: PaymentInformation;
    consumerAuthenticationInformation: ConsumerAuthenticationInformation;
  }

  export type CreatePaymentResponse = AuthResponse | ThreeDSResponse;

  export class ValidatePayerAuthenticationResponse extends BaseResponse {
    id: string;
    submitTimeUtc: string;
    status: ValidatePayerAuthenticationStatusResponse;
    clientReferenceInformation: ClientReferenceInformation;
    consumerAuthenticationInformation: ConsumerAuthenticationInformation;
  }

  export class CheckPayerAuthEnrollmentResponse extends BaseResponse {
    id: string;
    submitTimeUtc: string;
    status: string;
    clientReferenceInformation: ClientReferenceInformation;
    consumerAuthenticationInformation: ConsumerAuthenticationInformation;
  }

  export class VoidPaymentResponse extends BaseResponse {
    _links: Links;
    id: string;
    submitTimeUtc: string;
    status: string;
    clientReferenceInformation: ClientReferenceInformation;
    processorInformation: ProcessorInformation;
  }

  /**
   * -----------------------------------------------------------------
   * APIS
   * -----------------------------------------------------------------
   */
  export class ApiClient {}
  export class InstrumentIdentifierApi {
    postInstrumentIdentifier: CreatePaymentInstrumentIdentifierResponse;

    constructor(private apiConfig: ApiConfig, private apiClient: ApiClient) {}
  }

  export class PaymentInstrumentApi {
    postPaymentInstrument: CreatePaymentInstrumentResponse;

    constructor(private apiConfig: ApiConfig, private apiClient: ApiClient) {}
  }

  export class PayerAuthenticationApi {
    payerAuthSetup: PayerAuthSetupResponse;
    validateAuthenticationResults: ValidatePayerAuthenticationResponse;
    checkPayerAuthEnrollment: CheckPayerAuthEnrollmentResponse;

    constructor(private apiConfig: ApiConfig, private apiClient: ApiClient) {}
  }

  export class VoidApi {
    voidPayment: BaseResponse;
    constructor(private apiConfig: ApiConfig, private apiClient: ApiClient) {}
  }

  export class TransactionDetailsApi {
    getTransaction: BaseResponse;
    constructor(private apiConfig: ApiConfig, private apiClient: ApiClient) {}
  }

  export class SearchTransactionsApi {
    createSearch: SearchResponse;
    constructor(private apiConfig: ApiConfig, private apiClient: ApiClient) {}
  }

  export class CaptureApi {
    capturePayment: PayerAuthSetupResponse;

    constructor(private apiConfig: ApiConfig, private apiClient: ApiClient) {}
  }

  export class PaymentsApi {
    createPayment: CreatePaymentResponse;

    constructor(private apiConfig: ApiConfig, private apiClient: ApiClient) {}
  }

  /**
   * -----------------------------------------------------------------
   * Requests
   * -----------------------------------------------------------------
   */
  export class PostInstrumentIdentifierRequest {
    public card: any;
  }

  export class PostPaymentInstrumentRequest {
    public card: Tmsv2customersEmbeddedDefaultPaymentInstrumentCard;
    public billTo: Tmsv2customersEmbeddedDefaultPaymentInstrumentBillTo;
    public instrumentIdentifier: Tmsv2customersEmbeddedDefaultPaymentInstrumentInstrumentIdentifier;
  }

  export class PayerAuthSetupRequest {
    public clientReferenceInformation: Riskv1decisionsClientReferenceInformation;
    public paymentInformation: Riskv1authenticationsetupsPaymentInformation;
  }

  export class CapturePaymentRequest {
    clientReferenceInformation: Riskv1decisionsClientReferenceInformation;
    orderInformation: Ptsv2paymentsidcapturesOrderInformation;
  }

  export class CreatePaymentRequest {
    public clientReferenceInformation: Ptsv2paymentsClientReferenceInformation;
    public processingInformation: Ptsv2paymentsProcessingInformation;
    public paymentInformation: Ptsv2paymentsPaymentInformation;
    public orderInformation: Ptsv2paymentsOrderInformation;
    public consumerAuthenticationInformation: Ptsv2paymentsConsumerAuthenticationInformation;
    public deviceInformation: Ptsv2paymentsDeviceInformation;
    public merchantInformation: Ptsv2paymentsMerchantInformation;
  }

  export class ValidateRequest {
    public clientReferenceInformation: Ptsv2paymentsClientReferenceInformation;
    public paymentInformation: Ptsv2paymentsPaymentInformation;
    public consumerAuthenticationInformation: Riskv1authenticationresultsConsumerAuthenticationInformation;
    public processingInformation: Riskv1authenticationsetupsProcessingInformation;
  }

  export class CheckPayerAuthEnrollmentRequest {
    clientReferenceInformation: Ptsv2paymentsClientReferenceInformation;
    orderInformation: Ptsv2paymentsOrderInformation;
    paymentInformation: Ptsv2paymentsPaymentInformation;
    /**
     * @member {module:model/Riskv1authenticationsetupsProcessingInformation} processingInformation
     */
    processingInformation: Riskv1authenticationsetupsProcessingInformation;
  }

  export class VoidPaymentRequest {
    clientReferenceInformation: Ptsv2paymentsClientReferenceInformation;
  }

  export class Riskv1authenticationsOrderInformation extends Ptsv2paymentsOrderInformation {}
  export class Riskv1authenticationsOrderInformationAmountDetails extends Ptsv2paymentsOrderInformationAmountDetails {}
  export class Riskv1authenticationsOrderInformationBillTo extends Ptsv2paymentsOrderInformationBillTo {}
  export class Riskv1authenticationsPaymentInformation extends Ptsv2paymentsPaymentInformation {}
  export class Riskv1authenticationsPaymentInformationCard extends Ptsv2paymentsPaymentInformationCard {}

  export class Ptsv2paymentsDeviceInformation {
    /**
     * DNS resolved hostname from `ipAddress`.
     * @member {String} hostName
     */
    hostName: string;
    /**
     * IP address of the customer.  #### Used by **Authorization, Capture, and Credit** Optional field.
     * @member {String} ipAddress
     */
    ipAddress: string;
    /**
     * Customer’s browser as identified from the HTTP header data. For example, `Mozilla` is the value that identifies the Netscape browser.
     * @member {String} userAgent
     */
    userAgent: string;
    /**
     * Field that contains the session ID that you send to Decision Manager to obtain the device fingerprint information. The string can contain uppercase and lowercase letters, digits, hyphen (-), and underscore (_). However, do not use the same uppercase and lowercase letters to indicate different session IDs.  The session ID must be unique for each merchant ID. You can use any string that you are already generating, such as an order number or web session ID.  The session ID must be unique for each page load, regardless of an individual’s web session ID. If a user navigates to a profiled page and is assigned a web session, navigates away from the profiled page, then navigates back to the profiled page, the generated session ID should be different and unique. You may use a web session ID, but it is preferable to use an application GUID (Globally Unique Identifier). This measure ensures that a unique ID is generated every time the page is loaded, even if it is the same user reloading the page.
     * @member {String} fingerprintSessionId
     */
    fingerprintSessionId: string;
    /**
     * Boolean that indicates whether request contains the device fingerprint information. Values: - `true`: Use raw fingerprintSessionId when looking up device details. - `false` (default): Use merchant id + fingerprintSessionId as the session id for Device detail collection.
     * @member {Boolean} useRawFingerprintSessionId
     */
    useRawFingerprintSessionId: boolean;
    /**
     * The device type at the client side.
     * @member {String} deviceType
     */
    deviceType: string;
    /**
     * This field will contain the deep link that would help the Customer App to wake up.
     * @member {String} appUrl
     */
    appUrl: string;
    /**
     * @member {Array.<module:model/Ptsv2paymentsDeviceInformationRawData>} rawData
     */
    rawData: Array<Ptsv2paymentsDeviceInformationRawData>;
    /**
     * Value of the Accept header sent by the customer’s web browser. **Note** If the customer’s browser provides a value, you must include it in your request.
     * @member {String} httpAcceptBrowserValue
     */
    httpAcceptBrowserValue: string;
    /**
     * The exact content of the HTTP accept header.
     * @member {String} httpAcceptContent
     */
    httpAcceptContent: string;
    /**
     * Email address set in the customer’s browser, which may differ from customer email.
     * @member {String} httpBrowserEmail
     */
    httpBrowserEmail: string;
    /**
     * Value represents the browser language as defined in IETF BCP47. Example:en-US, refer  https://en.wikipedia.org/wiki/IETF_language_tag for more details.
     * @member {String} httpBrowserLanguage
     */
    httpBrowserLanguage: string;
    /**
     * A Boolean value that represents the ability of the cardholder browser to execute Java. Value is returned from the navigator.javaEnabled property. Possible Values:True/False
     * @member {Boolean} httpBrowserJavaEnabled
     */
    httpBrowserJavaEnabled: boolean;
    /**
     * A Boolean value that represents the ability of the cardholder browser to execute JavaScript. Possible Values:True/False. **Note**: Merchants should be able to know the values from fingerprint details of cardholder's browser.
     * @member {Boolean} httpBrowserJavaScriptEnabled
     */
    httpBrowserJavaScriptEnabled: boolean;
    /**
     * Value represents the bit depth of the color palette for displaying images, in bits per pixel. Example : 24, refer https://en.wikipedia.org/wiki/Color_depth for more details
     * @member {String} httpBrowserColorDepth
     */
    httpBrowserColorDepth: string;
    /**
     * Total height of the Cardholder's scree in pixels, example: 864.
     * @member {String} httpBrowserScreenHeight
     */
    httpBrowserScreenHeight: string;
    /**
     * Total width of the cardholder's screen in pixels. Example: 1536.
     * @member {String} httpBrowserScreenWidth
     */
    httpBrowserScreenWidth: string;
    /**
     * Time difference between UTC time and the cardholder browser local time, in minutes, Example:300
     * @member {String} httpBrowserTimeDifference
     */
    httpBrowserTimeDifference: string;
    /**
     * Value of the User-Agent header sent by the customer’s web browser. Note If the customer’s browser provides a value, you must include it in your request.
     * @member {String} userAgentBrowserValue
     */
    userAgentBrowserValue: string;
  }

  /**
   * -----------------------------------------------------------------
   * Types
   * -----------------------------------------------------------------
   */

  export class Tmsv2customersEmbeddedDefaultPaymentInstrumentInstrumentIdentifier {
    /**
     * The Id of the Instrument Identifier linked to the Payment Instrument.
     * @member {String} id
     */
    id: string;
  }

  export class Riskv1authenticationresultsConsumerAuthenticationInformation {
    /**
     * Payer authentication transaction identifier passed to link the check enrollment and validate authentication messages.For Rupay,this is passed only in Re-Send OTP usecase. **Note**: Required for Standard integration, Rupay Seamless server to server integration for enroll service. Required for Hybrid integration for validate service.
     * @member {String} authenticationTransactionId
     */
    authenticationTransactionId: string;
    /**
     * Authentication transaction context is used as a unique identifier to link enroll and validate call.
     * @member {String} authenticationTransactionContext
     */
    authenticationTransactionContext: string;
    /**
     * OTP entered by the card holder.
     * @member {String} otpToken
     */
    otpToken: string;
    /**
     * Indicates the type of authentication that will be used to challenge the card holder.  Possible Values:  01 - Static  02 - Dynamic  03 - OOB (Out of Band)  04 - Decoupled  20 - OTP hosted at merchant end. (Rupay S2S flow) **NOTE**:  EMV 3-D Secure version 2.1.0 supports values 01-03.  Version 2.2.0 supports values 01-04.  Decoupled authentication is not supported at this time.
     * @member {String} authenticationType
     */
    authenticationType: string;
    /**
     * This field describes the type of 3DS transaction flow that took place.  It can be one of three possible flows; CH - Challenge FR - Frictionless FD - Frictionless with delegation, (challenge not generated by the issuer but by the scheme on behalf of the issuer).
     * @member {String} effectiveAuthenticationType
     */
    effectiveAuthenticationType: string;
    /**
     * JWT returned by the 3D Secure provider when the authentication is complete. Required for Hybrid integration if you use the Cybersource-generated access token. Note: Max. length of this field is 2048 characters.
     * @member {String} responseAccessToken
     */
    responseAccessToken: string;
    /**
     * Provides additional information as to why the PAResStatus has a specific value.
     * @member {String} signedParesStatusReason
     */
    signedParesStatusReason: string;
    /**
     * Payer authentication result (PARes) message returned by the card-issuing bank. If you need to show proof of enrollment checking, you may need to decrypt and parse the string for the information required by the payment card company. For more information, see \"Storing Payer Authentication Data,\" page 160. Important The value is in base64. You must remove all carriage returns and line feeds before adding the PARes to the request.
     * @member {String} signedPares
     */
    signedPares: string;
    /**
     * Enables the communication of trusted beneficiary/whitelist status between the ACS, the DS and the 3DS Requestor.  Possible Values:  Y - 3DS Requestor is whitelisted by cardholder  N - 3DS Requestor is not whitelisted by cardholder
     * @member {String} whiteListStatus
     */
    whiteListStatus: string;
    /**
     * A flag to indicate if the passed credential has been encrypted by the Merchant.
     * @member {String} credentialEncrypted
     */
    credentialEncrypted: string;
  }

  export class BillTo {
    /**
     * Customer’s first name. This name must be the same as the name on the card.  **Important** It is your responsibility to determine whether a field is required for the transaction you are requesting.  #### CyberSource Latin American Processing **Important** For an authorization request, CyberSource Latin American Processing concatenates `orderInformation.billTo.firstName` and `orderInformation.billTo.lastName`. If the concatenated value exceeds 30 characters, CyberSource Latin American Processing declines the authorization request.\\ **Note** CyberSource Latin American Processing is the name of a specific processing connection that CyberSource supports. In the CyberSource API documentation, CyberSource Latin American Processing does not refer to the general topic of processing in Latin America. The information in this field description is for the specific processing connection called _CyberSource Latin American Processing_. It is not for any other Latin American processors that CyberSource supports.  #### CyberSource through VisaNet Credit card networks cannot process transactions that contain non-ASCII characters. CyberSource through VisaNet accepts and stores non-ASCII characters correctly and displays them correctly in reports. However, the limitations of the credit card networks prevent CyberSource through VisaNet from transmitting non-ASCII characters to the credit card networks. Therefore, CyberSource through VisaNet replaces non-ASCII characters with meaningless ASCII characters for transmission to the credit card networks.  #### For Payouts: This field may be sent only for FDC Compass.  #### Chase Paymentech Solutions Optional field.  ####  Credit Mutuel-CIC Optional field.  #### OmniPay Direct Optional field.  #### SIX Optional field.  #### TSYS Acquiring Solutions Required when `processingInformation.billPaymentOptions.billPayment=true` and `pointOfSaleInformation.entryMode=keyed`.  #### Worldpay VAP Optional field.  #### All other processors Not used.
     * @member {String} firstName
     */
    firstName: string;
    /**
     * Customer’s last name. This name must be the same as the name on the card.  **Important** It is your responsibility to determine whether a field is required for the transaction you are requesting.  #### Chase Paymentech Solutions Optional field.  ####  Credit Mutuel-CIC Optional field.  #### CyberSource Latin American Processing **Important** For an authorization request, CyberSource Latin American Processing concatenates `orderInformation.billTo.firstName` and `orderInformation.billTo.lastName`. If the concatenated value exceeds 30 characters, CyberSource Latin American Processing declines the authorization request.\\ **Note** CyberSource Latin American Processing is the name of a specific processing connection that CyberSource supports. In the CyberSource API documentation, CyberSource Latin American Processing does not refer to the general topic of processing in Latin America. The information in this field description is for the specific processing connection called CyberSource Latin American Processing. It is not for any other Latin American processors that CyberSource supports.  #### CyberSource through VisaNet Credit card networks cannot process transactions that contain non-ASCII characters. CyberSource through VisaNet accepts and stores non-ASCII characters correctly and displays them correctly in reports. However, the limitations of the credit card networks prevent CyberSource through VisaNet from transmitting non-ASCII characters to the credit card networks. Therefore, CyberSource through VisaNet replaces non-ASCII characters with meaningless ASCII characters for transmission to the credit card networks.  #### For Payouts: This field may be sent only for FDC Compass.  #### OmniPay Direct Optional field.  #### RBS WorldPay Atlanta Optional field.  #### SIX Optional field.  #### TSYS Acquiring Solutions Required when `processingInformation.billPaymentOptions.billPayment=true` and `pointOfSaleInformation.entryMode=keyed`.  #### Worldpay VAP Optional field.  #### All other processors Not used.
     * @member {String} lastName
     */
    lastName: string;
    /**
     * Customer’s middle name.
     * @member {String} middleName
     */
    middleName: string;
    /**
     * Customer’s name suffix.
     * @member {String} nameSuffix
     */
    nameSuffix: string;
    /**
     * Title.
     * @member {String} title
     */
    title: string;
    /**
     * @member {module:model/Ptsv2paymentsOrderInformationBillToCompany} company
     */
    company: Ptsv2paymentsOrderInformationBillToCompany;
    /**
     * Payment card billing street address as it appears on the credit card issuer’s records.  #### Atos This field must not contain colons (:).  #### CyberSource through VisaNet **Important** When you populate orderInformation.billTo.address1 and orderInformation.billTo.address2, CyberSource through VisaNet concatenates the two values. If the concatenated value exceeds 40 characters, CyberSource through VisaNet truncates the value at 40 characters before sending it to Visa and the issuing bank. Truncating this value affects AVS results and therefore might also affect risk decisions and chargebacks. Credit card networks cannot process transactions that contain non-ASCII characters. CyberSource through VisaNet accepts and stores non-ASCII characters correctly and displays them correctly in reports. However, the limitations of the credit card networks prevent CyberSource through VisaNet from transmitting non-ASCII characters to the credit card networks. Therefore, CyberSource through VisaNet replaces non-ASCII characters with meaningless ASCII characters for transmission to the credit card networks.  #### FDMS Nashville When the street name is numeric, it must be sent in numeric format. For example, if the address is _One First Street_, it must be sent as _1 1st Street_.  Required if keyed; not used if swiped.  String (20)  #### TSYS Acquiring Solutions Required when `processingInformation.billPaymentOptions.billPayment=true` and `pointOfSaleInformation.entryMode=keyed`.  #### All other processors: Optional. String (60)  #### For Payouts This field may be sent only for FDC Compass.  **Important** It is your responsibility to determine whether a field is required for the transaction you are requesting.
     * @member {String} address1
     */
    address1: string;
    /**
     * Used for additional address information. For example: _Attention: Accounts Payable_ Optional field.  For Payouts: This field may be sent only for FDC Compass.  #### Atos This field must not contain colons (:).  #### CyberSource through VisaNet **Important** When you populate `orderInformation.billTo.address1` and `orderInformation.billTo.address2`, CyberSource through VisaNet concatenates the two values. If the concatenated value exceeds 40 characters, CyberSource through VisaNet truncates the value at 40 characters before sending it to Visa and the issuing bank. Truncating this value affects AVS results and therefore might also affect risk decisions and chargebacks. Credit card networks cannot process transactions that contain non-ASCII characters. CyberSource through VisaNet accepts and stores non-ASCII characters correctly and displays them correctly in reports. However, the limitations of the credit card networks prevent CyberSource through VisaNet from transmitting non-ASCII characters to the credit card networks. Therefore, CyberSource through VisaNet replaces non-ASCII characters with meaningless ASCII characters for transmission to the credit card networks.  #### Chase Paymentech Solutions, FDC Compass, and TSYS Acquiring Solutions This value is used for AVS.  #### FDMS Nashville `orderInformation.billTo.address1` and `orderInformation.billTo.address2` together cannot exceed 20 characters. String (20)  #### All Other Processors String (60)
     * @member {String} address2
     */
    address2: string;
    /**
     * Additional address information (third line of the billing address)
     * @member {String} address3
     */
    address3: string;
    /**
     * Additional address information (fourth line of the billing address)
     * @member {String} address4
     */
    address4: string;
    /**
     * Payment card billing city.  #### Atos This field must not contain colons (:).  #### CyberSource through VisaNet Credit card networks cannot process transactions that contain non-ASCII characters. CyberSource through VisaNet accepts and stores non-ASCII characters correctly and displays them correctly in reports. However, the limitations of the credit card networks prevent CyberSource through VisaNet from transmitting non-ASCII characters to the credit card networks. Therefore, CyberSource through VisaNet replaces non-ASCII characters with meaningless ASCII characters for transmission to the credit card networks.  #### For Payouts: This field may be sent only for FDC Compass.  #### Chase Paymentech Solutions Optional field.  ####  Credit Mutuel-CIC Optional field.  #### OmniPay Direct Optional field.  #### SIX Optional field.  #### TSYS Acquiring Solutions Required when `processingInformation.billPaymentOptions.billPayment=true` and `pointOfSaleInformation.entryMode=keyed`.  #### Worldpay VAP Optional field.  #### All other processors Not used.
     * @member {String} locality
     */
    locality: string;
    /**
     * State or province of the billing address. Use the [State, Province, and Territory Codes for the United States and Canada](https://developer.cybersource.com/library/documentation/sbc/quickref/states_and_provinces.pdf).  For Payouts: This field may be sent only for FDC Compass.  ##### CyberSource through VisaNet Credit card networks cannot process transactions that contain non-ASCII characters. CyberSource through VisaNet accepts and stores non-ASCII characters correctly and displays them correctly in reports. However, the limitations of the credit card networks prevent CyberSource through VisaNet from transmitting non-ASCII characters to the credit card networks. Therefore, CyberSource through VisaNet replaces non-ASCII characters with meaningless ASCII characters for transmission to the credit card networks.  **Important** It is your responsibility to determine whether a field is required for the transaction you are requesting.  #### Chase Paymentech Solutions Optional field.  ####  Credit Mutuel-CIC Optional field.  #### OmniPay Direct Optional field.  #### SIX Optional field.  #### TSYS Acquiring Solutions Required when `processingInformation.billPaymentOptions.billPayment=true` and `pointOfSaleInformation.entryMode=keyed`.  #### Worldpay VAP Optional field.  #### All other processors Not used.
     * @member {String} administrativeArea
     */
    administrativeArea: string;
    /**
     * Postal code for the billing address. The postal code must consist of 5 to 9 digits.  When the billing country is the U.S., the 9-digit postal code must follow this format: [5 digits][dash][4 digits]  **Example** `12345-6789`  When the billing country is Canada, the 6-digit postal code must follow this format: [alpha][numeric][alpha][space][numeric][alpha][numeric]  **Example** `A1B 2C3`  **Important** It is your responsibility to determine whether a field is required for the transaction you are requesting.  #### For Payouts:  This field may be sent only for FDC Compass.  #### American Express Direct Before sending the postal code to the processor, CyberSource removes all nonalphanumeric characters and, if the remaining value is longer than nine characters, truncates the value starting from the right side.  #### Atos This field must not contain colons (:).  #### CyberSource through VisaNet Credit card networks cannot process transactions that contain non-ASCII characters. CyberSource through VisaNet accepts and stores non-ASCII characters correctly and displays them correctly in reports. However, the limitations of the credit card networks prevent CyberSource through VisaNet from transmitting non-ASCII characters to the credit card networks. Therefore, CyberSource through VisaNet replaces non-ASCII characters with meaningless ASCII characters for transmission to the credit card networks.  #### FDMS Nashville Required if `pointOfSaleInformation.entryMode=keyed` and the address is in the U.S. or Canada. Optional if `pointOfSaleInformation.entryMode=keyed` and the address is **not** in the U.S. or Canada. Not used if swiped.  #### RBS WorldPay Atlanta: For best card-present keyed rates, send the postal code if `pointOfSaleInformation.entryMode=keyed`.  #### TSYS Acquiring Solutions Required when `processingInformation.billPaymentOptions.billPayment=true` and `pointOfSaleInformation.entryMode=keyed`.  #### All other processors: Optional field.
     * @member {String} postalCode
     */
    postalCode: string;
    /**
     * U.S. county if available.
     * @member {String} county
     */
    county: string;
    /**
     * Payment card billing country. Use the two-character [ISO Standard Country Codes](http://apps.cybersource.com/library/documentation/sbc/quickref/countries_alpha_list.pdf).  #### CyberSource through VisaNet Credit card networks cannot process transactions that contain non-ASCII characters. CyberSource through VisaNet accepts and stores non-ASCII characters correctly and displays them correctly in reports. However, the limitations of the credit card networks prevent CyberSource through VisaNet from transmitting non-ASCII characters to the credit card networks. Therefore, CyberSource through VisaNet replaces non-ASCII characters with meaningless ASCII characters for transmission to the credit card networks.  **Important** It is your responsibility to determine whether a field is required for the transaction you are requesting.  #### Chase Paymentech Solutions Optional field.  ####  Credit Mutuel-CIC Optional field.  #### OmniPay Direct Optional field.  #### SIX Optional field.  #### TSYS Acquiring Solutions Required when `processingInformation.billPaymentOptions.billPayment=true` and `pointOfSaleInformation.entryMode=keyed`.  #### Worldpay VAP Optional field.  #### All other processors Not used.
     * @member {String} country
     */
    country: string;
    /**
     * Customer’s neighborhood, community, or region (a barrio in Brazil) within the city or municipality. This field is available only on **Cielo**.
     * @member {String} district
     */
    district: string;
    /**
     * Building number in the street address.  For example, if the street address is: Rua da Quitanda 187 then the building number is 187.  This field is supported only for:  - Cielo transactions.  - Redecard customer validation with CyberSource Latin American Processing.
     * @member {String} buildingNumber
     */
    buildingNumber: string;
    /**
     * Customer's email address, including the full domain name.  #### CyberSource through VisaNet Credit card networks cannot process transactions that contain non-ASCII characters. CyberSource through VisaNet accepts and stores non-ASCII characters correctly and displays them correctly in reports. However, the limitations of the credit card networks prevent CyberSource through VisaNet from transmitting non-ASCII characters to the credit card networks. Therefore, CyberSource through VisaNet replaces non-ASCII characters with meaningless ASCII characters for transmission to the credit card networks.  **Important** It is your responsibility to determine whether a field is required for the transaction you are requesting.  For processor-specific information, see the `customer_email` request-level field description in [Credit Card Services Using the SCMP API.](http://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html)  #### Invoicing Email address for the customer for sending the invoice. If the invoice is in SENT status and email is updated, the old email customer payment link won't work and you must resend the invoice with the new payment link.  #### Chase Paymentech Solutions Optional field.  ####  Credit Mutuel-CIC Optional field.  #### OmniPay Direct Optional field.  #### SIX Optional field.  #### TSYS Acquiring Solutions Required when `processingInformation.billPaymentOptions.billPayment=true` and `pointOfSaleInformation.entryMode=keyed`.  #### Worldpay VAP Optional field.  #### All other processors Not used.
     * @member {String} email
     */
    email: string;
    /**
     * Email domain of the customer. The domain of the email address comprises all characters that follow the @ symbol, such as mail.example.com. For the Risk Update service, if the email address and the domain are sent in the request, the domain supersedes the email address.
     * @member {String} emailDomain
     */
    emailDomain: string;
    /**
     * Customer’s phone number.  It is recommended that you include the country code when the order is from outside the U.S.  #### Chase Paymentech Solutions Optional field.  ####  Credit Mutuel-CIC Optional field.  #### CyberSource through VisaNet Credit card networks cannot process transactions that contain non-ASCII characters. CyberSource through VisaNet accepts and stores non-ASCII characters correctly and displays them correctly in reports. However, the limitations of the credit card networks prevent CyberSource through VisaNet from transmitting non-ASCII characters to the credit card networks. Therefore, CyberSource through VisaNet replaces non-ASCII characters with meaningless ASCII characters for transmission to the credit card networks.  #### For Payouts: This field may be sent only for FDC Compass.  #### OmniPay Direct Optional field.  #### SIX Optional field.  #### TSYS Acquiring Solutions Optional field.  #### Worldpay VAP Optional field.  #### All other processors Not used.
     * @member {String} phoneNumber
     */
    phoneNumber: string;
    /**
     * Customer's phone number type.  #### For Payouts: This field may be sent only for FDC Compass.  Possible Values: * day * home * night * work
     * @member {String} phoneType
     */
    phoneType: string;
    /**
     * Whether buyer has verified their identity. Used in case of PayPal transactions.  Possible Values: * VERIFIED * UNVERIFIED
     * @member {String} verificationStatus
     */
    verificationStatus: string;
    /**
     * #### Visa Platform Connect contains customer’s alternate phone number.
     * @member {String} alternatePhoneNumber
     */
    alternatePhoneNumber: string;
    /**
     * #### Visa Platform Connect contains customer’s alternate email address.
     * @member {String} alternateEmail
     */
    alternateEmail: string;
  }
  export class Ptsv2paymentsOrderInformationBillTo extends BillTo {}
  export class Tmsv2customersEmbeddedDefaultPaymentInstrumentBillTo extends BillTo {}
  export class Ptsv2paymentsConsumerAuthenticationInformation {
    /**
     * Reference ID that corresponds to the device fingerprinting data that was collected previously. Note Required for Hybrid integration.
     * @member {String} referenceId
     */
    referenceId: string;
    /**
     * The URL of the merchant’s return page. CyberSource adds this return URL to the step-up JWT and returns it in the response of the Payer Authentication enrollment call. The merchant's return URL page serves as a listening URL. Once the bank session completes, the merchant receives a POST to their URL. This response contains the completed bank session’s transactionId. The merchant’s return page should capture the transaction ID and send it in the Payer Authentication validation call.
     * @member {String} returnUrl
     */
    returnUrl: string;

    authenticationTransactionId: string;

    messageCategory: string;
  }

  export class Ptsv2paymentsOrderInformationAmountDetails {
    /**
     * Grand total for the order. This value cannot be negative. You can include a decimal point (.), but no other special characters. CyberSource truncates the amount to the correct number of decimal places.  **Note** For CTV, FDCCompass, Paymentech processors, the maximum length for this field is 12.  **Important** Some processors have specific requirements and limitations, such as maximum amounts and maximum field lengths. For details, see: - \"Authorization Information for Specific Processors\" in the [Credit Card Services Using the SCMP API Guide](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/). - \"Capture Information for Specific Processors\" in the [Credit Card Services Using the SCMP API Guide](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/). - \"Credit Information for Specific Processors\" in the [Credit Card Services Using the SCMP API Guide](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/).  If your processor supports zero amount authorizations, you can set this field to 0 for the authorization to check if the card is lost or stolen. For details, see \"Zero Amount Authorizations,\" \"Credit Information for Specific Processors\" in [Credit Card Services Using the SCMP API.](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/)  #### Card Present Required to include either this field or `orderInformation.lineItems[].unitPrice` for the order.  #### Invoicing Required for creating a new invoice.  #### PIN Debit Amount you requested for the PIN debit purchase. This value is returned for partial authorizations. The issuing bank can approve a partial amount if the balance on the debit card is less than the requested transaction amount.  Required field for PIN Debit purchase and PIN Debit credit requests. Optional field for PIN Debit reversal requests.  #### GPX This field is optional for reversing an authorization or credit; however, for all other processors, these fields are required.  #### DCC with a Third-Party Provider Set this field to the converted amount that was returned by the DCC provider. You must include either this field or the 1st line item in the order and the specific line-order amount in your request. For details, see `grand_total_amount` field description in [Dynamic Currency Conversion For First Data Using the SCMP API](http://apps.cybersource.com/library/documentation/dev_guides/DCC_FirstData_SCMP/DCC_FirstData_SCMP_API.pdf).  #### FDMS South If you accept IDR or CLP currencies, see the entry for FDMS South in \"Authorization Information for Specific Processors\" of the [Credit Card Services Using the SCMP API.](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/)  #### DCC for First Data Not used.
     * @member {String} totalAmount
     */
    totalAmount: string;
    /**
     * Subtotal amount of all the items.This amount (which is the value of all items in the cart, not including the additional amounts such as tax, shipping, etc.) cannot change after a sessions request. When there is a change to any of the additional amounts, this field should be resent in the order request. When the sub total amount changes, you must initiate a new transaction starting with a sessions request. Note The amount value must be a non-negative number containing 2 decimal places and limited to 7 digits before the decimal point. This value can not be changed after a sessions request.
     * @member {String} subTotalAmount
     */
    subTotalAmount: string;
    /**
     * Currency used for the order. Use the three-character [ISO Standard Currency Codes.](http://apps.cybersource.com/library/documentation/sbc/quickref/currencies.pdf)  #### Used by **Authorization** Required field.  **Authorization Reversal** For an authorization reversal (`reversalInformation`) or a capture (`processingOptions.capture` is set to `true`), you must use the same currency that you used in your payment authorization request.  #### PIN Debit Currency for the amount you requested for the PIN debit purchase. This value is returned for partial authorizations. The issuing bank can approve a partial amount if the balance on the debit card is less than the requested transaction amount. For the possible values, see the [ISO Standard Currency Codes](https://developer.cybersource.com/library/documentation/sbc/quickref/currencies.pdf). Returned by PIN debit purchase.  For PIN debit reversal requests, you must use the same currency that was used for the PIN debit purchase or PIN debit credit that you are reversing. For the possible values, see the [ISO Standard Currency Codes](https://developer.cybersource.com/library/documentation/sbc/quickref/currencies.pdf).  Required field for PIN Debit purchase and PIN Debit credit requests. Optional field for PIN Debit reversal requests.  #### GPX This field is optional for reversing an authorization or credit.  #### DCC for First Data Your local currency. For details, see the `currency` field description in [Dynamic Currency Conversion For First Data Using the SCMP API](http://apps.cybersource.com/library/documentation/dev_guides/DCC_FirstData_SCMP/DCC_FirstData_SCMP_API.pdf).  #### Tax Calculation Required for international tax and value added tax only. Optional for U.S. and Canadian taxes. Your local currency.
     * @member {String} currency
     */
    currency: string;
    /**
     * Total discount amount applied to the order.
     * @member {String} discountAmount
     */
    discountAmount: string;
    /**
     * Total charges for any import or export duties included in the order.
     * @member {String} dutyAmount
     */
    dutyAmount: string;
    /**
     * Gratuity or tip amount for restaurants. Allowed only when industryDatatype=restaurant. When your customer uses a debit card or prepaid card, and you receive a partial authorization, the payment networks recommend that you do not submit a capture amount that is higher than the authorized amount. When the capture amount exceeds the partial amount that was approved, the issuer has chargeback rights for the excess amount.  Used by **Capture** Optional field.  #### CyberSource through VisaNet Restaurant data is supported only on CyberSource through VisaNet when card is present.
     * @member {String} gratuityAmount
     */
    gratuityAmount: string;
    /**
     * Total tax amount for all the items in the order.
     * @member {String} taxAmount
     */
    taxAmount: string;
    /**
     * Flag that indicates whether a national tax is included in the order total.  Possible values:   - **0**: national tax not included  - **1**: national tax included
     * @member {String} nationalTaxIncluded
     */
    nationalTaxIncluded: string;
    /**
     * Flag that indicates how the merchant manages discounts.  Possible values:   - **0**: no invoice level discount included  - **1**: tax calculated on the postdiscount invoice total  - **2**: tax calculated on the prediscount invoice total
     * @member {String} taxAppliedAfterDiscount
     */
    taxAppliedAfterDiscount: string;
    /**
     * Flag that indicates how you calculate tax.  Possible values:   - **0**: net prices with tax calculated at line item level  - **1**: net prices with tax calculated at invoice level  - **2**: gross prices with tax provided at line item level  - **3**: gross prices with tax provided at invoice level  - **4**: no tax applies on the invoice for the transaction
     * @member {String} taxAppliedLevel
     */
    taxAppliedLevel: string;
    /**
     * For tax amounts that can be categorized as one tax type.  This field contains the tax type code that corresponds to the entry in the _lineItems.taxAmount_ field.  Possible values:   - **056**: sales tax (U.S only)  - **TX~**: all taxes (Canada only)   Note ~ = space.
     * @member {String} taxTypeCode
     */
    taxTypeCode: string;
    /**
     * Total freight or shipping and handling charges for the order. When you include this field in your request, you must also include the **totalAmount** field.  For processor-specific information, see the freight_amount field in [Level II and Level III Processing Using the SCMP API.](http://apps.cybersource.com/library/documentation/dev_guides/Level_2_3_SCMP_API/html)
     * @member {String} freightAmount
     */
    freightAmount: string;
    /**
     * Set this field to the converted amount that was returned by the DCC provider. For processor-specific information, see the `foreign_amount` field description in the [Credit Card Services Using the SCMP API Guide.](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/)
     * @member {String} foreignAmount
     */
    foreignAmount: string;
    /**
     * Set this field to the converted amount that was returned by the DCC provider.
     * @member {String} foreignCurrency
     */
    foreignCurrency: string;
    /**
     * Exchange rate returned by the DCC service. Includes a decimal point and a maximum of 4 decimal places.  For details, see `exchange_rate` request-level field description in the [Dynamic Currency Conversion For First Data Using the SCMP API](http://apps.cybersource.com/library/documentation/dev_guides/DCC_FirstData_SCMP/DCC_FirstData_SCMP_API.pdf)
     * @member {String} exchangeRate
     */
    exchangeRate: string;
    /**
     * Time stamp for the exchange rate. This value is returned by the DCC service.  Format: `YYYYMMDD~HH:MM`  where ~ denotes a space.
     * @member {String} exchangeRateTimeStamp
     */
    exchangeRateTimeStamp: string;
    /**
     * @member {module:model/Ptsv2paymentsOrderInformationAmountDetailsSurcharge} surcharge
     */
    surcharge: Ptsv2paymentsOrderInformationAmountDetailsSurcharge;
    /**
     * This is a multicurrency field. It contains the transaction amount (field 4), converted to the Currency used to bill the cardholder’s account. This field is returned for OCT transactions.
     * @member {String} settlementAmount
     */
    settlementAmount: string;
    /**
     * This is a multicurrency-only field. It contains a 3-digit numeric code that identifies the currency used by the issuer to bill the cardholder's account. This field is returned for OCT transactions.
     * @member {String} settlementCurrency
     */
    settlementCurrency: string;
    /**
     * @member {Array.<module:model/Ptsv2paymentsOrderInformationAmountDetailsAmexAdditionalAmounts>} amexAdditionalAmounts
     */
    amexAdditionalAmounts: Array<Ptsv2paymentsOrderInformationAmountDetailsAmexAdditionalAmounts>;
    /**
     * @member {Array.<module:model/Ptsv2paymentsOrderInformationAmountDetailsTaxDetails>} taxDetails
     */
    taxDetails: Array<Ptsv2paymentsOrderInformationAmountDetailsTaxDetails>;
    /**
     * Service fee. Required for service fee transactions.
     * @member {String} serviceFeeAmount
     */
    serviceFeeAmount: string;
    /**
     * Amount in your original local pricing currency.  This value cannot be negative. You can include a decimal point (.) in this field to denote the currency exponent, but you cannot include any other special characters.  If needed, CyberSource truncates the amount to the correct number of decimal places.
     * @member {String} originalAmount
     */
    originalAmount: string;
    /**
     * Your local pricing currency code.  For the possible values, see the [ISO Standard Currency Codes.](http://apps.cybersource.com/library/documentation/sbc/quickref/currencies.pdf)
     * @member {String} originalCurrency
     */
    originalCurrency: string;
    /**
     * Cashback amount in the acquirer’s currency. If a cashback amount is included in the request, it must be included in the `orderInformation.amountDetails.totalAmount` value.  This field is supported only on CyberSource through VisaNet.  #### Used by **Authorization** Optional. **Authorization Reversal** Optional.  #### PIN debit Optional field for PIN debit purchase, PIN debit credit or PIN debit reversal.
     * @member {String} cashbackAmount
     */
    cashbackAmount: string;
    /**
     * @member {module:model/Ptsv2paymentsOrderInformationAmountDetailsCurrencyConversion} currencyConversion
     */
    currencyConversion: Ptsv2paymentsOrderInformationAmountDetailsCurrencyConversion;
  }

  export class Ptsv2paymentsOrderInformation {
    /**
     * @member {module:model/Ptsv2paymentsOrderInformationAmountDetails} amountDetails
     */
    amountDetails: Ptsv2paymentsOrderInformationAmountDetails;
    /**
     * @member {module:model/Ptsv2paymentsOrderInformationBillTo} billTo
     */
    billTo: Ptsv2paymentsOrderInformationBillTo;
    /**
     * @member {module:model/Ptsv2paymentsOrderInformationShipTo} shipTo
     */
    shipTo: Ptsv2paymentsOrderInformationShipTo;
    /**
     * @member {Array.<module:model/Ptsv2paymentsOrderInformationLineItems>} lineItems
     */
    lineItems: Ptsv2paymentsOrderInformationLineItems;
    /**
     * @member {module:model/Ptsv2paymentsOrderInformationInvoiceDetails} invoiceDetails
     */
    invoiceDetails: Ptsv2paymentsOrderInformationInvoiceDetails;
    /**
     * @member {module:model/Ptsv2paymentsOrderInformationShippingDetails} shippingDetails
     */
    shippingDetails: Ptsv2paymentsOrderInformationShippingDetails;
    /**
     * This is only needed when you are requesting both payment and DM service at same time.  Boolean that indicates whether returns are accepted for this order. This field can contain one of the following values: - true: Returns are accepted for this order. - false: Returns are not accepted for this order.
     * @member {Boolean} returnsAccepted
     */
    returnsAccepted: boolean;
    /**
     * #### Visa Platform Connect : This API will contain the Flag that specifies whether the payment is for the purchase of cryptocurrency. Additional values to add : This API will contain the Flag that specifies whether the payment is for the purchase of cryptocurrency. valid values are - Y/y, true - N/n, false
     * @member {String} isCryptocurrencyPurchase
     */
    isCryptocurrencyPurchase: string;
    /**
     * Starting date and time for an event or a journey that is independent of which transportation mechanism, in UTC. The cutoffDateTime will supersede travelInformation.transit.airline.legs[].departureDate and travelInformation.transit.airline.legs[].departureTime if these fields are supplied in the request. Format: YYYY-MM-DDThh:mm:ssZ. Example 2016-08-11T22:47:57Z equals August 11, 2016, at 22:47:57 (10:47:57 p.m.). The T separates the date and the time. The Z indicates UTC.
     * @member {String} cutoffDateTime
     */
    cutoffDateTime: string;
    /**
     * Indicates whether cardholder is placing an order with a future availability or release date. This field can contain one of these values: - MERCHANDISE_AVAILABLE: Merchandise available - FUTURE_AVAILABILITY: Future availability
     * @member {String} preOrder
     */
    preOrder: string;
    /**
     * Expected date that a pre-ordered purchase will be available. Format: YYYYMMDD
     * @member {String} preOrderDate
     */
    preOrderDate: string;
    /**
     * Indicates whether the cardholder is reordering previously purchased merchandise. This field can contain one of these values: - false: First time ordered - true: Reordered
     * @member {Boolean} reordered
     */
    reordered: boolean;
    /**
     * Total number of articles/items in the order as a numeric decimal count. Possible values: 00 - 99
     * @member {String} totalOffersCount
     */
    totalOffersCount: string;
  }

  export class Riskv1authenticationresultsPaymentInformation extends Ptsv2paymentsPaymentInformation {}

  export class Ptsv2paymentsPaymentInformationPaymentInstrument {
    /**
     * Unique identifier for the Payment Instrument token used in the transaction. When you include this value in your request, many of the fields that are normally required for an authorization or credit become optional.
     * @member {String} id
     */
    id: string;
  }
  export class Ptsv2paymentsPaymentInformation {
    /**
     * @member {module:model/Ptsv2paymentsPaymentInformationCard} card
     */
    card: Ptsv2paymentsPaymentInformationCard;
    /**
     * @member {module:model/Ptsv2paymentsPaymentInformationTokenizedCard} tokenizedCard
     */
    tokenizedCard: Ptsv2paymentsPaymentInformationTokenizedCard;
    /**
     * @member {module:model/Ptsv2paymentsPaymentInformationFluidData} fluidData
     */
    fluidData: Ptsv2paymentsPaymentInformationFluidData;
    /**
     * @member {module:model/Ptsv2paymentsPaymentInformationCustomer} customer
     */
    customer: Ptsv2paymentsPaymentInformationCustomer;
    /**
     * @member {module:model/Ptsv2paymentsPaymentInformationPaymentInstrument} paymentInstrument
     */
    paymentInstrument: Ptsv2paymentsPaymentInformationPaymentInstrument;
    /**
     * @member {module:model/Ptsv2paymentsPaymentInformationInstrumentIdentifier} instrumentIdentifier
     */
    instrumentIdentifier: Ptsv2paymentsPaymentInformationInstrumentIdentifier;
    /**
     * @member {module:model/Ptsv2paymentsPaymentInformationShippingAddress} shippingAddress
     */
    shippingAddress: Ptsv2paymentsPaymentInformationShippingAddress;
    /**
     * @member {module:model/Ptsv2paymentsPaymentInformationLegacyToken} legacyToken
     */
    legacyToken: Ptsv2paymentsPaymentInformationLegacyToken;
    /**
     * @member {module:model/Ptsv2paymentsPaymentInformationBank} bank
     */
    bank: Ptsv2paymentsPaymentInformationBank;
    /**
     * @member {module:model/Ptsv2paymentsPaymentInformationPaymentType} paymentType
     */
    paymentType: Ptsv2paymentsPaymentInformationPaymentType;
    /**
     * Mastercard-defined code that indicates how the account information was obtained.  - `00`: Card - `01`: Mobile Network Operator (MNO) controlled removable secure element (SIM or UICC) personalized for use with a mobile phone or smartphone - `02`: Key fob - `03`: Watch using a contactless chip or a fixed (non-removable) secure element not controlled by the MNO - `04`: Mobile tag - `05`: Wristband - `06`: Mobile phone case or sleeve - `07`: Mobile phone or smartphone with a fixed (non-removable) secure element controlled by the MNO,for example, code division multiple access (CDMA) - `08`: Removable secure element not controlled by the MNO, for example, memory card personalized forused with a mobile phone or smartphone - `09`: Mobile Phone or smartphone with a fixed (non-removable) secure element not controlled by the MNO - `10`: MNO controlled removable secure element (SIM or UICC) personalized for use with a tablet or e-book - `11`: Tablet or e-book with a fixed (non-removable) secure element controlled by the MNO - `12`: Removable secure element not controlled by the MNO, for example, memory card personalized foruse with a tablet or e-book - `13`: Tablet or e-book with fixed (non-removable) secure element not controlled by the MNO - `14`: Mobile phone or smartphone with a payment application running in a host processor - `15`: Tablet or e-book with a payment application running in a host processor - `16`: Mobile phone or smartphone with a payment application running in the Trusted ExecutionEnvironment (TEE) of a host processor - `17`: Tablet or e-book with a payment application running in the TEE of a host processor - `18`: Watch with a payment application running in the TEE of a host processor - `19`: Watch with a payment application running in a host processor  Values from 20–99 exclusively indicate the form factor only without also indicating the storage technology  - `20`: Card - `21`: Phone e.g. Mobile Phone - `22`: Tablet/e-reader - `23`: Watch/Wristband e.g. Watch or wristband, including a fitness band, smart strap, disposable band, watch add-on, and security/ID band - `24`: Sticker - `25`: PC - `26`: Device Peripheral e.g. mobile phone case or sleeve - `27`: Tag e.g. key fob or mobile tag - `28`: Jewelry e.g. ring, bracelet, necklace and cuff links - `29`: Fashion Accessory e.g. handbag, bag charm and glasses - `30`: Garment e.g. dress - `31`: Domestic Appliance e.g refrigerator, washing machine - `32`: Vehicle e.g. vehicle, including vehicle attached devices - `33`: Media/Gaming Device e.g. media or gaming device, including a set top box, media player and television  34–99 are reserved for future form factors. Any value in this range may occur within form factor and transaction data without prior notice.  This field is supported only for Mastercard on CyberSource through VisaNet. When initiation channel is not provided via this API field, the value is extracted from EMV tag 9F6E for Mastercard transactions. To enable this feature please call support.  #### Used by **Authorization** Optional field.
     * @member {String} initiationChannel
     */
    initiationChannel: string;
    /**
     * @member {module:model/Ptsv2paymentsPaymentInformationEWallet} eWallet
     */
    eWallet: Ptsv2paymentsPaymentInformationEWallet;
  }

  export class Ptsv2paymentsPaymentInformationCustomer {
    /**
     * Unique identifier for the customer's card and billing information.  When you use Payment Tokenization or Recurring Billing and you include this value in your request, many of the fields that are normally required for an authorization or credit become optional.  **NOTE** When you use Payment Tokenization or Recurring Billing, the value for the Customer ID is actually the Cybersource payment token for a customer. This token stores information such as the consumer's card number so it can be applied towards bill payments, recurring payments, or one-time payments. By using this token in a payment API request, the merchant doesn't need to pass in data such as the card number or expiration date in the request itself.  For details, see the `subscription_id` field description in [Credit Card Services Using the SCMP API.](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/)
     * @member {String} customerId
     */
    customerId: string;
    /**
     * Unique identifier for the Customer token used in the transaction. When you include this value in your request, many of the fields that are normally required for an authorization or credit become optional.
     * @member {String} id
     */
    id: string;
  }

  export class Ptsv2paymentsProcessingInformation {
    /**
     * Array of actions (one or more) to be included in the payment to invoke bundled services along with payment.  Possible values are one or more of follows:   - `DECISION_SKIP`: Use this when you want to skip Decision Manager service(s).   - `TOKEN_CREATE`: Use this when you want to create a token from the card/bank data in your payment request.   - `CONSUMER_AUTHENTICATION`: Use this when you want to check if a card is enrolled in Payer Authentication along with your payment request.   - `VALIDATE_CONSUMER_AUTHENTICATION`: Use this after you acquire a Payer Authentication result that needs to be included for your payment request.    - `AP_INITIATE`: Use this when Alternative Payment Initiate service is requested.   - `WATCHLIST_SCREENING` : Use this when you want to call Watchlist Screening service.
     * @member {Array.<String>} actionList
     */
    actionList: Array<string>;
    /**
     * Indicates whether to use the customer’s escrow agreement. Possible values: - `true`: yes, use the customer’s escrow agreement. - `false`: no, do not use the customer’s escrow agreement.
     * @member {Boolean} enableEscrowOption
     */
    enableEscrowOption: boolean;
    /**
     * CyberSource tokens types you are performing a create on. If not supplied the default token type for the merchants token vault will be used.  Valid values: - customer - paymentInstrument - instrumentIdentifier - shippingAddress
     * @member {Array.<String>} actionTokenTypes
     */
    actionTokenTypes: Array<string>;
    /**
     * Bin Source File Identifier. Possible values: - itmx - rupay
     * @member {String} binSource
     */
    binSource: string;
    /**
     * Indicates whether to also include a capture  in the submitted authorization request or not.  Possible values: - `true`: Include a capture with an authorization request. - `false`: (default) Do not include a capture with an authorization request.  #### Used by **Authorization and Capture** Optional field.
     * @member {Boolean} capture
     * @default false
     */
    capture: boolean;
    /**
     * Value that identifies the processor/acquirer to use for the transaction. This value is supported only for **CyberSource through VisaNet**.  Contact CyberSource Customer Support to get the value for this field.
     * @member {String} processorId
     */
    processorId: string;
    /**
     * Payouts transaction type. Required for OCT transactions. This field is a pass-through, which means that CyberSource does not verify the value or modify it in any way before sending it to the processor. **Note** When the request includes this field, this value overrides the information in your CyberSource account.  For valid values, see the `invoiceHeader_businessApplicationID` field description in [Payouts Using the Simple Order API.](http://apps.cybersource.com/library/documentation/dev_guides/payouts_SO/Payouts_SO_API.pdf)
     * @member {String} businessApplicationId
     */
    businessApplicatioStringnId: string;
    /**
     * Type of transaction. Some payment card companies use this information when determining discount rates.  #### Used by **Authorization** Required payer authentication transactions; otherwise, optional. **Credit** Required for standalone credits on Chase Paymentech solutions; otherwise, optional.  The list of valid values in this field depends on your processor. See Appendix I, \"Commerce Indicators,\" on page 441 of the Cybersource Credit Card Guide.  #### Ingenico ePayments When you omit this field for Ingenico ePayments, the processor uses the default transaction type they have on file for you instead of the default value (listed in Appendix I, \"Commerce Indicators,\" on page 441.)  #### Payer Authentication Transactions For the possible values and requirements, see \"Payer Authentication,\" page 195.  #### Card Present You must set this field to `retail`. This field is required for a card-present transaction. Note that this should ONLY be used when the cardholder and card are present at the time of the transaction. For all keyed transactions originated from a POS terminal where the cardholder and card are not present, commerceIndicator should be submitted as “moto\"
     * @member {String} commerceIndicator
     */
    commerceIndicator: string;
    /**
     * Type of transaction. Some payment card companies use this information when determining discount rates.  #### Used by **Authorization** Required payer authentication transactions; otherwise, optional. **Credit** Required for standalone credits on Chase Paymentech solutions; otherwise, optional.  The list of valid values in this field depends on your processor. See Appendix I, \"Commerce Indicators,\" on page 441 of the Cybersource Credit Card Guide.  #### Ingenico ePayments When you omit this field for Ingenico ePayments, the processor uses the default transaction type they have on file for you instead of the default value (listed in Appendix I, \"Commerce Indicators,\" on page 441.)  #### Payer Authentication Transactions For the possible values and requirements, see \"Payer Authentication,\" page 195.  #### Card Present You must set this field to `retail`. This field is required for a card-present transaction. Note that this should ONLY be used when the cardholder and card are present at the time of the transaction. For all keyed transactions originated from a POS terminal where the cardholder and card are not present, commerceIndicator should be submitted as “moto\"
     * @member {String} commerceIndicatorLabel
     */
    commerceIndicatorLStringabel: string;
    /**
     * Type of digital payment solution for the transaction. Possible Values:   - `visacheckout`: Visa Checkout. This value is required for Visa Checkout transactions. For details, see `payment_solution` field description in [Visa Checkout Using the SCMP API.](https://apps.cybersource.com/library/documentation/dev_guides/VCO_SCMP_API/html/)  - `001`: Apple Pay.  - `004`: Cybersource In-App Solution.  - `005`: Masterpass. This value is required for Masterpass transactions on OmniPay Direct. For details, see \"Masterpass\" in the [Credit Card Services Using the SCMP API Guide.](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/)  - `006`: Android Pay.  - `007`: Chase Pay.  - `008`: Samsung Pay.  - `012`: Google Pay.  - `013`: Cybersource P2PE Decryption  - `014`: Mastercard credential on file (COF) payment network token. Returned in authorizations that use a payment network token associated with a TMS token.  - `015`: Visa credential on file (COF) payment network token. Returned in authorizations that use a payment network token associated with a TMS token.  - `027`: Click to Pay.
     * @member {String} paymentSolution
     */
    paymentSolution: string;
    /**
     * Please check with Cybersource customer support to see if your merchant account is configured correctly so you can include this field in your request. * For Payouts: max length for FDCCompass is String (22).
     * @member {String} reconciliationId
     */
    reconciliationId: string;
    /**
     * Value that links the current authorization request to the original authorization request. Set this value to the ID that was returned in the reply message from the original authorization request.  This value is used for:  - Partial authorizations - Split shipments  For details, see `link_to_request` field description in [Credit Card Services Using the SCMP API.](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/)
     * @member {String} linkId
     */
    linkId: string;
    /**
     * Set this field to 3 to indicate that the request includes Level III data.
     * @member {String} purchaseLevel
     */
    purchaseLevel: string;
    /**
     * This field is to accept the id of credit/capture in the body of L1 requests so the type of void can be identified and processed correctly downstream.
     * @member {String} paymentId
     */
    paymentId: string;
    /**
     * Attribute that lets you define custom grouping for your processor reports. This field is supported only for **Worldpay VAP**.  For details, see `report_group` field description in [Credit Card Services Using the SCMP API.](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/)
     * @member {String} reportGroup
     */
    reportGroup: string;
    /**
     * Identifier for the **Visa Checkout** order. Visa Checkout provides a unique order ID for every transaction in the Visa Checkout **callID** field.
     * @member {String} visaCheckoutId
     */
    visaCheckoutId: string;
    /**
     * Indicates that the transaction includes industry-specific data.  Possible Values: - `airline` - `restaurant` - `lodging` - `auto_rental` - `transit` - `healthcare_medical` - `healthcare_transit` - `transit`  #### Card Present, Airlines and Auto Rental You must set this field to `airline` in order for airline data to be sent to the processor. For example, if this field is not set to `airline` or is not included in the request, no airline data is sent to the processor.  You must set this field to `restaurant` in order for restaurant data to be sent to the processor. When this field is not set to `restaurant` or is not included in the request, no restaurant data is sent to the processor.  You must set this field to `auto_rental` in order for auto rental data to be sent to the processor. For example, if this field is not set to `auto_rental` or is not included in the request, no auto rental data is sent to the processor.  Restaurant data is supported only on CyberSource through VisaNet.
     * @member {String} industryDataType
     */
    industryDataType: string;
    /**
     * @member {module:model/Ptsv2paymentsProcessingInformationAuthorizationOptions} authorizationOptions
     */
    authorizationOptions: Ptsv2paymentsProcessingInformationAuthorizationOptions;
    /**
     * @member {module:model/Ptsv2paymentsProcessingInformationCaptureOptions} captureOptions
     */
    captureOptions: Ptsv2paymentsProcessingInformationCaptureOptions;
    /**
     * @member {module:model/Ptsv2paymentsProcessingInformationRecurringOptions} recurringOptions
     */
    recurringOptions: Ptsv2paymentsProcessingInformationRecurringOptions;
    /**
     * @member {module:model/Ptsv2paymentsProcessingInformationBankTransferOptions} bankTransferOptions
     */
    bankTransferOptions: Ptsv2paymentsProcessingInformationBankTransferOptions;
    /**
     * @member {module:model/Ptsv2paymentsProcessingInformationPurchaseOptions} purchaseOptions
     */
    purchaseOptions: Ptsv2paymentsProcessingInformationPurchaseOptions;
    /**
     * @member {module:model/Ptsv2paymentsProcessingInformationElectronicBenefitsTransfer} electronicBenefitsTransfer
     */
    electronicBenefitsTransfer: Ptsv2paymentsProcessingInformationElectronicBenefitsTransfer;
    /**
     * @member {module:model/Ptsv2paymentsProcessingInformationLoanOptions} loanOptions
     */
    loanOptions: Ptsv2paymentsProcessingInformationLoanOptions;
    /**
     * This field carries the wallet type in authorization requests and credit requests. Possible value are: - `101`: Masterpass remote payment. The customer created the wallet by manually interacting with a customer-controlled device such as a computer, tablet, or phone. This value is supported only for Masterpass transactions on Chase Paymentech Solutions and CyberSource through VisaNet. - `102`: Masterpass remote near field communication (NFC) payment. The customer created the wallet by tapping a PayPass card or customer-controlled device at a contactless card reader. This value is supported only for card-present Masterpass transactions on CyberSource through VisaNet. - `103`: Masterpass Apple Pay payment. The payment was made with a combination of Masterpass and Apple Pay. This value is supported only for Masterpass Apple Pay transactions on CyberSource through VisaNet. - `216`: Masterpass Google Pay payment. The payment was made with a combination of Masterpass and Google Pay. This value is supported only for Masterpass Google Pay transactions on CyberSource through VisaNet. - `217`: Masterpass Samsung Pay payment. The payment was made with a combination of Masterpass and Samsung Pay. This value is supported only for Masterpass Samsung Pay transactions on CyberSource through VisaNet. - `SDW`: Staged digital wallet. An issuer or operator created the wallet. This value is supported only for Masterpass transactions on Chase Paymentech Solutions. - `VCIND`: Visa Checkout payment. This value is supported only on CyberSource through VisaNet, FDC Compass, FDC Nashville Global, FDI Australia, and TSYS Acquiring Solutions. See Getting Started with Visa Checkout. For Visa Checkout transactions, the way CyberSource processes the value for this field depends on the processor. See the Visa Checkout section below. For all other values, this field is a passthrough; therefore, CyberSource does not verify the value or modify it in any way before sending it to the processor. Masterpass (101, 102, 103, 216, and 217): The Masterpass platform generates the wallet type value and passes it to you along with the customer’s checkout information.  Visa Checkout: This field is optional for Visa Checkout authorizations on FDI Australia. For all other processors, this field is required for Visa Checkout authorizations. For Visa Checkout transactions on the following processors, CyberSource sends the value that the processor expects for this field:FDC Compass,FDC Nashville Global,FDI Australia,TSYS Acquiring Solutions For all other processors, this field is a passthrough; therefore, CyberSource does not verify the value or modify it in any way before sending it to the processor. For incremental authorizations, this field is supported only for Mastercard and the supported values are 101 and 102. Payment card companies can introduce new values without notice. Your order management system should be able to process new values without problems.  CyberSource through VisaNet When the value for this field is 101, 102, 103, 216, or 217, it corresponds to the following data in the TC 33 capture file5: Record: CP01 TCR6, Position: 88-90,  Field: Mastercard Wallet Identifier. When the value for this field is VCIND, it corresponds to the following data in the TC 33 capture file5: Record: CP01 TCR8, Position: 72-76, Field: Agent Unique ID.
     * @member {String} walletType
     */
    walletType: string;
    /**
     * Supplementary domestic transaction information provided by the acquirer for National Net Settlement Service (NNSS) transactions. NNSS is a settlement service that Visa provides. For transactions on CyberSource through VisaNet in countries that subscribe to NNSS: VisaNet clears transactions; VisaNet transfers funds to the acquirer after deducting processing fees and interchange fees. VisaNet settles transactions in the local pricing currency through a local financial institution. This field is supported only on CyberSource through VisaNet for domestic data in Colombia
     * @member {String} nationalNetDomesticData
     */
    nationalNetDomestiStringcData: string;
    /**
     * @member {module:model/Ptsv2paymentsProcessingInformationJapanPaymentOptions} japanPaymentOptions
     */
    japanPaymentOptions: Ptsv2paymentsProcessingInformationJapanPaymentOptions;
    /**
     * Type of payment initiated from a cardholder's mobile device. Possible values: - `1` :  Consumer-initiated remote purchase, face-to-face - `2` :  Consumer-initiated remote purchase, e-commerce - `3` :  Consumer-initiated remote purchase, mail order / telephone order - `4` :  Consumer-initiated bill pay - `5` :  Consumer-initiated top up - `6` :  Consumer-initiated cash out - `7` :  ATM triggered or agent-initiated cash out - `8` :  Merchant-initiated remote purchase, face-to-face - `9` :  Merchant-initiated remote purchase, e-commerce  This field is supported only for Mastercard transactions on CyberSource through VisaNet.  Optional field.  **Note** On CyberSource through VisaNet, the value for this field corresponds to the following data in the TC 33 capture file: - Record: CP01 TCR6 - Position: 94 - Field: Mastercard Mobile Remote Payment Program Indicator  The TC 33 Capture file contains information about the purchases and refunds that a merchant submits to CyberSource. CyberSource through VisaNet creates the TC 33 Capture file at the end of the day and sends it to the merchant’s acquirer, who uses this information to facilitate end-of-day clearing processing with payment networks.
     * @member {String} mobileRemotePaymentType
     */
    mobileRemotePaymenStringtType: string;
    /**
     * A private national-use field submitted by acquirers and issuers in South Africa for South Africa-domestic (intra-country) authorizations and financial requests. Values for this field are 00 through 99.
     * @member {String} extendedCreditTotalCount
     */
    extendedCreditTotaStringlCount: string;
    /**
     * On PIN Debit Gateways: This U.S.-only field is optionally used by  participants (merchants and acquirers) to specify the network access priority. VisaNet checks to determine if there are issuer routing preferences for any of the networks specified by the sharing group code. If an issuer preference exists for one of the specified debit networks, VisaNet makes a routing selection based on the issuer’s preference. If an issuer preference exists for more than one of the specified debit networks, or if no issuer preference exists, VisaNet makes a selection based on the acquirer’s routing priorities.  #### PIN debit Priority order of the networks through which he transaction will be routed. Set this value to a series of one-character network codes in your preferred order. This is a list of the network codes:  | Network | Code | | --- | --- | | Accel | E | | AFFN | U | | Alaska Option | 3 | | CU24 | C | | Interlink | G | | Maestro | 8 | | NETS | P | | NYCE | F | | Pulse | H | | Shazam | 7 | | Star | M | | Visa | V |  For example, if the Star network is your first preference and Pulse is your second preference, set this field to a value of `MH`.  When you do not include this value in your PIN debit request, the list of network codes from your account is used. **Note** This field is supported only for businesses located in the U.S.  Optional field for PIN debit credit or PIN debit purchase.
     * @member {String} networkRoutingOrder
     */
    networkRoutingOrdeStringr: string;
    /**
     * Flag that indicates if the transaction is pay by points transaction true: Transaction uses loyalty points false: Transaction does not use loyalty points Default: false
     * @member {Boolean} payByPointsIndicator
     */
    payByPointsIndicatoBooleanr: boolean;
    /**
     * Flag that indicates the functionality we are having for merchants for which auth is done through Cybersource but settlement is done by themselves. true: functionality is supported. Processor should send raw processor auth response to Merchant. false: functionality is not supported. Default: false
     * @member {Boolean} isReturnAuthRecordEnabled
     */
    isReturnAuthRecordEBooleannabled: boolean;
  }

  export class Ptsv2paymentsPaymentInformationCard extends Card {}
  export class Tmsv2customersEmbeddedDefaultPaymentInstrumentCard {
    /**
     * Two-digit month in which the payment card expires.  Format: `MM`.  Possible Values: `01` through `12`.
     * @member {String} expirationMonth
     */
    expirationMonth: string;
    /**
     * Four-digit year in which the credit card expires.  Format: `YYYY`.
     * @member {String} expirationYear
     */
    expirationYear: string;
    /**
     * Value that indicates the card type. Possible Values v2 : v1:   * 001 : visa   * 002 : mastercard - Eurocard—European regional brand of Mastercard   * 003 : american express   * 004 : discover   * 005 : diners club   * 006 : carte blanche   * 007 : jcb   * 008 : optima   * 011 : twinpay credit   * 012 : twinpay debit   * 013 : walmart   * 014 : enRoute   * 015 : lowes consumer   * 016 : home depot consumer   * 017 : mbna   * 018 : dicks sportswear   * 019 : casual corner   * 020 : sears   * 021 : jal   * 023 : disney   * 024 : maestro uk domestic   * 025 : sams club consumer   * 026 : sams club business   * 028 : bill me later   * 029 : bebe   * 030 : restoration hardware   * 031 : delta online — use this value only for Ingenico ePayments. For other processors, use 001 for all Visa card types.   * 032 : solo   * 033 : visa electron   * 034 : dankort   * 035 : laser   * 036 : carte bleue — formerly Cartes Bancaires   * 037 : carta si   * 038 : pinless debit   * 039 : encoded account   * 040 : uatp   * 041 : household   * 042 : maestro international   * 043 : ge money uk   * 044 : korean cards   * 045 : style   * 046 : jcrew   * 047 : payease china processing ewallet   * 048 : payease china processing bank transfer   * 049 : meijer private label   * 050 : hipercard — supported only by the Comercio Latino processor.   * 051 : aura — supported only by the Comercio Latino processor.   * 052 : redecard   * 054 : elo — supported only by the Comercio Latino processor.   * 055 : capital one private label   * 056 : synchrony private label   * 057 : costco private label   * 060 : mada   * 062 : china union pay   * 063 : falabella private label
     * @member {String} type
     */
    type: string;
    /**
     * Number of times a Maestro (UK Domestic) card has been issued to the account holder. The card might or might not have an issue number. The number can consist of one or two digits, and the first digit might be a zero. When you include this value in your request, include exactly what is printed on the card. A value of 2 is different than a value of 02. Do not include the field, even with a blank value, if the card is not a Maestro (UK Domestic) card.  **Note** The issue number is not required for Maestro (UK Domestic) transactions.
     * @member {String} issueNumber
     */
    issueNumber: string;
    /**
     * Month of the start of the Maestro (UK Domestic) card validity period. Do not include the field, even with a blank value, if the card is not a Maestro (UK Domestic) card. `Format: MM`. Possible Values: 01 through 12.  **Note** The start date is not required for Maestro (UK Domestic) transactions.
     * @member {String} startMonth
     */
    startMonth: string;
    /**
     * Year of the start of the Maestro (UK Domestic) card validity period. Do not include the field, even with a blank value, if the card is not a Maestro (UK Domestic) card. `Format: YYYY`.  **Note** The start date is not required for Maestro (UK Domestic) transactions.
     * @member {String} startYear
     */
    startYear: string;
    /**
     * 'Payment Instrument was created / updated as part of a pinless debit transaction.'
     * @member {String} useAs
     */
    useAs: string;
    /**
     * Hash value representing the card.
     * @member {String} hash
     */
    hash: string;
    /**
     * @member {module:model/Tmsv2customersEmbeddedDefaultPaymentInstrumentCardTokenizedInformation} tokenizedInformation
     */
    tokenizedInformation: Tmsv2customersEmbeddedDefaultPaymentInstrumentCardTokenizedInformation;
  }
  export class Riskv1authenticationresultsPaymentInformationCard extends Card {}

  export class Riskv1decisionsClientReferenceInformation extends ClientReferenceInformation {}
  export class Ptsv2paymentsClientReferenceInformation extends ClientReferenceInformation {}

  export class Riskv1authenticationsetupsProcessingInformation {
    actionList: string[];
  }

  export class Riskv1authenticationsetupsPaymentInformation {
    /**
     * @member {module:model/Riskv1authenticationsetupsPaymentInformationTokenizedCard} tokenizedCard
     */
    tokenizedCard: Riskv1authenticationsetupsPaymentInformationTokenizedCard;

    /**
     * /!\ This is not in the CyberSource documentation, but it is required in order for the request to work.
     * @member {module:model/Ptsv2paymentsPaymentInformationInstrumentIdentifier} instrumentIdentifier
     */
    instrumentIdentifier: Ptsv2paymentsPaymentInformationInstrumentIdentifier;

    /**
     * @member {module:model/Riskv1authenticationsetupsPaymentInformationCustomer} customer
     */
    customer: Riskv1authenticationsetupsPaymentInformationCustomer;
  }

  export class Ptsv2paymentsidcapturesOrderInformation {
    amountDetails: Ptsv2paymentsidcapturesOrderInformationAmountDetails;
    /**
     * @member {module:model/Ptsv2paymentsidcapturesOrderInformationBillTo} billTo
     */
    billTo: Ptsv2paymentsidcapturesOrderInformationBillTo;
    /**
     * @member {module:model/Ptsv2paymentsidcapturesOrderInformationShipTo} shipTo
     */
    shipTo: Ptsv2paymentsidcapturesOrderInformationShipTo;
    /**
     * @member {Array.<module:model/Ptsv2paymentsOrderInformationLineItems>} lineItems
     */
    lineItems: Ptsv2paymentsOrderInformationLineItems;
    /**
     * @member {module:model/Ptsv2paymentsidcapturesOrderInformationInvoiceDetails} invoiceDetails
     */
    invoiceDetails: Ptsv2paymentsidcapturesOrderInformationInvoiceDetails;
    /**
     * @member {module:model/Ptsv2paymentsidcapturesOrderInformationShippingDetails} shippingDetails
     */
    shippingDetails: Ptsv2paymentsidcapturesOrderInformationShippingDetails;
  }

  export class Ptsv2paymentsidcapturesOrderInformationAmountDetails {
    /**
     * Grand total for the order. This value cannot be negative. You can include a decimal point (.), but no other special characters. CyberSource truncates the amount to the correct number of decimal places.  **Note** For CTV, FDCCompass, Paymentech processors, the maximum length for this field is 12.  **Important** Some processors have specific requirements and limitations, such as maximum amounts and maximum field lengths. For details, see: - \"Authorization Information for Specific Processors\" in the [Credit Card Services Using the SCMP API Guide](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/). - \"Capture Information for Specific Processors\" in the [Credit Card Services Using the SCMP API Guide](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/). - \"Credit Information for Specific Processors\" in the [Credit Card Services Using the SCMP API Guide](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/).  If your processor supports zero amount authorizations, you can set this field to 0 for the authorization to check if the card is lost or stolen. For details, see \"Zero Amount Authorizations,\" \"Credit Information for Specific Processors\" in [Credit Card Services Using the SCMP API.](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/)  #### Card Present Required to include either this field or `orderInformation.lineItems[].unitPrice` for the order.  #### Invoicing Required for creating a new invoice.  #### PIN Debit Amount you requested for the PIN debit purchase. This value is returned for partial authorizations. The issuing bank can approve a partial amount if the balance on the debit card is less than the requested transaction amount.  Required field for PIN Debit purchase and PIN Debit credit requests. Optional field for PIN Debit reversal requests.  #### GPX This field is optional for reversing an authorization or credit; however, for all other processors, these fields are required.  #### DCC with a Third-Party Provider Set this field to the converted amount that was returned by the DCC provider. You must include either this field or the 1st line item in the order and the specific line-order amount in your request. For details, see `grand_total_amount` field description in [Dynamic Currency Conversion For First Data Using the SCMP API](http://apps.cybersource.com/library/documentation/dev_guides/DCC_FirstData_SCMP/DCC_FirstData_SCMP_API.pdf).  #### FDMS South If you accept IDR or CLP currencies, see the entry for FDMS South in \"Authorization Information for Specific Processors\" of the [Credit Card Services Using the SCMP API.](https://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html/)  #### DCC for First Data Not used.
     * @member {String} totalAmount
     */
    totalAmount: number;
    /**
     * Currency used for the order. Use the three-character [ISO Standard Currency Codes.](http://apps.cybersource.com/library/documentation/sbc/quickref/currencies.pdf)  #### Used by **Authorization** Required field.  **Authorization Reversal** For an authorization reversal (`reversalInformation`) or a capture (`processingOptions.capture` is set to `true`), you must use the same currency that you used in your payment authorization request.  #### PIN Debit Currency for the amount you requested for the PIN debit purchase. This value is returned for partial authorizations. The issuing bank can approve a partial amount if the balance on the debit card is less than the requested transaction amount. For the possible values, see the [ISO Standard Currency Codes](https://developer.cybersource.com/library/documentation/sbc/quickref/currencies.pdf). Returned by PIN debit purchase.  For PIN debit reversal requests, you must use the same currency that was used for the PIN debit purchase or PIN debit credit that you are reversing. For the possible values, see the [ISO Standard Currency Codes](https://developer.cybersource.com/library/documentation/sbc/quickref/currencies.pdf).  Required field for PIN Debit purchase and PIN Debit credit requests. Optional field for PIN Debit reversal requests.  #### GPX This field is optional for reversing an authorization or credit.  #### DCC for First Data Your local currency. For details, see the `currency` field description in [Dynamic Currency Conversion For First Data Using the SCMP API](http://apps.cybersource.com/library/documentation/dev_guides/DCC_FirstData_SCMP/DCC_FirstData_SCMP_API.pdf).  #### Tax Calculation Required for international tax and value added tax only. Optional for U.S. and Canadian taxes. Your local currency.
     * @member {String} currency
     */
    currency: string;
  }

  export class Riskv1authenticationsetupsPaymentInformationCustomer {
    /**
     * Unique identifier for the legacy Secure Storage token used in the transaction. When you include this value in your request, many of the fields that are normally required for an authorization or credit become optional.
     * @member {String} customerId
     */
    customerId: string;
  }

  export class Riskv1authenticationsetupsPaymentInformationTokenizedCard {
    /**
     * Type of transaction that provided the token data. This value does not specify the token service provider; it specifies the entity that provided you with information about the token.  Possible value: - `2`: Near-field communication (NFC) transaction. The customer’s mobile device provided the token data for a contactless EMV transaction. For recurring transactions, use this value if the original transaction was a contactless EMV transaction.  #### Visa Platform Connect - `1`: For Rupay and In App tokenization. Example: InApp apple pay. - `3`: Card/Credential On File Tokenization.  **NOTE** No CyberSource through VisaNet acquirers support EMV at this time.  Required field for PIN debit credit or PIN debit purchase transactions that use payment network tokens; otherwise, not used.
     * @member {String} transactionType
     */
    transactionType: string;
    /**
     * Three-digit value that indicates the card type.  **IMPORTANT** It is strongly recommended that you include the card type field in request messages even if it is optional for your processor and card type. Omitting the card type can cause the transaction to be processed with the wrong card type.  Possible values: - `001`: Visa. For card-present transactions on all processors except SIX, the Visa Electron card type is processed the same way that the Visa debit card is processed. Use card type value `001` for Visa Electron. - `002`: Mastercard, Eurocard[^1], which is a European regional brand of Mastercard. - `003`: American Express - `004`: Discover - `005`: Diners Club - `006`: Carte Blanche[^1] - `007`: JCB[^1] - `014`: Enroute[^1] - `021`: JAL[^1] - `024`: Maestro (UK Domestic)[^1] - `031`: Delta[^1]: Use this value only for Ingenico ePayments. For other processors, use `001` for all Visa card types. - `033`: Visa Electron[^1]. Use this value only for Ingenico ePayments and SIX. For other processors, use `001` for all Visa card types. - `034`: Dankort[^1] - `036`: Cartes Bancaires[^1,4] - `037`: Carta Si[^1] - `039`: Encoded account number[^1] - `040`: UATP[^1] - `042`: Maestro (International)[^1] - `050`: Hipercard[^2,3] - `051`: Aura - `054`: Elo[^3] - `062`: China UnionPay - '070': EFTPOS  [^1]: For this card type, you must include the `paymentInformation.card.type` or `paymentInformation.tokenizedCard.type` field in your request for an authorization or a stand-alone credit. [^2]: For this card type on Cielo 3.0, you must include the `paymentInformation.card.type` or `paymentInformation.tokenizedCard.type` field in a request for an authorization or a stand-alone credit. This card type is not supported on Cielo 1.5. [^3]: For this card type on Getnet and Rede, you must include the `paymentInformation.card.type` or `paymentInformation.tokenizedCard.type` field in a request for an authorization or a stand-alone credit. [^4]: For this card type, you must include the `paymentInformation.card.type` in your request for any payer authentication services.  #### Used by **Authorization** Required for Carte Blanche and JCB. Optional for all other card types.  #### Card Present reply This field is included in the reply message when the client software that is installed on the POS terminal uses the token management service (TMS) to retrieve tokenized payment details. You must contact customer support to have your account enabled to receive these fields in the credit reply message.  Returned by the Credit service.  This reply field is only supported by the following processors: - American Express Direct - Credit Mutuel-CIC - FDC Nashville Global - OmniPay Direct - SIX  #### Google Pay transactions For PAN-based Google Pay transactions, this field is returned in the API response.  #### GPX This field only supports transactions from the following card types: - Visa - Mastercard - AMEX - Discover - Diners - JCB - Union Pay International
     * @member {String} type
     */
    type: string;
    /**
     * One of two possible meanings: - The two-digit month in which a token expires. - The two-digit month in which a card expires. Format: `MM` Possible values: `01` through `12`  **NOTE** The meaning of this field is dependent on the payment processor that is returning the value in an authorization reply. Please see the processor-specific details below.  #### Barclays and Streamline For Maestro (UK Domestic) and Maestro (International) cards on Barclays and Streamline, this must be a valid value (`01` through `12`) but is not required to be a valid expiration date. In other words, an expiration date that is in the past does not cause CyberSource to reject your request. However, an invalid expiration date might cause the issuer to reject your request.  #### Encoded Account Numbers For encoded account numbers (`card_type=039`), if there is no expiration date on the card, use `12`.\\ **Important** It is your responsibility to determine whether a field is required for the transaction you are requesting.  #### Samsung Pay and Apple Pay Month in which the token expires. CyberSource includes this field in the reply message when it decrypts the payment blob for the tokenized transaction.  For processor-specific information, see the `customer_cc_expmo` field in [Credit Card Services Using the SCMP API.](http://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html)
     * @member {String} expirationMonth
     */
    expirationMonth: string;
    /**
     * One of two possible meanings: - The four-digit year in which a token expires. - The four-digit year in which a card expires. Format: `YYYY` Possible values: `1900` through `3000` Data type: Non-negative integer  **NOTE** The meaning of this field is dependent on the payment processor that is returning the value in an authorization reply. Please see the processor-specific details below.  #### Barclays and Streamline For Maestro (UK Domestic) and Maestro (International) cards on Barclays and Streamline, this must be a valid value (1900 through 3000) but is not required to be a valid expiration date. In other words, an expiration date that is in the past does not cause CyberSource to reject your request. However, an invalid expiration date might cause the issuer to reject your request.  #### Encoded Account Numbers For encoded account numbers (`card_ type=039`), if there is no expiration date on the card, use `2021`.  #### FDC Nashville Global and FDMS South You can send in 2 digits or 4 digits. When you send in 2 digits, they must be the last 2 digits of the year.  #### Samsung Pay and Apple Pay Year in which the token expires. CyberSource includes this field in the reply message when it decrypts the payment blob for the tokenized transaction.  **Important** It is your responsibility to determine whether a field is required for the transaction you are requesting.  For processor-specific information, see the `customer_cc_expyr` or `token_expiration_year` field in [Credit Card Services Using the SCMP API.](http://apps.cybersource.com/library/documentation/dev_guides/CC_Svcs_SCMP_API/html)
     * @member {String} expirationYear
     */
    expirationYear: string;
    /**
     * Customer’s payment network token value.
     * @member {String} number
     */
    number: string;
  }

  export class Ptsv2paymentsPaymentInformationInstrumentIdentifier {
    /**
     * Unique identifier for the Instrument Identifier token used in the transaction. When you include this value in your request, many of the fields that can be supplied for an authorization or credit become optional.
     * @member {String} id
     */
    id: string;
  }

  /**
   * -----------------------------------------------------------------
   * Configs
   * -----------------------------------------------------------------
   */
  interface ApiConfig {
    authenticationType: string;
    runEnvironment: string;
    merchantID: string;
    merchantKeyId: string;
    merchantsecretKey: string;
    keyAlias: string;
    keyPass: string;
    keyFileName: string;
    keysDirectory: string;
    useMetaKey: boolean;
    portfolioID: string;
    pemFileDirectory: string;
    logConfiguration: LogConfiguration;
  }

  interface LogConfiguration {
    enableLog: boolean;
    logFileName: string;
    logDirectory: string;
    logFileMaxSize: string;
    loggingLevel: string;
    enableMasking: boolean;
  }

  /**
   * -----------------------------------------------------------------
   * Responses Sub Types
   * -----------------------------------------------------------------
   */

  class ClientReferenceInformation {
    /**
     * Merchant-generated order reference or tracking number. It is recommended that you send a unique value for each transaction so that you can perform meaningful searches for the transaction.  #### Used by **Authorization** Required field.  #### PIN Debit Requests for PIN debit reversals need to use the same merchant reference number that was used in the transaction that is being reversed.  Required field for all PIN Debit requests (purchase, credit, and reversal).  #### FDC Nashville Global Certain circumstances can cause the processor to truncate this value to 15 or 17 characters for Level II and Level III processing, which can cause a discrepancy between the value you submit and the value included in some processor reports.
     * @member {String} code
     */
    code: string;
    /**
     * Used to resume a transaction that was paused for an order modification rule to allow for payer authentication to complete. To resume and continue with the authorization/decision service flow, call the services and include the request id from the prior decision call.
     * @member {String} pausedRequestId
     */
    pausedRequestId: string;
    /**
     * Brief description of the order or any comment you wish to add to the order.
     * @member {String} comments
     */
    comments: string;
    /**
     * @member {module:model/Riskv1decisionsClientReferenceInformationPartner} partner
     */
    partner: string;
  }

  interface ConsumerAuthenticationInformation {
    accessToken: string;
    referenceId: string;
    deviceDataCollectionUrl: string;
  }

  export class Card {
    // The customer’s payment card number, also known as the Primary Account Number (PAN). You can also use this field for encoded account numbers.
    number: string;
    // Two-digit month in which the payment card expires.  Format: `MM`.  Possible Values: `01` through `12`.
    expirationMonth: string;
    // Four-digit year in which the credit card expires.  Format: `YYYY`.
    expirationYear: string;
    // Card Verification Code.  This value is sent to the issuer to support the approval of a network token provision. It is not persisted against the Instrument Identifier.
    securityCode: string;
  }

  interface Links {
    self: Self;
    paymentInstruments: Self;
  }

  interface ConsumerAuthenticationInformation {
    accessToken: string;
    acsTransactionId: string;
    acsUrl: string;
    authenticationTransactionId: string;
    challengeRequired: string;
    strongAuthentication: StrongAuthentication;
    pareq: string;
    specificationVersion: string;
    stepUpUrl: string;
    threeDSServerTransactionId: string;
    veresEnrolled: string;
    directoryServerTransactionId: string;
  }

  interface StrongAuthentication {}

  interface PaymentInformation {
    card: Card;
    instrumentIdentifier: InstrumentIdentifier;
  }

  interface ErrorInformation {
    reason: string;
    message: string;
  }

  interface ConsumerAuthenticationInformation {
    acsTransactionId: string;
    authenticationResult: string;
    authenticationStatusMsg: string;
    cavv: string;
    indicator: string;
    eci: string;
    eciRaw: string;
    paresStatus: string;
    specificationVersion: string;
    threeDSServerTransactionId: string;
    xid: string;
    directoryServerTransactionId: string;
  }

  interface Partner {
    developerId: string;
  }

  type ValidatePayerAuthenticationStatusResponse =
    | "PENDING_AUTHENTICATION"
    | "AUTHENTICATION_SUCCESSFUL"
    | "AUTHENTICATION_FAILED";

  interface Embedded {
    instrumentIdentifier: InstrumentIdentifier;
  }

  interface InstrumentIdentifier {
    _links: any[];
    id: string;
    object: string;
    state: string;
    card: any[];
    processingInformation: any[];
    metadata: any[];
  }

  interface Metadata {
    creator: string;
  }

  interface Self {
    href: string;
  }

  export class Ptsv2paymentsMerchantInformationMerchantDescriptor {
    name: string;
  }

  export class Ptsv2paymentsMerchantInformation {
    merchantDescriptor: Ptsv2paymentsMerchantInformationMerchantDescriptor;
  }
}

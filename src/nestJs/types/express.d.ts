import { Request as ExpressRequest } from "express";
import { DecodedIdToken } from "firebase-admin/auth";

import { RoleData } from "@nest/modules/admin/adminAuthUser/dto/updateUserRole.dto";

declare module "express" {
  export interface Request extends ExpressRequest {
    // FOR LEGACY CODE
    // if you're not using mongoose
    user?: DecodedIdToken;
    verifyPinResult?: VerifyPinResult;
  }

  export interface AuthenticatedRequest<T extends RoleData = object> extends ExpressRequest {
    user: DecodedIdToken & T;
  }
}

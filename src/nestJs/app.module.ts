import { HttpModule } from "@nestjs/axios";
import { CacheModule } from "@nestjs/cache-manager";
import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { APP_INTERCEPTOR } from "@nestjs/core";
import { SentryModule } from "@sentry/nestjs/setup";

import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { LoggerInterceptor } from "./infrastructure/interceptors/logger.interceptor";
import { XApiAuthMiddleware } from "./infrastructure/middlewares/xApiAuth.middleware";
import { AdminModule } from "./modules/admin/admin.module";
import { AppDatabaseModule } from "./modules/appDatabase/appDatabase.module";
import { AuditModule } from "./modules/audit/audit.module";
import { AuthModule } from "./modules/auth/auth.module";
import { CampaignModule } from "./modules/campaign/campaign.module";
import { CloudTaskNotificationModule } from "./modules/cloud-task-notification-handler/cloud-task-notification-handler.module";
import { CloudTaskFleetOrderModule } from "./modules/cloudTaskFleetOrder/cloudTaskFleetOrder.module";
import { CloudTaskOrderNotificationModule } from "./modules/cloudTaskOrderNotification/cloudTaskOrderNotification.module";
import { DatabaseModule } from "./modules/database/database.module";
import { EmailModule } from "./modules/email/email.module";
import { EncryptionModule } from "./modules/encryption/encryption.module";
import { FcmModule } from "./modules/fcm/fcm.module";
import { HailingModule } from "./modules/hailing/hailing.module";
import { HealthModule } from "./modules/health/health.module";
import { IdentityModule } from "./modules/identity/identity.module";
import { LinkModule } from "./modules/link/link.module";
import { GeospatialModule } from "./modules/location/geospatial/geospatial.module";
import { LocationModule } from "./modules/location/location.module";
import { MeModule } from "./modules/me/me.module";
import { MerchantModule } from "./modules/merchant/merchant.module";
import { DriverModule } from "./modules/merchant/merchantDriver/merchantDriver.module";
import { MerchantKeysModule } from "./modules/merchant/merchantKeys/merchantKeys.module";
import { MessageModule } from "./modules/message/message.module";
import { MessageTeamsModule } from "./modules/messageTeams/messageTeams.module";
import { MeterModule } from "./modules/meter/meter.module";
import { GlobalPaymentModule } from "./modules/payment/modules/paymentInstrument/modules/globalPayment/globalPayment.module";
import { PaymentModule } from "./modules/payment/payment.module";
import { PubSubModule } from "./modules/pubsub/pubsub.module";
import { QrCodeModule } from "./modules/qrCode/qrCode.module";
import { SecretsModule } from "./modules/secrets/secrets.module";
import { StorageModule } from "./modules/storage/storage.module";
import { SystemModule } from "./modules/system/system.module";
import { TeamOrderNotificationModule } from "./modules/teamOrderNotification/teamOrderNotification.module";
import { TeamsWebhookModule } from "./modules/teams-webhook/teams-webhook.module";
import { TransactionModule } from "./modules/transaction/transaction.module";
import { TripModule } from "./modules/transaction/transactionFactory/modules/trip/trip.module";
import { UserModule } from "./modules/user/user.module";
import { ContextModule } from "./modules/utils/context/clsContextStorage.module";
import { LoggerModule } from "./modules/utils/logger/logger.module";
import { UtilsModule } from "./modules/utils/utils.module";
import { ValidationModule } from "./modules/validation/validation.module";
import { WebhookModule } from "./modules/webhook/webhook.module";

/**
 * App module
 */
@Module({
  imports: [
    SentryModule.forRoot(),
    AppDatabaseModule.forRoot(),
    ConfigModule.forRoot(),
    MerchantModule,
    DatabaseModule.forRoot(),
    MessageModule,
    MessageTeamsModule,
    MeterModule,
    PaymentModule,
    PubSubModule,
    StorageModule,
    TransactionModule,
    TripModule,
    ContextModule,
    LoggerModule.forRoot(),
    UtilsModule.forRoot(),
    ValidationModule.forRoot(),
    HealthModule,
    AdminModule,
    IdentityModule,
    MeModule,
    UserModule,
    AuditModule,
    QrCodeModule,
    GlobalPaymentModule,
    AuthModule,
    CacheModule.register({ isGlobal: true }),
    EmailModule,
    FcmModule,
    SecretsModule.forRoot(),
    EncryptionModule,
    DriverModule,
    LocationModule,
    HailingModule,
    SystemModule,
    LinkModule,
    MerchantKeysModule,
    WebhookModule,
    CampaignModule,
    CloudTaskNotificationModule,
    CloudTaskFleetOrderModule,
    HttpModule,
    GeospatialModule,
    TeamsWebhookModule,
    TeamOrderNotificationModule,
    CloudTaskOrderNotificationModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggerInterceptor,
    },
  ],
})
export class AppModule implements NestModule {
  /**
   * Configure middleware
   * @param consumer MiddlewareConsumer
   */
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(XApiAuthMiddleware).forRoutes("/cloud-task");
  }
}

import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1733820248327 implements MigrationInterface {
  name = "Migration1733820248327";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "merchant" ADD "email" character varying');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "merchant" DROP COLUMN "email"');
  }
}

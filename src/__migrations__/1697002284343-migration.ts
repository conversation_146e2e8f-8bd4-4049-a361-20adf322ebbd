import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1697002284343 implements MigrationInterface {
  name = "Migration1697002284343";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query("CREATE TYPE \"public\".\"payment_tx_gateway_enum\" AS ENUM('SOEPAY', 'MANUAL')");
    await queryRunner.query(
      'CREATE TABLE "payment_tx" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" character varying NOT NULL, "amount" double precision, "gatewayTransactionId" character varying, "cardNumber" character varying, "paymentMethod" character varying, "gateway" "public"."payment_tx_gateway_enum" NOT NULL DEFAULT \'SOEPAY\', "gatewayResponse" jsonb, "status" character varying, "type" character varying, "txId" character varying, "parentId" character varying, CONSTRAINT "PK_6b5ee36524bbc77b032ce144509" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'CREATE TABLE "tx_tag" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" SERIAL NOT NULL, "tag" character varying NOT NULL, "createdBy" character varying NOT NULL, "note" character varying(500), "removedAt" TIMESTAMP, "txId" character varying, CONSTRAINT "PK_f89a20a8f0763c4985a8b8df4fe" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'CREATE TABLE "tx" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" character varying NOT NULL, "type" character varying NOT NULL, "metadata" jsonb NOT NULL, "payoutStatus" character varying, "total" double precision, "adjustment" double precision, "dashFee" double precision, "parentTxId" character varying, "merchantId" uuid, CONSTRAINT "PK_2e04a1db73a003a59dcd4fe916b" PRIMARY KEY ("id"))',
    );
    await queryRunner.query("CREATE TYPE \"public\".\"merchant_gender_enum\" AS ENUM('M', 'F')");
    await queryRunner.query(
      'CREATE TABLE "merchant" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "phoneNumber" character varying NOT NULL, "name" character varying, "activatedAt" TIMESTAMP, "bankAccount" character varying, "bankAccountOwnerName" character varying, "bankId" character varying, "gender" "public"."merchant_gender_enum", "idDocument" character varying, "idDocumentPhoto" character varying, "isAdmin" boolean NOT NULL DEFAULT false, "nameLocal" character varying, "profilePhoto" character varying, "session" json, "metadata" json NOT NULL, "lastExpectedEndTime" TIMESTAMP, "showCashTrip" boolean NOT NULL DEFAULT false, CONSTRAINT "UQ_a42a24966dba38738d8edbfb943" UNIQUE ("phoneNumber"), CONSTRAINT "PK_9a3850e0537d869734fc9bff5d6" PRIMARY KEY ("id"))',
    );
    await queryRunner.query('CREATE INDEX "phoneNumber_index_for_merchant" ON "merchant" ("phoneNumber") ');
    await queryRunner.query('CREATE INDEX "name_index" ON "merchant" ("name") ');
    await queryRunner.query(
      'CREATE TABLE "user" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "phoneNumber" character varying, "email" character varying, CONSTRAINT "UQ_f2578043e491921209f5dadd080" UNIQUE ("phoneNumber"), CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))',
    );
    await queryRunner.query('CREATE INDEX "phoneNumber_index_for_user" ON "user" ("phoneNumber") ');
    await queryRunner.query('CREATE INDEX "email_index" ON "user" ("email") ');
    await queryRunner.query(
      'CREATE TABLE "message" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" SERIAL NOT NULL, "type" character varying NOT NULL, "template" character varying NOT NULL, "language" character varying NOT NULL, "params" json NOT NULL, "metadata" json NOT NULL, "messageProviderId" character varying NOT NULL, "userId" uuid, CONSTRAINT "PK_ba01f0a3e0123651915008bc578" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'CREATE TABLE "payout" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL, "originalRequest" json NOT NULL, "batchFile" character varying NOT NULL, "bankFile" character varying, "requestedBy" character varying NOT NULL, "requestedAt" TIMESTAMP NOT NULL, "processedAt" TIMESTAMP, "status" character varying NOT NULL, CONSTRAINT "UQ_4a9da19e00e856860989c17c359" UNIQUE ("batchFile"), CONSTRAINT "UQ_ac573d4806df71211da1df52c94" UNIQUE ("bankFile"), CONSTRAINT "PK_1cb73ce021dc6618a3818b0a474" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'ALTER TABLE "payment_tx" ADD CONSTRAINT "FK_5133bcffaaad31a701a78b77b81" FOREIGN KEY ("txId") REFERENCES "tx"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "payment_tx" ADD CONSTRAINT "FK_6630f74a70367f528c2cbff9b98" FOREIGN KEY ("parentId") REFERENCES "payment_tx"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "tx_tag" ADD CONSTRAINT "FK_1a7a9ffeff03860089341a8fdb7" FOREIGN KEY ("txId") REFERENCES "tx"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "tx" ADD CONSTRAINT "FK_6dba74861cf7c8f781b7016fd28" FOREIGN KEY ("parentTxId") REFERENCES "tx"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "tx" ADD CONSTRAINT "FK_7a3357d6b0a7651ba4f9dcfa64b" FOREIGN KEY ("merchantId") REFERENCES "merchant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "message" ADD CONSTRAINT "FK_446251f8ceb2132af01b68eb593" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "message" DROP CONSTRAINT "FK_446251f8ceb2132af01b68eb593"');
    await queryRunner.query('ALTER TABLE "tx" DROP CONSTRAINT "FK_7a3357d6b0a7651ba4f9dcfa64b"');
    await queryRunner.query('ALTER TABLE "tx" DROP CONSTRAINT "FK_6dba74861cf7c8f781b7016fd28"');
    await queryRunner.query('ALTER TABLE "tx_tag" DROP CONSTRAINT "FK_1a7a9ffeff03860089341a8fdb7"');
    await queryRunner.query('ALTER TABLE "payment_tx" DROP CONSTRAINT "FK_6630f74a70367f528c2cbff9b98"');
    await queryRunner.query('ALTER TABLE "payment_tx" DROP CONSTRAINT "FK_5133bcffaaad31a701a78b77b81"');
    await queryRunner.query('DROP TABLE "payout"');
    await queryRunner.query('DROP TABLE "message"');
    await queryRunner.query('DROP INDEX "public"."email_index"');
    await queryRunner.query('DROP INDEX "public"."phoneNumber_index_for_user"');
    await queryRunner.query('DROP TABLE "user"');
    await queryRunner.query('DROP INDEX "public"."name_index"');
    await queryRunner.query('DROP INDEX "public"."phoneNumber_index_for_merchant"');
    await queryRunner.query('DROP TABLE "merchant"');
    await queryRunner.query('DROP TYPE "public"."merchant_gender_enum"');
    await queryRunner.query('DROP TABLE "tx"');
    await queryRunner.query('DROP TABLE "tx_tag"');
    await queryRunner.query('DROP TABLE "payment_tx"');
    await queryRunner.query('DROP TYPE "public"."payment_tx_gateway_enum"');
  }
}

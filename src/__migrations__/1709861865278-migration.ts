import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1709861865278 implements MigrationInterface {
  name = "Migration1709861865278";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" ADD "userId" uuid');
    await queryRunner.query('ALTER TABLE "payment_instrument" ADD "instrumentIdentifier" character varying NOT NULL');
    await queryRunner.query(
      'CREATE INDEX "card_payment_instrument_identifier_index" ON "payment_instrument" ("instrumentIdentifier") ',
    );
    await queryRunner.query(
      'CREATE UNIQUE INDEX "user_instrumentIdentifier_deletedAt" ON "payment_instrument" ("userId", "instrumentIdentifier") WHERE "deletedAt" IS NULL',
    );
    await queryRunner.query(
      'ALTER TABLE "tx" ADD CONSTRAINT "FK_dc7398fde7adaac5eb01a14f816" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" DROP CONSTRAINT "FK_dc7398fde7adaac5eb01a14f816"');
    await queryRunner.query('DROP INDEX "public"."user_instrumentIdentifier_deletedAt"');
    await queryRunner.query('DROP INDEX "public"."card_payment_instrument_identifier_index"');
    await queryRunner.query('ALTER TABLE "payment_instrument" DROP COLUMN "instrumentIdentifier"');
    await queryRunner.query('ALTER TABLE "tx" DROP COLUMN "userId"');
  }
}

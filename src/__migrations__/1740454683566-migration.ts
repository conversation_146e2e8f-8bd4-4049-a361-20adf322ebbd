import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1740454683566 implements MigrationInterface {
  name = "Migration1740454683566";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payment_instrument" ADD "tokenKraken" character varying');
    await queryRunner.query(
      'ALTER TYPE "public"."payment_instrument_paymentgateway_enum" RENAME TO "payment_instrument_paymentgateway_enum_old"',
    );
    await queryRunner.query(
      "CREATE TYPE \"public\".\"payment_instrument_paymentgateway_enum\" AS ENUM('SOEPAY', 'GLOBAL_PAYMENTS', 'KRAKEN', 'MANUAL')",
    );
    await queryRunner.query(
      'ALTER TABLE "payment_instrument" ALTER COLUMN "paymentGateway" TYPE "public"."payment_instrument_paymentgateway_enum" USING "paymentGateway"::"text"::"public"."payment_instrument_paymentgateway_enum"',
    );
    await queryRunner.query('DROP TYPE "public"."payment_instrument_paymentgateway_enum_old"');
    await queryRunner.query('ALTER TYPE "public"."payment_tx_gateway_enum" RENAME TO "payment_tx_gateway_enum_old"');
    await queryRunner.query(
      "CREATE TYPE \"public\".\"payment_tx_gateway_enum\" AS ENUM('SOEPAY', 'GLOBAL_PAYMENTS', 'KRAKEN', 'MANUAL')",
    );
    await queryRunner.query('ALTER TABLE "payment_tx" ALTER COLUMN "gateway" DROP DEFAULT');
    await queryRunner.query(
      'ALTER TABLE "payment_tx" ALTER COLUMN "gateway" TYPE "public"."payment_tx_gateway_enum" USING "gateway"::"text"::"public"."payment_tx_gateway_enum"',
    );
    await queryRunner.query('ALTER TABLE "payment_tx" ALTER COLUMN "gateway" SET DEFAULT \'SOEPAY\'');
    await queryRunner.query('DROP TYPE "public"."payment_tx_gateway_enum_old"');
    await queryRunner.query('CREATE INDEX "card_token_kraken_index" ON "payment_instrument" ("tokenKraken") ');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP INDEX "public"."card_token_kraken_index"');
    await queryRunner.query(
      "CREATE TYPE \"public\".\"payment_tx_gateway_enum_old\" AS ENUM('SOEPAY', 'GLOBAL_PAYMENTS', 'MANUAL')",
    );
    await queryRunner.query('ALTER TABLE "payment_tx" ALTER COLUMN "gateway" DROP DEFAULT');
    await queryRunner.query(
      'ALTER TABLE "payment_tx" ALTER COLUMN "gateway" TYPE "public"."payment_tx_gateway_enum_old" USING "gateway"::"text"::"public"."payment_tx_gateway_enum_old"',
    );
    await queryRunner.query('ALTER TABLE "payment_tx" ALTER COLUMN "gateway" SET DEFAULT \'SOEPAY\'');
    await queryRunner.query('DROP TYPE "public"."payment_tx_gateway_enum"');
    await queryRunner.query('ALTER TYPE "public"."payment_tx_gateway_enum_old" RENAME TO "payment_tx_gateway_enum"');
    await queryRunner.query(
      "CREATE TYPE \"public\".\"payment_instrument_paymentgateway_enum_old\" AS ENUM('SOEPAY', 'GLOBAL_PAYMENTS', 'MANUAL')",
    );
    await queryRunner.query(
      'ALTER TABLE "payment_instrument" ALTER COLUMN "paymentGateway" TYPE "public"."payment_instrument_paymentgateway_enum_old" USING "paymentGateway"::"text"::"public"."payment_instrument_paymentgateway_enum_old"',
    );
    await queryRunner.query('DROP TYPE "public"."payment_instrument_paymentgateway_enum"');
    await queryRunner.query(
      'ALTER TYPE "public"."payment_instrument_paymentgateway_enum_old" RENAME TO "payment_instrument_paymentgateway_enum"',
    );
    await queryRunner.query('ALTER TABLE "payment_instrument" DROP COLUMN "tokenKraken"');
  }
}

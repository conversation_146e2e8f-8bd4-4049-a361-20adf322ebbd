import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1710759183232 implements MigrationInterface {
  name = "Migration1710759183232";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "CREATE TYPE \"public\".\"payment_instrument_paymentgateway_enum\" AS ENUM('SOEPAY', 'GLOBAL_PAYMENTS', 'MANUAL')",
    );
    await queryRunner.query(
      'ALTER TABLE "payment_instrument" ADD "paymentGateway" "public"."payment_instrument_paymentgateway_enum" NOT NULL',
    );
    await queryRunner.query('ALTER TABLE "payment_tx" ADD "paymentInstrumentId" uuid');
    await queryRunner.query(
      'ALTER TABLE "payment_tx" ADD CONSTRAINT "FK_d822fe3e5d164f0588a7bb8ae16" FOREIGN KEY ("paymentInstrumentId") REFERENCES "payment_instrument"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payment_tx" DROP CONSTRAINT "FK_d822fe3e5d164f0588a7bb8ae16"');
    await queryRunner.query('ALTER TABLE "payment_tx" DROP COLUMN "paymentInstrumentId"');
    await queryRunner.query('ALTER TABLE "payment_instrument" DROP COLUMN "paymentGateway"');
    await queryRunner.query('DROP TYPE "public"."payment_instrument_paymentgateway_enum"');
  }
}

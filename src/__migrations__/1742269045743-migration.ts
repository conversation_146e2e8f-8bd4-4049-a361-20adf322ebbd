import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1742269045743 implements MigrationInterface {
  name = "Migration1742269045743";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TYPE "public"."report_job_reporttype_enum" RENAME TO "report_job_reporttype_enum_old"',
    );
    await queryRunner.query(
      "CREATE TYPE \"public\".\"report_job_reporttype_enum\" AS ENUM('TRIP', 'HEARTBEAT', 'SYSTEM_ALERT')",
    );
    await queryRunner.query(
      'ALTER TABLE "report_job" ALTER COLUMN "reportType" TYPE "public"."report_job_reporttype_enum" USING "reportType"::"text"::"public"."report_job_reporttype_enum"',
    );
    await queryRunner.query('DROP TYPE "public"."report_job_reporttype_enum_old"');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query("CREATE TYPE \"public\".\"report_job_reporttype_enum_old\" AS ENUM('TRIP', 'HEARTBEAT')");
    await queryRunner.query(
      'ALTER TABLE "report_job" ALTER COLUMN "reportType" TYPE "public"."report_job_reporttype_enum_old" USING "reportType"::"text"::"public"."report_job_reporttype_enum_old"',
    );
    await queryRunner.query('DROP TYPE "public"."report_job_reporttype_enum"');
    await queryRunner.query(
      'ALTER TYPE "public"."report_job_reporttype_enum_old" RENAME TO "report_job_reporttype_enum"',
    );
  }
}

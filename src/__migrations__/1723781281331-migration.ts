import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1723781281331 implements MigrationInterface {
  name = "Migration1723781281331";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE "tx_event" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" character varying NOT NULL, "createdBy" character varying NOT NULL, "content" jsonb, "txId" character varying, CONSTRAINT "PK_fee8cf0a8000d84d3e2208e63c7" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'ALTER TABLE "tx_event" ADD CONSTRAINT "FK_a43ddc7e922be4267617ff47d1d" FOREIGN KEY ("txId") REFERENCES "tx"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx_event" DROP CONSTRAINT "FK_a43ddc7e922be4267617ff47d1d"');
    await queryRunner.query('DROP TABLE "tx_event"');
  }
}

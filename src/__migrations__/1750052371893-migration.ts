import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1750052371893 implements MigrationInterface {
  name = "Migration1750052371893";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "fleet_partner" DROP COLUMN "timeout_task_interval"');
    await queryRunner.query('ALTER TABLE "fleet_partner" ADD "matching_task_interval" integer NOT NULL DEFAULT \'2\'');
    await queryRunner.query(
      'ALTER TABLE "fleet_partner" ADD "approaching_task_interval" integer NOT NULL DEFAULT \'60\'',
    );
    await queryRunner.query('ALTER TABLE "fleet_partner" ALTER COLUMN "accept_task_interval" SET DEFAULT \'1800\'');
    await queryRunner.query('ALTER TABLE "fleet_partner" ALTER COLUMN "ongoing_task_interval" SET DEFAULT \'300\'');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "fleet_partner" ALTER COLUMN "ongoing_task_interval" DROP DEFAULT');
    await queryRunner.query('ALTER TABLE "fleet_partner" ALTER COLUMN "accept_task_interval" DROP DEFAULT');
    await queryRunner.query('ALTER TABLE "fleet_partner" DROP COLUMN "approaching_task_interval"');
    await queryRunner.query('ALTER TABLE "fleet_partner" DROP COLUMN "matching_task_interval"');
    await queryRunner.query('ALTER TABLE "fleet_partner" ADD "timeout_task_interval" integer NOT NULL');
  }
}

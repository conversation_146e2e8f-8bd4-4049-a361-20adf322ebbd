import { MigrationInterface, QueryRunner } from "typeorm";

export class FixMerchantTableColumnTypo1752654734992 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "merchant" RENAME COLUMN "contractPhoneNumber" TO "contactPhoneNumber"');
    await queryRunner.query('DROP INDEX "public"."contractPhoneNumber_index_for_merchant"');
    await queryRunner.query(
      'CREATE INDEX "contactPhoneNumber_index_for_merchant" ON "merchant" ("contactPhoneNumber") ',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "merchant" RENAME COLUMN "contactPhoneNumber" TO "contractPhoneNumber"');
    await queryRunner.query('DROP INDEX "public"."contactPhoneNumber_index_for_merchant"');
    await queryRunner.query(
      'CREATE INDEX "contractPhoneNumber_index_for_merchant" ON "merchant" ("contractPhoneNumber") ',
    );
  }
}

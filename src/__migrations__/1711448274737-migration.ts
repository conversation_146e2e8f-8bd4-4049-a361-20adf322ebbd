import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1711448274737 implements MigrationInterface {
  name = "Migration1711448274737";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE "user_notification_token" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "token" character varying NOT NULL, "lastUpdateDate" TIMESTAMP NOT NULL, "userId" uuid, CONSTRAINT "PK_fd4c8bf87a4c5bf846c27285a24" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'CREATE UNIQUE INDEX "IDX_6a2d032c6a760a1cd761ce2d01" ON "user_notification_token" ("token") ',
    );
    await queryRunner.query(
      'ALTER TABLE "user_notification_token" ADD CONSTRAINT "FK_bcf510bfdbbf2d7ae31d6d304e7" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "user_notification_token" DROP CONSTRAINT "FK_bcf510bfdbbf2d7ae31d6d304e7"');
    await queryRunner.query('DROP INDEX "public"."IDX_6a2d032c6a760a1cd761ce2d01"');
    await queryRunner.query('DROP TABLE "user_notification_token"');
  }
}

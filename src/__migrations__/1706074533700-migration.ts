import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1706074533700 implements MigrationInterface {
  name = "Migration1706074533700";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "user" ADD "gender" character varying');
    await queryRunner.query('ALTER TABLE "user" ADD "dateOfBirth" TIMESTAMP');
    await queryRunner.query('ALTER TABLE "user" ADD "firstName" character varying(50)');
    await queryRunner.query('ALTER TABLE "user" ADD "lastName" character varying(50)');
    await queryRunner.query('ALTER TABLE "user" ADD "salt" character varying');
    await queryRunner.query('ALTER TABLE "user" ADD "hashPinHash" character varying');
    await queryRunner.query('ALTER TABLE "user" ADD "preferredLanguage" character varying');
    await queryRunner.query('ALTER TABLE "user" ADD "marketingPreferenceEmail" boolean DEFAULT false');
    await queryRunner.query('ALTER TABLE "user" ADD "marketingPreferenceSMS" boolean DEFAULT false');
    await queryRunner.query('ALTER TABLE "user" ADD "marketingConsent" boolean DEFAULT false');
    await queryRunner.query('ALTER TABLE "user" ADD "privateKey" character varying');
    await queryRunner.query('ALTER TABLE "user" ADD "publicKey" character varying');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "publicKey"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "privateKey"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "marketingConsent"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "marketingPreferenceSMS"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "marketingPreferenceEmail"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "preferredLanguage"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "hashPinHash"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "salt"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "lastName"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "firstName"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "dateOfBirth"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "gender"');
  }
}

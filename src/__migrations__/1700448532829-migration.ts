import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1700448532829 implements MigrationInterface {
  name = "Migration1700448532829";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" ADD "payoutAmount" double precision');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" DROP COLUMN "payoutAmount"');
  }
}

import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1732184093715 implements MigrationInterface {
  name = "Migration1732184093715";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP INDEX "public"."user_campaign_tx"');
    await queryRunner.query('ALTER TABLE "campaign" ADD "priority" integer NOT NULL DEFAULT \'0\'');
    await queryRunner.query('ALTER TABLE "campaign" ADD "campaignMaxCount" integer');
    await queryRunner.query('ALTER TYPE "public"."discount_state_enum" RENAME TO "discount_state_enum_old"');
    await queryRunner.query("CREATE TYPE \"public\".\"discount_state_enum\" AS ENUM('FAILED', 'APPLIED', 'REDEEMED')");
    await queryRunner.query(
      'ALTER TABLE "discount" ALTER COLUMN "state" TYPE "public"."discount_state_enum" USING "state"::"text"::"public"."discount_state_enum"',
    );
    await queryRunner.query('DROP TYPE "public"."discount_state_enum_old"');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "applicationRules"');
    await queryRunner.query('ALTER TABLE "campaign" ADD "applicationRules" character varying NOT NULL');
    await queryRunner.query('CREATE UNIQUE INDEX "campaign_tx" ON "discount" ("campaignId", "txId") ');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP INDEX "public"."campaign_tx"');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "applicationRules"');
    await queryRunner.query('ALTER TABLE "campaign" ADD "applicationRules" jsonb NOT NULL');
    await queryRunner.query(
      "CREATE TYPE \"public\".\"discount_state_enum_old\" AS ENUM('UNCLAIMED', 'APPLIED', 'REDEEMED')",
    );
    await queryRunner.query(
      'ALTER TABLE "discount" ALTER COLUMN "state" TYPE "public"."discount_state_enum_old" USING "state"::"text"::"public"."discount_state_enum_old"',
    );
    await queryRunner.query('DROP TYPE "public"."discount_state_enum"');
    await queryRunner.query('ALTER TYPE "public"."discount_state_enum_old" RENAME TO "discount_state_enum"');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "campaignMaxCount"');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "priority"');
    await queryRunner.query('CREATE UNIQUE INDEX "user_campaign_tx" ON "discount" ("campaignId", "userId", "txId") ');
  }
}

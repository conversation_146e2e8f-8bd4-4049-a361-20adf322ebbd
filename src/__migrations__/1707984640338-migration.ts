import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1707984640338 implements MigrationInterface {
  name = "Migration1707984640338";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "CREATE TYPE \"public\".\"payment_instrument_state_enum\" AS ENUM('ACTIVE', 'INACTIVE', 'VERIFICATION_FAILED', 'VERIFICATION_PENDING', 'VERIFIED')",
    );
    await queryRunner.query(
      "CREATE TYPE \"public\".\"payment_instrument_cardtype_enum\" AS ENUM('VISA', 'MASTERCARD')",
    );
    await queryRunner.query(
      'CREATE TABLE "payment_instrument" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "token" character varying NOT NULL, "cardHolderName" character varying NOT NULL, "verifiedAt" TIMESTAMP, "expirationDate" TIMESTAMP NOT NULL, "state" "public"."payment_instrument_state_enum" NOT NULL, "cardPrefix" character varying(6) NOT NULL, "cardSuffix" character varying(4) NOT NULL, "verificationTransactionId" character varying, "isPayerAuthEnroled" boolean, "isPreferred" boolean NOT NULL DEFAULT false, "cardType" "public"."payment_instrument_cardtype_enum" NOT NULL, "deletedAt" TIMESTAMP, "userId" uuid NOT NULL, CONSTRAINT "PK_e6e6c7e0d0cd21865cef13175e9" PRIMARY KEY ("id"))',
    );
    await queryRunner.query('CREATE INDEX "card_token_index" ON "payment_instrument" ("token") ');
    await queryRunner.query(
      'CREATE UNIQUE INDEX "user_token_deletedAt" ON "payment_instrument" ("userId", "token") WHERE "deletedAt" IS NULL',
    );
    await queryRunner.query('ALTER TYPE "public"."payment_tx_gateway_enum" RENAME TO "payment_tx_gateway_enum_old"');
    await queryRunner.query(
      "CREATE TYPE \"public\".\"payment_tx_gateway_enum\" AS ENUM('SOEPAY', 'GLOBAL_PAYMENTS', 'MANUAL')",
    );
    await queryRunner.query('ALTER TABLE "payment_tx" ALTER COLUMN "gateway" DROP DEFAULT');
    await queryRunner.query(
      'ALTER TABLE "payment_tx" ALTER COLUMN "gateway" TYPE "public"."payment_tx_gateway_enum" USING "gateway"::"text"::"public"."payment_tx_gateway_enum"',
    );
    await queryRunner.query('ALTER TABLE "payment_tx" ALTER COLUMN "gateway" SET DEFAULT \'SOEPAY\'');
    await queryRunner.query('DROP TYPE "public"."payment_tx_gateway_enum_old"');
    await queryRunner.query(
      'ALTER TABLE "payment_instrument" ADD CONSTRAINT "FK_4704279a88b7d5d5d27493a2692" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payment_instrument" DROP CONSTRAINT "FK_4704279a88b7d5d5d27493a2692"');
    await queryRunner.query("CREATE TYPE \"public\".\"payment_tx_gateway_enum_old\" AS ENUM('SOEPAY', 'MANUAL')");
    await queryRunner.query('ALTER TABLE "payment_tx" ALTER COLUMN "gateway" DROP DEFAULT');
    await queryRunner.query(
      'ALTER TABLE "payment_tx" ALTER COLUMN "gateway" TYPE "public"."payment_tx_gateway_enum_old" USING "gateway"::"text"::"public"."payment_tx_gateway_enum_old"',
    );
    await queryRunner.query('ALTER TABLE "payment_tx" ALTER COLUMN "gateway" SET DEFAULT \'SOEPAY\'');
    await queryRunner.query('DROP TYPE "public"."payment_tx_gateway_enum"');
    await queryRunner.query('ALTER TYPE "public"."payment_tx_gateway_enum_old" RENAME TO "payment_tx_gateway_enum"');
    await queryRunner.query('DROP INDEX "public"."user_token_deletedAt"');
    await queryRunner.query('DROP INDEX "public"."card_token_index"');
    await queryRunner.query('DROP TABLE "payment_instrument"');
    await queryRunner.query('DROP TYPE "public"."payment_instrument_cardtype_enum"');
    await queryRunner.query('DROP TYPE "public"."payment_instrument_state_enum"');
  }
}

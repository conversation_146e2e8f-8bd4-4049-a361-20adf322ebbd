import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1739243017847 implements MigrationInterface {
  name = "Migration1739243017847";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "CREATE TYPE \"public\".\"webhook_event_type_enum\" AS ENUM('meter.heartbeat', 'meter.session.start', 'meter.session.end')",
    );
    await queryRunner.query(
      'CREATE TABLE "webhook_event" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" "public"."webhook_event_type_enum" NOT NULL, "webhookId" uuid NOT NULL, CONSTRAINT "PK_0f56d2f40f5ec823acf8e8edad1" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'CREATE TABLE "webhook" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "url" character varying NOT NULL, "deletedAt" TIMESTAMP, "signatureKeyId" uuid NOT NULL, "merchantId" uuid NOT NULL, CONSTRAINT "PK_e6765510c2d078db49632b59020" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'ALTER TABLE "webhook_event" ADD CONSTRAINT "FK_d4a62199a45ea905f9ceef9fe89" FOREIGN KEY ("webhookId") REFERENCES "webhook"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "webhook" ADD CONSTRAINT "FK_c093e3f81dd04cfec3f281cfb47" FOREIGN KEY ("signatureKeyId") REFERENCES "merchant_key"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "webhook" ADD CONSTRAINT "FK_6bb220efdc2c409063d5b376a31" FOREIGN KEY ("merchantId") REFERENCES "merchant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "webhook" DROP CONSTRAINT "FK_6bb220efdc2c409063d5b376a31"');
    await queryRunner.query('ALTER TABLE "webhook" DROP CONSTRAINT "FK_c093e3f81dd04cfec3f281cfb47"');
    await queryRunner.query('ALTER TABLE "webhook_event" DROP CONSTRAINT "FK_d4a62199a45ea905f9ceef9fe89"');
    await queryRunner.query('DROP TABLE "webhook"');
    await queryRunner.query('DROP TABLE "webhook_event"');
    await queryRunner.query('DROP TYPE "public"."webhook_event_type_enum"');
  }
}

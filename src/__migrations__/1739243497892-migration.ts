import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1739243497892 implements MigrationInterface {
  name = "Migration1739243497892";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query("CREATE TYPE \"public\".\"report_job_reporttype_enum\" AS ENUM('TRIP', 'HEARTBEAT')");
    await queryRunner.query(
      'CREATE TABLE "report_job" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "lastRunAt" TIMESTAMP, "nextRunAt" TIMESTAMP NOT NULL, "cron" character varying NOT NULL, "reportType" "public"."report_job_reporttype_enum" NOT NULL, "destination" character varying NOT NULL, "queryMetadata" jsonb, "queryString" character varying, "merchantMetadata" jsonb NOT NULL, "createdBy" character varying, CONSTRAINT "PK_35e715439e5691b45a9e4710aeb" PRIMARY KEY ("id"))',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE "report_job"');
    await queryRunner.query('DROP TYPE "public"."report_job_reporttype_enum"');
  }
}

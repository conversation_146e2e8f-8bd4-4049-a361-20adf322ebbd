import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1749706526909 implements MigrationInterface {
  name = "Migration1749706526909";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "fleet_order" ADD "tripTxId" character varying');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "fleet_order" DROP COLUMN "tripTxId"');
  }
}

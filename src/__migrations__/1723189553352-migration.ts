import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1723189553352 implements MigrationInterface {
  name = "Migration1723189553352";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE "merchant_notification_token" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "token" character varying NOT NULL, "lastUpdateDate" TIMESTAMP NOT NULL, "merchantId" uuid, CONSTRAINT "PK_f4db4ed8b0c7ee1892fa2d172da" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'CREATE UNIQUE INDEX "IDX_035b3d4737647d5d89b8c3d85d" ON "merchant_notification_token" ("token") ',
    );
    await queryRunner.query(
      'ALTER TABLE "merchant_notification_token" ADD CONSTRAINT "FK_8650beb3cf18674c753b6d21b5a" FOREIGN KEY ("merchantId") REFERENCES "merchant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "merchant_notification_token" DROP CONSTRAINT "FK_8650beb3cf18674c753b6d21b5a"',
    );
    await queryRunner.query('DROP INDEX "public"."IDX_035b3d4737647d5d89b8c3d85d"');
    await queryRunner.query('DROP TABLE "merchant_notification_token"');
  }
}

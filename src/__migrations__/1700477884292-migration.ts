import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1700477884292 implements MigrationInterface {
  name = "Migration1700477884292";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payment_tx" ADD "requestedBy" character varying');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payment_tx" DROP COLUMN "requestedBy"');
  }
}

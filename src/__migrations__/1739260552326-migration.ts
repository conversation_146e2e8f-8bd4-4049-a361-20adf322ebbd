import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1739260552326 implements MigrationInterface {
  name = "Migration1739260552326";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('CREATE INDEX "webhook_type" ON "webhook_event" ("webhookId", "type") ');
    await queryRunner.query('CREATE INDEX "merchant_deletedAt" ON "webhook" ("merchantId") WHERE "deletedAt" IS NULL');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP INDEX "public"."merchant_deletedAt"');
    await queryRunner.query('DROP INDEX "public"."webhook_type"');
  }
}

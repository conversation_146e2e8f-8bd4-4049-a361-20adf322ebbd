import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateMerchantContractPhoneNumber1752459353913 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "merchant" ADD "contractPhoneNumber" character varying');
    await queryRunner.query(
      'CREATE INDEX "contractPhoneNumber_index_for_merchant" ON "merchant" ("contractPhoneNumber") ',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "merchant" DROP COLUMN "contractPhoneNumber"');
    await queryRunner.query('DROP INDEX "public"."contractPhoneNumber_index_for_merchant"');
  }
}

import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1750154003886 implements MigrationInterface {
  name = "Migration1750154003886";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "user" ADD "marketingPreferencePush" boolean DEFAULT false');
    await queryRunner.query(
      'ALTER TYPE "public"."profile_audit_action_enum" RENAME TO "profile_audit_action_enum_old"',
    );
    await queryRunner.query(
      "CREATE TYPE \"public\".\"profile_audit_action_enum\" AS ENUM('FIREBASE_AUTH_SIGN_UP', 'FIREBASE_AUTH_SIGN_IN', 'FIREBASE_AUTH_PROFILE_UPDATE', 'FIREBASE_AUTH_EMAIL_VERIFIED', 'FIRESTORE_EMAIL_UPDATE', 'FIRESTORE_MARKETING_SMS_UPDATE', 'FIRESTORE_MARKETING_EMAIL_UPDATE', 'FIRESTORE_MARKETING_PUSH_UPDATE', 'ME_API_SET_PIN', 'ME_API_VERIFY_PIN', 'ME_API_UPDATE_PIN', 'ME_API_CREARE_PAYMENT_INSTRUMENT', 'ME_API_UPDATE_PAYMENT_INSTRUMENT')",
    );
    await queryRunner.query(
      'ALTER TABLE "profile_audit" ALTER COLUMN "action" TYPE "public"."profile_audit_action_enum" USING "action"::"text"::"public"."profile_audit_action_enum"',
    );
    await queryRunner.query('DROP TYPE "public"."profile_audit_action_enum_old"');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "CREATE TYPE \"public\".\"profile_audit_action_enum_old\" AS ENUM('FIREBASE_AUTH_SIGN_UP', 'FIREBASE_AUTH_SIGN_IN', 'FIREBASE_AUTH_PROFILE_UPDATE', 'FIREBASE_AUTH_EMAIL_VERIFIED', 'FIRESTORE_EMAIL_UPDATE', 'FIRESTORE_MARKETING_SMS_UPDATE', 'FIRESTORE_MARKETING_EMAIL_UPDATE', 'ME_API_SET_PIN', 'ME_API_VERIFY_PIN', 'ME_API_UPDATE_PIN', 'ME_API_CREARE_PAYMENT_INSTRUMENT', 'ME_API_UPDATE_PAYMENT_INSTRUMENT')",
    );
    await queryRunner.query(
      'ALTER TABLE "profile_audit" ALTER COLUMN "action" TYPE "public"."profile_audit_action_enum_old" USING "action"::"text"::"public"."profile_audit_action_enum_old"',
    );
    await queryRunner.query('DROP TYPE "public"."profile_audit_action_enum"');
    await queryRunner.query(
      'ALTER TYPE "public"."profile_audit_action_enum_old" RENAME TO "profile_audit_action_enum"',
    );
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "marketingPreferencePush"');
  }
}

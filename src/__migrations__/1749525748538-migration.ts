import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1749525748538 implements MigrationInterface {
  name = "Migration1749525748538";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "merchant" ADD "platformMerchantType" character varying NOT NULL DEFAULT \'DASH\'',
    );
    // DROP UQ_a42a24966dba38738d8edbfb943 for merchant
    await queryRunner.query('ALTER TABLE "merchant" DROP CONSTRAINT "UQ_a42a24966dba38738d8edbfb943"');

    await queryRunner.query(
      'ALTER TABLE "merchant" ADD CONSTRAINT "unique_idx_phoneNumber_platformMerchantType" UNIQUE ("phoneNumber", "platformMerchantType")',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "merchant" DROP COLUMN "platformMerchantType"');
    // ADD UQ_a42a24966dba38738d8edbfb943 for merchant
    await queryRunner.query(
      'ALTER TABLE "merchant" ADD CONSTRAINT "UQ_a42a24966dba38738d8edbfb943" UNIQUE ("phoneNumber")',
    );
    await queryRunner.query('ALTER TABLE "merchant" DROP CONSTRAINT "unique_idx_phoneNumber_platformMerchantType"');
  }
}

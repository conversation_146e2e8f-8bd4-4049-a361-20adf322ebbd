import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1752051691714 implements MigrationInterface {
  name = "Migration1752051691714";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE "notification_task" ADD "userType" character varying NOT NULL DEFAULT \'USERS\'',
    );
    await queryRunner.query(
      'ALTER TABLE "notification_history" ADD "userType" character varying NOT NULL DEFAULT \'USERS\'',
    );
    await queryRunner.query('ALTER TABLE "notification_history" DROP CONSTRAINT "FK_notification_history__userIdx"');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "notification_history" DROP COLUMN "userType"');
    await queryRunner.query('ALTER TABLE "notification_task" DROP COLUMN "userType"');
    await queryRunner.query(
      'ALTER TABLE "notification_history" ADD CONSTRAINT "FK_notification_history__userIdx" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }
}

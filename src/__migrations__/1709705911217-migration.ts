import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1709705911217 implements MigrationInterface {
  name = "Migration1709705911217";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "discount" DROP COLUMN "claimExpiredAt"');
    await queryRunner.query('ALTER TABLE "campaign" ADD "maxCount" integer NOT NULL');
    await queryRunner.query('ALTER TABLE "discount" DROP CONSTRAINT "FK_297c856b703e3c424a904d89203"');
    await queryRunner.query('ALTER TABLE "discount" DROP CONSTRAINT "REL_297c856b703e3c424a904d8920"');
    await queryRunner.query('CREATE UNIQUE INDEX "user_campaign_tx" ON "discount" ("userId", "campaignId", "txId") ');
    await queryRunner.query(
      'ALTER TABLE "discount" ADD CONSTRAINT "FK_297c856b703e3c424a904d89203" FOREIGN KEY ("txId") REFERENCES "tx"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "discount" DROP CONSTRAINT "FK_297c856b703e3c424a904d89203"');
    await queryRunner.query('DROP INDEX "public"."user_campaign_tx"');
    await queryRunner.query('ALTER TABLE "discount" ADD CONSTRAINT "REL_297c856b703e3c424a904d8920" UNIQUE ("txId")');
    await queryRunner.query(
      'ALTER TABLE "discount" ADD CONSTRAINT "FK_297c856b703e3c424a904d89203" FOREIGN KEY ("txId") REFERENCES "tx"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "maxCount"');
    await queryRunner.query('ALTER TABLE "discount" ADD "claimExpiredAt" TIMESTAMP NOT NULL');
  }
}

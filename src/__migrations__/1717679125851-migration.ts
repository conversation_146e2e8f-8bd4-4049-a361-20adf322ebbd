import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1717679125851 implements MigrationInterface {
  name = "Migration1717679125851";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" ADD "createdBy" character varying');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" DROP COLUMN "createdBy"');
  }
}

import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1708509768335 implements MigrationInterface {
  name = "Migration1708509768335";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payment_instrument" ADD "expirationYear" character varying NOT NULL');
    await queryRunner.query('ALTER TABLE "payment_instrument" ADD "expirationMonth" character varying NOT NULL');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payment_instrument" DROP COLUMN "expirationMonth"');
    await queryRunner.query('ALTER TABLE "payment_instrument" DROP COLUMN "expirationYear"');
  }
}

import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1709022436022 implements MigrationInterface {
  name = "Migration1709022436022";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE "campaign" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "nameEn" character varying(100) NOT NULL, "nameTc" character varying(100) NOT NULL, "descriptionEn" character varying(4000) NOT NULL, "descriptionTc" character varying(4000) NOT NULL, "imageEn" character varying(200) NOT NULL, "imageTc" character varying(200) NOT NULL, "startAt" TIMESTAMP NOT NULL, "endAt" TIMESTAMP NOT NULL, "applicationRules" jsonb NOT NULL, "discountRules" jsonb, "bonusRules" jsonb, CONSTRAINT "PK_0ce34d26e7f2eb316a3a592cdc4" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      "CREATE TYPE \"public\".\"discount_state_enum\" AS ENUM('CLAIMING', 'UNCLAIMED', 'CLAIMED', 'APPLIED', 'REDEEMED')",
    );
    await queryRunner.query(
      'CREATE TABLE "discount" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "claimExpiredAt" TIMESTAMP NOT NULL, "state" "public"."discount_state_enum" NOT NULL, "campaignId" uuid, "userId" uuid, "txId" character varying, CONSTRAINT "REL_297c856b703e3c424a904d8920" UNIQUE ("txId"), CONSTRAINT "PK_d05d8712e429673e459e7f1cddb" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'ALTER TABLE "discount" ADD CONSTRAINT "FK_994f09daeeb06e2a048bd4ade2b" FOREIGN KEY ("campaignId") REFERENCES "campaign"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "discount" ADD CONSTRAINT "FK_cf500130a0d2ac5af6109068e63" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "discount" ADD CONSTRAINT "FK_297c856b703e3c424a904d89203" FOREIGN KEY ("txId") REFERENCES "tx"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "discount" DROP CONSTRAINT "FK_297c856b703e3c424a904d89203"');
    await queryRunner.query('ALTER TABLE "discount" DROP CONSTRAINT "FK_cf500130a0d2ac5af6109068e63"');
    await queryRunner.query('ALTER TABLE "discount" DROP CONSTRAINT "FK_994f09daeeb06e2a048bd4ade2b"');
    await queryRunner.query('DROP TABLE "discount"');
    await queryRunner.query('DROP TYPE "public"."discount_state_enum"');
    await queryRunner.query('DROP TABLE "campaign"');
  }
}

import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1741764941621 implements MigrationInterface {
  name = "Migration1741764941621";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" ADD "discountThirdParty" double precision');
    await queryRunner.query('ALTER TABLE "tx" ADD "discountDash" double precision');
    await queryRunner.query('ALTER TABLE "discount" ADD "startAt" TIMESTAMP');
    await queryRunner.query('ALTER TABLE "discount" ADD "endAt" TIMESTAMP');
    await queryRunner.query('ALTER TABLE "discount" ADD "redeemedAt" TIMESTAMP');
    await queryRunner.query('ALTER TABLE "discount" ADD "redeemedValue" double precision');
    await queryRunner.query('ALTER TABLE "discount" ADD "rewardCode" character varying(20)');
    await queryRunner.query(
      'ALTER TABLE "discount" ADD CONSTRAINT "UQ_ec23c6947436445e26eed955413" UNIQUE ("rewardCode")',
    );
    await queryRunner.query('ALTER TABLE "campaign" ADD "validityDescriptionEn" character varying(400)');
    await queryRunner.query('ALTER TABLE "campaign" ADD "validityDescriptionTc" character varying(400)');
    await queryRunner.query('ALTER TABLE "campaign" ADD "isOnDemand" boolean NOT NULL DEFAULT true');
    await queryRunner.query('ALTER TABLE "campaign" ADD "eventTriggers" text[]');
    await queryRunner.query('ALTER TABLE "campaign" ADD "expiryRules" text');
    await queryRunner.query("CREATE TYPE \"public\".\"campaign_sponsortype_enum\" AS ENUM('DASH', 'THIRD_PARTY')");
    await queryRunner.query(
      'ALTER TABLE "campaign" ADD "sponsorType" "public"."campaign_sponsortype_enum" NOT NULL DEFAULT \'THIRD_PARTY\'',
    );
    await queryRunner.query('ALTER TABLE "campaign" ADD "availableValue" double precision NOT NULL DEFAULT 0');
    await queryRunner.query('ALTER TYPE "public"."discount_state_enum" RENAME TO "discount_state_enum_old"');
    await queryRunner.query(
      "CREATE TYPE \"public\".\"discount_state_enum\" AS ENUM('FAILED', 'APPLIED', 'REDEEMED', 'ISSUED')",
    );
    await queryRunner.query(
      'ALTER TABLE "discount" ALTER COLUMN "state" TYPE "public"."discount_state_enum" USING "state"::"text"::"public"."discount_state_enum"',
    );
    await queryRunner.query('DROP TYPE "public"."discount_state_enum_old"');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "CREATE TYPE \"public\".\"discount_state_enum_old\" AS ENUM('FAILED', 'APPLIED', 'REDEEMED')",
    );
    await queryRunner.query(
      'ALTER TABLE "discount" ALTER COLUMN "state" TYPE "public"."discount_state_enum_old" USING "state"::"text"::"public"."discount_state_enum_old"',
    );
    await queryRunner.query('DROP TYPE "public"."discount_state_enum"');
    await queryRunner.query('ALTER TYPE "public"."discount_state_enum_old" RENAME TO "discount_state_enum"');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "availableValue"');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "sponsorType"');
    await queryRunner.query('DROP TYPE "public"."campaign_sponsortype_enum"');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "expiryRules"');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "eventTriggers"');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "isOnDemand"');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "validityDescriptionTc"');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "validityDescriptionEn"');
    await queryRunner.query('ALTER TABLE "discount" DROP COLUMN "redeemedValue"');
    await queryRunner.query('ALTER TABLE "discount" DROP COLUMN "redeemedAt"');
    await queryRunner.query('ALTER TABLE "discount" DROP COLUMN "endAt"');
    await queryRunner.query('ALTER TABLE "discount" DROP COLUMN "startAt"');
    await queryRunner.query('ALTER TABLE "discount" DROP CONSTRAINT "UQ_ec23c6947436445e26eed955413"');
    await queryRunner.query('ALTER TABLE "discount" DROP COLUMN "rewardCode"');
    await queryRunner.query('ALTER TABLE "tx" DROP COLUMN "discountDash"');
    await queryRunner.query('ALTER TABLE "tx" DROP COLUMN "discountThirdParty"');
  }
}

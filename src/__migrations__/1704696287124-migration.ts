import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1704696287124 implements MigrationInterface {
  name = "Migration1704696287124";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "user" ADD "appDatabaseId" character varying');
    await queryRunner.query(
      'ALTER TABLE "user" ADD CONSTRAINT "UQ_d33e2a1cd4fd0c7b52eead1d423" UNIQUE ("appDatabaseId")',
    );
    await queryRunner.query('ALTER TABLE "user" ADD "deletedAt" TIMESTAMP');
    await queryRunner.query('ALTER TABLE "user" ALTER COLUMN "phoneNumber" SET NOT NULL');
    await queryRunner.query('ALTER TABLE "user" DROP CONSTRAINT "UQ_f2578043e491921209f5dadd080"');
    await queryRunner.query('ALTER TABLE "user" DROP CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22"');
    await queryRunner.query('CREATE INDEX "appDatabase_index_for_user" ON "user" ("appDatabaseId") ');
    await queryRunner.query(
      'CREATE UNIQUE INDEX "registered_user_email_deletedAt" ON "user" ("email") WHERE "deletedAt" IS NULL',
    );
    await queryRunner.query(
      'CREATE UNIQUE INDEX "anonymous_user_phoneNumber_appDatabaseId_deletedAt" ON "user" ("phoneNumber") WHERE "deletedAt" IS NULL AND "appDatabaseId" IS NULL',
    );
    await queryRunner.query(
      'CREATE UNIQUE INDEX "registered_user_phoneNumber_appDatabaseId_deletedAt" ON "user" ("phoneNumber") WHERE "deletedAt" IS NULL AND "appDatabaseId" IS NOT NULL',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP INDEX "public"."registered_user_phoneNumber_appDatabaseId_deletedAt"');
    await queryRunner.query('DROP INDEX "public"."anonymous_user_phoneNumber_appDatabaseId_deletedAt"');
    await queryRunner.query('DROP INDEX "public"."registered_user_email_deletedAt"');
    await queryRunner.query('DROP INDEX "public"."appDatabase_index_for_user"');
    await queryRunner.query('ALTER TABLE "user" ADD CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email")');
    await queryRunner.query(
      'ALTER TABLE "user" ADD CONSTRAINT "UQ_f2578043e491921209f5dadd080" UNIQUE ("phoneNumber")',
    );
    await queryRunner.query('ALTER TABLE "user" ALTER COLUMN "phoneNumber" DROP NOT NULL');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "deletedAt"');
    await queryRunner.query('ALTER TABLE "user" DROP CONSTRAINT "UQ_d33e2a1cd4fd0c7b52eead1d423"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "appDatabaseId"');
  }
}

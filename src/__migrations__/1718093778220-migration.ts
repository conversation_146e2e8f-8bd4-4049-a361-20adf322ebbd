import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1718093778220 implements MigrationInterface {
  name = "Migration1718093778220";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "CREATE TYPE \"public\".\"merchant_applicationstatus_enum\" AS ENUM('UNDER_REVIEW', 'FOLLOW_UP_NEEDED', 'APPROVED', 'REJECTED')",
    );
    await queryRunner.query(
      'ALTER TABLE "merchant" ADD "applicationStatus" "public"."merchant_applicationstatus_enum"',
    );
    await queryRunner.query('ALTER TABLE "merchant" ADD "bankCardPhoto" character varying');
    await queryRunner.query('ALTER TABLE "merchant" ADD "dateOfBirth" TIMESTAMP');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "merchant" DROP COLUMN "dateOfBirth"');
    await queryRunner.query('ALTER TABLE "merchant" DROP COLUMN "bankCardPhoto"');
    await queryRunner.query('ALTER TABLE "merchant" DROP COLUMN "applicationStatus"');
    await queryRunner.query('DROP TYPE "public"."merchant_applicationstatus_enum"');
  }
}

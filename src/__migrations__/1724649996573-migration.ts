import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1724649996573 implements MigrationInterface {
  name = "Migration1724649996573";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx_event" ALTER COLUMN "createdBy" DROP NOT NULL');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx_event" ALTER COLUMN "createdBy" SET NOT NULL');
  }
}

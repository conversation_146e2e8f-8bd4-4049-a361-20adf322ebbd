import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1750147592885 implements MigrationInterface {
  name = "Migration1750147592885";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "fleet_order" ADD "bookingReceiptSnapshot" jsonb');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "fleet_order" DROP COLUMN "bookingReceiptSnapshot"');
  }
}

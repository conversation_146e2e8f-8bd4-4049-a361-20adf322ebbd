import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1749119639303 implements MigrationInterface {
  name = "Migration1749119639303";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE "fleet_quote" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "userId" uuid NOT NULL, "thirdPartyQuoteId" character varying NOT NULL, "snapshot" jsonb NOT NULL DEFAULT \'{}\', "partnerKey" character varying NOT NULL, CONSTRAINT "PK_0fed55a1ca3efd2a45d7b9b997b" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'CREATE TABLE "fleet_order" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "txId" character varying NOT NULL, "hailingRequestId" character varying NOT NULL, "thirdPartyOrderId" character varying NOT NULL, "status" character varying NOT NULL, "partnerKey" character varying NOT NULL, "userId" uuid, CONSTRAINT "PK_cacc90d38df2b499b0f88e7011f" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'CREATE TABLE "fleet_order_timeline" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "fleetOrderId" uuid NOT NULL, "status" character varying NOT NULL DEFAULT \'MATCHING\', "thirdPartyStatus" character varying NOT NULL, "snapshot" jsonb NOT NULL DEFAULT \'{}\', CONSTRAINT "PK_e8b916b7b63c8a9d8455491842b" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'ALTER TABLE "fleet_quote" ADD CONSTRAINT "FK_fleet_quote_user_id" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "fleet_order" ADD CONSTRAINT "FK_fleet_order_user_id" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "fleet_order_timeline" ADD CONSTRAINT "FK_fleet_order_timeline_fleet_order_id" FOREIGN KEY ("fleetOrderId") REFERENCES "fleet_order"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );

    await queryRunner.query(
      'ALTER TABLE "fleet_order" ADD CONSTRAINT "FK_fleet_order_tx_id" FOREIGN KEY ("txId") REFERENCES "tx"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE "fleet_order_timeline"');
    await queryRunner.query('DROP TABLE "fleet_order"');
    await queryRunner.query('DROP TABLE "fleet_quote"');
  }
}

import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1738659511489 implements MigrationInterface {
  name = "Migration1738659511489";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query("CREATE TYPE \"public\".\"merchant_key_type_enum\" AS ENUM('SIGNATURE_KEY', 'API_KEY')");
    await queryRunner.query(
      'CREATE TABLE "merchant_key" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" "public"."merchant_key_type_enum" NOT NULL, "encryptedKey" bytea NOT NULL, "deletedAt" TIMESTAMP, "merchantId" uuid, CONSTRAINT "UQ_e01ae96f6304e38f3143e7394a2" UNIQUE ("encryptedKey"), CONSTRAINT "PK_4e9269368d6abb8ba4e465d1e0a" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'ALTER TABLE "merchant_key" ADD CONSTRAINT "FK_526999bcb43eff7eb02fde80c9e" FOREIGN KEY ("merchantId") REFERENCES "merchant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "merchant_key" DROP CONSTRAINT "FK_526999bcb43eff7eb02fde80c9e"');
    await queryRunner.query('DROP TABLE "merchant_key"');
    await queryRunner.query('DROP TYPE "public"."merchant_key_type_enum"');
  }
}

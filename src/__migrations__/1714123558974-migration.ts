import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1714123558974 implements MigrationInterface {
  name = "Migration1714123558974";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" ADD "discount" double precision');
    await queryRunner.query('ALTER TABLE "tx" ADD "bonus" double precision');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" DROP COLUMN "bonus"');
    await queryRunner.query('ALTER TABLE "tx" DROP COLUMN "discount"');
  }
}

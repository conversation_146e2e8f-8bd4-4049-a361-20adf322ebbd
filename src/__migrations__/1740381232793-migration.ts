import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1740381232793 implements MigrationInterface {
  name = "Migration1740381232793";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "user" ADD "referralCode" character varying');
    await queryRunner.query(
      'ALTER TABLE "user" ADD CONSTRAINT "UQ_bf0e513b5cd8b4e937fa0702311" UNIQUE ("referralCode")',
    );
    await queryRunner.query('ALTER TABLE "user" ADD "referrerId" uuid');
    await queryRunner.query('ALTER TABLE "merchant" ADD "referralCode" character varying');
    await queryRunner.query(
      'ALTER TABLE "merchant" ADD CONSTRAINT "UQ_c00ec16d35542c42eec0ebdb200" UNIQUE ("referralCode")',
    );
    await queryRunner.query('ALTER TABLE "merchant" ADD "referrerId" uuid');
    await queryRunner.query(
      'ALTER TABLE "user" ADD CONSTRAINT "FK_d928d4688b6eabe05384011f350" FOREIGN KEY ("referrerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "merchant" ADD CONSTRAINT "FK_231d34d03fb278b610ce8bbe206" FOREIGN KEY ("referrerId") REFERENCES "merchant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "merchant" DROP CONSTRAINT "FK_231d34d03fb278b610ce8bbe206"');
    await queryRunner.query('ALTER TABLE "user" DROP CONSTRAINT "FK_d928d4688b6eabe05384011f350"');
    await queryRunner.query('ALTER TABLE "merchant" DROP COLUMN "referrerId"');
    await queryRunner.query('ALTER TABLE "merchant" DROP CONSTRAINT "UQ_c00ec16d35542c42eec0ebdb200"');
    await queryRunner.query('ALTER TABLE "merchant" DROP COLUMN "referralCode"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "referrerId"');
    await queryRunner.query('ALTER TABLE "user" DROP CONSTRAINT "UQ_bf0e513b5cd8b4e937fa0702311"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "referralCode"');
  }
}

import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1707201379425 implements MigrationInterface {
  name = "Migration1707201379425";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "CREATE TYPE \"public\".\"profile_audit_action_enum\" AS ENUM('FIREBASE_AUTH_SIGN_UP', 'FIREBASE_AUTH_SIGN_IN', 'FIREBASE_AUTH_PROFILE_UPDATE', 'FIREBASE_AUTH_EMAIL_VERIFIED', 'FIRESTORE_EMAIL_UPDATE', 'FIRESTORE_MARKETING_SMS_UPDATE', 'FIRESTORE_MARKETING_EMAIL_UPDATE', 'ME_API_SET_PIN', 'ME_API_VERIFY_PIN', 'ME_API_UPDATE_PIN', 'ME_API_CREARE_PAYMENT_INSTRUMENT', 'ME_API_UPDATE_PAYMENT_INSTRUMENT')",
    );
    await queryRunner.query(
      "CREATE TYPE \"public\".\"profile_audit_source_enum\" AS ENUM('FIREBASE_AUTH', 'FIRESTORE', 'ME_API', 'ADMIN')",
    );
    await queryRunner.query(
      'CREATE TABLE "profile_audit" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "action" "public"."profile_audit_action_enum" NOT NULL, "valueAfter" jsonb NOT NULL, "actionBy" character varying NOT NULL, "metadata" jsonb NOT NULL, "source" "public"."profile_audit_source_enum" NOT NULL, "userId" uuid NOT NULL, CONSTRAINT "PK_374a77cfea2fd143458e4e760ec" PRIMARY KEY ("id"))',
    );
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "hashPinHash"');
    await queryRunner.query('ALTER TABLE "user" ADD "hashedPin" character varying');
    await queryRunner.query('ALTER TABLE "user" ADD "pinErrorCount" integer NOT NULL DEFAULT \'0\'');
    await queryRunner.query(
      'ALTER TABLE "profile_audit" ADD CONSTRAINT "FK_f79344d589bbfe245f88f5529f7" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "profile_audit" DROP CONSTRAINT "FK_f79344d589bbfe245f88f5529f7"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "pinErrorCount"');
    await queryRunner.query('ALTER TABLE "user" DROP COLUMN "hashedPin"');
    await queryRunner.query('ALTER TABLE "user" ADD "hashPinHash" character varying');
    await queryRunner.query('DROP TABLE "profile_audit"');
    await queryRunner.query('DROP TYPE "public"."profile_audit_source_enum"');
    await queryRunner.query('DROP TYPE "public"."profile_audit_action_enum"');
  }
}

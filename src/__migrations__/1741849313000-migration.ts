import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1741849313000 implements MigrationInterface {
  name = "Migration1741849313000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query("CREATE INDEX idx_tx_tripStart ON tx ((metadata->>'tripStart') DESC NULLS LAST); ");
    await queryRunner.query("CREATE INDEX idx_tx_tripEnd ON tx ((metadata->>'tripEnd') DESC NULLS LAST); ");
    await queryRunner.query('CREATE INDEX idx_tx_tag ON tx_tag ("txId");');
    await queryRunner.query('CREATE INDEX idx_tx_merchantId ON tx ("merchantId");');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query("DROP INDEX idx_tx_tripStart");
    await queryRunner.query("DROP INDEX idx_tx_tripEnd");
    await queryRunner.query("DROP INDEX idx_tx_tag");
    await queryRunner.query("DROP INDEX idx_tx_merchantId");
  }
}

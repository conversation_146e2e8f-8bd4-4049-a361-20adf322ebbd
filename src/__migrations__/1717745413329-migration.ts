import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1717745413329 implements MigrationInterface {
  name = "Migration1717745413329";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query("CREATE TYPE \"public\".\"merchant_payoutperiod_enum\" AS ENUM('DAILY', 'WEEKLY')");
    await queryRunner.query(
      'ALTER TABLE "merchant" ADD "payoutPeriod" "public"."merchant_payoutperiod_enum" NOT NULL DEFAULT \'DAILY\'',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "merchant" DROP COLUMN "payoutPeriod"');
    await queryRunner.query('DROP TYPE "public"."merchant_payoutperiod_enum"');
  }
}

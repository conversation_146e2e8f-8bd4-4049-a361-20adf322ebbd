import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1726714771499 implements MigrationInterface {
  name = "Migration1726714771499";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "discountRules"');
    await queryRunner.query('ALTER TABLE "campaign" ADD "discountRules" character varying');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "bonusRules"');
    await queryRunner.query('ALTER TABLE "campaign" ADD "bonusRules" character varying');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "bonusRules"');
    await queryRunner.query('ALTER TABLE "campaign" ADD "bonusRules" jsonb');
    await queryRunner.query('ALTER TABLE "campaign" DROP COLUMN "discountRules"');
    await queryRunner.query('ALTER TABLE "campaign" ADD "discountRules" jsonb');
  }
}

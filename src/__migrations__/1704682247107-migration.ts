import { randomUUID } from "node:crypto";

import { MigrationInterface, QueryRunner } from "typeorm";

import { TxAppsNames } from "../nestJs/modules/apps/dto/Apps.dto";

export class Migration1704682247107 implements MigrationInterface {
  name = "Migration1704682247107";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE "tx_app" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, CONSTRAINT "UQ_8e7f1ada6d4d7b73c2a9ff64062" UNIQUE ("name"), CONSTRAINT "PK_41d6d0196bbb350973c12361c71" PRIMARY KEY ("id"))',
    );
    await queryRunner.query('CREATE INDEX "app_name_index" ON "tx_app" ("name") ');
    await queryRunner.query('ALTER TABLE "tx" ADD "txAppId" uuid');
    await queryRunner.query(
      'ALTER TABLE "tx" ADD CONSTRAINT "FK_950ff2f422570c367e546a1a60e" FOREIGN KEY ("txAppId") REFERENCES "tx_app"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );

    /**
     * ===========================================
     * Custom Migrations
     * ===========================================
     */
    const appId = randomUUID();
    await queryRunner.query(`INSERT INTO "tx_app" ("id", "name") VALUES ('${appId}', '${TxAppsNames.TAPXI}')`);

    await queryRunner.query(`UPDATE "tx" SET "txAppId" = '${appId}'`);
    /**
     * ===========================================
     */
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" DROP CONSTRAINT "FK_950ff2f422570c367e546a1a60e"');
    await queryRunner.query('ALTER TABLE "tx" DROP COLUMN "txAppId"');
    await queryRunner.query('DROP INDEX "public"."app_name_index"');
    await queryRunner.query('DROP TABLE "tx_app"');
  }
}

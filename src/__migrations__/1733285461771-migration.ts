import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1733285461771 implements MigrationInterface {
  name = "Migration1733285461771";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" ADD "payoutMerchantId" uuid');
    await queryRunner.query(
      'ALTER TABLE "tx" ADD CONSTRAINT "FK_36373879425b1a1be1b9c47a937" FOREIGN KEY ("payoutMerchantId") REFERENCES "merchant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "tx" DROP CONSTRAINT "FK_36373879425b1a1be1b9c47a937"');
    await queryRunner.query('ALTER TABLE "tx" DROP COLUMN "payoutMerchantId"');
  }
}

import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1726559309606 implements MigrationInterface {
  name = "Migration1726559309606";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TYPE "public"."discount_state_enum" RENAME TO "discount_state_enum_old"');
    await queryRunner.query(
      "CREATE TYPE \"public\".\"discount_state_enum\" AS ENUM('UNCLAIMED', 'APPLIED', 'REDEEMED')",
    );
    await queryRunner.query(
      'ALTER TABLE "discount" ALTER COLUMN "state" TYPE "public"."discount_state_enum" USING "state"::"text"::"public"."discount_state_enum"',
    );
    await queryRunner.query('DROP TYPE "public"."discount_state_enum_old"');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "CREATE TYPE \"public\".\"discount_state_enum_old\" AS ENUM('CLAIMING', 'UNCLAIMED', 'CLAIMED', 'APPLIED', 'REDEEMED')",
    );
    await queryRunner.query(
      'ALTER TABLE "discount" ALTER COLUMN "state" TYPE "public"."discount_state_enum_old" USING "state"::"text"::"public"."discount_state_enum_old"',
    );
    await queryRunner.query('DROP TYPE "public"."discount_state_enum"');
    await queryRunner.query('ALTER TYPE "public"."discount_state_enum_old" RENAME TO "discount_state_enum"');
  }
}

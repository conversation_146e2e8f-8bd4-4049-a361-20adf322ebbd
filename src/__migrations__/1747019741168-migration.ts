import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1747019741168 implements MigrationInterface {
  name = "Migration1747019741168";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payment_instrument" ALTER COLUMN "tokenKraken" SET NOT NULL');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payment_instrument" ALTER COLUMN "tokenKraken" DROP NOT NULL');
  }
}

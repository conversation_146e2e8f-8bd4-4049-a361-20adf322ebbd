import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1721984350217 implements MigrationInterface {
  name = "Migration1721984350217";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payout" DROP CONSTRAINT "UQ_ac573d4806df71211da1df52c94"');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "payout" ADD CONSTRAINT "UQ_ac573d4806df71211da1df52c94" UNIQUE ("bankFile")');
  }
}

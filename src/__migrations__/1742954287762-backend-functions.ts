import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1742954287762 implements MigrationInterface {
  name = "Migration1742954287762";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('CREATE INDEX "campaign_index_for_discount" ON "discount" ("campaignId") ');
    await queryRunner.query('CREATE INDEX "user_index_for_discount" ON "discount" ("userId") ');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP INDEX "public"."user_index_for_discount"');
    await queryRunner.query('DROP INDEX "public"."campaign_index_for_discount"');
  }
}

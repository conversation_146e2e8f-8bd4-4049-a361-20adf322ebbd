import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1750241243566 implements MigrationInterface {
  name = "Migration1750241243566";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE "notification_task" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "status" character varying NOT NULL, "failureReason" character varying, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "type" character varying NOT NULL, "createdBy" character varying NOT NULL, "scheduledAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "cloudTaskReference" character varying, "payload" jsonb NOT NULL, CONSTRAINT "notification_task_pkey" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'CREATE TABLE "notification_history" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "status" character varying NOT NULL, "failureReason" character varying, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying NOT NULL, "body" character varying NOT NULL, "metadata" jsonb NOT NULL, "notificationTaskId" uuid, "userId" uuid, CONSTRAINT "notification_history_pkey" PRIMARY KEY ("id")); COMMENT ON COLUMN "notification_history"."metadata" IS \'Stores anything that enriches our context of this notification\'',
    );
    await queryRunner.query(
      'ALTER TABLE "notification_history" ADD CONSTRAINT "FK_notification_history__notificationTaskIdx" FOREIGN KEY ("notificationTaskId") REFERENCES "notification_task"("id") ON DELETE RESTRICT ON UPDATE NO ACTION',
    );
    await queryRunner.query(
      'ALTER TABLE "notification_history" ADD CONSTRAINT "FK_notification_history__userIdx" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE "notification_history" DROP CONSTRAINT "FK_notification_history__userIdx"');
    await queryRunner.query(
      'ALTER TABLE "notification_history" DROP CONSTRAINT "FK_notification_history__notificationTaskIdx"',
    );
    await queryRunner.query('DROP TABLE "notification_history"');
    await queryRunner.query('DROP TABLE "notification_task"');
  }
}

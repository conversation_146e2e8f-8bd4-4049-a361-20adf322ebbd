import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1749540109437 implements MigrationInterface {
  name = "Migration1749540109437";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'CREATE TABLE "fleet_partner" ("createdAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "updatedAt" TIMESTAMP NOT NULL DEFAULT (\'now\'::text)::timestamp(6) with time zone, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "partnerKey" character varying NOT NULL, "config" jsonb NOT NULL, "accept_task_interval" integer NOT NULL, "ongoing_task_interval" integer NOT NULL, "timeout_task_interval" integer NOT NULL, CONSTRAINT "UQ_93932cc1aeb881acb2efbd29d7c" UNIQUE ("partnerK<PERSON>"), CONSTRAINT "PK_99c0c5e0a36077fa28a79964745" PRIMARY KEY ("id"))',
    );
    await queryRunner.query(
      'INSERT INTO "fleet_partner" ("createdAt", "updatedAt", "id", "partnerKey", "config", "accept_task_interval", "ongoing_task_interval", "timeout_task_interval") VALUES (now(), now(), uuid_generate_v4(), \'SYNCAB\', \'{}\', 10, 10, 10)',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE "fleet_partner"');
  }
}

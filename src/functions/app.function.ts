import cors from "cors";
import express from "express";
import * as functions from "firebase-functions";

import { camelizeMiddleware } from "@legacy/middleware/camelize";
import { loggerMiddleware } from "@legacy/middleware/logger";
import routes from "@legacy/routes";

import { defaultRuntimeOptionsGen1 } from "./_defaultOptions";

const server = express();

server.use(cors({ origin: true }));
server.use(camelizeMiddleware);
server.use(loggerMiddleware);
server.use(routes);

export const app = functions.runWith(defaultRuntimeOptionsGen1).https.onRequest(server);

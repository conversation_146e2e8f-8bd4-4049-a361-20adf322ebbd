import * as functions from "firebase-functions";

import { SqlOnOffRequest } from "@legacy/api/sql";
import Batch<PERSON>ontroller from "@legacy/controller/batchContoller";
import SqlController from "@legacy/controller/sqlController";
import { driver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, driverTripDeleteHand<PERSON> } from "@legacy/handler/driver";
import { meterTripChangeHandler, meterChangeHandler } from "@legacy/handler/meter";
import { session<PERSON><PERSON>e<PERSON><PERSON><PERSON>, unpairExpiredSessionHandler } from "@legacy/handler/session";
import { tripChangeHandler } from "@legacy/handler/trip";
import BankFileService from "@legacy/service/bankfile";
import { MeterDocument } from "@nest/modules/appDatabase/documents/meter.document";
import { TripType } from "@nest/modules/transaction/transactionFactory/modules/trip/types";
import loggerUtils from "@nest/modules/utils/utils/logger.utils";
import { isGreaterOrEqualThan } from "@nest/modules/utils/utils/version.utils";

/** Triggers */
// Meter Colection > Trip -> Trip Collection
export const meterTripChanged = functions
  .runWith({
    minInstances: 2,
  })
  .firestore.document("meters/{meterId}/trips/{tripId}")
  .onWrite(async (change, context) => {
    const tripId = context.params.tripId;
    const meterId = context.params.meterId;
    const { meter_software_version, type } = change.after.data() ?? {};
    if (
      (meter_software_version &&
        isGreaterOrEqualThan(meter_software_version, MeterDocument.newFlowMeterSoftwareVersion)) ||
      type === TripType.HAIL
    ) {
      loggerUtils.info(`trigger: meters/${meterId}/trips/${tripId} skipped`, {
        tx: tripId,
        data: { meter_software_version },
      });
      return;
    }
    loggerUtils.info(`trigger: meters/${meterId}/trips/${tripId} changed`, {
      tx: tripId,
      data: { meter_software_version },
    });
    return await meterTripChangeHandler(change, context);
  });

export const tripChanged = functions
  .runWith({
    minInstances: 2,
  })
  .firestore.document("trips/{tripId}")
  .onWrite(async (change, context) => {
    const tripId = context.params.tripId;
    functions.logger.info("trigger: trips/" + tripId + " changed");
    return await tripChangeHandler(change, context);
  });

/*
 * Add meter change trigger to detect meter status change
 * Log only
 */
export const meterChange = functions
  .runWith({ minInstances: 1 })
  .firestore.document("meters/{meterId}")
  .onUpdate(async (change, context) => {
    const meterId = context.params.meterId;
    console.log(change.after.data());
    functions.logger.info("trigger: meters/" + meterId + " changed");
    return await meterChangeHandler(change, context);
  });

export const sessionChange = functions
  .runWith({
    minInstances: 2,
  })
  .firestore.document("sessions/{sessionId}")
  .onWrite(async (change, context) => {
    const sessionId = context.params.sessionId;
    functions.logger.info("trigger: sessions/" + sessionId + " changed");
    return await sessionChangeHandler(change, context);
  });

export const driverChange = functions
  .runWith({
    minInstances: 2,
  })
  .firestore.document("drivers/{driverId}")
  .onWrite(async (change, context) => {
    const driverId = context.params.driverId;
    functions.logger.info("trigger: drivers/" + driverId + " changed");
    return await driverChangeHandler(change, context);
  });

export const driverSessionTripDelete = functions
  .runWith({
    minInstances: 2,
  })
  .firestore.document("drivers/{driverId}/sessions/{sessionId}/trips/{tripId}")
  .onDelete(async (change, context) => {
    const driverId = context.params.driverId;
    const sessionId = context.params.sessionId;
    const tripId = context.params.tripId;
    functions.logger.info("trigger: drivers/%s/sessions/%s/trips/%s deleted", driverId, sessionId, tripId);
    return await driverTripDeleteHandler(change, context);
  });

export const batchChange = functions
  .runWith({
    minInstances: 2,
  })
  .firestore.document("batches/{batchId}")
  .onWrite(async (change, context) => {
    const batchId = context.params.batchId;
    functions.logger.info("trigger: batch/" + batchId + " changed");
    return await BatchController.batchChangeHandler(change, context);
  });

/** Storage Trigger */
/**
 * TODO: Add bucket name to trigger
 */
export const bucketChange = functions.storage.object().onFinalize(async (object) => {
  const objectPath = object.name || "";
  functions.logger.info("object path finalized: %s", objectPath);
  if (objectPath.startsWith("batch_files/bank_response")) {
    // it's a bank file upload
    functions.logger.debug("processResponseFile", objectPath);
    await BankFileService.processResponseFile(objectPath);
  } else {
    functions.logger.debug("No need to process");
  }
});

// PubSub trigger
export const batchJob = functions.pubsub.topic("batch-job").onPublish((message, context) => {
  console.log("The function was triggered at %s", context.timestamp);
  console.log("The unique ID for the event is %s", context.eventId);
  functions.logger.info(message.json);
  // const payload:BatchRequest = message.json as BatchRequest;
  // BatchController.batchHandler(payload);
});

// cron
export const sessionExpireScheduler = functions.pubsub
  .schedule("* * * * *")
  .timeZone("Asia/Hong_Kong")
  .onRun(async (context) => {
    console.log("running sessionExpireScheduler:" + context.timestamp);
    await unpairExpiredSessionHandler(context);
  });

export const startOrStopCloudSqlInstance = functions.pubsub
  .topic("start-or-stop-cloud-sql-instance")
  .onPublish((message, context) => {
    const data: SqlOnOffRequest = message.json;
    loggerUtils.info("nest trigger: startOrStopCloudSqlInstance", {
      instanceId: data.instanceId,
      projectId: data.projectId,
      policy: data.policy,
    });
    SqlController.restartOrStopSqlInstance(data);
  });

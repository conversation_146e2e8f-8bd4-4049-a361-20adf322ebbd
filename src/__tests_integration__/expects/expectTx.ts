import expects from "../../__tests__/utils/expects.specs.utils";

const expectTx = () =>
  expect.objectContaining({
    adjustment: expect.toBeTypeOrNull(Number),
    createdAt: expect.any(String),
    dashFee: expect.any(Number),
    id: expect.stringMatching(expects.uuid),
    merchant: expect.any(Object),
    merchantId: expect.toBeTypeOrNull(String),
    metadata: expect.any(Object),
    parentTxId: expect.toBeTypeOrNull(String),
    payoutAmount: expect.any(Number),
    payoutStatus: expect.toBeTypeOrNull(String),
    total: expect.any(Number),
    txtag: expect.any(Array),
    type: expect.any(String),
    updatedAt: expect.any(String),
  });

export default expectTx;

import { randomUUID } from "crypto";

import axios from "axios";

import PaymentTx from "../../nestJs/modules/database/entities/paymentTx.entity";
import config from "../config";
import { closeDatabase } from "../database";
import { generatePaymentTxInDB } from "../generators/paymentTx";

import "../customMatchers";

/**
 * transactions test
 */
describe("payments", () => {
  afterAll(async () => {
    await closeDatabase();
  });

  describe("regular enpoint call", () => {
    let paymentTx: PaymentTx;
    let cleanUp: () => void;

    beforeAll(async () => {
      const { result, cleanUp: cleanUpPaymentTx } = await generatePaymentTxInDB();
      paymentTx = result;
      cleanUp = cleanUpPaymentTx;
    });

    afterAll(async () => {
      if (cleanUp) {
        await cleanUp();
      }
    });

    it("Should get the paymentTx", async () => {
      const response = await axios.get(`${config.integrationTestEndpoint}/payments/${paymentTx.id}`);
      expect(response.data).toEqual({
        id: paymentTx.id,
        amount: paymentTx.amount,
        gatewayTransactionId: paymentTx.gatewayTransactionId,
        cardNumber: paymentTx.cardNumber,
        paymentMethod: paymentTx.paymentMethod,
        gateway: paymentTx.gateway,
        gatewayResponse: paymentTx.gatewayResponse,
        status: paymentTx.status,
        type: paymentTx.type,
        requestedBy: null,
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
        tx: expect.any(Object),
      });
    });
  });

  describe("wait for resource to be in db", () => {
    let paymentTxId: string;
    let paymentTx: PaymentTx;
    let cleanUp: () => void;

    beforeAll(async () => {
      paymentTxId = randomUUID();
    });

    afterAll(async () => {
      if (cleanUp) {
        await cleanUp();
      }
    });

    it("Should get the paymentTx with delay while it's not created", async () => {
      const startTime = new Date().getTime();
      const repsonsePromise = axios.get(
        `${config.integrationTestEndpoint}/payments/${paymentTxId}?raw=false&secondsTimeout=10`,
      );

      const setPaymentPromise = new Promise((resolve) => {
        setTimeout(async () => {
          const { result, cleanUp: cleanUpPaymentTx } = await generatePaymentTxInDB({ id: paymentTxId });
          paymentTx = result;
          cleanUp = cleanUpPaymentTx;
          resolve(paymentTx);
        }, 3000);
      });

      const [response] = await Promise.all([repsonsePromise, setPaymentPromise]);

      const endTime = new Date().getTime();

      expect(response.data).toEqual({
        id: paymentTx.id,
        amount: paymentTx.amount,
        gatewayTransactionId: paymentTx.gatewayTransactionId,
        cardNumber: paymentTx.cardNumber,
        paymentMethod: paymentTx.paymentMethod,
        gateway: paymentTx.gateway,
        gatewayResponse: paymentTx.gatewayResponse,
        status: paymentTx.status,
        type: paymentTx.type,
        requestedBy: null,
        createdAt: expect.any(String),
        updatedAt: expect.any(String),
        tx: expect.any(Object),
      });

      expect(endTime - startTime).toBeGreaterThan(3000);
    }, 10000);
  });
});

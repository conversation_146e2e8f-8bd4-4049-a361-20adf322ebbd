import axios from "axios";

import Tx from "../../nestJs/modules/database/entities/tx.entity";
import config from "../config";
import { closeDatabase } from "../database";
import { generateTxInDB } from "../generators/tx";

import "../customMatchers";

/**
 * transactions test
 */
describe("transactions", () => {
  let tx: Tx;
  let cleanUp: () => void;

  beforeAll(async () => {
    const { result, cleanUp: cleanUpTx } = await generateTxInDB();
    tx = result;
    cleanUp = cleanUpTx;
  });

  afterAll(async () => {
    await cleanUp();
    await closeDatabase();
  });

  it("Should get the transactions", async () => {
    const repsonse = await axios.get(`${config.integrationTestEndpoint}/transactions?txId=${tx.id}`);
    expect(repsonse.data).toEqual({
      count: expect.any(Number),
      transactions: expect.any(Array),
    });
    repsonse.data.transactions.forEach((transaction: Tx) => {
      expect(transaction).toEqual({
        adjustment: expect.toBeTypeOrNull(Number),
        createdAt: expect.any(String),
        dashFee: tx.dashFee,
        id: tx.id,
        merchant: expect.any(Object),
        merchantId: expect.toBeTypeOrNull(String),
        metadata: tx.metadata,
        parentTxId: expect.toBeTypeOrNull(String),
        payoutAmount: tx.payoutAmount,
        payoutStatus: expect.toBeTypeOrNull(String),
        total: tx.total,
        txtag: expect.any(Array),
        type: tx.type,
        updatedAt: expect.any(String),
      });
    });
  });
});

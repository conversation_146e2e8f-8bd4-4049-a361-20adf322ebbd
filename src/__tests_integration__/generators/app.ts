import { randomUUID } from "crypto";

import { TxAppsNames } from "../../nestJs/modules/apps/dto/Apps.dto";
import TxApp from "../../nestJs/modules/database/entities/app.entity";
import Tx from "../../nestJs/modules/database/entities/tx.entity";
import { getDatabase } from "../database";

import { GenerateDbFct } from ".";

export const generateAppInDB = async (data?: Partial<TxApp>): GenerateDbFct<TxApp> => {
  const database = await getDatabase();

  const app = TxApp.fromJson({
    id: randomUUID(),
    name: TxAppsNames.TAPXI,
    ...data,
  });

  await database.getRepository(TxApp).insert(app);

  return {
    result: app,
    cleanUp: async () => {
      await database.getRepository(Tx).delete(app.id);
    },
  };
};

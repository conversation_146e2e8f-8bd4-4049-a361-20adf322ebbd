import { randomUUID } from "crypto";

import { generateAppInDB } from "./app";
import Tx from "../../nestJs/modules/database/entities/tx.entity";
import { TxTypes } from "../../nestJs/modules/transaction/dto/txType.dto";
import { getDatabase } from "../database";

import { GenerateDbFct } from ".";

export const generateTxInDB = async (data?: Partial<Tx>): GenerateDbFct<Tx> => {
  const database = await getDatabase();
  let appIdLocal;
  let cleanUpApp: () => Promise<void>;

  if (!data?.txApp?.id) {
    const { result: app, cleanUp } = await generateAppInDB(data?.txApp);
    cleanUpApp = cleanUp;
    data = data ?? {};
    data.txApp = app;
    appIdLocal = app.id;
  }

  const tx = Tx.fromJson({
    id: randomUUID(),
    txApp: { id: appIdLocal },
    dashFee: <PERSON>.ceil(Math.random() * 1000) / 100,
    payoutAmount: Math.ceil(Math.random() * 30000) / 100,
    total: Math.ceil(Math.random() * 30000) / 100,
    type: TxTypes.TRIP,
    metadata: {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...data,
  });

  await database.getRepository(Tx).insert(tx);

  return {
    result: tx,
    cleanUp: async () => {
      await database.getRepository(Tx).delete(tx.id);
      if (cleanUpApp) {
        await cleanUpApp();
      }
    },
  };
};

import { randomUUID } from "crypto";

import { generateTxInDB } from "./tx";
import PaymentTx from "../../nestJs/modules/database/entities/paymentTx.entity";
import { PaymentGatewayTypes } from "../../nestJs/modules/payment/dto/paymentGatewayTypes.dto";
import { PaymentInformationStatus } from "../../nestJs/modules/payment/dto/paymentInformationStatus.dto";
import { PaymentInformationType } from "../../nestJs/modules/payment/dto/paymentInformationType.dto";
import { getDatabase } from "../database";

import { GenerateDbFct } from ".";

export const generatePaymentTxInDB = async (data?: Partial<PaymentTx>): GenerateDbFct<PaymentTx> => {
  const database = await getDatabase();
  let txIdLocal = data?.tx?.id;
  let cleanUpTx = () => {};

  if (!data?.tx?.id) {
    const { result: tx, cleanUp } = await generateTxInDB(data?.tx);
    cleanUpTx = cleanUp;
    data = data ?? {};
    data.tx = tx;
    txIdLocal = tx.id;
  }

  if (!txIdLocal) {
    throw new Error("txIdLocal is undefined");
  }

  const paymentTx = PaymentTx.fromJson(
    {
      id: randomUUID(),
      tx: { id: txIdLocal },
      amount: Math.ceil(Math.random() * 30000) / 100,
      gatewayTransactionId: randomUUID(),
      cardNumber: `${Math.ceil(Math.random() * 100000)}******${Math.ceil(Math.random() * 10000)}`,
      paymentMethod: "DASH",
      gateway: PaymentGatewayTypes.SOEPAY,
      gatewayResponse: {},
      status: PaymentInformationStatus.SUCCESS,
      type: PaymentInformationType.AUTH,
      createdAt: new Date().toISOString(),
      ...data,
    },
    txIdLocal,
  );

  await database.getRepository(PaymentTx).insert(paymentTx);

  return {
    result: paymentTx,
    cleanUp: async () => {
      await database.getRepository(PaymentTx).delete(paymentTx.id);
      await cleanUpTx();
    },
  };
};

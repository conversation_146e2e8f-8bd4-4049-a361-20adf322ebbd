import { ConfigService } from "@nestjs/config";
import dotenv from "dotenv";
import { DataSource } from "typeorm";

import { buildDataSourceOptions } from "../nestJs/modules/database/database.provider";

dotenv.config({ path: `.env.${process.env.GCLOUD_PROJECT ?? "local"}` });

const fakeConfigService = {
  get(key: string) {
    return process.env[key];
  },
};

const dataSource = new DataSource(buildDataSourceOptions(fakeConfigService as ConfigService));

let database: DataSource;

export const getDatabase = async () => {
  if (!database) {
    database = await dataSource.initialize();
  }
  return database;
};

export const closeDatabase = async () => {
  if (database) {
    await database.close();
  }
};

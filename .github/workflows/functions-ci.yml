# CI for functions
name: functions-ci

# Controls when the workflow will run
on:
  push:
    branches-ignore:
      - qa
      - dev
      - main
      - dev2
      
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  install:
    name: Install
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Install Dependencies
        run: yarn

      - name: Cache node_modules
        uses: actions/cache@v3
        id: cache_node_modules
        with:
          path: ./node_modules
          key: node_modules_${{ github.sha }}

  audit:
    name: Audit
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Audit
        run: yarn run audit

  linter:
    name: Lin<PERSON>
    needs: [install, audit]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Download node_modules
        uses: actions/cache@v3
        id: cache_node_modules
        with:
          path: ./node_modules
          key: node_modules_${{ github.sha }}

      - name: Build
        run: yarn build

      - name: Linter
        run: yarn lint

  tests:
    name: Tests
    needs: [install, audit]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Download node_modules
        uses: actions/cache@v3
        id: cache_node_modules
        with:
          path: ./node_modules
          key: node_modules_${{ github.sha }}

      - name: Build
        run: yarn build

      - name: Test
        run: yarn test

  build:
    name: Build
    needs: [install, audit]
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Install Yarn
        run: npm install -g yarn

      - name: Download node_modules
        uses: actions/cache@v3
        id: cache_node_modules
        with:
          path: ./node_modules
          key: node_modules_${{ github.sha }}

      - name: Build
        run: yarn build

      - name: Cache lib
        uses: actions/cache@v3
        id: cache_lib
        with:
          path: ./lib
          key: lib_${{ github.sha }}

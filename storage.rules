rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /meter/{allPaths=**} {
      allow read;
      allow write: if request.auth != null;
    }
    match /public/{allPaths=**} {
      allow read;
      allow write: if request.auth != null;
    }
    match /user_app/carousels/{allPaths=**} {
      allow read;
    }
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
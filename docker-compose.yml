services:
  dash-db:
    image: postgres:15
    restart: always
    ports:
      - 5432:5432
    environment:
      - "POSTGRES_USER=${PGSQL_USERNAME}"
      - "POSTGRES_PASSWORD=${PGSQL_PASSWORD}"
      - "POSTGRES_DB=${PGSQL_DATABASE}"
    healthcheck:
      # we can hook from health signal when it's ready to accept connections
      test: ["CMD-SHELL", "pg_isready -U ${PGSQL_USERNAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 5s
    volumes: 
      # just making sure it's not reused by others i.e. firebase emulator
      - ./.pgdata:/var/lib/postgresql/data
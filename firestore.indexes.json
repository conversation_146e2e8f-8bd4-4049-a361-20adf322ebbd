{"indexes": [{"collectionGroup": "audit_trail", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "trip_pause", "order": "ASCENDING"}, {"fieldPath": "time", "order": "DESCENDING"}]}, {"collectionGroup": "devices", "queryScope": "COLLECTION", "fields": [{"fieldPath": "license_plate", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}]}, {"collectionGroup": "sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "end_time", "order": "ASCENDING"}, {"fieldPath": "expected_end_time", "order": "ASCENDING"}]}, {"collectionGroup": "sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "license_plate", "order": "ASCENDING"}, {"fieldPath": "start_time", "order": "DESCENDING"}]}, {"collectionGroup": "sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "meter_id", "order": "ASCENDING"}, {"fieldPath": "start_time", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "payment_type", "order": "ASCENDING"}, {"fieldPath": "trip_start", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "dispute", "order": "ASCENDING"}, {"fieldPath": "payout_status", "order": "ASCENDING"}, {"fieldPath": "driver.id", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "driver.id", "order": "ASCENDING"}, {"fieldPath": "creation_time", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "driver.id", "order": "ASCENDING"}, {"fieldPath": "creation_time", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "driver.id", "order": "ASCENDING"}, {"fieldPath": "trip_end", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "license_plate", "order": "ASCENDING"}, {"fieldPath": "trip_start", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "meter_id", "order": "ASCENDING"}, {"fieldPath": "creation_time", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "meter_id", "order": "ASCENDING"}, {"fieldPath": "creation_time", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "payment_type", "order": "ASCENDING"}, {"fieldPath": "creation_time", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "payment_type", "order": "ASCENDING"}, {"fieldPath": "payout_status", "order": "ASCENDING"}, {"fieldPath": "creation_time", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "payment_type", "order": "ASCENDING"}, {"fieldPath": "payout_status", "order": "ASCENDING"}, {"fieldPath": "driver.id", "order": "ASCENDING"}, {"fieldPath": "creation_time", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "payout_status", "order": "ASCENDING"}, {"fieldPath": "creation_time", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "payout_status", "order": "ASCENDING"}, {"fieldPath": "creation_time", "order": "DESCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "payout_status", "order": "ASCENDING"}, {"fieldPath": "dispute", "order": "ASCENDING"}, {"fieldPath": "trip_end", "order": "DESCENDING"}, {"fieldPath": "driver.id", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "trip_end", "order": "ASCENDING"}, {"fieldPath": "driver.id", "order": "ASCENDING"}]}, {"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "dispute", "order": "ASCENDING"}, {"fieldPath": "payment_type", "order": "ASCENDING"}, {"fieldPath": "payout_status", "order": "ASCENDING"}, {"fieldPath": "driver.id", "order": "ASCENDING"}, {"fieldPath": "creation_time", "order": "DESCENDING"}]}, {"collectionGroup": "locks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "unlocked_at", "order": "ASCENDING"}, {"fieldPath": "expired_at", "order": "ASCENDING"}]}, {"collectionGroup": "auths", "queryScope": "COLLECTION", "fields": [{"fieldPath": "password_reset.token", "order": "ASCENDING"}, {"fieldPath": "password_reset.expiration", "order": "ASCENDING"}]}, {"collectionGroup": "loggings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "action", "order": "ASCENDING"}, {"fieldPath": "time", "order": "DESCENDING"}]}, {"collectionGroup": "securities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "void_tx_jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "voided_at", "order": "ASCENDING"}, {"fieldPath": "expect_void_at", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "trips", "fieldPath": "trip_start", "ttl": false, "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}, {"collectionGroup": "trips", "fieldPath": "expires_at", "ttl": true, "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"order": "DESCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}, {"order": "ASCENDING", "queryScope": "COLLECTION_GROUP"}]}]}
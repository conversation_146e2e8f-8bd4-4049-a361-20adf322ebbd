import {
  axiosPostMock,
  axiosPatchMock,
  axiosGetMock,
  axiosPutMock,
  axiosDeleteMock,
  axiosCreateMock,
} from "../src/__tests__/utils/axios/axiosMock.specs.utils";

export class AxiosError {}

export const isAxiosError = (error: any): error is AxiosError => {
  return error instanceof AxiosError;
};

export default {
  create: axiosCreateMock,
  get: axiosGetMock,
  post: axiosPostMock,
  put: axiosPutMock,
  patch: axiosPatchMock,
  delete: axiosDeleteMock,
  isAxiosError,
};

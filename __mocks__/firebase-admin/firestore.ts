import {
  CollectionReferenceMock,
  TimestampMock,
} from "../../src/__tests__/utils/firebase-admin/firestoreMock.specs.utils";

export const CollectionReference = CollectionReferenceMock;
export const Timestamp = TimestampMock;

export class Firestore {
  collection = jest.fn().mockReturnValue({
    withConverter: CollectionReferenceMock,
  });
}

export const Filter = {
  where: jest.fn(),
};

export default { CollectionReference, Timestamp, Firestore, Filter };

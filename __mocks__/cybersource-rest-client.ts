import {
  ApiClientMock,
  ApiConfigMock,
  PostInstrumentIdentifierRequestMock,
  PayerAuthSetupRequestMock,
  VoidPaymentRequestMock,
  Riskv1decisionsClientReferenceInformationMock,
  Riskv1authenticationsetupsPaymentInformationMock,
  Riskv1authenticationsetupsPaymentInformationTokenizedCardMock,
  Tmsv2customersEmbeddedDefaultPaymentInstrumentEmbeddedInstrumentIdentifierCardMock,
  Ptsv2paymentsPaymentInformationInstrumentIdentifierMock,
  InstrumentIdentifierApiMock,
  PayerAuthenticationApiMock,
  CreatePaymentRequestMock,
  Ptsv2paymentsClientReferenceInformationMock,
  Ptsv2paymentsProcessingInformationMock,
  Ptsv2paymentsPaymentInformationMock,
  Ptsv2paymentsPaymentInformationCardMock,
  Ptsv2paymentsOrderInformationMock,
  Ptsv2paymentsOrderInformationAmountDetailsMock,
  Ptsv2paymentsConsumerAuthenticationInformationMock,
  Ptsv2paymentsOrderInformationBillToMock,
  Ptsv2paymentsDeviceInformationMock,
  PaymentsApiMock,
  ValidateRequestMock,
  Riskv1authenticationresultsPaymentInformationMock,
  Riskv1authenticationresultsPaymentInformationCardMock,
  Riskv1authenticationresultsConsumerAuthenticationInformationMock,
  CheckPayerAuthEnrollmentRequestMock,
  Riskv1authenticationsOrderInformationMock,
  Riskv1authenticationsOrderInformationAmountDetailsMock,
  Riskv1authenticationsOrderInformationBillToMock,
  Riskv1authenticationsPaymentInformationMock,
  Riskv1authenticationsPaymentInformationCardMock,
  PostPaymentInstrumentRequestMock,
  Tmsv2customersEmbeddedDefaultPaymentInstrumentCardMock,
  Tmsv2customersEmbeddedDefaultPaymentInstrumentBillToMock,
  Tmsv2customersEmbeddedDefaultPaymentInstrumentInstrumentIdentifierMock,
  PaymentInstrumentApiMock,
  Ptsv2paymentsPaymentInformationCustomerMock,
  Ptsv2paymentsPaymentInformationPaymentInstrumentMock,
  Riskv1authenticationsetupsPaymentInformationCustomerMock,
  CapturePaymentRequestMock,
  Ptsv2paymentsidcapturesOrderInformationMock,
  Ptsv2paymentsidcapturesOrderInformationAmountDetailsMock,
  CaptureApiMock,
  VoidApiMock,
  Ptsv2paymentsMerchantInformationMock,
  Ptsv2paymentsMerchantInformationMerchantDescriptorMock,
} from "../src/__tests__/utils/cybersource-rest-client/cybersourceRestClientMocks.specs.utils";

export const CaptureApi = CaptureApiMock;
export const VoidApi = VoidApiMock;
export const ApiClient = ApiClientMock;
export const ApiConfig = ApiConfigMock;
export const PostInstrumentIdentifierRequest = PostInstrumentIdentifierRequestMock;
export const PayerAuthSetupRequest = PayerAuthSetupRequestMock;
export const VoidPaymentRequest = VoidPaymentRequestMock;
export const Riskv1decisionsClientReferenceInformation = Riskv1decisionsClientReferenceInformationMock;
export const Riskv1authenticationsetupsPaymentInformation = Riskv1authenticationsetupsPaymentInformationMock;
export const Riskv1authenticationsetupsPaymentInformationTokenizedCard =
  Riskv1authenticationsetupsPaymentInformationTokenizedCardMock;
export const Tmsv2customersEmbeddedDefaultPaymentInstrumentEmbeddedInstrumentIdentifierCard =
  Tmsv2customersEmbeddedDefaultPaymentInstrumentEmbeddedInstrumentIdentifierCardMock;
export const Ptsv2paymentsPaymentInformationInstrumentIdentifier =
  Ptsv2paymentsPaymentInformationInstrumentIdentifierMock;
export const InstrumentIdentifierApi = InstrumentIdentifierApiMock;
export const PayerAuthenticationApi = PayerAuthenticationApiMock;
export const CreatePaymentRequest = CreatePaymentRequestMock;
export const Ptsv2paymentsClientReferenceInformation = Ptsv2paymentsClientReferenceInformationMock;
export const Ptsv2paymentsProcessingInformation = Ptsv2paymentsProcessingInformationMock;
export const Ptsv2paymentsPaymentInformation = Ptsv2paymentsPaymentInformationMock;
export const Ptsv2paymentsPaymentInformationCard = Ptsv2paymentsPaymentInformationCardMock;
export const Ptsv2paymentsOrderInformation = Ptsv2paymentsOrderInformationMock;
export const Ptsv2paymentsOrderInformationAmountDetails = Ptsv2paymentsOrderInformationAmountDetailsMock;
export const Ptsv2paymentsConsumerAuthenticationInformation = Ptsv2paymentsConsumerAuthenticationInformationMock;
export const PaymentsApi = PaymentsApiMock;
export const Ptsv2paymentsOrderInformationBillTo = Ptsv2paymentsOrderInformationBillToMock;
export const Ptsv2paymentsDeviceInformation = Ptsv2paymentsDeviceInformationMock;
export const ValidateRequest = ValidateRequestMock;
export const Riskv1authenticationresultsPaymentInformation = Riskv1authenticationresultsPaymentInformationMock;
export const Riskv1authenticationresultsPaymentInformationCard = Riskv1authenticationresultsPaymentInformationCardMock;
export const Riskv1authenticationresultsConsumerAuthenticationInformation =
  Riskv1authenticationresultsConsumerAuthenticationInformationMock;
export const CheckPayerAuthEnrollmentRequest = CheckPayerAuthEnrollmentRequestMock;
export const Riskv1authenticationsOrderInformation = Riskv1authenticationsOrderInformationMock;
export const Riskv1authenticationsOrderInformationAmountDetails =
  Riskv1authenticationsOrderInformationAmountDetailsMock;
export const Riskv1authenticationsOrderInformationBillTo = Riskv1authenticationsOrderInformationBillToMock;
export const Riskv1authenticationsPaymentInformation = Riskv1authenticationsPaymentInformationMock;
export const Riskv1authenticationsPaymentInformationCard = Riskv1authenticationsPaymentInformationCardMock;
export const PostPaymentInstrumentRequest = PostPaymentInstrumentRequestMock;
export const Tmsv2customersEmbeddedDefaultPaymentInstrumentCard =
  Tmsv2customersEmbeddedDefaultPaymentInstrumentCardMock;
export const Tmsv2customersEmbeddedDefaultPaymentInstrumentBillTo =
  Tmsv2customersEmbeddedDefaultPaymentInstrumentBillToMock;
export const Tmsv2customersEmbeddedDefaultPaymentInstrumentInstrumentIdentifier =
  Tmsv2customersEmbeddedDefaultPaymentInstrumentInstrumentIdentifierMock;
export const PaymentInstrumentApi = PaymentInstrumentApiMock;
export const Ptsv2paymentsPaymentInformationCustomer = Ptsv2paymentsPaymentInformationCustomerMock;
export const Ptsv2paymentsPaymentInformationPaymentInstrument = Ptsv2paymentsPaymentInformationPaymentInstrumentMock;
export const Riskv1authenticationsetupsPaymentInformationCustomer =
  Riskv1authenticationsetupsPaymentInformationCustomerMock;
export const CapturePaymentRequest = CapturePaymentRequestMock;
export const Ptsv2paymentsidcapturesOrderInformation = Ptsv2paymentsidcapturesOrderInformationMock;
export const Ptsv2paymentsidcapturesOrderInformationAmountDetails =
  Ptsv2paymentsidcapturesOrderInformationAmountDetailsMock;
export const Ptsv2paymentsMerchantInformation = Ptsv2paymentsMerchantInformationMock;
export const Ptsv2paymentsMerchantInformationMerchantDescriptor =
  Ptsv2paymentsMerchantInformationMerchantDescriptorMock;
const cybersourceRestApi = {
  Riskv1authenticationsetupsPaymentInformationCustomer,
  Ptsv2paymentsPaymentInformationPaymentInstrument,
  Ptsv2paymentsPaymentInformationCustomer,
  PaymentInstrumentApi,
  Tmsv2customersEmbeddedDefaultPaymentInstrumentInstrumentIdentifier,
  Tmsv2customersEmbeddedDefaultPaymentInstrumentBillTo,
  Tmsv2customersEmbeddedDefaultPaymentInstrumentCard,
  PostPaymentInstrumentRequest,
  ApiClient,
  ApiConfig,
  PostInstrumentIdentifierRequest,
  PayerAuthSetupRequest,
  CreatePaymentRequest,
  Tmsv2customersEmbeddedDefaultPaymentInstrumentEmbeddedInstrumentIdentifierCard,
  Riskv1decisionsClientReferenceInformation,
  Riskv1authenticationsetupsPaymentInformationTokenizedCard,
  Riskv1authenticationsetupsPaymentInformation,
  Ptsv2paymentsPaymentInformationInstrumentIdentifier,
  InstrumentIdentifierApi,
  PayerAuthenticationApi,
  Ptsv2paymentsClientReferenceInformation,
  Ptsv2paymentsProcessingInformation,
  Ptsv2paymentsPaymentInformation,
  Ptsv2paymentsPaymentInformationCard,
  Ptsv2paymentsOrderInformation,
  Ptsv2paymentsOrderInformationAmountDetails,
  Ptsv2paymentsConsumerAuthenticationInformation,
  PaymentsApi,
  Ptsv2paymentsOrderInformationBillTo,
  Ptsv2paymentsDeviceInformation,
  ValidateRequest,
  Riskv1authenticationresultsPaymentInformation,
  Riskv1authenticationresultsPaymentInformationCard,
  Riskv1authenticationresultsConsumerAuthenticationInformation,
  CheckPayerAuthEnrollmentRequest,
  Riskv1authenticationsOrderInformation,
  Riskv1authenticationsOrderInformationAmountDetails,
  Riskv1authenticationsOrderInformationBillTo,
  Riskv1authenticationsPaymentInformation,
  Riskv1authenticationsPaymentInformationCard,
  CapturePaymentRequest,
  Ptsv2paymentsidcapturesOrderInformation,
  Ptsv2paymentsidcapturesOrderInformationAmountDetails,
  CaptureApi,
  VoidApi,
  VoidPaymentRequest,
  Ptsv2paymentsMerchantInformation,
  Ptsv2paymentsMerchantInformationMerchantDescriptor,
};

export default cybersourceRestApi;

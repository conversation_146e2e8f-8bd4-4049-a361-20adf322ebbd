import {
  RSA_PKCS1_OAEP_PADDING_Mock,
  mockPrivateDecrypt,
  mockGenerateKeyPairSync,
} from "../src/__tests__/utils/crypto.specs.utils";

export const constants = {
  RSA_PKCS1_OAEP_PADDING: RSA_PKCS1_OAEP_PADDING_Mock,
};

export const privateDecrypt = mockPrivateDecrypt;
export const generateKeyPairSync = mockGenerateKeyPairSync;

const crypto = () => ({
  privateDecrypt,
  generateKeyPairSync,
  constants,
});

// function to generate a random UUID
export function randomUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

export default crypto;

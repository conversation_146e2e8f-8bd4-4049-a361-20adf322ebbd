{
  "compilerOptions": {
    "lib": [
      "es6",
      "es2015.promise"
    ],

    "module": "commonjs",
    "noImplicitReturns": true,
    "emitDecoratorMetadata": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "noUnusedLocals": true,
    "outDir": "lib",
    "sourceMap": true,
    "strict": true,
    "target": "es6",
    "experimentalDecorators": true,
    "strictPropertyInitialization": false,
    "skipLibCheck": true,
    "inlineSources": true,

    // Set `sourceRoot` to  "/" to strip the build path prefix
    // from generated source code references.
    // This improves issue grouping in Sentry.
    "sourceRoot": "/",
    "baseUrl": "./",
    "paths": {
      "@tests/*": ["src/__tests__/*"],
      "@migrations/*": ["src/__migrations__/*"],
      "@functions/*": ["src/functions/*"],
      "@static/*": ["src/static/*"],
      "@legacy/*": ["src/legacy/*"],
      "@nest/*": ["src/nestJs/*"]
    }
  },
  "compileOnSave": true,
  "include": [
    "src"
  ],
  "exclude": [
    "node_modules"
  ],
  "ts-node": {
    "require": ["tsconfig-paths/register"]
  }
}
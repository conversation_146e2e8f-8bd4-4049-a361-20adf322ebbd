const readline = require("readline");
const fs = require("fs");
const config = require("../audit.config.json");

const skippedVulnerabilities = config?.skippedVulnerabilities ?? [];
const limits = config?.limits ?? {
  info: 1,
  low: 1,
  moderate: 1,
  high: 1,
  critical: 1,
};

const readFile = () => {
  const jsonArray = [];
  const rl = readline.createInterface({
    input: fs.createReadStream("./audit.jsonl"),
    crlfDelay: Infinity,
  });

  return new Promise((resolve) => {
    rl.on("line", (line) => {
      jsonArray.push(JSON.parse(line));
    });
    rl.on("close", () => {
      resolve(jsonArray);
    });
  });
};

const logResults = (content) => {
  content
    .filter((content) => content.type === "auditAdvisory")
    .map((content) => ({
      Level: content.data.advisory.severity,
      Title: content.data.advisory.title,
      Package: content.data.advisory.module_name,
      Recommendation: content.data.advisory.recommendation,
      Url: content.data.advisory.url,
    }))
    .forEach((content) => {
      console.table(content);
    });
};

readFile().then((content) => {
  const audit = content
    .filter((content) => content.type === "auditAdvisory")
    .reduce((result, current) => {
      if (skippedVulnerabilities.includes(current.data.advisory.url)) {
        return result;
      }

      if (!result[current.data.advisory.severity]) {
        result[current.data.advisory.severity] = [];
      }
      result[current.data.advisory.severity].push(current);

      return result;
    }, {});

  Object.entries(audit).forEach(([level, vulnerabilities]) => {
    if (limits[level] !== 0 && vulnerabilities.length >= limits[level]) {
      logResults(content);
      console.log(vulnerabilities.length, level, "vulnerabilities found");
      process.exit(1);
    }
  });

  console.log("No thresholds reached");
  logResults(content);
});

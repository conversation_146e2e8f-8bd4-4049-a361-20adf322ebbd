#!/bin/bash

GIT_HASH=$(git rev-parse HEAD)
CURRENT_TIMESTAMP=$(date +"%Y-%m-%dT%H:%M:%S%z")
GET_CURRENT_GIT_DIFF=$(git diff --name-only)
SOURCE=$(hostname)

# Write GIT_HASH, CURRENT_TIMESTAMP and GET_CURRENT_GIT_DIFF in the file ./src/static/info.json.
echo '{
  "source": "'${SOURCE}'",
  "commit_hash": "'${GIT_HASH}'",
  "timestamp": "'${CURRENT_TIMESTAMP}'",
  "git_diff": "'${GET_CURRENT_GIT_DIFF}'"
}' > ./src/static/info.json

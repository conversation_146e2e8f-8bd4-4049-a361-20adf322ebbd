#!/bin/bash

# command: ./scripts/updateConnectionStringCloudRun.sh dash-dev2-edcb3:asia-east2:dash,dash-dev2-edcb3:asia-east2:dash-replica-read0
# remember to replace dash-dev2-edcb3 with project id
updateFunction() { 
  echo "------------------------------------------------------------------------------------"
  echo "Updating $1..."
  gcloud run services update $1 \
    --region asia-east2 \
    --set-cloudsql-instances $2
  echo "Done $1"
  echo "------------------------------------------------------------------------------------"
}

FUNCTIONS=$(gcloud run services list | sed 's/|/ /' | awk '{print $2}')

for FUNCTION in $FUNCTIONS
do
  if [[ "$FUNCTION" = "REGION" ]]; then
    continue
  fi

  {
    updateFunction $FUNCTION $1
  } &
done

{"functions": [{"source": ".", "codebase": "default", "runtime": "nodejs20", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"]}], "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5002}, "pubsub": {"port": 8085}, "storage": {"port": 9199}, "ui": {"enabled": true}, "singleProjectMode": true}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}}
module.exports = {
  root: true,
  env: {
    es6: true,
    node: true,
    jest: true,
  },
  extends: ["eslint:recommended", "plugin:import/recommended", "plugin:import/typescript", "prettier"],
  plugins: ["unused-imports"],
  settings: {
    "import/resolver": {
      typescript: true,
      node: true,
    },
  },
  parser: "@typescript-eslint/parser",
  parserOptions: {
    tsconfigRootDir: __dirname,
    allowImportExportEverywhere: true,
    ecmaVersion: 8,
    project: ["tsconfig.json", "tsconfig.dev.json", "jest.config.json"],
    sourceType: "module",
  },
  ignorePatterns: ["node_modules/*", "**/node_modules/*", "/lib/**/*", "/coverage/**/*"],
  overrides: [
    {
      files: ["**/*.ts"],
      parser: "@typescript-eslint/parser",
      extends: ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"],
      rules: {
        "@typescript-eslint/no-unused-vars": ["warn", { argsIgnorePattern: "^_" }],
        "@typescript-eslint/explicit-function-return-type": "off",
        "prettier/prettier": ["error", {}, { usePrettierrc: true }],
        "@typescript-eslint/explicit-module-boundary-types": "off",
        "@typescript-eslint/no-namespace": "off",
        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/no-non-null-assertion": "off",
        "@typescript-eslint/ban-ts-comment": "off",
        "@typescript-eslint/no-explicit-any": 1,
        "@typescript-eslint/naming-convention": [
          "error",
          {
            selector: "enumMember",
            format: [],
            custom: {
              regex: "^[A-Z_0-9]+$",
              match: true,
            },
          },
        ],
        quotes: ["error", "double", { avoidEscape: true }],
        "import/order": [
          "error",
          {
            groups: ["builtin", "external", "internal", ["parent", "sibling"], "object", "type", "index"],
            "newlines-between": "always",
            alphabetize: {
              order: "asc",
              caseInsensitive: true,
            },
          },
        ],
        "no-unused-vars": "off", // or "@typescript-eslint/no-unused-vars": "off",
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": [
          "warn",
          { vars: "all", varsIgnorePattern: "^_", args: "after-used", argsIgnorePattern: "^_" },
        ],
        "import/namespace": "off",
      },
    },
  ],
};

// DO NOT UPDATE MANUALLY!

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents{
    function role() {
    	let defaultRole = "DEFAULT";
    	return request.auth.token.get('role', defaultRole);
    }
    function isAdmin() {
      return role() == "ADMIN";
    }
    function isMeterUser() {
    	return role() == "METER" || request.auth.token.get('meterDevice', false) == true;
    }
    function isDriverUser() {
      return role() == "DRIVER" || request.auth.token.get('phone_number', null) != null;
    }
    function isGarageUser() {
    	return role() == "GARAGE";
    }
    function isAppUser() {
      return request.auth.token.get('user_id', null) != null;
    }
    function isDriverAdmin() {
      return isDriverUser() 
        && get(/databases/$(database)/documents/drivers/$(request.auth.token.phone_number)).data.get('is_admin', false) == true;
    }
    function isDriverUserOwner(documentId) {
      return isDriverUser() && (request.auth.token.get('phone_number', null) == documentId);
    }
    function isTripInfoUser() {
      return isMeterUser();
    }
    function isInsructiveUser() {
      return isMeterUser();
    }
    function isAppUserOwner(documentId) {
      return isAppUser() && (request.auth.token.get('user_id', null) == documentId);
    }

    match /activation/{document=**} {
    	allow read, write: if isAdmin();
    }
    match /batches/{document=**} {
    	allow read, write: if isAdmin();
    }
    match /configurations/{document=**} {
    	allow read;
    	allow write: if isAdmin();
    }
    match /drivers {
      allow read, write: if isAdmin() || isDriverAdmin();

      match /{document=**} {
        allow read, write: if isAdmin() || isDriverAdmin();
      }

      match /{driverId} {
        allow read, write: if isDriverUserOwner(driverId) || isDriverAdmin() || isAdmin();

        match /sessions/{sessionId}/{document=**} {
          allow read: if isMeterUser() || isDriverUserOwner(driverId) || isAdmin() || isDriverAdmin();
          allow write: if isMeterUser() || isAdmin() || isDriverAdmin();
        }

        match /notifications/{notificationId}/{document=**} {
          allow read: if isAdmin() || isDriverUserOwner(driverId) || isDriverAdmin();
          allow write: if isAdmin() || isDriverAdmin();
        }

        match /trips/{tripId}/{document=**} {
          allow read: if isAdmin() || isDriverUserOwner(driverId) || isDriverAdmin();
          allow write: if isAdmin() || isDriverAdmin();
        }

        match /hailing_vehicles/{vehicle_id}/{document=**} {
        	allow read: if isAdmin() || isDriverUserOwner(driverId) || isDriverAdmin();
          allow write: if isAdmin() || isDriverUserOwner(driverId) || isDriverAdmin();
        }

        match /{document=**} {
        	allow read, write: if isAdmin() || isDriverAdmin() || isDriverUserOwner(driverId);
        }
      }
    }
    match /meters/{document=**} { // No way to identify the meter id in the token so can't segregate by meter Id -> any meter can access other meter data
    	allow read, write: if isMeterUser() || isInsructiveUser() || isTripInfoUser() || isAdmin() || isGarageUser();
    }
    match /sessions/{document=**} {
    	allow read, write: if isMeterUser() || isAdmin();
    }
    match /trips/{document=**} {
    	allow read, write: if isAdmin();
    }
    match /users/{userId}/{document=**} {
    	allow read, write: if isAppUserOwner(userId) || isAdmin();
    }
    match /happenings/{document=**} {
      allow read;
      allow write: if isAdmin();
    }
    match /soepay_devices/{document=**} {
      allow read, write: if isMeterUser() || isInsructiveUser() || isTripInfoUser() || isAdmin() || isGarageUser();
    }
    match /meter_devices/{document=**} {
      allow read, write: if isAdmin() || isGarageUser() || isMeterUser();
    }
    match /vehicle_types/{document=**}{
    	allow read;
    	allow write: if isAdmin();
    }
    match /campaigns/{document=**} {
      allow read;
      allow write: if isAdmin();
    }
    match /fleets/{document=**}{
    	allow read: if isGarageUser() || isDriverUser() || isAdmin() || isAppUser()
      allow write: if isAdmin();
    }
  }
}